-- Add new columns to profiles table
ALTER TABLE profiles
ADD COLUMN IF NOT EXISTS completed_lessons integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_points integer DEFAULT 0,
ADD COLUMN IF NOT EXISTS updated_at timestamp with time zone DEFAULT timezone('utc'::text, now());

-- Create achievements table
CREATE TABLE IF NOT EXISTS achievements (
    id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    name text NOT NULL,
    description text,
    icon text,
    points integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now())
);

-- Create user_achievements table for tracking user achievements
CREATE TABLE IF NOT EXISTS user_achievements (
    id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
    achievement_id uuid REFERENCES achievements(id) ON DELETE CASCADE,
    completed_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    UNIQUE(user_id, achievement_id)
);

-- Create notification_settings table
CREATE TABLE IF NOT EXISTS notification_settings (
    id uuid DEFAULT extensions.uuid_generate_v4() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
    course_updates boolean DEFAULT true,
    new_achievements boolean DEFAULT true,
    learning_reminders boolean DEFAULT true,
    progress_reports boolean DEFAULT true,
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    UNIQUE(user_id)
);

-- Insert default achievements
INSERT INTO achievements (name, description, points) VALUES
('First Module', 'Complete your first learning module', 100),
('Perfect Score', 'Achieve 100% in any assessment', 250),
('Quick Learner', 'Complete 5 lessons in one day', 150)
ON CONFLICT DO NOTHING;

-- Create RLS policies

-- Profiles policies
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own profile"
ON profiles FOR SELECT
USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
ON profiles FOR UPDATE
USING (auth.uid() = id);

-- Achievements policies
ALTER TABLE achievements ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Achievements are viewable by all users"
ON achievements FOR SELECT
TO authenticated
USING (true);

-- User achievements policies
ALTER TABLE user_achievements ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own achievements"
ON user_achievements FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own achievements"
ON user_achievements FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Notification settings policies
ALTER TABLE notification_settings ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own notification settings"
ON notification_settings FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notification settings"
ON notification_settings FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own notification settings"
ON notification_settings FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Create functions and triggers

-- Function to update user points when achievement is earned
CREATE OR REPLACE FUNCTION update_user_points()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE profiles
  SET total_points = total_points + (
    SELECT points FROM achievements WHERE id = NEW.achievement_id
  )
  WHERE id = NEW.user_id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update points on achievement completion
CREATE TRIGGER on_achievement_earned
  AFTER INSERT ON user_achievements
  FOR EACH ROW
  EXECUTE FUNCTION update_user_points(); 