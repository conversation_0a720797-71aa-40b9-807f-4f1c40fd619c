-- Comprehensive RLS policy fix for all tables

-- Enable extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================
-- USER ROLES TABLE
-- =====================

-- Create user_roles table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.user_roles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  role TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(user_id)
);

-- Enable Row Level Security
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Anyone can view user_roles" ON public.user_roles;
DROP POLICY IF EXISTS "Teachers can insert user_roles" ON public.user_roles;
DROP POLICY IF EXISTS "Teachers can update user_roles" ON public.user_roles;
DROP POLICY IF EXISTS "Teachers can delete user_roles" ON public.user_roles;
DROP POLICY IF EXISTS "Service role bypass" ON public.user_roles;
DROP POLICY IF EXISTS "Only teachers can insert roles" ON public.user_roles;
DROP POLICY IF EXISTS "Only teachers can update roles" ON public.user_roles;
DROP POLICY IF EXISTS "Only teachers can delete roles" ON public.user_roles;

-- Create new policies
CREATE POLICY "Anyone can view user_roles" 
ON public.user_roles FOR SELECT 
USING (true);

CREATE POLICY "Teachers can insert user_roles" 
ON public.user_roles FOR INSERT 
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can update user_roles" 
ON public.user_roles FOR UPDATE 
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can delete user_roles" 
ON public.user_roles FOR DELETE 
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- =====================
-- COURSES TABLE
-- =====================

-- Create courses table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.courses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  description TEXT,
  instructor TEXT,
  total_modules INTEGER DEFAULT 0,
  completed_modules INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- Enable Row Level Security
ALTER TABLE public.courses ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Anyone can view courses" ON public.courses;
DROP POLICY IF EXISTS "Teachers can insert courses" ON public.courses;
DROP POLICY IF EXISTS "Teachers can update courses" ON public.courses;
DROP POLICY IF EXISTS "Teachers can delete courses" ON public.courses;
DROP POLICY IF EXISTS "Service role bypass" ON public.courses;

-- Create new policies
CREATE POLICY "Anyone can view courses" 
ON public.courses FOR SELECT 
USING (true);

CREATE POLICY "Teachers can insert courses" 
ON public.courses FOR INSERT 
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can update courses" 
ON public.courses FOR UPDATE 
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can delete courses" 
ON public.courses FOR DELETE 
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- =====================
-- MODULES TABLE
-- =====================

-- Create modules table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.modules (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  course_id UUID REFERENCES public.courses(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  slug TEXT NOT NULL,
  module_number INTEGER NOT NULL,
  is_locked BOOLEAN DEFAULT false,
  is_completed BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(course_id, slug)
);

-- Enable Row Level Security
ALTER TABLE public.modules ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Anyone can view modules" ON public.modules;
DROP POLICY IF EXISTS "Teachers can insert modules" ON public.modules;
DROP POLICY IF EXISTS "Teachers can update modules" ON public.modules;
DROP POLICY IF EXISTS "Teachers can delete modules" ON public.modules;
DROP POLICY IF EXISTS "Service role bypass" ON public.modules;

-- Create new policies
CREATE POLICY "Anyone can view modules" 
ON public.modules FOR SELECT 
USING (true);

CREATE POLICY "Teachers can insert modules" 
ON public.modules FOR INSERT 
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can update modules" 
ON public.modules FOR UPDATE 
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can delete modules" 
ON public.modules FOR DELETE 
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- =====================
-- LESSONS TABLE
-- =====================

-- Create lessons table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.lessons (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  module_id UUID REFERENCES public.modules(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  slug TEXT NOT NULL,
  duration TEXT,
  type TEXT NOT NULL,
  content TEXT,
  requirement TEXT,
  completed BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(module_id, slug)
);

-- Enable Row Level Security
ALTER TABLE public.lessons ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Anyone can view lessons" ON public.lessons;
DROP POLICY IF EXISTS "Teachers can insert lessons" ON public.lessons;
DROP POLICY IF EXISTS "Teachers can update lessons" ON public.lessons;
DROP POLICY IF EXISTS "Teachers can delete lessons" ON public.lessons;

-- Create new policies
CREATE POLICY "Anyone can view lessons" 
ON public.lessons FOR SELECT 
USING (true);

CREATE POLICY "Teachers can insert lessons" 
ON public.lessons FOR INSERT 
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can update lessons" 
ON public.lessons FOR UPDATE 
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
) WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can delete lessons" 
ON public.lessons FOR DELETE 
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- =====================
-- SERVICE ROLE BYPASS POLICIES
-- =====================

-- Create service role bypass policies for all tables
CREATE POLICY "Service role bypass for user_roles" 
ON public.user_roles 
USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role bypass for courses" 
ON public.courses 
USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role bypass for modules" 
ON public.modules 
USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role bypass for lessons" 
ON public.lessons 
USING (auth.jwt() ->> 'role' = 'service_role');

-- =====================
-- HELPER FUNCTIONS
-- =====================

-- Drop existing functions
DROP FUNCTION IF EXISTS public.has_role(UUID, TEXT);
DROP FUNCTION IF EXISTS public.assign_role(UUID, TEXT);

-- Create a function to check if a user has a specific role
CREATE OR REPLACE FUNCTION public.has_role(
  _user_id UUID,
  _role TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM public.user_roles
    WHERE user_id = _user_id AND role = _role
  );
END;
$$;

-- Grant permissions to use this function
GRANT EXECUTE ON FUNCTION public.has_role TO authenticated;

-- Create a function to assign a role to a user
CREATE OR REPLACE FUNCTION public.assign_role(
  _user_id UUID,
  _role TEXT
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the user already has a role
  IF EXISTS (SELECT 1 FROM public.user_roles WHERE user_id = _user_id) THEN
    -- Update the existing role
    UPDATE public.user_roles
    SET role = _role
    WHERE user_id = _user_id;
  ELSE
    -- Insert a new role
    INSERT INTO public.user_roles (user_id, role)
    VALUES (_user_id, _role);
  END IF;
END;
$$;

-- Grant permissions to use this function
GRANT EXECUTE ON FUNCTION public.assign_role TO authenticated;

-- =====================
-- REALTIME SETUP
-- =====================

-- Add tables to the realtime publication
ALTER PUBLICATION supabase_realtime ADD TABLE public.user_roles;
ALTER PUBLICATION supabase_realtime ADD TABLE public.courses;
ALTER PUBLICATION supabase_realtime ADD TABLE public.modules;
ALTER PUBLICATION supabase_realtime ADD TABLE public.lessons;

-- Set up replica identity for realtime
ALTER TABLE public.user_roles REPLICA IDENTITY FULL;
ALTER TABLE public.courses REPLICA IDENTITY FULL;
ALTER TABLE public.modules REPLICA IDENTITY FULL;
ALTER TABLE public.lessons REPLICA IDENTITY FULL;

-- =====================
-- ASSIGN INITIAL TEACHER ROLE
-- =====================

-- Assign teacher role to the first user (if any)
DO $$
DECLARE
  first_user_id UUID;
BEGIN
  SELECT id INTO first_user_id FROM auth.users LIMIT 1;
  
  IF first_user_id IS NOT NULL THEN
    INSERT INTO public.user_roles (user_id, role)
    VALUES (first_user_id, 'teacher')
    ON CONFLICT (user_id) DO UPDATE SET role = 'teacher';
  END IF;
END $$;
