var w=Object.defineProperty;var y=(l,e,t)=>e in l?w(l,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[e]=t;var m=(l,e,t)=>y(l,typeof e!="symbol"?e+"":e,t);class _{constructor(e=100,t=30*60*1e3){m(this,"cache",new Map);m(this,"maxSize");m(this,"maxAge");m(this,"stats",{hits:0,misses:0});this.maxSize=e,this.maxAge=t}generateHash(e,t){const r=t?JSON.stringify(t):"",a=e+r;let i=0;for(let o=0;o<a.length;o++){const d=a.charCodeAt(o);i=(i<<5)-i+d,i=i&i}return i.toString(36)}get(e,t){const r=this.generateHash(e,t),a=this.cache.get(r);if(!a)return this.stats.misses++,null;const i=Date.now();return i-a.timestamp>this.maxAge?(this.cache.delete(r),this.stats.misses++,null):(a.accessCount++,a.lastAccessed=i,this.stats.hits++,a.html)}set(e,t,r){const a=this.generateHash(e,r),i=Date.now();this.cache.size>=this.maxSize&&!this.cache.has(a)&&this.evictLRU();const o={content:e,html:t,timestamp:i,accessCount:1,lastAccessed:i,hash:a};this.cache.set(a,o)}evictLRU(){let e=null,t=null;for(const[r,a]of this.cache.entries())(!e||a.lastAccessed<e.lastAccessed)&&(e=a,t=r);t&&this.cache.delete(t)}cleanup(){const e=Date.now(),t=[];for(const[r,a]of this.cache.entries())e-a.timestamp>this.maxAge&&t.push(r);t.forEach(r=>this.cache.delete(r))}clear(){this.cache.clear(),this.stats.hits=0,this.stats.misses=0}getStats(){const e=this.stats.hits+this.stats.misses;return{hits:this.stats.hits,misses:this.stats.misses,size:this.cache.size,maxSize:this.maxSize,hitRate:e>0?this.stats.hits/e:0}}invalidate(e){if(!e){this.clear();return}const t=[];for(const[r,a]of this.cache.entries())e.test(a.content)&&t.push(r);t.forEach(r=>this.cache.delete(r))}getEntries(){return Array.from(this.cache.entries()).map(([e,t])=>({key:e,entry:t}))}preload(e){e.forEach(({content:t,html:r,options:a})=>{this.set(t,r,a)})}getMemoryUsage(){let e=0;for(const t of this.cache.values())e+=t.content.length+t.html.length;return e}}const f=new _(200,60*60*1e3);typeof window!="undefined"&&setInterval(()=>{f.cleanup()},10*60*1e3);function C(l,e,t){try{const r=f.get(l,t);if(r)return r;const a=e(l);return f.set(l,a,t),a}catch(r){return console.error("Error in getCachedMarkdown:",r),e(l)}}function k(l){if(!l)return!1;const e=l.trim();return e.startsWith("<")&&e.includes("</")}function b(l){if(!l)return"";if(k(l))return l;try{return C(l,$)}catch(e){return console.error("Error in markdownToHtml:",e),$(l)}}function $(l){if(!l)return"";try{let e=l;const t=[],r=[];e=e.replace(/```(\w*)\n?([\s\S]*?)```/gm,(c,s,n)=>{const g=`__CODE_BLOCK_${t.length}__`;return t.push({language:s||"",code:n.trim()}),g}),e=e.replace(/<([a-z][a-z0-9]*)\b[^>]*>([\s\S]*?)<\/\1>/gi,c=>{const s=`__HTML_BLOCK_${r.length}__`;return r.push(c),s}),e=e.replace(/^#{6}\s+(.*$)/gm,(c,s)=>`<h6 id="${s.toLowerCase().replace(/[^\w\s-]/g,"").replace(/\s+/g,"-")}">${s}</h6>`),e=e.replace(/^#{5}\s+(.*$)/gm,(c,s)=>`<h5 id="${s.toLowerCase().replace(/[^\w\s-]/g,"").replace(/\s+/g,"-")}">${s}</h5>`),e=e.replace(/^#{4}\s+(.*$)/gm,(c,s)=>`<h4 id="${s.toLowerCase().replace(/[^\w\s-]/g,"").replace(/\s+/g,"-")}">${s}</h4>`),e=e.replace(/^#{3}\s+(.*$)/gm,(c,s)=>`<h3 id="${s.toLowerCase().replace(/[^\w\s-]/g,"").replace(/\s+/g,"-")}">${s}</h3>`),e=e.replace(/^#{2}\s+(.*$)/gm,(c,s)=>`<h2 id="${s.toLowerCase().replace(/[^\w\s-]/g,"").replace(/\s+/g,"-")}">${s}</h2>`),e=e.replace(/^#{1}\s+(.*$)/gm,(c,s)=>`<h1 id="${s.toLowerCase().replace(/[^\w\s-]/g,"").replace(/\s+/g,"-")}">${s}</h1>`),e=e.replace(/\*\*\*([^*]+)\*\*\*/g,"<strong><em>$1</em></strong>"),e=e.replace(/\*\*([^*]+)\*\*/g,"<strong>$1</strong>"),e=e.replace(/\*([^*\n]+)\*/g,"<em>$1</em>"),e=e.replace(/~~([^~]+)~~/g,"<del>$1</del>"),e=e.replace(/==([^=]+)==/g,"<mark>$1</mark>"),e=e.replace(/\[([^\]]+)\]\(([^)]+)\)/g,'<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>'),e=e.replace(/!\[([^\]]*)\]\(([^)]+)\)/g,(c,s,n)=>`<img src="${n}" alt="${s||""}" loading="lazy" class="max-w-full h-auto rounded-lg shadow-sm mx-auto my-4" />`),e=e.replace(/^>\s*(.+)$/gm,"<blockquote>$1</blockquote>"),e=e.replace(/^---+$/gm,"<hr />"),e=e.replace(/^-\s*\[\s*\]\s*(.+)$/gm,'<div class="task-item"><input type="checkbox" disabled /> $1</div>'),e=e.replace(/^-\s*\[x\]\s*(.+)$/gm,'<div class="task-item"><input type="checkbox" checked disabled /> $1</div>');const a=e.split(`
`),i=[];let o=!1,d=!1;for(let c=0;c<a.length;c++){const s=a[c],n=s.trim(),g=n.match(/^[-*+]\s+(.+)$/),h=n.match(/^\d+\.\s+(.+)$/);g?(d&&(i.push("</ol>"),d=!1),o||(i.push("<ul>"),o=!0),i.push(`<li>${g[1]}</li>`)):h?(o&&(i.push("</ul>"),o=!1),d||(i.push("<ol>"),d=!0),i.push(`<li>${h[1]}</li>`)):(o&&(i.push("</ul>"),o=!1),d&&(i.push("</ol>"),d=!1),i.push(s))}return o&&i.push("</ul>"),d&&i.push("</ol>"),e=i.join(`
`),e=e.replace(/^\|(.+)\|\s*\n\|[-:\s|]+\|\s*\n((?:\|.+\|\s*\n?)*)/gm,c=>{const s=c.trim().split(`
`);if(s.length<3)return c;const n=s[0].split("|").slice(1,-1).map(p=>p.trim()),g=s.slice(2).map(p=>p.split("|").slice(1,-1).map(u=>u.trim()));let h='<table class="w-full border-collapse"><thead><tr>';return n.forEach(p=>{h+=`<th>${p}</th>`}),h+="</tr></thead><tbody>",g.forEach(p=>{h+="<tr>",p.forEach(u=>{h+=`<td>${u}</td>`}),h+="</tr>"}),h+="</tbody></table>",h}),e=e.replace(/`([^`\n]+)`/g,"<code>$1</code>"),t.forEach((c,s)=>{const{language:n,code:g}=c,h=g.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;"),p=`<pre class="language-${n}"><code class="language-${n}">${h}</code></pre>`;e=e.replace(`__CODE_BLOCK_${s}__`,p)}),r.forEach((c,s)=>{e=e.replace(`__HTML_BLOCK_${s}__`,c)}),e=e.split(`

`).map(c=>(c=c.trim(),c?c.startsWith("<")?c:`<p>${c.replace(/\n/g,"<br>")}</p>`:"")).filter(Boolean).join(`
`),e=e.replace(/<p>\s*<\/p>/g,""),e=e.replace(/<p>(<[^>]+>.*<\/[^>]+>)<\/p>/g,"$1"),e}catch(e){return console.error("Error processing markdown content:",e),`<p>${l.replace(/</g,"&lt;").replace(/>/g,"&gt;")}</p>`}}export{b as m};
