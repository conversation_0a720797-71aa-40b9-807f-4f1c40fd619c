export interface StudentProfile {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  fullName: string;
}

export interface TestSubmissionSummary {
  id: string;
  testId: string;
  testTitle: string;
  testType: 'pre_test' | 'post_test';
  moduleId: string;
  moduleTitle: string;
  courseId: string;
  courseTitle: string;
  submittedAt: string;
  responseCount: number;
}

export interface StudentTestAnalytics {
  student: StudentProfile;
  testSubmissions: TestSubmissionSummary[];
  totalTestsCompleted: number;
}

export interface DetailedTestResponse {
  id: string;
  testId: string;
  userId: string;
  student: StudentProfile;
  test: {
    id: string;
    title: string;
    type: 'pre_test' | 'post_test';
    description?: string;
    module: {
      id: string;
      title: string;
      course: {
        id: string;
        title: string;
      };
    };
  };
  responses: Array<{
    questionId: string;
    rating: number;
    question: {
      id: string;
      question: string;
      questionNumber: number;
      type: 'rating';
      minRating: number;
      maxRating: number;
      minLabel: string;
      maxLabel: string;
    };
  }>;
  createdAt: string;
  updatedAt: string;
}

export interface TestAnalyticsOverview {
  totalStudents: number;
  totalTestsCompleted: number;
  studentsWithTests: StudentTestAnalytics[];
}

export interface PDFExportData {
  student: StudentProfile;
  testResponse: DetailedTestResponse;
  generatedAt: string;
}

export interface QuestionAnalytics {
  questionId: string;
  questionText: string;
  questionNumber: number;
  totalResponses: number;
  optionBreakdown: {
    rating: number;
    label: string;
    count: number;
    percentage: number;
  }[];
  averageRating: number;
}

export interface TestAnalyticsSummary {
  testId: string;
  testTitle: string;
  testType: 'pre_test' | 'post_test';
  moduleTitle: string;
  courseTitle: string;
  totalResponses: number;
  questions: QuestionAnalytics[];
}

export interface OverallAnalyticsExport {
  generatedAt: string;
  totalStudents: number;
  totalResponses: number;
  testSummaries: TestAnalyticsSummary[];
}
