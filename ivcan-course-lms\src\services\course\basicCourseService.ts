import { supabase } from '@/integrations/supabase/client';
import { processImageBasic } from '@/lib/basic-image';

/**
 * Interface for course data
 */
interface CourseData {
  title: string;
  slug: string;
  description: string;
  instructor: string;
  image?: File | null;
}

/**
 * Create a new course with an image
 *
 * @param courseData The course data
 * @returns A Promise that resolves to the created course
 */
export async function createCourse(courseData: CourseData) {
  console.log('Creating course:', courseData.title);

  try {
    // Prepare the course data
    const courseInsert: any = {
      title: courseData.title,
      slug: courseData.slug,
      description: courseData.description,
      instructor: courseData.instructor,
      total_modules: 0,
      completed_modules: 0
    };

    // Process the image if provided
    if (courseData.image) {
      console.log('Processing image for course');
      try {
        const imageDataUrl = await processImageBasic(courseData.image);
        console.log('Image processed successfully, length:', imageDataUrl.length);

        // Try different column names that might exist
        // First try image_url
        courseInsert.image_url = imageDataUrl;
        // Also try image (as a backup)
        courseInsert.image = imageDataUrl;
      } catch (imageError) {
        console.error('Failed to process image:', imageError);
        // Continue without the image
      }
    }

    // Insert the course
    const { data, error } = await supabase
      .from('courses')
      .insert([courseInsert])
      .select();

    if (error) {
      console.error('Error creating course:', error);
      throw new Error(`Failed to create course: ${error.message}`);
    }

    console.log('Course created successfully:', data[0].id);
    return data[0];
  } catch (error: any) {
    console.error('Error in createCourse:', error);
    throw error;
  }
}

/**
 * Update an existing course
 *
 * @param courseId The ID of the course to update
 * @param courseData The updated course data
 * @returns A Promise that resolves to the updated course
 */
export async function updateCourse(courseId: string, courseData: CourseData) {
  console.log('Updating course:', courseId);

  try {
    // Prepare the course data
    const courseUpdate: any = {
      title: courseData.title,
      slug: courseData.slug,
      description: courseData.description,
      instructor: courseData.instructor,
      updated_at: new Date().toISOString()
    };

    // Process the image if provided
    if (courseData.image) {
      console.log('Processing image for course update');
      try {
        const imageDataUrl = await processImageBasic(courseData.image);
        console.log('Image processed successfully, length:', imageDataUrl.length);

        // Try different column names that might exist
        // First try image_url
        courseUpdate.image_url = imageDataUrl;
        // Also try image (as a backup)
        courseUpdate.image = imageDataUrl;
      } catch (imageError) {
        console.error('Failed to process image:', imageError);
        // Continue without the image
      }
    }

    // Update the course
    const { data, error } = await supabase
      .from('courses')
      .update(courseUpdate)
      .eq('id', courseId)
      .select();

    if (error) {
      console.error('Error updating course:', error);
      throw new Error(`Failed to update course: ${error.message}`);
    }

    console.log('Course updated successfully');
    return data[0];
  } catch (error: any) {
    console.error('Error in updateCourse:', error);
    throw error;
  }
}

/**
 * Get a course by ID
 *
 * @param courseId The ID of the course to get
 * @returns A Promise that resolves to the course
 */
export async function getCourse(courseId: string) {
  console.log('Getting course:', courseId);

  try {
    const { data, error } = await supabase
      .from('courses')
      .select('*')
      .eq('id', courseId)
      .single();

    if (error) {
      console.error('Error getting course:', error);
      throw new Error(`Failed to get course: ${error.message}`);
    }

    return data;
  } catch (error: any) {
    console.error('Error in getCourse:', error);
    throw error;
  }
}

/**
 * Get all courses
 *
 * @returns A Promise that resolves to an array of courses
 */
export async function getAllCourses() {
  console.log('Getting all courses');

  try {
    const { data, error } = await supabase
      .from('courses')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error getting courses:', error);
      throw new Error(`Failed to get courses: ${error.message}`);
    }

    return data;
  } catch (error: any) {
    console.error('Error in getAllCourses:', error);
    throw error;
  }
}

/**
 * Get lessons for a module
 *
 * @param moduleId The ID of the module to get lessons for
 * @returns A Promise that resolves to an array of lessons
 */
export async function getLessons(moduleId: string) {
  console.log('Getting lessons for module:', moduleId);

  try {
    const { data, error } = await supabase
      .from('lessons')
      .select('*')
      .eq('module_id', moduleId)
      .order('created_at', { ascending: true }); // Order by creation time, oldest first

    if (error) {
      console.error('Error getting lessons:', error);
      throw new Error(`Failed to get lessons: ${error.message}`);
    }

    return data;
  } catch (error: any) {
    console.error('Error in getLessons:', error);
    throw error;
  }
}
