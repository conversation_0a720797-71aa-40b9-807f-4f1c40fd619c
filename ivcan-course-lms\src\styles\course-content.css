/* Course Content Styling */

/* Module styling */
.module-container {
  font-family: system-ui, sans-serif;
  transition: all 0.2s ease-out;
  border-radius: 0.5rem;
  overflow: hidden;
  background-color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.06);
  width: 100%;
  margin-bottom: 0.5rem; /* Reduced margin between modules */
}

.module-container:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.dark .module-container {
  background-color: #1a1a1a;
  border-color: rgba(255, 255, 255, 0.06);
}

.module-header {
  padding: 0.75rem 1rem; /* Reduced padding */
  background-color: #f8f9fa;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.dark .module-header {
  background-color: #222;
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.module-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

@media (max-width: 768px) {
  .module-title {
    font-size: 1.125rem;
  }
}

.dark .module-title {
  color: #fff;
}

.module-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.875rem;
  color: #666;
}

.dark .module-info {
  color: #aaa;
}

.module-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.35rem 0.9rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.module-badge-completed {
  background-color: rgba(74, 222, 128, 0.15);
  color: #16a34a;
}

.module-badge-completed:hover {
  background-color: rgba(74, 222, 128, 0.2);
  transform: translateY(-1px);
}

.dark .module-badge-completed {
  background-color: rgba(74, 222, 128, 0.25);
  color: #4ade80;
}

.dark .module-badge-completed:hover {
  background-color: rgba(74, 222, 128, 0.3);
}

.module-badge-locked {
  background-color: rgba(0, 0, 0, 0.05);
  color: #666;
}

.dark .module-badge-locked {
  background-color: rgba(255, 255, 255, 0.1);
  color: #aaa;
}

/* Lesson styling */
.lesson-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.2rem;
  border-radius: 0.5rem;
  transition: all 0.25s cubic-bezier(0.16, 1, 0.3, 1);
  margin-bottom: 0.5rem;
  border: 1px solid rgba(0, 0, 0, 0.06);
  background-color: white;
  position: relative;
  overflow: hidden;
  width: 100%;
}

.lesson-item:hover {
  background-color: #f8f9fa;
  border-color: rgba(74, 222, 128, 0.15);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.04);
  transform: translateY(-1px);
}

.dark .lesson-item {
  background-color: rgba(30, 30, 30, 0.7);
  border-color: rgba(255, 255, 255, 0.06);
}

.dark .lesson-item:hover {
  background-color: rgba(35, 35, 35, 0.9);
  border-color: rgba(74, 222, 128, 0.2);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.lesson-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 0.35rem;
  transition: all 0.25s cubic-bezier(0.16, 1, 0.3, 1);
  font-size: 0.875rem; /* 14px */
}

@media (min-width: 768px) {
  .lesson-title {
    font-size: 0.9375rem; /* 15px */
  }
}

@media (max-width: 768px) {
  .lesson-title {
    font-size: 0.813rem;
  }
  
  .lesson-item {
    padding: 0.75rem 1rem;
    margin-bottom: 0.375rem;
  }
}

.lesson-item:hover .lesson-title {
  color: #16a34a;
}

.dark .lesson-title {
  color: #ddd;
}

.dark .lesson-item:hover .lesson-title {
  color: #4ade80;
}

.lesson-info {
  display: flex;
  align-items: center;
  gap: 0.6rem;
  font-size: 0.75rem; /* 12px */
  color: #666;
}

.dark .lesson-info {
  color: #aaa;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

.animate-slide-in {
  animation: slideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

.animate-delay-100 {
  animation-delay: 0.1s;
}

.animate-delay-200 {
  animation-delay: 0.2s;
}

.animate-delay-300 {
  animation-delay: 0.3s;
}

/* Media queries */
@media (max-width: 768px) {
  .course-modules-container {
    padding: 0.5rem;
  }

  .module-container {
    margin-bottom: 0.5rem;
    border-radius: 0.5rem;
  }

  .module-header {
    padding: 0.5rem; /* Reduced mobile padding */
  }

  .lesson-item {
    padding: 0.5rem; /* Reduced mobile padding */
    margin-bottom: 0.25rem; /* Reduced mobile margin */
  }

  .lesson-item:hover {
    transform: none;
  }
  
  /* Lesson page mobile optimizations */
  .lesson-header {
    padding: 0.5rem 0; /* Reduced mobile padding */
  }

  .lesson-title {
    font-size: 1.25rem;
    line-height: 1.4;
  }

  .lesson-navigation {
    padding: 0.25rem 0; /* Reduced mobile padding */
    gap: 0.375rem; /* Reduced mobile gap */
  }

  .lesson-navigation-button {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem; /* Reduced mobile padding */
  }

  .lesson-content-wrapper {
    padding: 0.25rem 0.5rem; /* Reduced mobile padding */
  }
  
  .lesson-content {
    font-size: 0.9375rem;
    line-height: 1.6;
  }
  
  .lesson-content h1 {
    font-size: 1.5rem;
  }
  
  .lesson-content h2 {
    font-size: 1.25rem;
  }
  
  .lesson-content h3 {
    font-size: 1.125rem;
  }
  
  .lesson-content img,
  .lesson-content video {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
  }
  
  /* Content with padding for fixed navigation */
  .content-with-fixed-nav {
    padding-bottom: 0;
  }
}

/* Course details section */
.course-details {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  gap: 0.5rem; /* Reduced gap */
  padding: 0.75rem; /* Reduced padding */
  border-radius: 0.5rem;
  margin-bottom: 1rem; /* Reduced margin */
  background-color: white;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.dark .course-details {
  background-color: #1a1a1a;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.course-detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem; /* Reduced gap */
  padding: 0.5rem; /* Reduced padding */
  background-color: #f8f9fa;
  border-radius: 0.375rem;
}

.dark .course-detail-item {
  background-color: #222;
}

/* Module content */
.module-content {
  padding: 0.75rem; /* Reduced padding */
  background-color: white;
  border-radius: 0 0 0.5rem 0.5rem;
}

.dark .module-content {
  background-color: #1a1a1a;
}
