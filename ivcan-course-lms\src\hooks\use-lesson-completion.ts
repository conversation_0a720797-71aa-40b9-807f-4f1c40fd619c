import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { markLessonAsCompleted } from '@/services/course/completionService';
import { enrollInCourse } from '@/services/course/enrollmentApi';
import { useToast } from '@/hooks/use-toast';

interface UseLessonCompletionProps {
  lessonId: string;
  lessonSlug?: string;
  userId: string;
  courseId: string;
  onSchemaError?: () => void;
  enabled?: boolean;
}

export function useLessonCompletion({
  lessonId,
  lessonSlug,
  userId,
  courseId,
  onSchemaError,
  enabled = true
}: UseLessonCompletionProps) {
  const [isCompleting, setIsCompleting] = useState(false);
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const completeMutation = useMutation({
    mutationFn: async () => {
      if (!enabled) {
        console.log('Lesson completion mutation not enabled yet, waiting for lesson data to load');
        throw new Error('Please wait for lesson data to load completely before marking as completed');
      }
      
      if (!userId) {
        console.error('Completion error: User not logged in');
        throw new Error('You must be logged in to mark a lesson as completed');
      }

      if (!lessonId) {
        console.error('Completion error: No valid lesson ID available', { 
          providedId: lessonId,
          slug: lessonSlug
        });
        throw new Error('Lesson data not found or not loaded yet');
      }
      
      // Very important: Validate that we have a UUID, not a slug
      const isValidUuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(lessonId);
      
      if (!isValidUuid) {
        console.error('Completion error: Invalid lesson ID format - not a UUID', { 
          providedId: lessonId,
          slug: lessonSlug
        });
        throw new Error('Cannot mark lesson as complete - lesson data not fully loaded');
      }

      console.log('Starting lesson completion process', { 
        lessonId, 
        userId,
        isValidUuid
      });

      // Set local state to prevent duplicate calls
      setIsCompleting(true);

      // Update course enrollment status to 'in_progress'
      try {
        console.log('Updating course enrollment status');
        await enrollInCourse(courseId, userId, 'in_progress');
      } catch (enrollError) {
        console.error('Error enrolling in course, but continuing with lesson completion:', enrollError);
      }

      // Call the improved completion service
      const result = await markLessonAsCompleted(lessonId, userId);
      
      console.log('Mark lesson completed result:', result);
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to mark lesson as completed');
      }
      
      // Check for schema issues that might need repair
      if (result.error && onSchemaError && (
        result.error.includes("is_completed") || 
        result.error.includes("schema cache") ||
        result.error.includes("column") ||
        result.error.includes("not found")
      )) {
        onSchemaError();
      }
      
      return true;
    },
    onSuccess: () => {
      // Invalidate relevant queries to refresh data
      console.log('Invalidating related queries');
      queryClient.invalidateQueries({
        queryKey: ['lesson', lessonSlug || lessonId],
      });
      queryClient.invalidateQueries({
        queryKey: ['courseModules', courseId],
      });
      queryClient.invalidateQueries({
        queryKey: ['dashboard-courses', userId],
      });
      queryClient.invalidateQueries({
        queryKey: ['courses'],
      });

      // Invalidate module progress queries used by CourseModulesPage
      queryClient.invalidateQueries({
        queryKey: ['moduleProgress', courseId, userId],
      });
      queryClient.invalidateQueries({
        queryKey: ['modules', courseId],
      });

      // Invalidate lesson completion queries used by ModuleList
      queryClient.invalidateQueries({
        queryKey: ['lesson-completions', userId],
      });

      // Show success toast
      toast({
        title: "Success",
        description: "Lesson marked as completed!",
      });

      setIsCompleting(false);
    },
    onError: (error: any) => {
      console.error('Error marking lesson as completed:', error);
      setIsCompleting(false);
      
      // Extract detailed error message if available
      let errorMessage = "Failed to mark lesson as completed. Please try again.";
      if (error?.message) {
        errorMessage = error.message;
      }
      
      // If the error has details from the API, show them
      if (error?.cause?.message) {
        errorMessage += `: ${error.cause.message}`;
      }
      
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  });

  return {
    completeMutation,
    isCompleting
  };
} 