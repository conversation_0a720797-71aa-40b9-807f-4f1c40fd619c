
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

// An interface for user with roles to improve type safety
export interface UserWithRole {
  id: string;
  first_name: string | null;
  last_name: string | null;
  user_roles?: Array<{ role: string }> | null;
}

/**
 * Gets all users eligible for promotion to teacher role
 * (students or users without assigned roles)
 */
export const getPromotableUsers = async (): Promise<UserWithRole[]> => {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select(`
        id,
        first_name,
        last_name,
        user_roles (role)
      `);
      
    if (error) {
      console.error('Error fetching users for promotion:', error);
      return [];
    }
    
    // Type assertion to ensure we handle the data correctly
    const typedData = data as unknown as UserWithRole[];
    
    // Filter to only include students or users without roles
    return typedData.filter(user => {
      // If user has no roles or is a student
      return !user.user_roles || 
        user.user_roles.length === 0 || 
        (user.user_roles[0] && user.user_roles[0].role === 'student');
    });
  } catch (error) {
    console.error('Error in getPromotableUsers:', error);
    return [];
  }
};

/**
 * Promotes a user to teacher role
 * Only existing teachers can promote other users
 */
export const promoteUserToTeacher = async (userId: string): Promise<boolean> => {
  try {
    if (!userId) {
      toast.error('User ID is required');
      return false;
    }
    
    // Get current user to verify they are a teacher
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      toast.error('You must be logged in to perform this action');
      return false;
    }
    
    // Check if current user is a teacher
    const { data: currentUserRole, error: roleError } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', session.user.id)
      .single();
      
    if (roleError || !currentUserRole || currentUserRole.role !== 'teacher') {
      toast.error('Only teachers can promote other users to teacher role');
      return false;
    }
    
    // Promote the user to teacher
    const { error } = await supabase
      .from('user_roles')
      .upsert({ user_id: userId, role: 'teacher' }, { onConflict: 'user_id' });
    
    if (error) {
      console.error('Error promoting user to teacher:', error);
      toast.error(`Failed to promote user: ${error.message}`);
      return false;
    }
    
    // For logging role changes, log to console instead
    console.log(`Role change: User ${userId} promoted to teacher by ${session.user.id}`);
    
    toast.success('User successfully promoted to teacher role');
    return true;
  } catch (error: any) {
    console.error('Unexpected error in promoteUserToTeacher:', error);
    toast.error(`An unexpected error occurred: ${error.message}`);
    return false;
  }
};
