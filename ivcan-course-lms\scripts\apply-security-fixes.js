// Script to apply security fixes to the database
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

// Create Supabase client with service role key for admin operations
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function applySecurity() {
  console.log('Starting security fixes application...');
  
  try {
    // Read the security fix SQL file
    const securitySqlPath = path.join(__dirname, '../supabase/migrations/20250701001_fix_security_policies.sql');
    
    if (!fs.existsSync(securitySqlPath)) {
      console.error('Security SQL file not found:', securitySqlPath);
      process.exit(1);
    }
    
    const securitySql = fs.readFileSync(securitySqlPath, 'utf8');
    
    // Split SQL into smaller chunks to avoid timeouts
    // Each statement ends with a semicolon
    const statements = securitySql
      .split(';')
      .filter(stmt => stmt.trim().length > 0)
      .map(stmt => stmt.trim() + ';');
    
    console.log(`Applying ${statements.length} SQL statements...`);
    
    // Apply each statement separately
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      try {
        console.log(`Applying statement ${i + 1}/${statements.length}`);
        // Execute with the service role
        const { data, error } = await supabase.rpc('exec_sql', { sql: statement });
        
        if (error) {
          console.error(`Error applying statement ${i + 1}:`, error);
          console.error('Statement:', statement);
        } else {
          console.log(`Successfully applied statement ${i + 1}`);
        }
      } catch (stmtError) {
        console.error(`Exception applying statement ${i + 1}:`, stmtError);
        console.error('Statement:', statement);
      }
    }
    
    console.log('Security fixes application completed!');
  } catch (error) {
    console.error('Error applying security fixes:', error);
    process.exit(1);
  }
}

// Execute the function
applySecurity().catch(err => {
  console.error('Unhandled error:', err);
  process.exit(1);
}); 