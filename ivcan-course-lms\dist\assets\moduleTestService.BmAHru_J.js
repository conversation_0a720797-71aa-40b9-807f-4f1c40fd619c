var d=(r,t,e)=>new Promise((s,a)=>{var c=i=>{try{n(e.next(i))}catch(u){a(u)}},l=i=>{try{n(e.throw(i))}catch(u){a(u)}},n=i=>i.done?s(i.value):Promise.resolve(i.value).then(c,l);n((e=e.apply(r,t)).next())});import{s as o}from"./index.BLDhDn0D.js";const m=r=>d(void 0,null,function*(){const{data:t,error:e}=yield o.from("module_tests").select("*").eq("module_id",r).order("created_at");if(e)throw console.error("Error fetching module tests:",e),e;return(t||[]).map(s=>({id:s.id,moduleId:s.module_id,title:s.title,type:s.type,description:s.description||void 0,questions:JSON.parse(s.questions||"[]"),createdAt:s.created_at,updatedAt:s.updated_at}))}),y=r=>d(void 0,null,function*(){const{data:t,error:e}=yield o.from("module_tests").select("*").eq("id",r).single();if(e)throw console.error("Error fetching module test:",e),e;return{id:t.id,moduleId:t.module_id,title:t.title,type:t.type,description:t.description||void 0,questions:JSON.parse(t.questions||"[]"),createdAt:t.created_at,updatedAt:t.updated_at}}),f=(r,t)=>d(void 0,null,function*(){const{data:e,error:s}=yield o.from("module_tests").select("*").eq("module_id",r).eq("type",t).single();if(s){if(s.code==="PGRST116")return null;throw console.error("Error fetching module test by type:",s),s}return{id:e.id,moduleId:e.module_id,title:e.title,type:e.type,description:e.description||void 0,questions:JSON.parse(e.questions||"[]"),createdAt:e.created_at,updatedAt:e.updated_at}}),g=r=>d(void 0,null,function*(){const{data:t,error:e}=yield o.from("module_tests").insert({module_id:r.moduleId,title:r.title,type:r.type,description:r.description,questions:JSON.stringify(r.questions||[])}).select().single();if(e)throw console.error("Error creating module test:",e),e;return{id:t.id,moduleId:t.module_id,title:t.title,type:t.type,description:t.description||void 0,questions:JSON.parse(t.questions||"[]"),createdAt:t.created_at,updatedAt:t.updated_at}}),q=(r,t)=>d(void 0,null,function*(){const{data:e,error:s}=yield o.from("module_tests").update({title:t.title,description:t.description,questions:t.questions?JSON.stringify(t.questions):void 0,updated_at:new Date().toISOString()}).eq("id",r).select().single();if(s)throw console.error("Error updating module test:",s),s;return{id:e.id,moduleId:e.module_id,title:e.title,type:e.type,description:e.description||void 0,questions:JSON.parse(e.questions||"[]"),createdAt:e.created_at,updatedAt:e.updated_at}}),w=r=>d(void 0,null,function*(){const{error:t}=yield o.from("module_tests").delete().eq("id",r);if(t)throw console.error("Error deleting module test:",t),t}),S=(r,t,e)=>d(void 0,null,function*(){const{data:s,error:a}=yield o.from("module_test_responses").upsert({test_id:r,user_id:t,responses:JSON.stringify(e),updated_at:new Date().toISOString()}).select().single();if(a)throw console.error("Error submitting test response:",a),a;return{id:s.id,testId:s.test_id,userId:s.user_id,responses:JSON.parse(s.responses||"[]"),createdAt:s.created_at,updatedAt:s.updated_at}}),A=(r,t)=>d(void 0,null,function*(){const{data:e,error:s}=yield o.from("module_test_responses").select("*").eq("test_id",r).eq("user_id",t).single();if(s){if(s.code==="PGRST116")return null;throw console.error("Error fetching user test response:",s),s}return{id:e.id,testId:e.test_id,userId:e.user_id,responses:JSON.parse(e.responses||"[]"),createdAt:e.created_at,updatedAt:e.updated_at}});export{A as a,y as b,g as c,m as d,w as e,f as g,S as s,q as u};
