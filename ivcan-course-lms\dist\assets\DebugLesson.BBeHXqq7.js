var r=(m,u,n)=>new Promise((o,t)=>{var x=c=>{try{a(n.next(c))}catch(i){t(i)}},g=c=>{try{a(n.throw(c))}catch(i){t(i)}},a=c=>c.done?o(c.value):Promise.resolve(c.value).then(x,g);a((n=n.apply(m,u)).next())});import{r as f,j as e}from"./vendor-react.BcAa1DKr.js";import{B as d,s as h}from"./index.BLDhDn0D.js";import{d as j}from"./courseApi.BQX5-7u-.js";import"./vendor.DQpuTRuB.js";import"./vendor-supabase.sufZ44-y.js";import"./utils.Qa9QlCj_.js";import"./enrollmentApi.CstnsQi_.js";const w=()=>{const[m,u]=f.useState([]),[n,o]=f.useState(!1),t=(s,l,p)=>{u(b=>[...b,{test:s,result:l,error:p,timestamp:new Date().toISOString()}])},x=()=>r(void 0,null,function*(){o(!0);try{console.log("Testing basic Supabase connection...");const{data:s,error:l}=yield h.from("lessons").select("count").limit(1);t("Basic Connection",s,l)}catch(s){t("Basic Connection",null,s)}o(!1)}),g=()=>r(void 0,null,function*(){o(!0);try{console.log("Testing lessons table access...");const{data:s,error:l}=yield h.from("lessons").select("id, title, slug").limit(10);t("Lessons Table",s,l)}catch(s){t("Lessons Table",null,s)}o(!1)}),a=()=>r(void 0,null,function*(){o(!0);try{console.log("Testing specific lesson slug...");const{data:s,error:l}=yield h.from("lessons").select("*").eq("slug","introduction-to-intravenous-cannulation").single();t("Specific Lesson (Direct)",s,l)}catch(s){t("Specific Lesson (Direct)",null,s)}o(!1)}),c=()=>r(void 0,null,function*(){o(!0);try{console.log("Testing fetchLessonBySlug function...");const s=yield j("introduction-to-intravenous-cannulation");t("fetchLessonBySlug",s,null)}catch(s){t("fetchLessonBySlug",null,s)}o(!1)}),i=()=>{u([])};return e.jsxs("div",{className:"p-6 max-w-4xl mx-auto",children:[e.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Debug Lesson Loading"}),e.jsxs("div",{className:"space-y-4 mb-6",children:[e.jsx(d,{onClick:x,disabled:n,children:"Test Basic Connection"}),e.jsx(d,{onClick:g,disabled:n,children:"Test Lessons Table"}),e.jsx(d,{onClick:a,disabled:n,children:"Test Specific Lesson (Direct)"}),e.jsx(d,{onClick:c,disabled:n,children:"Test fetchLessonBySlug"}),e.jsx(d,{onClick:i,variant:"outline",children:"Clear Results"})]}),n&&e.jsx("div",{className:"text-blue-600",children:"Running test..."}),e.jsx("div",{className:"space-y-4",children:m.map((s,l)=>e.jsxs("div",{className:"border p-4 rounded-lg",children:[e.jsx("h3",{className:"font-semibold text-lg",children:s.test}),e.jsx("p",{className:"text-sm text-gray-500 mb-2",children:s.timestamp}),s.error?e.jsxs("div",{className:"bg-red-50 p-3 rounded",children:[e.jsx("h4",{className:"font-medium text-red-800",children:"Error:"}),e.jsx("pre",{className:"text-sm text-red-700 mt-1 overflow-auto",children:JSON.stringify(s.error,null,2)})]}):e.jsxs("div",{className:"bg-green-50 p-3 rounded",children:[e.jsx("h4",{className:"font-medium text-green-800",children:"Success:"}),e.jsx("pre",{className:"text-sm text-green-700 mt-1 overflow-auto max-h-40",children:JSON.stringify(s.result,null,2)})]})]},l))})]})};export{w as default};
