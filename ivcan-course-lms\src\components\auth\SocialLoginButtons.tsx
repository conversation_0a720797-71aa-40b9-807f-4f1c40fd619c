import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { Loader2 } from 'lucide-react';
import { getRedirectUrl, getGoogleOAuthParams, getCallbackUrl } from '@/lib/oauth-helpers';
import OAuthErrorDisplay from './OAuthErrorDisplay';
import { useLocation } from 'react-router-dom';

const SocialLoginButtons: React.FC = () => {
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [isAppleLoading, setIsAppleLoading] = useState(false);
  const [googleError, setGoogleError] = useState<Error | null>(null);
  const [appleError, setAppleError] = useState<Error | null>(null);
  const location = useLocation();

  // Get the return URL from query parameters
  const getReturnUrl = () => {
    const searchParams = new URLSearchParams(location.search);
    const returnTo = searchParams.get('returnTo');
    return returnTo || '/dashboard';
  };

  const handleGoogleSignIn = async () => {
    // Clear any previous errors
    setGoogleError(null);

    try {
      setIsGoogleLoading(true);
      console.log('Starting Google OAuth sign-in process');

      // Get the callback URL for the OAuth redirect
      const redirectUrl = getCallbackUrl();
      console.log('Callback URL:', redirectUrl);

      // Get the return URL for after authentication
      const returnTo = getReturnUrl();
      console.log('Return URL after auth:', returnTo);

      // Get Google OAuth parameters - don't override the state parameter
      // Let Supabase handle the state parameter internally
      const queryParams = getGoogleOAuthParams();

      console.log('OAuth query params:', queryParams);

      // Log the environment
      console.log('Environment:', import.meta.env.MODE);
      console.log('Origin:', window.location.origin);
      console.log('Hostname:', window.location.hostname);

      // Sign in with Google OAuth
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: redirectUrl,
          queryParams,
          // Don't skip the browser redirect
          skipBrowserRedirect: false
        },
      });

      if (error) {
        console.error('Google OAuth error:', error);
        throw error;
      }

      console.log('Google OAuth response:', data);

      // The page will be redirected by Supabase, so we don't need to do anything else here
      // But we'll show a loading toast in case there's a delay
      toast.loading('Redirecting to Google authentication...');
    } catch (error: any) {
      console.error('Google sign-in error details:', error);
      setGoogleError(error);
      toast.error(error.message || 'Error signing in with Google');
      setIsGoogleLoading(false);
    }
  };

  const dismissGoogleError = () => {
    setGoogleError(null);
  };

  const handleAppleSignIn = () => {
    // Clear any previous errors
    setAppleError(null);

    setIsAppleLoading(true);
    try {
      toast.error('Apple sign in is not available yet');
    } finally {
      setIsAppleLoading(false);
    }
  };

  const dismissAppleError = () => {
    setAppleError(null);
  };

  return (
    <div className="space-y-3">
      {/* Error displays */}
      {googleError && (
        <OAuthErrorDisplay
          error={googleError}
          provider="Google"
          onDismiss={dismissGoogleError}
        />
      )}

      {appleError && (
        <OAuthErrorDisplay
          error={appleError}
          provider="Apple"
          onDismiss={dismissAppleError}
        />
      )}
      {/* Google Login */}
      <motion.button
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        type="button"
        onClick={handleGoogleSignIn}
        disabled={isGoogleLoading}
        className="flex justify-center items-center w-full px-4 py-3 border border-border rounded-lg text-sm font-medium text-foreground bg-card hover:bg-accent transition-colors disabled:opacity-70 disabled:cursor-not-allowed"
      >
        {isGoogleLoading ? (
          <Loader2 className="w-5 h-5 mr-3 animate-spin" />
        ) : (
          <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24">
            <path
              d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              fill="#4285F4"
            />
            <path
              d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              fill="#34A853"
            />
            <path
              d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              fill="#FBBC05"
            />
            <path
              d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              fill="#EA4335"
            />
          </svg>
        )}
        {isGoogleLoading ? 'Signing in...' : 'Log in with Google'}
      </motion.button>

      {/* Apple Login */}
      <motion.button
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        type="button"
        onClick={handleAppleSignIn}
        disabled={isAppleLoading}
        className="flex justify-center items-center w-full px-4 py-3 border border-border rounded-lg text-sm font-medium text-foreground bg-card hover:bg-accent transition-colors disabled:opacity-70 disabled:cursor-not-allowed"
      >
        {isAppleLoading ? (
          <Loader2 className="w-5 h-5 mr-3 animate-spin" />
        ) : (
          <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24" fill="currentColor">
            <path d="M17.05 20.28c-.98.95-2.05.88-3.08.4-1.09-.5-2.08-.52-3.2 0-.83.38-1.54.35-2.31-.04A9.64 9.64 0 014.85 16C2.46 11.18 4.24 4.71 8.6 4.23c1.61-.19 2.97.83 3.96.91 1.22-.3 2.37-1.01 3.82-.85 1.79.2 3.11.94 3.93 2.16-3.36 2.11-2.66 6.27.54 7.61-.54 1.61-1.24 3.18-2.8 5.22zm-3.15-17.4c-.1 1.33-.56 2.44-1.34 3.38-.82.94-1.98 1.59-3.22 1.45C9.04 5.86 10.15 3.36 12 3c.07 0 .13-.04.19-.06.01-.04.02-.08.02-.12l-.31.06z"/>
          </svg>
        )}
        {isAppleLoading ? 'Signing in...' : 'Log in with Apple'}
      </motion.button>
    </div>
  );
};

export default SocialLoginButtons;
