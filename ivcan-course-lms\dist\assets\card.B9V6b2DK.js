var x=Object.defineProperty;var l=Object.getOwnPropertySymbols;var f=Object.prototype.hasOwnProperty,b=Object.prototype.propertyIsEnumerable;var p=(a,e,r)=>e in a?x(a,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[e]=r,o=(a,e)=>{for(var r in e||(e={}))f.call(e,r)&&p(a,r,e[r]);if(l)for(var r of l(e))b.call(e,r)&&p(a,r,e[r]);return a};var t=(a,e)=>{var r={};for(var s in a)f.call(a,s)&&e.indexOf(s)<0&&(r[s]=a[s]);if(a!=null&&l)for(var s of l(a))e.indexOf(s)<0&&b.call(a,s)&&(r[s]=a[s]);return r};import{r as i,j as n}from"./vendor-react.BcAa1DKr.js";import{a3 as h}from"./vendor.DQpuTRuB.js";import{c as m}from"./index.BLDhDn0D.js";const v=h("rounded-xl border bg-card text-card-foreground shadow-sm transition-all duration-200 touch-manipulation min-h-[64px] sm:min-h-0",{variants:{variant:{default:"border-border/40 hover:border-border/60 hover:shadow-md active:scale-[0.98]",outline:"bg-transparent border-border/40 hover:border-border/60",glass:"bg-white/80 dark:bg-black/20 backdrop-blur-sm border-white/20 dark:border-white/10 hover:shadow-md",elevated:"shadow-md hover:shadow-lg border-border/20"}},defaultVariants:{variant:"default"}}),C=i.forwardRef((d,s)=>{var c=d,{className:a,variant:e}=c,r=t(c,["className","variant"]);return n.jsx("div",o({ref:s,className:m(v({variant:e,className:a}))},r))});C.displayName="Card";const w=i.forwardRef((s,r)=>{var d=s,{className:a}=d,e=t(d,["className"]);return n.jsx("div",o({ref:r,className:m("flex flex-col space-y-1.5","p-6 sm:p-4",a)},e))});w.displayName="CardHeader";const u=i.forwardRef((s,r)=>{var d=s,{className:a}=d,e=t(d,["className"]);return n.jsx("h3",o({ref:r,className:m("text-xl sm:text-lg font-semibold leading-none tracking-tight",a)},e))});u.displayName="CardTitle";const g=i.forwardRef((s,r)=>{var d=s,{className:a}=d,e=t(d,["className"]);return n.jsx("p",o({ref:r,className:m("text-base sm:text-sm text-muted-foreground",a)},e))});g.displayName="CardDescription";const N=i.forwardRef((s,r)=>{var d=s,{className:a}=d,e=t(d,["className"]);return n.jsx("div",o({ref:r,className:m("p-6 sm:p-4 pt-0",a)},e))});N.displayName="CardContent";const j=i.forwardRef((s,r)=>{var d=s,{className:a}=d,e=t(d,["className"]);return n.jsx("div",o({ref:r,className:m("flex items-center p-6 sm:p-4 pt-0",a)},e))});j.displayName="CardFooter";export{C,w as a,u as b,g as c,N as d,j as e};
