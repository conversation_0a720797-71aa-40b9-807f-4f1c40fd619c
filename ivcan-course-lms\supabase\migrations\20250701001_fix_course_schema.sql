-- Fix course schema issues
-- This migration ensures the courses table has all required columns and proper indexes

-- Add image_url column to courses table if it doesn't exist
ALTER TABLE IF EXISTS public.courses 
ADD COLUMN IF NOT EXISTS image_url TEXT;

-- Make sure all required columns exist with proper types
ALTER TABLE IF EXISTS public.courses 
ALTER COLUMN title TYPE TEXT,
ALTER COLUMN slug TYPE TEXT,
ALTER COLUMN description TYPE TEXT,
ALTER COLUMN instructor TYPE TEXT;

-- Make sure the slug column has a unique constraint
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'courses_slug_key' AND conrelid = 'public.courses'::regclass
  ) THEN
    ALTER TABLE public.courses ADD CONSTRAINT courses_slug_key UNIQUE (slug);
  END IF;
EXCEPTION WHEN undefined_table THEN
  -- Table doesn't exist, do nothing
END $$;

-- Create index on slug for faster lookups
CREATE INDEX IF NOT EXISTS idx_courses_slug ON public.courses(slug);

-- Update RLS policies to ensure proper access
DROP POLICY IF EXISTS "Anyone can view courses" ON public.courses;
CREATE POLICY "Anyone can view courses" 
ON public.courses FOR SELECT 
USING (true);

-- Allow teachers to update courses
DROP POLICY IF EXISTS "Teachers can update courses" ON public.courses;
CREATE POLICY "Teachers can update courses" 
ON public.courses FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_roles.user_id = auth.uid() AND user_roles.role = 'teacher'
  )
);

-- Allow teachers to insert courses
DROP POLICY IF EXISTS "Teachers can insert courses" ON public.courses;
CREATE POLICY "Teachers can insert courses" 
ON public.courses FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_roles.user_id = auth.uid() AND user_roles.role = 'teacher'
  )
);

-- Allow teachers to delete courses
DROP POLICY IF EXISTS "Teachers can delete courses" ON public.courses;
CREATE POLICY "Teachers can delete courses" 
ON public.courses FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_roles.user_id = auth.uid() AND user_roles.role = 'teacher'
  )
);

-- Create a function to complete a course
CREATE OR REPLACE FUNCTION complete_course(p_user_id UUID, p_course_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  v_enrollment_exists BOOLEAN;
BEGIN
  -- Check if enrollment exists
  SELECT EXISTS(
    SELECT 1 FROM public.user_course_enrollment
    WHERE user_id = p_user_id AND course_id = p_course_id
  ) INTO v_enrollment_exists;
  
  -- If enrollment doesn't exist, create it
  IF NOT v_enrollment_exists THEN
    INSERT INTO public.user_course_enrollment (
      user_id, course_id, status, enrolled_at, completed_at, updated_at
    ) VALUES (
      p_user_id, p_course_id, 'completed', NOW(), NOW(), NOW()
    );
  ELSE
    -- Update existing enrollment
    UPDATE public.user_course_enrollment
    SET 
      status = 'completed',
      completed_at = COALESCE(completed_at, NOW()),
      updated_at = NOW()
    WHERE 
      user_id = p_user_id AND course_id = p_course_id;
  END IF;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in complete_course: %', SQLERRM;
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
