#!/usr/bin/env node

/**
 * Test RLS Policies Script
 * This script tests the RLS policies for storage buckets and database tables
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Please check VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY in your .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

console.log('\n=== Testing RLS Policies ===\n');

async function testStorageBuckets() {
  console.log('🔍 Testing Storage Buckets...');
  
  try {
    const { data: buckets, error } = await supabase.storage.listBuckets();
    
    if (error) {
      console.error('❌ Error listing buckets:', error.message);
      return false;
    }
    
    const expectedBuckets = ['course-images', 'avatars', 'app-uploads', 'uploads', 'default-bucket'];
    const existingBuckets = buckets.map(bucket => bucket.name);
    
    console.log('📦 Existing buckets:', existingBuckets);
    
    const missingBuckets = expectedBuckets.filter(bucket => !existingBuckets.includes(bucket));
    
    if (missingBuckets.length > 0) {
      console.error('❌ Missing buckets:', missingBuckets);
      return false;
    }
    
    console.log('✅ All required buckets exist');
    return true;
    
  } catch (error) {
    console.error('❌ Error testing storage buckets:', error.message);
    return false;
  }
}

async function testStorageUpload() {
  console.log('\n🔍 Testing Storage Upload (Anonymous)...');
  
  try {
    // Test uploading a small file to course-images bucket
    const testFile = new Blob(['test content'], { type: 'text/plain' });
    const fileName = `test-${Date.now()}.txt`;
    
    const { data, error } = await supabase.storage
      .from('course-images')
      .upload(fileName, testFile);
    
    if (error) {
      console.log('⚠️ Anonymous upload failed (expected):', error.message);
      return true; // This is expected for anonymous users
    }
    
    console.log('✅ Anonymous upload succeeded:', data.path);
    
    // Clean up the test file
    await supabase.storage.from('course-images').remove([fileName]);
    
    return true;
    
  } catch (error) {
    console.error('❌ Error testing storage upload:', error.message);
    return false;
  }
}

async function testDatabaseRLS() {
  console.log('\n🔍 Testing Database RLS...');
  
  try {
    // Test reading from courses table (should work for anonymous users)
    const { data: courses, error: coursesError } = await supabase
      .from('courses')
      .select('id, title')
      .limit(1);
    
    if (coursesError) {
      console.error('❌ Error reading courses:', coursesError.message);
      return false;
    }
    
    console.log('✅ Can read courses table');
    
    // Test reading from users table (should be restricted)
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email')
      .limit(1);
    
    if (usersError) {
      console.log('✅ Users table properly restricted:', usersError.message);
    } else {
      console.log('⚠️ Users table accessible (may be intended)');
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ Error testing database RLS:', error.message);
    return false;
  }
}

async function testAuthenticatedAccess() {
  console.log('\n🔍 Testing Authenticated Access...');
  
  try {
    // Try to sign in with a test user (this will likely fail, which is fine)
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'testpassword'
    });
    
    if (authError) {
      console.log('⚠️ Test authentication failed (expected):', authError.message);
      console.log('✅ Authentication system is working (rejecting invalid credentials)');
      return true;
    }
    
    console.log('✅ Test authentication succeeded');
    
    // If authenticated, test upload
    const testFile = new Blob(['authenticated test content'], { type: 'text/plain' });
    const fileName = `auth-test-${Date.now()}.txt`;
    
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('course-images')
      .upload(fileName, testFile);
    
    if (uploadError) {
      console.error('❌ Authenticated upload failed:', uploadError.message);
    } else {
      console.log('✅ Authenticated upload succeeded:', uploadData.path);
      // Clean up
      await supabase.storage.from('course-images').remove([fileName]);
    }
    
    // Sign out
    await supabase.auth.signOut();
    
    return true;
    
  } catch (error) {
    console.error('❌ Error testing authenticated access:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('Starting RLS Policy Tests...\n');
  
  const results = {
    storageBuckets: await testStorageBuckets(),
    storageUpload: await testStorageUpload(),
    databaseRLS: await testDatabaseRLS(),
    authenticatedAccess: await testAuthenticatedAccess()
  };
  
  console.log('\n=== Test Results Summary ===');
  console.log('Storage Buckets:', results.storageBuckets ? '✅ PASS' : '❌ FAIL');
  console.log('Storage Upload:', results.storageUpload ? '✅ PASS' : '❌ FAIL');
  console.log('Database RLS:', results.databaseRLS ? '✅ PASS' : '❌ FAIL');
  console.log('Authenticated Access:', results.authenticatedAccess ? '✅ PASS' : '❌ FAIL');
  
  const allPassed = Object.values(results).every(result => result);
  
  if (allPassed) {
    console.log('\n🎉 All RLS tests passed!');
    console.log('Your RLS policies are properly configured.');
  } else {
    console.log('\n⚠️ Some tests failed.');
    console.log('Please review the RLS policies and run the manual SQL script if needed.');
  }
  
  return allPassed;
}

// Run the tests
runTests().catch(console.error);
