// This script builds the application with production optimizations
const { execSync } = require('child_process');

console.log('Building with production optimizations...');

try {
  // Build with the production config
  execSync('vite build --config vite.config.prod.ts', { stdio: 'inherit' });
  console.log('Production build completed successfully!');
} catch (error) {
  console.error('Error building with production optimizations:', error.message);
  process.exit(1);
}
