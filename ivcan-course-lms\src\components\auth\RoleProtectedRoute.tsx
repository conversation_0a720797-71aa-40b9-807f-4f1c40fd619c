import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { Loader2, ShieldAlert } from 'lucide-react';
import { getUserRole } from '@/services/auth/userRoleService';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';

// Loading spinner component
const LoadingSpinner = () => (
  <div className="flex items-center justify-center min-h-screen bg-background">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
  </div>
);

interface RoleProtectedRouteProps {
  children: React.ReactNode;
  requiredRole: 'teacher' | 'student' | 'admin';
}

/**
 * A component that protects routes by requiring a specific role
 * This component performs a database check for the user's role
 */
const RoleProtectedRoute: React.FC<RoleProtectedRouteProps> = ({
  children,
  requiredRole
}) => {
  const { user, loading } = useAuth();
  const location = useLocation();
  const [roleChecked, setRoleChecked] = useState(false);
  const [hasRole, setHasRole] = useState<boolean | null>(null);
  const [roleCheckLoading, setRoleCheckLoading] = useState(true);

  useEffect(() => {
    const checkUserRole = async () => {
      if (!user) {
        setRoleCheckLoading(false);
        setHasRole(false);
        return;
      }

      try {
        const { data, error } = await supabase
          .from('user_roles')
          .select('role')
          .eq('user_id', user.id)
          .eq('role', requiredRole)
          .maybeSingle();

        if (error) throw error;
        setHasRole(!!data);
        setRoleChecked(true);
      } catch (error) {
        console.error('Error checking user role:', error);
        toast.error('Failed to verify your access permissions');
        setHasRole(false);
      } finally {
        setRoleCheckLoading(false);
      }
    };

    if (!loading) {
      checkUserRole();
    }
  }, [user, loading, requiredRole]);

  // Show loading state while checking authentication and role
  if (loading || roleCheckLoading) {
    return <LoadingSpinner />;
  }

  // If not authenticated, redirect to login
  if (!user) {
    // Save the current location to redirect back after login
    const returnPath = encodeURIComponent(location.pathname + location.search);
    return <Navigate to={`/login?returnTo=${returnPath}`} replace />;
  }

  // If role check is complete and user doesn't have the required role
  if (roleChecked && !hasRole) {
    return (
      <div className="flex justify-center items-center h-screen bg-background">
        <div className="max-w-md w-full p-8 bg-card rounded-xl shadow-sm border border-border text-center">
          <ShieldAlert className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold mb-2">Access Denied</h1>
          <p className="text-muted-foreground mb-6">
            You don't have permission to access this page. This area requires {requiredRole} privileges.
          </p>
          <div className="flex gap-4 justify-center">
            <button
              onClick={() => window.history.back()}
              className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
            >
              Go Back
            </button>
            <button
              onClick={() => window.location.href = '/dashboard'}
              className="px-4 py-2 bg-muted text-foreground rounded-lg hover:bg-muted/90 transition-colors"
            >
              Go to Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  // If authenticated and has the required role, render the children
  return <>{children}</>;
};

export default RoleProtectedRoute;
