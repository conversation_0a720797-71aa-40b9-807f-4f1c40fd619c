import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import Layout from '@/components/Layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';
import { Loader2, RefreshCcw, AlertCircle, CheckCircle2 } from 'lucide-react';
import { getCompletionSystemStatus } from '@/services/course/completionService';

const CompletionDiagnostics = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const [diagnosticData, setDiagnosticData] = useState<any>(null);
  const [completionStats, setCompletionStats] = useState<any>(null);
  const [auditLogs, setAuditLogs] = useState<any[]>([]);
  const [runningTest, setRunningTest] = useState(false);
  const [testResult, setTestResult] = useState<any>(null);

  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!user) {
        navigate('/login');
        return;
      }

      try {
        const { data } = await supabase
          .from('user_roles')
          .select('role')
          .eq('user_id', user.id)
          .eq('role', 'admin')
          .maybeSingle();

        // Check if user is an admin
        if (!data) {
          toast.error('You need admin privileges to access this page');
          navigate('/');
          return;
        }

        setIsAdmin(true);
        loadDiagnosticData();
      } catch (error) {
        console.error('Error checking admin status:', error);
        toast.error('Error checking permissions');
        navigate('/');
      }
    };

    checkAdminStatus();
  }, [user, navigate]);

  const loadDiagnosticData = async () => {
    setLoading(true);
    try {
      // Get system status
      const statusResult = await getCompletionSystemStatus();
      if (statusResult.success) {
        setDiagnosticData(statusResult.data);
      } else {
        toast.error(`Error fetching system status: ${statusResult.error}`);
      }

      // Get completion stats
      const { data: statsData, error: statsError } = await supabase
        .rpc('count_completion_records' as any);
      
      if (statsError) {
        console.error('Error fetching completion stats:', statsError);
      } else {
        setCompletionStats(statsData);
      }

      // Get recent audit logs
      const { data: logsData, error: logsError } = await supabase
        .from('completion_audit_log' as any)
        .select('*')
        .order('completed_at', { ascending: false })
        .limit(20);
      
      if (logsError) {
        console.error('Error fetching audit logs:', logsError);
      } else {
        setAuditLogs(logsData || []);
      }
    } catch (error) {
      console.error('Error loading diagnostic data:', error);
      toast.error('Failed to load diagnostic data');
    } finally {
      setLoading(false);
    }
  };

  const runTestInsertion = async () => {
    if (!user) return;
    
    setRunningTest(true);
    setTestResult(null);
    
    try {
      // Get an available lesson for testing
      const { data: availableLesson, error: lessonError } = await supabase
        .from('lessons')
        .select('id')
        .limit(1);
      
      if (lessonError || !availableLesson || availableLesson.length === 0) {
        throw new Error('No lessons available for testing');
      }
      
      const lessonId = availableLesson[0].id;
      
      // Clear any existing progress for this lesson
      await supabase
        .from('user_lesson_progress')
        .delete()
        .eq('user_id', user.id)
        .eq('lesson_id', lessonId);
      
      // Attempt direct insertion
      const { error: insertError } = await supabase
        .from('user_lesson_progress')
        .insert({
          user_id: user.id,
          lesson_id: lessonId,
          is_completed: true,
          completed_at: new Date().toISOString()
        });
      
      // Verify the insertion
      const { data: verification, error: verifyError } = await supabase
        .from('user_lesson_progress')
        .select('*')
        .eq('user_id', user.id)
        .eq('lesson_id', lessonId)
        .single();
      
      setTestResult({
        success: !insertError && !verifyError && verification?.is_completed === true,
        insertError: insertError?.message,
        verifyError: verifyError?.message,
        verification
      });
      
      // Refresh data
      loadDiagnosticData();
    } catch (error: any) {
      setTestResult({
        success: false,
        error: error.message
      });
    } finally {
      setRunningTest(false);
    }
  };

  if (!isAdmin) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[70vh]">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container py-10">
        <div className="mb-6 flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Completion System Diagnostics</h1>
            <p className="text-muted-foreground">Troubleshoot lesson completion issues</p>
          </div>
          <Button 
            onClick={loadDiagnosticData} 
            variant="outline" 
            className="flex items-center gap-2"
            disabled={loading}
          >
            {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <RefreshCcw className="h-4 w-4" />}
            Refresh
          </Button>
        </div>

        <Tabs defaultValue="overview">
          <TabsList className="mb-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="schema">Database Schema</TabsTrigger>
            <TabsTrigger value="audit">Audit Logs</TabsTrigger>
            <TabsTrigger value="test">Run Tests</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle>Total Progress Records</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold">
                    {loading ? <Loader2 className="h-5 w-5 animate-spin" /> : 
                      completionStats?.total || 0}
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle>Completed Lessons</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold">
                    {loading ? <Loader2 className="h-5 w-5 animate-spin" /> : 
                      completionStats?.completed || 0}
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle>Users with Progress</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold">
                    {loading ? <Loader2 className="h-5 w-5 animate-spin" /> : 
                      completionStats?.users || 0}
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <Card>
              <CardHeader>
                <CardTitle>System Status</CardTitle>
                <CardDescription>Details about the completion system's current state</CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex justify-center p-4">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-semibold mb-2">Recent Activity</h3>
                      <p>Last 24 hours: {auditLogs.filter(log => {
                        const date = new Date(log.completed_at);
                        const now = new Date();
                        const diff = now.getTime() - date.getTime();
                        return diff < 24 * 60 * 60 * 1000;
                      }).length} completion attempts</p>
                    </div>
                    
                    <div>
                      <h3 className="font-semibold mb-2">Database Table Health</h3>
                      <div className="space-y-2">
                        <div className="flex items-center">
                          <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
                          <span>user_lesson_progress table exists</span>
                        </div>
                        <div className="flex items-center">
                          {diagnosticData?.tableInfo && 
                          diagnosticData.tableInfo.some((col: any) => col.column_name === 'is_completed') ? (
                            <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
                          ) : (
                            <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                          )}
                          <span>is_completed column {diagnosticData?.tableInfo && 
                            diagnosticData.tableInfo.some((col: any) => col.column_name === 'is_completed') ? 
                            'exists' : 'missing'}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="schema">
            <Card>
              <CardHeader>
                <CardTitle>Database Schema</CardTitle>
                <CardDescription>Detailed information about the completion tables</CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex justify-center p-4">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : (
                  <div className="space-y-6">
                    <div>
                      <h3 className="font-semibold mb-2">user_lesson_progress Table</h3>
                      <div className="border rounded-lg overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50 dark:bg-gray-800">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Column Name</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Data Type</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Nullable</th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Default</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                            {diagnosticData?.tableInfo?.map((column: any, index: number) => (
                              <tr key={index} className={column.column_name === 'is_completed' ? 'bg-green-50 dark:bg-green-900/20' : ''}>
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">{column.column_name}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm">{column.data_type}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm">{column.is_nullable}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm">{column.column_default || '-'}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="audit">
            <Card>
              <CardHeader>
                <CardTitle>Completion Audit Logs</CardTitle>
                <CardDescription>Recent lesson completion attempts</CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex justify-center p-4">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : auditLogs.length > 0 ? (
                  <div className="border rounded-lg overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50 dark:bg-gray-800">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Timestamp</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">User</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Lesson</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Message</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                        {auditLogs.map((log, index) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">
                              {new Date(log.completed_at).toLocaleString()}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">{log.user_id.substring(0, 8)}...</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">{log.lesson_id.substring(0, 8)}...</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">
                              {log.success ? (
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                                  Success
                                </span>
                              ) : (
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300">
                                  Failed
                                </span>
                              )}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">{log.error_message || '-'}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-6 text-muted-foreground">
                    No audit logs found
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="test">
            <Card>
              <CardHeader>
                <CardTitle>Test Completion System</CardTitle>
                <CardDescription>Run tests against the completion system</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="p-4 border rounded-lg">
                    <h3 className="font-semibold mb-2">Direct Database Test</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Test direct insertion of a lesson completion record in the database.
                    </p>
                    
                    <Button 
                      onClick={runTestInsertion} 
                      disabled={runningTest || loading}
                      variant="outline"
                    >
                      {runningTest ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Running Test...
                        </>
                      ) : (
                        'Run Database Test'
                      )}
                    </Button>
                    
                    {testResult && (
                      <div className="mt-4 p-4 border rounded-lg bg-gray-50 dark:bg-gray-800/50">
                        <div className="flex items-center mb-2">
                          {testResult.success ? (
                            <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />
                          ) : (
                            <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                          )}
                          <span className="font-medium">
                            {testResult.success ? 'Test passed' : 'Test failed'}
                          </span>
                        </div>
                        
                        {testResult.insertError && (
                          <div className="text-sm text-red-500 mt-2">
                            Insert Error: {testResult.insertError}
                          </div>
                        )}
                        
                        {testResult.verifyError && (
                          <div className="text-sm text-red-500 mt-2">
                            Verify Error: {testResult.verifyError}
                          </div>
                        )}
                        
                        {testResult.error && (
                          <div className="text-sm text-red-500 mt-2">
                            Error: {testResult.error}
                          </div>
                        )}
                        
                        {testResult.verification && (
                          <div className="mt-2 text-sm">
                            <pre className="p-2 bg-gray-100 dark:bg-gray-900 rounded overflow-x-auto">
                              {JSON.stringify(testResult.verification, null, 2)}
                            </pre>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
};

export default CompletionDiagnostics; 