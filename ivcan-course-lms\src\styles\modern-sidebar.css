/* Modern Sidebar Styles */

/* Ensure consistent font sizing throughout sidebar */
.modern-sidebar,
.modern-sidebar * {
  font-family: 'Poppins', system-ui, sans-serif;
}

/* Floating sidebar with glass morphism */
.floating-sidebar {
  background: rgba(230, 57, 70, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.floating-sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, transparent 30%, rgba(255,255,255,0.05) 100%);
  pointer-events: none;
  border-radius: inherit;
}

.floating-sidebar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 0%, transparent 50%);
  pointer-events: none;
  border-radius: inherit;
}

/* Header section styling */
.floating-sidebar .header-section {
  backdrop-filter: blur(15px);
  background: rgba(255, 255, 255, 0.08);
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 1rem 1rem 0 0;
}

/* Logo hover effects */
.modern-sidebar .logo-container {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-sidebar .logo-container:hover {
  transform: translateY(-1px);
}

.modern-sidebar .logo-icon {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-sidebar .logo-container:hover .logo-icon {
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(5deg) scale(1.05);
}

/* Profile section styling */
.floating-sidebar .profile-section {
  backdrop-filter: blur(15px);
  background: rgba(255, 255, 255, 0.08);
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
}

.modern-sidebar .profile-avatar {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-sidebar .profile-section:hover .profile-avatar {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Navigation items styling */
.floating-sidebar .nav-item {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.floating-sidebar .nav-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: rgba(255, 255, 255, 0.9);
  transform: scaleY(0);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 0 2px 2px 0;
}

.floating-sidebar .nav-item.active::before {
  transform: scaleY(1);
}

.floating-sidebar .nav-item:hover {
  transform: translateX(4px);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
}

.floating-sidebar .nav-item.active {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(15px);
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.floating-sidebar .nav-item.active::after {
  content: '';
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
}

/* Navigation icons */
.modern-sidebar .nav-icon {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-sidebar .nav-item:hover .nav-icon {
  transform: scale(1.1);
}

.modern-sidebar .nav-item.active .nav-icon {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* Section headers */
.modern-sidebar .section-header {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 600;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  font-size: 0.75rem;
}

/* Footer section */
.floating-sidebar .footer-section {
  backdrop-filter: blur(15px);
  background: rgba(255, 255, 255, 0.08);
  border-top: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 0 0 1rem 1rem;
}

/* Theme toggle styling */
.modern-sidebar .theme-toggle {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-sidebar .theme-toggle:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

.modern-sidebar .theme-toggle-switch {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.modern-sidebar .theme-toggle-thumb {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Sign out button */
.modern-sidebar .sign-out-btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-sidebar .sign-out-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

/* Mobile specific styles */
@media (max-width: 768px) {
  .floating-sidebar {
    width: 260px !important;
    max-width: calc(80vw - 1rem);
    box-shadow:
      0 20px 60px rgba(0, 0, 0, 0.4),
      0 0 0 1px rgba(255, 255, 255, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
  }

  /* Dark mode mobile sidebar */
  .dark .floating-sidebar {
    box-shadow:
      0 20px 60px rgba(0, 0, 0, 0.6),
      0 0 0 1px rgba(255, 255, 255, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.12);
  }

  /* Improve touch targets on mobile with cleaner spacing */
  .floating-sidebar .nav-item,
  .floating-sidebar .theme-toggle,
  .floating-sidebar .sign-out-btn {
    min-height: 44px;
    padding-top: 10px;
    padding-bottom: 10px;
    margin-bottom: 2px;
    backdrop-filter: blur(12px);
  }

  /* Smaller, cleaner font sizing on mobile */
  .floating-sidebar .nav-item span,
  .floating-sidebar .theme-toggle span,
  .floating-sidebar .sign-out-btn span {
    font-size: 0.875rem;
  }

  .floating-sidebar .section-header {
    font-size: 0.75rem;
  }

  /* Smaller icons on mobile for cleaner spacing */
  .floating-sidebar .nav-icon,
  .floating-sidebar svg {
    width: 1.125rem;
    height: 1.125rem;
  }

  /* Enhanced mobile backdrop - transparent with blur */
  .modern-sidebar-backdrop {
    /* Fallback for browsers without backdrop-filter support */
    background: rgba(0, 0, 0, 0.5);
    transition: all 0.3s ease-out;
    /* Ensure backdrop works on all devices */
    will-change: backdrop-filter;
    backface-visibility: hidden;
  }

  /* Enhanced blur for browsers that support it */
  @supports (backdrop-filter: blur(1px)) {
    .modern-sidebar-backdrop {
      background: rgba(0, 0, 0, 0.3);
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
    }
  }

  /* Mobile header adjustments - more compact */
  .floating-sidebar .header-section {
    height: 64px;
    padding: 0 1.25rem;
    backdrop-filter: blur(20px);
  }

  /* Mobile profile section - more compact */
  .floating-sidebar .profile-section {
    padding: 1.25rem;
    backdrop-filter: blur(20px);
  }

  /* Mobile navigation spacing - more compact */
  .floating-sidebar nav {
    padding: 1.25rem 0.875rem;
  }

  /* Mobile footer - more compact */
  .floating-sidebar .footer-section {
    padding: 1.25rem;
    backdrop-filter: blur(20px);
  }
}

/* Smooth animations for mobile slide-in */
@media (max-width: 768px) {
  .floating-sidebar {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .floating-sidebar[data-state="closed"] {
    transform: translateX(-100%);
    opacity: 0;
  }

  .floating-sidebar[data-state="open"] {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Desktop floating effect */
@media (min-width: 769px) {
  .floating-sidebar {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .floating-sidebar:hover {
    transform: translateY(-2px);
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.4),
      0 0 0 1px rgba(255, 255, 255, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
  }
}

/* Accessibility improvements */
.modern-sidebar .nav-item:focus,
.modern-sidebar .theme-toggle:focus,
.modern-sidebar .sign-out-btn:focus {
  outline: 2px solid rgba(255, 255, 255, 0.8);
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .modern-sidebar *,
  .modern-sidebar *::before,
  .modern-sidebar *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .modern-sidebar {
    background: #e63946;
    border-right: 2px solid #ffffff;
  }
  
  .modern-sidebar .nav-item.active {
    background: rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.5);
  }
}

/* Dark mode adjustments - CSS class based */
.dark .floating-sidebar {
  background: rgba(230, 57, 70, 0.98);
  backdrop-filter: blur(25px);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.12);
}

.dark .floating-sidebar::before {
  background: linear-gradient(135deg, rgba(255,255,255,0.08) 0%, transparent 30%, rgba(255,255,255,0.03) 100%);
}

/* Dark mode backdrop */
.dark .modern-sidebar-backdrop {
  /* Fallback for browsers without backdrop-filter support */
  background: rgba(0, 0, 0, 0.6);
}

@supports (backdrop-filter: blur(1px)) {
  .dark .modern-sidebar-backdrop {
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }
}

/* Dark mode adjustments - System preference based */
@media (prefers-color-scheme: dark) {
  .floating-sidebar {
    background: rgba(230, 57, 70, 0.98);
    backdrop-filter: blur(25px);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.5),
      0 0 0 1px rgba(255, 255, 255, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.12);
  }

  .floating-sidebar::before {
    background: linear-gradient(135deg, rgba(255,255,255,0.08) 0%, transparent 30%, rgba(255,255,255,0.03) 100%);
  }

  /* Dark mode backdrop for system preference */
  .modern-sidebar-backdrop {
    /* Fallback for browsers without backdrop-filter support */
    background: rgba(0, 0, 0, 0.6) !important;
  }

  @supports (backdrop-filter: blur(1px)) {
    .modern-sidebar-backdrop {
      background: rgba(0, 0, 0, 0.4) !important;
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
    }
  }
}

/* Scrollbar styling for navigation */
.modern-sidebar nav::-webkit-scrollbar {
  width: 4px;
}

.modern-sidebar nav::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.modern-sidebar nav::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.modern-sidebar nav::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Additional modern enhancements */
.modern-sidebar .nav-item:hover::before {
  content: '';
  position: absolute;
  left: -4px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 0 1px 1px 0;
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    transform: scaleY(0);
    opacity: 0;
  }
  to {
    transform: scaleY(1);
    opacity: 1;
  }
}

/* Subtle glow effect for active items */
.modern-sidebar .nav-item.active {
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Enhanced hover states */
.modern-sidebar .nav-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Smooth transitions for all interactive elements */
.modern-sidebar button,
.modern-sidebar a,
.modern-sidebar .nav-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Focus states for better accessibility */
.modern-sidebar .nav-item:focus-visible,
.modern-sidebar .theme-toggle:focus-visible,
.modern-sidebar .sign-out-btn:focus-visible {
  outline: 2px solid rgba(255, 255, 255, 0.8);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.1);
}
