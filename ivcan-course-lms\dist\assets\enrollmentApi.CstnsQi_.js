var f=Object.defineProperty,N=Object.defineProperties;var L=Object.getOwnPropertyDescriptors;var m=Object.getOwnPropertySymbols;var w=Object.prototype.hasOwnProperty,h=Object.prototype.propertyIsEnumerable;var p=(o,e,r)=>e in o?f(o,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):o[e]=r,_=(o,e)=>{for(var r in e||(e={}))w.call(e,r)&&p(o,r,e[r]);if(m)for(var r of m(e))h.call(e,r)&&p(o,r,e[r]);return o},E=(o,e)=>N(o,L(e));var d=(o,e,r)=>new Promise((n,s)=>{var a=t=>{try{c(r.next(t))}catch(u){s(u)}},i=t=>{try{c(r.throw(t))}catch(u){s(u)}},c=t=>t.done?n(t.value):Promise.resolve(t.value).then(a,i);c((r=r.apply(o,e)).next())});import{s as l}from"./index.BLDhDn0D.js";import{a2 as g}from"./vendor.DQpuTRuB.js";const R=(o,e,r="not_started")=>d(void 0,null,function*(){if(console.log(`[ENROLLMENT] Starting enrollment process for user ${e} in course ${o} with status ${r}`),!o||!e){const s=new Error("Missing required parameters: courseId and userId are required");throw console.error("[ENROLLMENT] Validation error:",s.message),s}const n=new Date().toISOString();try{console.log("[ENROLLMENT] Processing enrollment for user:",e),console.log("[ENROLLMENT] Checking for existing enrollment...");const{data:s,error:a}=yield l.from("user_course_enrollment").select("*").eq("user_id",e).eq("course_id",o).maybeSingle();if(a)throw console.error("[ENROLLMENT] Error checking existing enrollment:",a),new Error(`Failed to check existing enrollment: ${a.message}`);if(console.log("[ENROLLMENT] Existing enrollment check result:",s),s){console.log("Updating enrollment status to:",r);const{data:i,error:c}=yield l.from("user_course_enrollment").update({status:r,updated_at:n}).eq("id",s.id).select();if(c)throw console.error("Error updating course enrollment:",c),c;if(console.log("Enrollment updated successfully:",i),r==="completed")try{console.log("[ENROLLMENT] Updating course progress record");const{error:t}=yield l.from("user_course_progress").upsert({user_id:e,course_id:o,last_accessed_at:n,updated_at:n},{onConflict:"user_id,course_id"});t&&console.error("[ENROLLMENT] Error updating progress record:",t)}catch(t){console.error("[ENROLLMENT] Error updating progress (non-critical):",t)}return i}else{console.log("[ENROLLMENT] Creating new enrollment with status:",r);const i={user_id:e,course_id:o,status:r,enrolled_at:n,updated_at:n};console.log("[ENROLLMENT] Insert data:",i);const{data:c,error:t}=yield l.from("user_course_enrollment").insert(i).select();if(t)throw console.error("[ENROLLMENT] Error creating course enrollment:",t),console.error("[ENROLLMENT] Error details:",{message:t.message,code:t.code,details:t.details,hint:t.hint}),new Error(`Failed to create enrollment: ${t.message} (Code: ${t.code})`);console.log("[ENROLLMENT] Enrollment created successfully:",c);try{console.log("[ENROLLMENT] Creating initial progress record");const{error:u}=yield l.from("user_course_progress").upsert({user_id:e,course_id:o,hours_spent:0,last_accessed_at:n,updated_at:n},{onConflict:"user_id,course_id"});u?console.error("[ENROLLMENT] Error creating progress record:",u):console.log("[ENROLLMENT] Progress record created successfully")}catch(u){console.error("[ENROLLMENT] Unexpected error creating progress record:",u)}return c}}catch(s){console.error("[ENROLLMENT] Error in enrollInCourse:",s);let a="Failed to update course enrollment status";throw s.message&&(a=s.message),g.error(a),s}}),T=(o,e)=>d(void 0,null,function*(){if(!e)return null;const{data:r,error:n}=yield l.from("user_course_enrollment").select("*").eq("user_id",e).eq("course_id",o).maybeSingle();return n?(console.error("Error fetching course enrollment:",n),null):r&&E(_({},r),{status:r.status})}),C=(o,e)=>d(void 0,null,function*(){try{if(console.log(`Marking course ${o} as completed for user ${e}`),console.log("Course ID type:",typeof o),console.log("User ID type:",typeof e),!o||typeof o!="string")return console.error("Invalid course ID:",o),g.error("Invalid course ID. Please try again."),!1;if(!e||typeof e!="string")return console.error("Invalid user ID:",e),g.error("Invalid user ID. Please try again."),!1;const r=new Date().toISOString();console.log("Using simplified direct database operations...");const{data:n,error:s}=yield l.from("user_course_enrollment").update({status:"completed",updated_at:r}).eq("user_id",e).eq("course_id",o).select("id");if(!s&&n&&n.length>0)return console.log("Successfully updated enrollment to completed status"),yield l.from("user_course_progress").upsert({user_id:e,course_id:o,completed_modules:100,updated_at:r},{onConflict:"user_id,course_id"}),!0;{console.log("No existing enrollment found or update failed, attempting insert...");const{error:a}=yield l.from("user_course_enrollment").insert({user_id:e,course_id:o,status:"completed",enrolled_at:r,updated_at:r});if(a){console.error("Error inserting enrollment:",a),console.log("Trying upsert as final approach...");const{error:i}=yield l.from("user_course_enrollment").upsert({user_id:e,course_id:o,status:"completed",enrolled_at:r,updated_at:r});return i?(console.error("All direct database approaches failed"),g.error("Failed to mark course as completed. Please try again."),!1):(console.log("Successfully upserted enrollment"),!0)}else return console.log("Successfully inserted new completed enrollment"),yield l.from("user_course_progress").upsert({user_id:e,course_id:o,completed_modules:100,updated_at:r},{onConflict:"user_id,course_id"}),!0}}catch(r){return console.error("Error in markCourseAsCompleted:",r),g.error("Failed to update course enrollment status"),!1}}),S=o=>d(void 0,null,function*(){if(!o)return 0;try{const{count:e,error:r}=yield l.from("user_course_enrollment").select("id",{count:"exact",head:!0}).eq("course_id",o);return r?(console.error("Error fetching course enrollment count:",r),0):e||0}catch(e){return console.error("Unexpected error fetching enrollment count:",e),0}});export{S as a,R as e,T as g,C as m};
