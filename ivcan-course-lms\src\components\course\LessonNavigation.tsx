import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { ChevronLeft, ChevronRight, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { UseMutationResult } from '@tanstack/react-query';
import { findNextItemInCourse } from '@/services/course/courseApi';
import { useAuth } from '@/context/AuthContext';

export interface LessonNavigationProps {
  courseId: string;
  currentLessonSlug: string;
  previousLesson?: string;
  nextLesson?: string;
  isCompleted?: boolean;
  completionMutation?: UseMutationResult<any, any, any>;
}

const LessonNavigation: React.FC<LessonNavigationProps> = ({
  courseId,
  currentLessonSlug,
  previousLesson,
  nextLesson,
  isCompleted = false,
  completionMutation
}) => {
  const [isNavigating, setIsNavigating] = useState(false);
  const navigate = useNavigate();
  const { user } = useAuth();

  // Handle next item click - complete lesson first, then navigate to next item (lesson or test)
  const handleNextClick = async (e: React.MouseEvent) => {
    e.preventDefault();

    if (isNavigating || completionMutation?.isPending || !user) return;

    setIsNavigating(true);

    try {
      // If lesson is not completed and we have a completion mutation, mark it as completed first
      if (!isCompleted && completionMutation) {
        await completionMutation.mutateAsync();
      }

      // Find the next item in the course sequence (could be lesson or test)
      const nextItem = await findNextItemInCourse(currentLessonSlug, user.id);

      if (nextItem.nextItemType && nextItem.nextItemSlug) {
        switch (nextItem.nextItemType) {
          case 'lesson':
            navigate(`/course/${courseId}/lesson/${nextItem.nextItemSlug}`);
            break;
          case 'pre_test':
          case 'post_test':
            navigate(`/course/${courseId}/module/${nextItem.nextItemSlug}?type=${nextItem.nextItemType}`);
            break;
          default:
            // Fallback to original next lesson if available
            if (nextLesson) {
              navigate(`/course/${courseId}/lesson/${nextLesson}`);
            } else {
              navigate(`/course/${courseId}/modules`);
            }
        }
      } else if (nextLesson) {
        // Fallback to original next lesson navigation
        navigate(`/course/${courseId}/lesson/${nextLesson}`);
      } else {
        // No next item, go to modules page
        navigate(`/course/${courseId}/modules`);
      }
    } catch (error) {
      console.error('Error completing lesson before navigation:', error);
      // Fallback navigation
      if (nextLesson) {
        navigate(`/course/${courseId}/lesson/${nextLesson}`);
      } else {
        navigate(`/course/${courseId}/modules`);
      }
    } finally {
      setIsNavigating(false);
    }
  };
  return (
    <div className="flex justify-between items-center py-2 mb-6 border-y">
      {previousLesson ? (
        <Button
          variant="ghost"
          asChild
          className="flex items-center gap-1"
        >
          <Link to={`/course/${courseId}/lesson/${previousLesson}`}>
            <ChevronLeft className="h-4 w-4" />
            Previous
          </Link>
        </Button>
      ) : (
        <Button variant="ghost" disabled className="invisible">
          <ChevronLeft className="h-4 w-4" />
          Previous
        </Button>
      )}

      {/* Removed "Back to Modules" button */}
      <div className="flex-1"></div>

      <Button
        variant="ghost"
        onClick={handleNextClick}
        disabled={isNavigating || completionMutation?.isPending || !user}
        className="flex items-center gap-1"
      >
        {isNavigating ? (
          <>
            <Loader2 className="h-4 w-4 animate-spin" />
            {isCompleted ? 'Loading...' : 'Completing...'}
          </>
        ) : (
          <>
            Next
            <ChevronRight className="h-4 w-4" />
          </>
        )}
      </Button>
    </div>
  );
};

export default LessonNavigation;
