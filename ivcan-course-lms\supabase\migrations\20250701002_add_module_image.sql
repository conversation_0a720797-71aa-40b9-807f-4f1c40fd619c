-- Add image_url column to modules table
ALTER TABLE modules ADD COLUMN IF NOT EXISTS image_url TEXT;

-- Update RLS policies to allow access to image_url
ALTER TABLE modules ENABLE ROW LEVEL SECURITY;

-- Allow all authenticated users to view module images
CREATE POLICY "Allow authenticated users to view module images" ON modules
    FOR SELECT
    USING (auth.role() = 'authenticated');

-- Allow teachers and admins to update module images
CREATE POLICY "Allow teachers and admins to update module images" ON modules
    FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM user_roles ur
            WHERE ur.user_id = auth.uid()
            AND ur.role IN ('teacher', 'admin')
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM user_roles ur
            WHERE ur.user_id = auth.uid()
            AND ur.role IN ('teacher', 'admin')
        )
    ); 