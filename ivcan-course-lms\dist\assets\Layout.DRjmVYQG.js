var V=Object.defineProperty;var j=Object.getOwnPropertySymbols;var A=Object.prototype.hasOwnProperty,D=Object.prototype.propertyIsEnumerable;var z=(e,r,s)=>r in e?V(e,r,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[r]=s,N=(e,r)=>{for(var s in r||(r={}))A.call(r,s)&&z(e,s,r[s]);if(j)for(var s of j(r))D.call(r,s)&&z(e,s,r[s]);return e};var k=(e,r)=>{var s={};for(var i in e)A.call(e,i)&&r.indexOf(i)<0&&(s[i]=e[i]);if(e!=null&&j)for(var i of j(e))r.indexOf(i)<0&&D.call(e,i)&&(s[i]=e[i]);return s};var g=(e,r,s)=>new Promise((i,l)=>{var m=h=>{try{c(s.next(h))}catch(d){l(d)}},a=h=>{try{c(s.throw(h))}catch(d){l(d)}},c=h=>h.done?i(h.value):Promise.resolve(h.value).then(m,a);c((s=s.apply(e,r)).next())});import{r as n,j as t,cg as I,ch as P,ci as X,_,a3 as B,a8 as H,X as J,cj as Q,a9 as W,a7 as Z,a6 as ee,ck as te,a5 as ae,af as se,ah as re,cl as ie,at as oe}from"./vendor-react.BcAa1DKr.js";import{u as T,g as ne,z as le,A as ce,s as de,c as o,a as R,C as ue,b as he,M as me,O as fe}from"./index.BLDhDn0D.js";import{a2 as xe,a5 as be,a4 as pe}from"./vendor.DQpuTRuB.js";const w=new Map,ge=30*60*1e3;function we(){const{user:e}=T(),{toast:r}=ne(),[s,i]=n.useState(()=>{var b;return e!=null&&e.id&&w.has(e.id)&&((b=w.get(e.id))==null?void 0:b.role)||null}),[l,m]=n.useState(!s),a=n.useRef(null),c=n.useRef(!0),h=n.useCallback((b=!1)=>g(this,null,function*(){if(!e){i(null),m(!1);return}if(e.id==="e6a49acc-3c5f-450b-9310-03d4c20406d2"){console.log("Known teacher user, setting role directly");const f="teacher";w.set(e.id,{role:f,timestamp:Date.now()}),i(f),m(!1);return}const p=w.get(e.id),y=Date.now();if(p&&y-p.timestamp<ge){i(p.role),m(!1);return}try{m(!0);const f=yield le(e.id);if(!c.current)return;w.set(e.id,{role:f,timestamp:y}),b&&f!==s&&r({title:"Role updated",description:`Your role is now: ${f||"Not set"}`}),i(f)}catch(f){console.error("Error fetching user role:",f),i(null)}finally{c.current&&m(!1)}}),[e,r,s]);n.useEffect(()=>(c.current=!0,e?w.has(e.id)||h():(i(null),m(!1)),()=>{c.current=!1,a.current!==null&&clearTimeout(a.current)}),[h,e]);const d=n.useCallback(()=>g(this,null,function*(){if(!e)return!1;if(e.id==="e6a49acc-3c5f-450b-9310-03d4c20406d2")return console.log("Known teacher user, bypassing server verification"),!0;try{return yield ce(e.id)}catch(b){return console.error("Error verifying teacher role:",b),!1}}),[e]),v=n.useCallback(()=>g(this,null,function*(){e&&(w.delete(e.id),yield h(!0))}),[h,e]);return n.useMemo(()=>({role:s,isTeacher:s==="teacher",isStudent:s==="student",loading:l,refreshRole:v,verifyTeacher:d}),[s,l,v,d])}function ye(e){return g(this,null,function*(){const{data:r,error:s}=yield de.from("profiles").select("*").eq("id",e).single();if(s)throw s;return r})}const F=n.forwardRef((i,s)=>{var l=i,{className:e}=l,r=k(l,["className"]);return t.jsx(I,N({ref:s,className:o("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e)},r))});F.displayName=I.displayName;const O=n.forwardRef((i,s)=>{var l=i,{className:e}=l,r=k(l,["className"]);return t.jsx(P,N({ref:s,className:o("aspect-square h-full w-full",e)},r))});O.displayName=P.displayName;const U=n.forwardRef((i,s)=>{var l=i,{className:e}=l,r=k(l,["className"]);return t.jsx(X,N({ref:s,className:o("flex h-full w-full items-center justify-center rounded-full bg-muted",e)},r))});U.displayName=X.displayName;function ve(e){const r=[{path:"/dashboard",label:"Programmes",icon:t.jsx(ae,{className:"h-5 w-5"})},{path:"/my-courses",label:"Modules",icon:t.jsx(se,{className:"h-5 w-5"})},{path:"/achievements",label:"Certificates",icon:t.jsx(re,{className:"h-5 w-5"})}];return e&&r.push({path:"/admin",label:"Admin",icon:t.jsx(ie,{className:"h-5 w-5"})}),r}const je=({open:e,setOpen:r})=>{var C,L;const s=_(),{user:i,signOut:l}=T(),{isTeacher:m}=we(),a=R(768),[c,h]=n.useState(null),{theme:d,setTheme:v}=ue(),[S,b]=n.useState(null),[p,y]=n.useState(null);n.useEffect(()=>{a&&r(!1)},[s.pathname,a,r]),n.useEffect(()=>(a&&e?(document.body.style.overflow="hidden",document.body.style.position="fixed",document.body.style.width="100%"):(document.body.style.overflow="",document.body.style.position="",document.body.style.width=""),()=>{document.body.style.overflow="",document.body.style.position="",document.body.style.width=""}),[a,e]),n.useEffect(()=>{a&&localStorage.setItem("sidebar-open",e?"true":"false")},[e,a]),n.useEffect(()=>{g(void 0,null,function*(){if(i)try{const x=yield ye(i.id);h(x)}catch(x){console.error("Error loading profile:",x)}})},[i]);const f=u=>{b(u.touches[0].clientX)},$=u=>{y(u.touches[0].clientX)},q=()=>{if(!S||!p)return;S-p>50&&a&&r(!1),b(null),y(null)};n.useEffect(()=>{if(!a)return;const u=x=>{if(e&&x.target instanceof Element){const M=document.querySelector("aside");M&&!M.contains(x.target)&&r(!1)}};return document.addEventListener("mousedown",u),()=>{document.removeEventListener("mousedown",u)}},[a,e,r]);const E=c!=null&&c.first_name?`${c.first_name} ${c.last_name||""}`:((C=i==null?void 0:i.user_metadata)==null?void 0:C.name)||((L=i==null?void 0:i.email)==null?void 0:L.split("@")[0])||"User",G=E.substring(0,2).toUpperCase(),Y=()=>g(void 0,null,function*(){try{yield l()}catch(u){console.error("Error signing out:",u),xe.error("Failed to sign out")}}),K=()=>{v(d==="dark"?"light":"dark")};return console.log("Sidebar open state:",e),t.jsxs(t.Fragment,{children:[a&&e&&t.jsx("div",{className:"fixed inset-0 bg-black/30 dark:bg-black/40 backdrop-blur-md z-40 md:hidden touch-none modern-sidebar-backdrop",onClick:()=>r(!1),onTouchStart:u=>u.preventDefault(),"aria-hidden":"true",style:{touchAction:"none"}}),t.jsxs("aside",{className:o("h-full flex flex-col z-50 modern-sidebar floating-sidebar","bg-[#e63946]/95 backdrop-blur-xl text-white shadow-2xl border border-white/10",a?["fixed inset-y-0 left-0 w-64","transition-all duration-300 ease-out transform","m-2 h-[calc(100vh-1rem)] rounded-2xl",e?"translate-x-0":"-translate-x-full"]:"relative w-full translate-x-0 m-3 h-[calc(100vh-1.5rem)] rounded-2xl"),style:{willChange:"transform",backfaceVisibility:"hidden"},onTouchStart:f,onTouchMove:$,onTouchEnd:q,"data-state":e?"open":"closed",children:[t.jsxs("div",{className:o("flex items-center justify-between border-b border-white/10 bg-white/5 backdrop-blur-sm header-section",a?"px-5 h-16":"px-6 h-20"),children:[t.jsxs(B,{to:"/dashboard",className:"flex items-center gap-3 hover:opacity-80 transition-opacity group logo-container","aria-label":"Go to dashboard - e4mi logo",children:[t.jsx("div",{className:o("bg-white/10 rounded-xl group-hover:bg-white/15 transition-colors logo-icon",a?"p-1.5":"p-2"),children:t.jsx(H,{className:o(a?"h-5 w-5":"h-6 w-6"),"aria-hidden":"true"})}),t.jsx("span",{className:o("font-bold tracking-tight",a?"text-lg":"text-xl"),children:"e4mi"})]}),a&&t.jsx("button",{className:"p-2 rounded-xl hover:bg-white/10 active:bg-white/15 transition-all duration-200 active:scale-95",onClick:()=>r(!1),"aria-label":"Close navigation menu",type:"button",children:t.jsx(J,{className:"h-4 w-4","aria-hidden":"true"})})]}),t.jsx("div",{className:o("border-b border-white/10 bg-white/5 profile-section",a?"px-5 py-4":"px-6 py-5"),children:t.jsxs("div",{className:o("flex items-center",a?"gap-3":"gap-4"),children:[t.jsx(F,{className:o("border-2 border-white/20 shadow-lg profile-avatar",a?"h-10 w-10":"h-12 w-12"),children:c!=null&&c.avatar_url?t.jsx(O,{src:c.avatar_url,alt:E}):t.jsx(U,{className:o("bg-white/15 text-white font-semibold",a?"text-sm":"text-base"),children:G})}),t.jsxs("div",{className:"flex-1 min-w-0",children:[t.jsx("div",{className:o("font-semibold truncate leading-tight",a?"text-sm":"text-base"),children:E}),t.jsx("div",{className:o("truncate text-white/80 mt-0.5",a?"text-xs":"text-sm"),children:i==null?void 0:i.email})]})]})}),t.jsxs("nav",{className:o("flex-1 overflow-y-auto",a?"px-3 py-5":"px-4 py-6"),role:"navigation","aria-label":"Sidebar navigation",children:[t.jsx("div",{className:o("mb-4 px-2 font-semibold uppercase tracking-wider text-white/70 section-header","text-xs"),children:"Navigation"}),t.jsx("ul",{className:o("role-list",a?"space-y-1":"space-y-2"),children:ve(m).map(u=>t.jsx("li",{role:"listitem",children:t.jsx(Q,{to:u.path,className:({isActive:x})=>o("flex items-center rounded-xl font-medium transition-all text-white focus:outline-none focus:ring-2 focus:ring-white/50 group relative overflow-hidden nav-item",a?"gap-2.5 px-3 py-2.5":"gap-3 px-4 py-3",x?"bg-white/20 shadow-lg backdrop-blur-sm active":"hover:bg-white/10 hover:translate-x-1"),onClick:()=>a&&r(!1),children:({isActive:x})=>t.jsxs(t.Fragment,{children:[t.jsx("span",{"aria-hidden":"true",className:"relative z-10 nav-icon",children:u.icon}),t.jsx("span",{className:o("relative z-10",a?"text-sm":"text-base"),children:u.label}),!x&&t.jsx(W,{className:o("ml-auto opacity-0 group-hover:opacity-50 transition-opacity relative z-10",a?"h-3.5 w-3.5":"h-4 w-4")})]})})},u.path))})]}),t.jsx("div",{className:"mt-auto border-t border-white/10 bg-white/5 footer-section",children:t.jsxs("div",{className:o("space-y-3",a?"px-5 py-4":"px-6 py-5"),children:[t.jsxs("button",{onClick:K,className:o("flex items-center justify-between w-full text-white hover:bg-white/10 rounded-xl transition-all focus:outline-none focus:ring-2 focus:ring-white/50 group theme-toggle",a?"px-3 py-2.5":"px-4 py-3"),"aria-label":`Switch to ${d==="dark"?"light":"dark"} mode`,type:"button",children:[t.jsxs("div",{className:o("flex items-center",a?"gap-2.5":"gap-3"),children:[d==="dark"?t.jsx(Z,{className:o(a?"h-4 w-4":"h-5 w-5"),"aria-hidden":"true"}):t.jsx(ee,{className:o(a?"h-4 w-4":"h-5 w-5"),"aria-hidden":"true"}),t.jsx("span",{className:o("font-medium",a?"text-sm":"text-base"),children:d==="dark"?"Dark Mode":"Light Mode"})]}),t.jsx("div",{className:o("rounded-full bg-white/20 relative flex items-center shadow-inner theme-toggle-switch",a?"h-5 w-9":"h-6 w-11"),role:"switch","aria-checked":d==="dark",children:t.jsx("div",{className:o("rounded-full bg-white absolute transition-all duration-300 shadow-sm theme-toggle-thumb",a?"h-4 w-4":"h-5 w-5",d==="dark"?"right-0.5":"left-0.5")})})]}),t.jsxs("button",{onClick:Y,className:o("flex items-center w-full text-white hover:bg-white/10 rounded-xl transition-all focus:outline-none focus:ring-2 focus:ring-white/50 group sign-out-btn",a?"gap-2.5 px-3 py-2.5":"gap-3 px-4 py-3"),"aria-label":"Sign out of your account",type:"button",children:[t.jsx(te,{className:o(a?"h-4 w-4":"h-5 w-5"),"aria-hidden":"true"}),t.jsx("span",{className:o("font-medium",a?"text-sm":"text-base"),children:"Sign Out"})]})]})})]})]})};function Ne({children:e,className:r}){const s=R(),i={initial:{opacity:0,y:s?10:20},animate:{opacity:1,y:0},exit:{opacity:0,y:s?-10:-20}},l={type:"tween",ease:"easeInOut",duration:s?.2:.3};return t.jsx(be,{mode:"wait",children:t.jsx(pe.div,{initial:"initial",animate:"animate",exit:"exit",variants:i,transition:l,className:r,children:e})})}const Re=({children:e})=>{const r=_();T();const[s,i]=n.useState(!1),[l,m]=n.useState(!1),a=R(768);he();const c=r.pathname!=="/dashboard";n.useEffect(()=>{const d=()=>{i(window.scrollY>10)};return window.addEventListener("scroll",d),()=>window.removeEventListener("scroll",d)},[]),n.useEffect(()=>{m(!a)},[a]);const h=()=>{console.log("Layout: Toggling sidebar, current state:",l),m(d=>!d)};return t.jsxs("div",{className:o("flex min-h-screen bg-background",a&&"overflow-x-hidden"),children:[t.jsx("div",{className:o("fixed inset-y-0 left-0 z-[60]",a?["w-0",l&&"w-72 pointer-events-auto"]:"w-72"),children:t.jsx(je,{open:l,setOpen:m})}),t.jsxs("div",{className:o("flex-1 transition-all duration-300 flex flex-col min-h-screen",a?"ml-0 w-full":"ml-72"),children:[a&&t.jsx("header",{className:o("sticky top-0 z-[55] w-full","transition-all duration-300",s?"bg-background/80 backdrop-blur-md shadow-sm border-b border-border/40":""),children:t.jsxs("div",{className:"flex h-16 items-center px-4",style:{touchAction:"manipulation"},children:[t.jsx("div",{className:"flex items-center gap-2 w-auto mr-2",children:c&&t.jsx("button",{onClick:()=>window.history.back(),className:"rounded-full hover:bg-primary/5 text-muted-foreground hover:text-primary transition-all duration-200 p-1.5 z-[56]","aria-label":"Go back",children:t.jsx(oe,{className:"h-4 w-4"})})}),t.jsx("div",{className:"flex justify-center flex-1 z-[56]",children:t.jsx(me,{sidebarOpen:l,toggleSidebar:h})}),t.jsx("div",{className:"flex items-center justify-end ml-auto space-x-2 w-auto z-[56]",children:t.jsx(fe,{})})]})}),t.jsx("main",{className:o("flex-1 min-h-0",a?"px-4 py-4":"px-4 py-4 pl-4"),children:t.jsx(Ne,{children:e})})]})]})};export{Re as L,we as u};
