/* Obsidian-inspired Markdown Editor and Preview Styles */

.obsidian-editor {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: var(--foreground);
  background-color: var(--background);
}

/* Editor Container */
.obsidian-editor .ProseMirror {
  outline: none;
  padding: 2rem;
  min-height: 500px;
  max-width: none;
  background-color: transparent;
}

/* Typography */
.obsidian-editor .ProseMirror h1,
.obsidian-editor .ProseMirror h2,
.obsidian-editor .ProseMirror h3,
.obsidian-editor .ProseMirror h4,
.obsidian-editor .ProseMirror h5,
.obsidian-editor .ProseMirror h6 {
  font-weight: 600;
  line-height: 1.3;
  margin: 1.5em 0 0.5em 0;
  color: var(--foreground);
}

.obsidian-editor .ProseMirror h1 {
  font-size: 2.25em;
  border-bottom: 2px solid var(--border);
  padding-bottom: 0.3em;
}

.obsidian-editor .ProseMirror h2 {
  font-size: 1.75em;
  border-bottom: 1px solid var(--border);
  padding-bottom: 0.2em;
}

.obsidian-editor .ProseMirror h3 {
  font-size: 1.5em;
}

.obsidian-editor .ProseMirror h4 {
  font-size: 1.25em;
}

.obsidian-editor .ProseMirror h5 {
  font-size: 1.1em;
}

.obsidian-editor .ProseMirror h6 {
  font-size: 1em;
  color: var(--muted-foreground);
}

/* Paragraphs */
.obsidian-editor .ProseMirror p {
  margin: 1em 0;
  line-height: 1.7;
}

/* Lists */
.obsidian-editor .ProseMirror ul,
.obsidian-editor .ProseMirror ol {
  margin: 1em 0;
  padding-left: 2em;
}

.obsidian-editor .ProseMirror li {
  margin: 0.5em 0;
  line-height: 1.6;
}

.obsidian-editor .ProseMirror ul {
  list-style-type: disc;
}

.obsidian-editor .ProseMirror ol {
  list-style-type: decimal;
}

/* Task Lists */
.obsidian-editor .ProseMirror ul[data-type="taskList"] {
  list-style: none;
  padding-left: 0;
}

.obsidian-editor .ProseMirror li[data-type="taskItem"] {
  display: flex;
  align-items: flex-start;
  margin: 0.5em 0;
}

.obsidian-editor .ProseMirror li[data-type="taskItem"] > label {
  margin-right: 0.75em;
  margin-top: 0.1em;
  user-select: none;
  cursor: pointer;
}

.obsidian-editor .ProseMirror li[data-type="taskItem"] > label input[type="checkbox"] {
  width: 1.1em;
  height: 1.1em;
  border-radius: 3px;
  border: 2px solid var(--border);
  background-color: var(--background);
  cursor: pointer;
}

.obsidian-editor .ProseMirror li[data-type="taskItem"] > label input[type="checkbox"]:checked {
  background-color: hsl(var(--primary));
  border-color: hsl(var(--primary));
}

.obsidian-editor .ProseMirror li[data-type="taskItem"] > div {
  flex: 1;
  min-width: 0;
}

/* Code */
.obsidian-editor .ProseMirror code {
  background-color: var(--muted);
  border-radius: 4px;
  font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.9em;
  padding: 0.2em 0.4em;
  color: var(--foreground);
}

.obsidian-editor .ProseMirror pre {
  background-color: var(--muted);
  border-radius: 8px;
  font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.9em;
  line-height: 1.5;
  margin: 1.5em 0;
  overflow-x: auto;
  padding: 1.5em;
  border: 1px solid var(--border);
}

.obsidian-editor .ProseMirror pre code {
  background: none;
  border-radius: 0;
  color: inherit;
  font-size: inherit;
  padding: 0;
}

/* Blockquotes */
.obsidian-editor .ProseMirror blockquote {
  border-left: 4px solid hsl(var(--primary));
  color: var(--muted-foreground);
  font-style: italic;
  margin: 1.5em 0;
  padding: 0.5em 0 0.5em 1.5em;
  background-color: var(--muted);
  border-radius: 0 8px 8px 0;
}

.obsidian-editor .ProseMirror blockquote p {
  margin: 0.5em 0;
}

/* Links */
.obsidian-editor .ProseMirror a {
  color: hsl(var(--primary));
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.2s ease;
}

.obsidian-editor .ProseMirror a:hover {
  border-bottom-color: hsl(var(--primary));
}

/* Images */
.obsidian-editor .ProseMirror img {
  border-radius: 8px;
  height: auto;
  max-width: 100%;
  margin: 1em 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.obsidian-editor .ProseMirror img:hover {
  transform: scale(1.02);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  cursor: zoom-in;
}

/* Horizontal Rule */
.obsidian-editor .ProseMirror hr {
  background-color: var(--border);
  border: 0;
  height: 2px;
  margin: 3em 0;
  border-radius: 1px;
}

/* Highlight */
.obsidian-editor .ProseMirror mark {
  background-color: hsl(var(--warning) / 0.3);
  border-radius: 3px;
  padding: 0.1em 0.2em;
  color: var(--foreground);
}

/* Tables */
.obsidian-editor .ProseMirror table {
  border-collapse: collapse;
  margin: 1.5em 0;
  overflow: hidden;
  table-layout: fixed;
  width: 100%;
  border-radius: 8px;
  border: 1px solid var(--border);
}

.obsidian-editor .ProseMirror table td,
.obsidian-editor .ProseMirror table th {
  border: 1px solid var(--border);
  box-sizing: border-box;
  min-width: 1em;
  padding: 12px 16px;
  position: relative;
  vertical-align: top;
}

.obsidian-editor .ProseMirror table th {
  background-color: var(--muted);
  font-weight: 600;
  text-align: left;
}

.obsidian-editor .ProseMirror table tr:nth-child(even) {
  background-color: var(--muted);
}

/* Details/Summary (Collapsible) */
.obsidian-editor .ProseMirror details {
  margin: 1.5em 0;
  border: 1px solid var(--border);
  border-radius: 8px;
  overflow: hidden;
}

.obsidian-editor .ProseMirror summary {
  background-color: var(--muted);
  padding: 1em 1.5em;
  cursor: pointer;
  font-weight: 600;
  user-select: none;
  transition: background-color 0.2s ease;
}

.obsidian-editor .ProseMirror summary:hover {
  background-color: var(--accent);
}

.obsidian-editor .ProseMirror details[open] summary {
  border-bottom: 1px solid var(--border);
}

.obsidian-editor .ProseMirror details > div {
  padding: 1.5em;
}

/* Callouts */
.obsidian-editor .ProseMirror [data-callout] {
  margin: 1.5em 0;
  border-radius: 8px;
  padding: 1.5em;
  border-left: 4px solid;
}

.obsidian-editor .ProseMirror [data-callout][data-type="info"] {
  background-color: hsl(var(--primary) / 0.1);
  border-left-color: hsl(var(--primary));
}

.obsidian-editor .ProseMirror [data-callout][data-type="warning"] {
  background-color: hsl(var(--warning) / 0.1);
  border-left-color: hsl(var(--warning));
}

.obsidian-editor .ProseMirror [data-callout][data-type="success"] {
  background-color: hsl(142 76% 36% / 0.1);
  border-left-color: hsl(142 76% 36%);
}

.obsidian-editor .ProseMirror [data-callout][data-type="error"] {
  background-color: hsl(var(--destructive) / 0.1);
  border-left-color: hsl(var(--destructive));
}

.obsidian-editor .ProseMirror [data-callout][data-type="tip"] {
  background-color: hsl(271 91% 65% / 0.1);
  border-left-color: hsl(271 91% 65%);
}

/* Placeholder */
.obsidian-editor .ProseMirror p.is-editor-empty:first-child::before {
  color: var(--muted-foreground);
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
  font-style: italic;
}

/* Focus styles */
.obsidian-editor .ProseMirror:focus {
  outline: none;
}

/* Selection */
.obsidian-editor .ProseMirror ::selection {
  background-color: hsl(var(--primary) / 0.2);
}

/* Dark mode specific adjustments */
.dark .obsidian-editor .ProseMirror img {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.dark .obsidian-editor .ProseMirror img:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
}

/* Enhanced Preview Styles */
.obsidian-preview {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: var(--foreground);
  max-width: none;
}

/* Task Lists in Preview */
.obsidian-preview .task-item {
  display: flex;
  align-items: flex-start;
  margin: 0.5em 0;
  gap: 0.75em;
}

.obsidian-preview .task-item input[type="checkbox"] {
  width: 1.1em;
  height: 1.1em;
  margin-top: 0.1em;
  border-radius: 3px;
  border: 2px solid var(--border);
  background-color: var(--background);
}

.obsidian-preview .task-item input[type="checkbox"]:checked {
  background-color: hsl(var(--primary));
  border-color: hsl(var(--primary));
}

/* GFM Tables */
.obsidian-preview .gfm-table {
  border-collapse: collapse;
  margin: 1.5em 0;
  width: 100%;
  border-radius: 8px;
  border: 1px solid var(--border);
  overflow: hidden;
}

.obsidian-preview .gfm-table th,
.obsidian-preview .gfm-table td {
  border: 1px solid var(--border);
  padding: 12px 16px;
  text-align: left;
}

.obsidian-preview .gfm-table th {
  background-color: var(--muted);
  font-weight: 600;
}

.obsidian-preview .gfm-table tr:nth-child(even) {
  background-color: var(--muted);
}

/* Collapsible Sections */
.obsidian-preview .collapsible-section {
  margin: 1.5em 0;
  border: 1px solid var(--border);
  border-radius: 8px;
  overflow: hidden;
}

.obsidian-preview .collapsible-summary {
  background-color: var(--muted);
  padding: 1em 1.5em;
  cursor: pointer;
  font-weight: 600;
  user-select: none;
  transition: background-color 0.2s ease;
  list-style: none;
}

.obsidian-preview .collapsible-summary:hover {
  background-color: var(--accent);
}

.obsidian-preview .collapsible-summary::-webkit-details-marker {
  display: none;
}

.obsidian-preview .collapsible-content {
  padding: 1.5em;
  border-top: 1px solid var(--border);
}

/* Callouts */
.obsidian-preview .callout {
  margin: 1.5em 0;
  border-radius: 8px;
  padding: 1.5em;
  border-left: 4px solid;
}

.obsidian-preview .callout-title {
  font-weight: 600;
  margin-bottom: 0.5em;
  text-transform: uppercase;
  font-size: 0.9em;
  letter-spacing: 0.05em;
}

.obsidian-preview .callout-info {
  background-color: hsl(var(--primary) / 0.1);
  border-left-color: hsl(var(--primary));
}

.obsidian-preview .callout-warning {
  background-color: hsl(var(--warning) / 0.1);
  border-left-color: hsl(var(--warning));
}

.obsidian-preview .callout-success {
  background-color: hsl(142 76% 36% / 0.1);
  border-left-color: hsl(142 76% 36%);
}

.obsidian-preview .callout-error {
  background-color: hsl(var(--destructive) / 0.1);
  border-left-color: hsl(var(--destructive));
}

.obsidian-preview .callout-tip,
.obsidian-preview .callout-note {
  background-color: hsl(271 91% 65% / 0.1);
  border-left-color: hsl(271 91% 65%);
}

/* Highlight */
.obsidian-preview .highlight {
  background-color: hsl(var(--warning) / 0.3);
  border-radius: 3px;
  padding: 0.1em 0.2em;
  color: var(--foreground);
}

/* Code Copy Button */
.code-copy-button {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: var(--muted);
  border: 1px solid var(--border);
  border-radius: 4px;
  padding: 0.5rem;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s ease;
  color: var(--muted-foreground);
}

.code-copy-button:hover {
  background: var(--accent);
  color: var(--foreground);
}

pre:hover .code-copy-button {
  opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .obsidian-editor .ProseMirror {
    padding: 1rem;
    font-size: 15px;
  }

  .obsidian-editor .ProseMirror h1 {
    font-size: 1.8em;
  }

  .obsidian-editor .ProseMirror h2 {
    font-size: 1.5em;
  }

  .obsidian-editor .ProseMirror h3 {
    font-size: 1.3em;
  }

  .obsidian-preview {
    font-size: 15px;
  }

  .obsidian-preview .gfm-table {
    font-size: 14px;
  }

  .obsidian-preview .gfm-table th,
  .obsidian-preview .gfm-table td {
    padding: 8px 12px;
  }
}
