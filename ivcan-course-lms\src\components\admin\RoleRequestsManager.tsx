import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import {
  CheckCircle,
  XCircle,
  Clock,
  User,
  Calendar,
  Search,
  Loader2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { formatDistanceToNow } from 'date-fns';

interface RoleRequest {
  id: string;
  user_id: string;
  requested_role: string;
  status: 'pending' | 'approved' | 'rejected';
  requested_at: string;
  processed_at?: string;
  processed_by?: string;
  notes?: string;
  user?: {
    email?: string;
    user_metadata?: {
      full_name?: string;
    };
  };
}

const RoleRequestsManager: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRequest, setSelectedRequest] = useState<RoleRequest | null>(null);
  const [isApproveDialogOpen, setIsApproveDialogOpen] = useState(false);
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false);
  const [notes, setNotes] = useState('');
  const queryClient = useQueryClient();

  // Fetch role requests
  const { data: roleRequests, isLoading, error } = useQuery({
    queryKey: ['role-requests'],
    queryFn: async () => {
      try {
        // Fetch role requests with user information
        const { data, error } = await supabase
          .from('role_requests')
          .select('*, user:user_id(email, user_metadata)')
          .order('requested_at', { ascending: false });

        if (error) throw error;
        return data as RoleRequest[];
      } catch (error: any) {
        console.error('Error fetching role requests:', error);
        toast.error('Failed to load role requests');
        throw error;
      }
    }
  });

  // Approve role request mutation
  const approveMutation = useMutation({
    mutationFn: async ({ requestId, notes }: { requestId: string; notes: string }) => {
      const { data, error } = await supabase.rpc('approve_role_request', {
        _request_id: requestId,
        _notes: notes
      });

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      toast.success('Role request approved successfully');
      queryClient.invalidateQueries({ queryKey: ['role-requests'] });
      setIsApproveDialogOpen(false);
      setSelectedRequest(null);
      setNotes('');
    },
    onError: (error: any) => {
      console.error('Error approving role request:', error);
      toast.error(error.message || 'Failed to approve role request');
    }
  });

  // Reject role request mutation
  const rejectMutation = useMutation({
    mutationFn: async ({ requestId, notes }: { requestId: string; notes: string }) => {
      const { data, error } = await supabase.rpc('reject_role_request', {
        _request_id: requestId,
        _notes: notes
      });

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      toast.success('Role request rejected');
      queryClient.invalidateQueries({ queryKey: ['role-requests'] });
      setIsRejectDialogOpen(false);
      setSelectedRequest(null);
      setNotes('');
    },
    onError: (error: any) => {
      console.error('Error rejecting role request:', error);
      toast.error(error.message || 'Failed to reject role request');
    }
  });

  // Handle approve button click
  const handleApprove = (request: RoleRequest) => {
    setSelectedRequest(request);
    setNotes('');
    setIsApproveDialogOpen(true);
  };

  // Handle reject button click
  const handleReject = (request: RoleRequest) => {
    setSelectedRequest(request);
    setNotes('');
    setIsRejectDialogOpen(true);
  };

  // Submit approve request
  const submitApprove = () => {
    if (!selectedRequest) return;
    approveMutation.mutate({ requestId: selectedRequest.id, notes });

    // Send a notification to the user
    try {
      supabase
        .from('notifications')
        .insert([{
          user_id: selectedRequest.user_id,
          type: 'role_approved',
          title: 'Teacher Role Approved',
          message: 'Your request for teacher privileges has been approved. You now have access to create and manage courses.',
          data: {
            request_id: selectedRequest.id,
            notes: notes || undefined
          },
          read: false,
          created_at: new Date().toISOString()
        }])
        .then(({ error }) => {
          if (error) {
            console.error('Error sending approval notification:', error);
          }
        });
    } catch (error) {
      console.error('Error in approval notification:', error);
    }
  };

  // Submit reject request
  const submitReject = () => {
    if (!selectedRequest) return;
    rejectMutation.mutate({ requestId: selectedRequest.id, notes });

    // Send a notification to the user
    try {
      supabase
        .from('notifications')
        .insert([{
          user_id: selectedRequest.user_id,
          type: 'role_rejected',
          title: 'Teacher Role Request Declined',
          message: 'Your request for teacher privileges has been declined.',
          data: {
            request_id: selectedRequest.id,
            notes: notes || undefined
          },
          read: false,
          created_at: new Date().toISOString()
        }])
        .then(({ error }) => {
          if (error) {
            console.error('Error sending rejection notification:', error);
          }
        });
    } catch (error) {
      console.error('Error in rejection notification:', error);
    }
  };

  // Filter requests based on search query
  const filteredRequests = roleRequests?.filter(request => {
    const userEmail = request.user?.email || '';
    const userName = request.user?.user_metadata?.full_name || '';
    const searchLower = searchQuery.toLowerCase();

    return (
      userEmail.toLowerCase().includes(searchLower) ||
      userName.toLowerCase().includes(searchLower) ||
      request.requested_role.toLowerCase().includes(searchLower) ||
      request.status.toLowerCase().includes(searchLower)
    );
  });

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-300">Pending</Badge>;
      case 'approved':
        return <Badge variant="outline" className="bg-red-100 text-red-800 border-red-300">Approved</Badge>;
      case 'rejected':
        return <Badge variant="outline" className="bg-red-100 text-red-800 border-red-300">Rejected</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Role Requests</h2>
        <div className="flex gap-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search requests..."
              className="pl-8 w-64"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => queryClient.invalidateQueries({ queryKey: ['role-requests'] })}
          >
            Refresh
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center p-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : error ? (
        <Card className="border-red-300 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center text-red-800">
              <XCircle className="h-5 w-5 mr-2" />
              <p>Failed to load role requests. Please try again.</p>
            </div>
          </CardContent>
        </Card>
      ) : filteredRequests?.length === 0 ? (
        <Card>
          <CardContent className="pt-6 text-center text-muted-foreground">
            {searchQuery ? 'No requests match your search' : 'No role requests found'}
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {filteredRequests?.map((request) => (
            <Card key={request.id} className="overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">
                      {request.user?.user_metadata?.full_name || 'Unknown User'}
                    </CardTitle>
                    <CardDescription>
                      {request.user?.email || 'No email available'}
                    </CardDescription>
                  </div>
                  {getStatusBadge(request.status)}
                </div>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center text-sm">
                    <User className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span>Requested role: </span>
                    <span className="font-medium ml-1 capitalize">{request.requested_role}</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                    <span>Requested: </span>
                    <span className="font-medium ml-1">
                      {formatDistanceToNow(new Date(request.requested_at), { addSuffix: true })}
                    </span>
                  </div>
                </div>
                {request.notes && (
                  <div className="mt-2 p-2 bg-muted rounded text-sm">
                    <p className="font-medium">Notes:</p>
                    <p>{request.notes}</p>
                  </div>
                )}
              </CardContent>
              <CardFooter className="pt-2">
                {request.status === 'pending' ? (
                  <div className="flex gap-2 w-full">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 border-red-500 text-red-600 hover:bg-red-50"
                      onClick={() => handleApprove(request)}
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Approve
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 border-red-500 text-red-600 hover:bg-red-50"
                      onClick={() => handleReject(request)}
                    >
                      <XCircle className="h-4 w-4 mr-2" />
                      Reject
                    </Button>
                  </div>
                ) : (
                  <div className="text-sm text-muted-foreground w-full text-right">
                    {request.processed_at && (
                      <span>
                        {request.status === 'approved' ? 'Approved' : 'Rejected'} {formatDistanceToNow(new Date(request.processed_at), { addSuffix: true })}
                      </span>
                    )}
                  </div>
                )}
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      {/* Approve Dialog */}
      <Dialog open={isApproveDialogOpen} onOpenChange={setIsApproveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Approve Role Request</DialogTitle>
            <DialogDescription>
              Are you sure you want to approve this request? This will grant teacher privileges to the user.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <p className="text-sm font-medium">User:</p>
              <p className="text-sm">{selectedRequest?.user?.user_metadata?.full_name} ({selectedRequest?.user?.email})</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium">Requested Role:</p>
              <p className="text-sm capitalize">{selectedRequest?.requested_role}</p>
            </div>
            <div className="space-y-2">
              <label htmlFor="notes" className="text-sm font-medium">
                Notes (optional):
              </label>
              <Textarea
                id="notes"
                placeholder="Add any notes about this approval"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsApproveDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={submitApprove}
              disabled={approveMutation.isPending}
              className="bg-red-600 hover:bg-red-700"
            >
              {approveMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Approving...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Approve
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reject Dialog */}
      <Dialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Role Request</DialogTitle>
            <DialogDescription>
              Are you sure you want to reject this request? The user will remain with their current role.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <p className="text-sm font-medium">User:</p>
              <p className="text-sm">{selectedRequest?.user?.user_metadata?.full_name} ({selectedRequest?.user?.email})</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium">Requested Role:</p>
              <p className="text-sm capitalize">{selectedRequest?.requested_role}</p>
            </div>
            <div className="space-y-2">
              <label htmlFor="reject-notes" className="text-sm font-medium">
                Reason for rejection (optional):
              </label>
              <Textarea
                id="reject-notes"
                placeholder="Provide a reason for rejecting this request"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRejectDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={submitReject}
              disabled={rejectMutation.isPending}
              variant="destructive"
            >
              {rejectMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Rejecting...
                </>
              ) : (
                <>
                  <XCircle className="h-4 w-4 mr-2" />
                  Reject
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default RoleRequestsManager;
