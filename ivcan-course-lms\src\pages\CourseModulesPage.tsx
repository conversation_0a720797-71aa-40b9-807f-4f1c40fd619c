import React from 'react';
import { useParams } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import Layout from '@/components/Layout';
import ModuleCard from '@/components/module/ModuleCard';
import SimpleFinishCourseButton from '@/components/course/SimpleFinishCourseButton';
import { Loader2 } from 'lucide-react';
import { SectionLoadingSpinner } from '@/components/ui/loading-spinner';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/context/AuthContext';
import { PageContainer, ContentSection } from '@/components/ui/floating-sidebar-container';

interface Module {
  id: string;
  title: string;
  slug: string;
  module_number: number;
  is_locked: boolean;
  is_completed?: boolean;
  course_id: string;
  description?: string;
  image_url?: string;
}

interface Course {
  id: string;
  title: string;
  description: string;
  instructor: string;
}

interface ModuleProgress {
  module_id: string;
  is_completed: boolean;
  completed_lessons: number;
  total_lessons: number;
  progress_percent: number;
}

const CourseModulesPage: React.FC = () => {
  const { courseId } = useParams<{ courseId: string }>();
  const { toast } = useToast();
  const { user } = useAuth();

  // Fetch course data
  const { data: course, isLoading: isLoadingCourse } = useQuery<Course>({
    queryKey: ['course', courseId],
    queryFn: async () => {
      if (!courseId) throw new Error('Course ID is required');

      const { data, error } = await supabase
        .from('courses')
        .select('*')
        .eq('id', courseId)
        .single();

      if (error) {
        toast({
          title: 'Error',
          description: 'Failed to load course details',
          variant: 'destructive',
        });
        throw error;
      }

      return data;
    },
    enabled: !!courseId
  });

  // Fetch modules
  const { data: modules, isLoading: isLoadingModules } = useQuery<Module[]>({
    queryKey: ['modules', courseId],
    queryFn: async () => {
      if (!courseId) throw new Error('Course ID is required');

      const { data, error } = await supabase
        .from('modules')
        .select('*')
        .eq('course_id', courseId)
        .order('module_number', { ascending: true });

      if (error) {
        toast({
          title: 'Error',
          description: 'Failed to load modules',
          variant: 'destructive',
        });
        throw error;
      }

      return data;
    },
    enabled: !!courseId
  });

  // Fetch module progress for the current user
  const { data: moduleProgress, isLoading: isLoadingProgress } = useQuery<Record<string, ModuleProgress>>({
    queryKey: ['moduleProgress', courseId, user?.id],
    queryFn: async () => {
      if (!courseId || !user?.id) return {};

      // First, fetch all module progress records for this user and course
      const { data: progressData, error: progressError } = await supabase
        .from('user_module_progress')
        .select('module_id, is_completed')
        .eq('user_id', user.id);

      if (progressError) {
        console.error('Error fetching module progress:', progressError);
        return {};
      }

      // Next, get lesson counts for each module
      const moduleProgresses: Record<string, ModuleProgress> = {};

      // Process all modules to get lesson counts and progress
      await Promise.all((modules || []).map(async (module) => {
        try {
          // Get total lessons for the module
          const { data: lessonData, error: lessonError } = await supabase
            .from('lessons')
            .select('id')
            .eq('module_id', module.id);

          if (lessonError) {
            console.error(`Error fetching lessons for module ${module.id}:`, lessonError);
            return;
          }

          const totalLessons = lessonData?.length || 0;

          // Get completed lessons count
          const { data: completedData, error: completedError } = await supabase
            .from('user_lesson_progress')
            .select('lesson_id')
            .eq('user_id', user.id)
            .eq('is_completed', true)
            .in('lesson_id', lessonData?.map(l => l.id) || []);

          if (completedError) {
            console.error(`Error fetching completed lessons for module ${module.id}:`, completedError);
            return;
          }

          const completedLessons = completedData?.length || 0;
          const progress = totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0;
          const isCompleted = progressData?.find(p => p.module_id === module.id)?.is_completed || false;

          moduleProgresses[module.id] = {
            module_id: module.id,
            is_completed: isCompleted,
            completed_lessons: completedLessons,
            total_lessons: totalLessons,
            progress_percent: progress
          };
        } catch (error) {
          console.error(`Error processing module ${module.id}:`, error);
        }
      }));

      return moduleProgresses;
    },
    enabled: !!courseId && !!user?.id && !!modules
  });

  const isLoading = isLoadingCourse || isLoadingModules || isLoadingProgress;

  if (isLoading) {
    return (
      <Layout>
        <PageContainer pageType="module">
          <SectionLoadingSpinner text="Loading course modules..." />
        </PageContainer>
      </Layout>
    );
  }

  return (
    <Layout>
      <PageContainer pageType="module">
        <ContentSection spacing="md">
          <div>
            <h1 className="text-3xl font-bold mb-2">{course?.title}</h1>
            <p className="text-muted-foreground">Course Modules</p>
          </div>

          <div className="grid gap-3"> {/* Reduced gap from gap-6 to gap-3 */}
          {modules && modules.length > 0 ? (
            modules.map((module) => {
              const progress = moduleProgress?.[module.id] || {
                is_completed: false,
                completed_lessons: 0,
                total_lessons: 0,
                progress_percent: 0
              };

              return (
                <ModuleCard
                  key={module.id}
                  id={module.id}
                  courseId={courseId || ''}
                  title={module.title}
                  description={module.description || ''}
                  module_number={module.module_number || 1}
                  status={progress.is_completed ? 'completed' : progress.progress_percent > 0 ? 'in_progress' : 'not_started'}
                  image={module.image_url}
                  totalLessons={progress.total_lessons}
                  completedLessons={progress.completed_lessons}
                  progress={progress.progress_percent}
                />
              );
            })
          ) : (
            <div className="text-center p-8 bg-muted/20 rounded-lg border">
              <p className="text-lg text-muted-foreground">No modules found for this course</p>
            </div>
          )}
          </div>

          {/* Finish Course Button - Moved to bottom */}
          {user && modules && course && (
            <div className="mt-6">
              <SimpleFinishCourseButton
                courseId={course.id}
                userId={user.id}
                modules={modules}
                courseName={course.title}
              />
            </div>
          )}
        </ContentSection>
      </PageContainer>
    </Layout>
  );
};

export default CourseModulesPage;