const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/userRoleService.Crm_K_HM.js","assets/vendor.DQpuTRuB.js","assets/vendor-react.BcAa1DKr.js","assets/vendor-supabase.sufZ44-y.js","assets/Login.D9BFbxWH.js","assets/ResetPassword.Cs9USIZW.js","assets/Dashboard.Cc5az03q.js","assets/Layout.DRjmVYQG.js","assets/css/Layout.CFLJ3FLk.css","assets/responsive-image.BMcUeCRG.js","assets/skeleton.C0Lb8Xms.js","assets/enrollmentApi.CstnsQi_.js","assets/loading-skeleton.DxUGnZ26.js","assets/floating-sidebar-container.BxlLgzat.js","assets/alert.EBLwCiZR.js","assets/ModuleContent.gv_-rfab.js","assets/progress.BMuwHUJX.js","assets/markdown-preview.Bw0U-NJA.js","assets/content-converter.L-GziWIP.js","assets/CourseCompletionCelebration.BJZAxw59.js","assets/confetti.ShHySCrk.js","assets/completionService.BUb78ZaE.js","assets/achievementService.Vkx_BHml.js","assets/courseApi.BQX5-7u-.js","assets/utils.Qa9QlCj_.js","assets/css/ModuleContent.DoOZiXEi.css","assets/ModuleManagementPage.DXOiTvi6.js","assets/breadcrumb.DyMjeizE.js","assets/label.D4YlnUPk.js","assets/card.B9V6b2DK.js","assets/alert-dialog.BK1A3Gb_.js","assets/ModulesPage.C6n5RnQl.js","assets/moduleImageUtils.AJjzI8xs.js","assets/moduleTestService.BmAHru_J.js","assets/ModuleTestPage.BPl7sTnK.js","assets/module-test.BaVeK2uM.js","assets/badge.C87ZuIxl.js","assets/LessonContent.CfhFGPRt.js","assets/LessonContent.B2xCUk6H.js","assets/css/LessonContent.BwOXDaPG.css","assets/css/professional-lesson-content.bN5oOkou.css","assets/NotFound.D7YCCh6G.js","assets/Admin.CC-kr3nO.js","assets/tabs.B0SF6qIv.js","assets/simple-markdown-editor.DQ2eiB7n.js","assets/tiptap-image-upload.B_U9gNrF.js","assets/TestAnalyticsDashboard.B08xtLlk.js","assets/CertificatePage.CeFjybHI.js","assets/EditorDemo.BB8Qx_Xq.js","assets/MarkdownEditorDemo.BPMRzgsL.js","assets/enhanced-markdown-preview.Du81wch8.js","assets/MarkdownDebug.Dlt2-8yI.js","assets/TableTest.-_qIgXdX.js","assets/MarkdownEditorTest.aGsVYXMm.js","assets/unified-markdown-editor.BzxuP8op.js","assets/TableRenderingTest.CUlbKRDO.js","assets/MarkdownTableTest.BWvqDVg7.js","assets/TableParsingVerification.Cs7ZuQbt.js","assets/AchievementsPage.DYWWrx_E.js","assets/TestLessonUI.BR2dGiiK.js","assets/SpacingTestPage.B3CFvju1.js","assets/DebugLesson.BBeHXqq7.js","assets/ImageDebugTest.DOOC9h5o.js","assets/LessonRedirect.LOlzeWdP.js","assets/YouTubeEditorTest.DkqlUuny.js","assets/AuthCallback.CF4zthXy.js","assets/CompletionDiagnostics.Dd73kFC9.js","assets/TestAnalyticsPage.9U_HeWPy.js"])))=>i.map(i=>d[i]);
var It=Object.defineProperty,Tt=Object.defineProperties;var At=Object.getOwnPropertyDescriptors;var se=Object.getOwnPropertySymbols;var Se=Object.prototype.hasOwnProperty,Ce=Object.prototype.propertyIsEnumerable;var he=(t,s,r)=>s in t?It(t,s,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[s]=r,v=(t,s)=>{for(var r in s||(s={}))Se.call(s,r)&&he(t,r,s[r]);if(se)for(var r of se(s))Ce.call(s,r)&&he(t,r,s[r]);return t},L=(t,s)=>Tt(t,At(s));var R=(t,s)=>{var r={};for(var a in t)Se.call(t,a)&&s.indexOf(a)<0&&(r[a]=t[a]);if(t!=null&&se)for(var a of se(t))s.indexOf(a)<0&&Ce.call(t,a)&&(r[a]=t[a]);return r};var re=(t,s,r)=>he(t,typeof s!="symbol"?s+"":s,r);var p=(t,s,r)=>new Promise((a,o)=>{var i=l=>{try{u(r.next(l))}catch(c){o(c)}},d=l=>{try{u(r.throw(l))}catch(c){o(c)}},u=l=>l.done?a(l.value):Promise.resolve(l.value).then(i,d);u((r=r.apply(t,s)).next())});import{j as e,r as n,C as Ue,P as Lt,c as Rt,T as Ot,u as Pt,R as J,O as Ve,d as ze,e as Dt,X as Fe,f as qe,D as Be,h as Mt,i as Ut,k as Vt,S as zt,l as $e,I as Ft,m as Ke,n as Ye,o as qt,p as Qe,q as Bt,t as We,V as $t,L as Ge,v as He,w as Kt,x as Yt,y as Qt,z as Je,A as Wt,B as Gt,E as Ht,F as Xe,G as Jt,H as Xt,J as Zt,K as Ze,M as es,N as ts,Q as et,U as ss,W as rs,Y as as,Z as Ie,_ as tt,$ as ne,a0 as ye,a1 as os,a2 as ns,a3 as q,a4 as is,a5 as ls,a6 as cs,a7 as ds,a8 as Te,a9 as Ae,aa as us,ab as ms,ac as hs,ad as ps,ae as fs,af as gs,ag as xs,ah as ys,ai as bs,aj as vs,ak as ws,al as js,am as Ns,an as j,ao as Es}from"./vendor-react.BcAa1DKr.js";import{Z as _s,$ as ks,a0 as Ss,a1 as Cs,W as b,a2 as g,a3 as Is,a4 as m,a5 as Ts,a6 as As,a7 as Ls,a8 as Rs,a9 as Os}from"./vendor.DQpuTRuB.js";import{c as Ps,P as Ds}from"./vendor-supabase.sufZ44-y.js";(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))a(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const d of i.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&a(d)}).observe(document,{childList:!0,subtree:!0});function r(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function a(o){if(o.ep)return;o.ep=!0;const i=r(o);fetch(o.href,i)}})();const Ms=s=>{var t=R(s,[]);const{theme:r="system"}=_s();return e.jsx(ks,v({theme:r,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}}},t))};function _(...t){return Ss(Cs(t))}const Us=Lt,Da=Rt,Ma=Ot,Vs=n.forwardRef((o,a)=>{var i=o,{className:t,sideOffset:s=4}=i,r=R(i,["className","sideOffset"]);return e.jsx(Ue,v({ref:a,sideOffset:s,className:_("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t)},r))});Vs.displayName=Ue.displayName;const K={serviceUnavailable:{lastErrorTime:0,notificationShown:!1,consecutiveFailures:0}},zs=3e4,Fs=1e3;function ue(t){return t?t.status===503||t.code==="503"||t.statusCode===503||t.status_code===503||typeof t.status=="number"&&t.status===503||t.message&&(t.message.includes("service unavailable")||t.message.includes("503")||t.message.includes("failed after")||t.message.toLowerCase().includes("service unavailable")):!1}function qs(){const t=Date.now();K.serviceUnavailable.consecutiveFailures++,K.serviceUnavailable={lastErrorTime:t,notificationShown:!0,consecutiveFailures:K.serviceUnavailable.consecutiveFailures}}function Bs(){if(K.serviceUnavailable.consecutiveFailures>0&&(K.serviceUnavailable.consecutiveFailures=0,K.serviceUnavailable.notificationShown)){K.serviceUnavailable.notificationShown=!1;try{b(()=>p(this,null,function*(){const{supabase:t}=yield Promise.resolve().then(()=>Js);return{supabase:t}}),void 0).then(({supabase:t})=>{t.auth.refreshSession()})}catch(t){console.error("Failed to refresh session:",t)}}}function $s(t,s=Fs){const r=s*Math.pow(2,t),a=.8+Math.random()*.4;return Math.min(zs,r*a)}function F(t,s=5,r=1e3){return p(this,null,function*(){let a=null,o=0;for(;o<s;)try{const i=yield t();return Bs(),i}catch(i){a=i;const d=v({},i);if(d.body&&typeof d.body=="string"&&d.body.length>500&&(d.body=d.body.substring(0,500)+"... [truncated]"),ue(i)){o++;const u=$s(o,r);yield new Promise(l=>setTimeout(l,u));continue}if(i.message&&(i.message.includes("network")||i.message.includes("timeout")||i.message.includes("connection")||i.message.includes("Failed to fetch"))){o++;const u=r*o;yield new Promise(l=>setTimeout(l,u));continue}break}throw a&&o>=s&&ue(a)&&qs(),a||new Error("Operation failed after multiple retries")})}const Ks={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1,VITE_SUPABASE_ANON_KEY:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMxMjUzNzIsImV4cCI6MjA1ODcwMTM3Mn0.H1cvGLXWvIMibv8dYsYIvY37IRoVR80HyeF1wM93IUI",VITE_SUPABASE_SERVICE_ROLE_KEY:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzEyNTM3MiwiZXhwIjoyMDU4NzAxMzcyfQ.I6Vxg6B0FvKPOeKMhAAu75kOf6ct5NBNs-azmAwqNWI",VITE_SUPABASE_URL:"https://jibspqwieubavucdtccv.supabase.co"},Ys=!0,pe=t=>{const s=Ks[t];if(!s&&Ys)throw console.error(`${t} is required in production but not set!`),new Error(`${t} is required in production but not set!`);return s||""},B={URL:pe("VITE_SUPABASE_URL"),ANON_KEY:pe("VITE_SUPABASE_ANON_KEY"),SERVICE_ROLE_KEY:pe("VITE_SUPABASE_SERVICE_ROLE_KEY"),PROJECT_ID:"jibspqwieubavucdtccv",SCHEMA:"public",CONNECTION:{TIMEOUT_MS:1e4,RETRY_COUNT:3,CIRCUIT_BREAKER_THRESHOLD:5,CIRCUIT_BREAKER_RESET_TIMEOUT_MS:3e4},STORAGE:{COURSE_IMAGES:"course-images",AVATARS:"avatars",APP_UPLOADS:"app-uploads"}},Qs=B.URL&&B.ANON_KEY;Qs?(console.log("Supabase configuration loaded successfully."),console.log("Environment mode:","production"),console.log("Supabase URL:",B.URL?"Set":"Not set"),console.log("Supabase Anon Key:",B.ANON_KEY?"Set":"Not set"),console.log("Supabase Service Role Key:",B.SERVICE_ROLE_KEY?"Set":"Not set")):console.warn("Supabase configuration incomplete. Check your environment variables.");const st=B.URL,rt=B.ANON_KEY,Ua=B.SERVICE_ROLE_KEY;let X=0;const Ws=20,Gs=6e4;let $=!1,Z=null;$&&(console.log("Manually resetting circuit breaker"),$=!1,X=0,Z&&(clearTimeout(Z),Z=null));const Hs=(t,s)=>p(void 0,null,function*(){const u=t.toString().includes("/auth/"),l=u?1e4:8e3;if($&&!u)return console.warn("Circuit breaker active, non-auth request rejected"),Promise.reject(new Error("Service temporarily unavailable"));let c=0,E=null;for(;c<3;)try{const w=new AbortController,P=setTimeout(()=>w.abort(),l),k=yield fetch(t,L(v({},s),{signal:w.signal}));if(clearTimeout(P),ue(k))throw X++,X>=Ws&&!$&&(console.warn("Circuit breaker tripped due to consecutive failures"),$=!0,Z=setTimeout(()=>{console.log("Circuit breaker reset timer expired"),$=!1,X=0,Z=null},Gs)),new Error("Service unavailable");return X=0,k}catch(w){if(E=w,$)break;if(w instanceof Error&&w.name==="AbortError"){console.warn("Request timed out");break}if(c++,c<3){const P=500*Math.pow(2,c-1);yield new Promise(k=>setTimeout(k,P))}}throw E||new Error("Request failed")}),y=Ps(st,rt,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0,storageKey:"ivcan-lms-auth",flowType:"pkce"},global:{headers:{"x-application-name":"ivcan-lms","Cache-Control":"no-cache"},fetch:Hs},realtime:{params:{eventsPerSecond:1},heartbeatIntervalMs:15e3,reconnectAfterMs:t=>Math.min(1e4,1e3*Math.pow(2,t))},db:{schema:"public"}}),Js=Object.freeze(Object.defineProperty({__proto__:null,supabase:y},Symbol.toStringTag,{value:"Module"}));function Xs(t,s){return p(this,null,function*(){if(!t||!s)return!1;if(t==="e6a49acc-3c5f-450b-9310-03d4c20406d2"&&s==="teacher")return console.log("Known teacher user recognized, bypassing role check"),!0;try{const{data:r,error:a}=yield F(()=>p(this,null,function*(){return yield y.rpc("has_role",{_user_id:t,_role:s})}));return a?(console.error("Error checking role:",a),!1):!!r}catch(r){return console.error("Error in hasRole:",r),!1}})}function Va(t){return p(this,null,function*(){if(!t)return null;if(t==="e6a49acc-3c5f-450b-9310-03d4c20406d2")return console.log("Known teacher user recognized, bypassing database check"),"teacher";try{const{data:s,error:r}=yield F(()=>p(this,null,function*(){return yield y.from("user_roles").select("role").eq("user_id",t).maybeSingle()}));return r?(console.error("Error getting user role:",r),null):(s==null?void 0:s.role)||null}catch(s){return console.error("Error in getUserRole:",s),null}})}function je(t){return p(this,null,function*(){if(!t)return!1;if(t==="e6a49acc-3c5f-450b-9310-03d4c20406d2")return console.log("Known teacher user recognized, bypassing service check"),!0;try{return yield Xs(t,"teacher")}catch(s){return console.error("Error verifying teacher role:",s),!1}})}function Q(r){return p(this,arguments,function*(t,s={}){try{{const{error:a}=yield y.from("analytics_events").insert({event_name:t,event_data:s,created_at:new Date().toISOString()});a&&console.error("Error logging analytics event:",a)}}catch(a){}})}function at(t,s){return s===`auto-complete-${t}-${new Date().getDate()}`}function Zs(t,s,r=!1){return p(this,null,function*(){if(console.log("=== AUTO COMPLETION SERVICE: MARKING ALL COURSES AS COMPLETED ==="),console.log("User ID:",t),console.log("Manual trigger:",r),!t)return console.error("Invalid user ID"),!1;if(s&&!at(t,s))return console.error("Invalid security token - auto-completion aborted"),Q("security_violation",{action:"auto_completion_attempt",userId:t,reason:"invalid_token"}),!1;if(!(yield je(t)))return console.log("Auto-completion blocked: User is not a teacher"),Q("security_violation",{action:"auto_completion_attempt",userId:t,reason:"not_teacher"}),!1;Q("auto_completion",{userId:t,action:"mark_all_courses",isManualTrigger:r});try{try{console.log("Using RPC function to complete all courses...");const{data:u,error:l}=yield y.rpc("complete_all_courses",{p_user_id:t});if(!l)return console.log("Successfully marked all courses as completed using RPC function"),r&&g.success("Successfully marked all courses as completed",{description:"All courses have been marked as completed for the user.",duration:5e3}),!0;console.error("RPC function failed:",l)}catch(u){console.error("Error calling RPC function:",u)}console.log("Falling back to manual course completion...");const{data:o,error:i}=yield y.from("courses").select("id, title").limit(10);if(i)return console.error("Error fetching courses:",i),!1;if(!o||o.length===0)return console.log("No courses found to complete"),!0;console.log(`Found ${o.length} courses to mark as completed`);let d=0;for(const u of o)try{const{error:l}=yield y.rpc("complete_course",{p_user_id:t,p_course_id:u.id});l?console.error(`Error marking course ${u.title} as completed:`,l):(d++,console.log(`Successfully marked course ${u.title} as completed`))}catch(l){console.error(`Error processing course ${u.title}:`,l)}return console.log(`Successfully marked ${d} out of ${o.length} courses as completed`),r&&g.success("Successfully marked all courses as completed",{description:"All courses have been marked as completed for the user.",duration:5e3}),d>0}catch(o){return console.error("Error in markAllCoursesAsCompleted:",o),!1}})}function er(t,s,r=!1){return p(this,null,function*(){if(console.log("=== AUTO COMPLETION SERVICE: MARKING ALL MODULES AND LESSONS AS COMPLETED ==="),console.log("User ID:",t),console.log("Manual trigger:",r),!t)return console.error("Invalid user ID"),!1;if(s&&!at(t,s))return console.error("Invalid security token - auto-completion aborted"),Q("security_violation",{action:"auto_completion_attempt",userId:t,reason:"invalid_token"}),!1;if(!(yield je(t)))return console.log("Auto-completion blocked: User is not a teacher"),Q("security_violation",{action:"auto_completion_attempt",userId:t,reason:"not_teacher"}),!1;Q("auto_completion",{userId:t,action:"mark_all_modules_and_lessons",isManualTrigger:r});try{const{data:o,error:i}=yield y.from("courses").select("id").limit(5);return i||!o?(console.error("Error fetching courses:",i),!1):(yield Promise.all(o.map(d=>p(this,null,function*(){try{const{data:u,error:l}=yield y.from("modules").select("id").eq("course_id",d.id).limit(10);if(l||!u||u.length===0){console.log(`No modules found for course ${d.id} or error:`,l);return}const c=new Date().toISOString();yield Promise.all(u.map(E=>p(this,null,function*(){try{yield y.from("user_module_progress").upsert({user_id:t,module_id:E.id,is_completed:!0,updated_at:c},{onConflict:"user_id,module_id",ignoreDuplicates:!1});const{data:w,error:P}=yield y.from("lessons").select("id").eq("module_id",E.id).limit(20);if(P||!w||w.length===0){console.log(`No lessons found for module ${E.id} or error:`,P);return}const k=w.map(S=>({user_id:t,lesson_id:S.id,is_completed:!0,updated_at:c})),x=10;for(let S=0;S<k.length;S+=x){const f=k.slice(S,S+x);yield y.from("user_lesson_progress").upsert(f,{onConflict:"user_id,lesson_id",ignoreDuplicates:!1})}}catch(w){console.error(`Error processing module ${E.id}:`,w)}})))}catch(u){console.error(`Error processing course ${d.id}:`,u)}}))),console.log("Successfully marked all modules and lessons as completed"),r&&g.success("Successfully marked all modules and lessons as completed",{description:"All modules and lessons have been marked as completed for the user.",duration:5e3}),!0)}catch(o){return console.error("Error in markAllModulesAndLessonsAsCompleted:",o),!1}})}const ot=n.createContext({user:null,loading:!0}),te=()=>n.useContext(ot),Y=t=>t?t.status===503||t.code==="503"||t.message&&(t.message.includes("service unavailable")||t.message.includes("failed after")||t.message.includes("network")||t.message.includes("Failed to fetch")):!1,tr=({children:t})=>{const[s,r]=n.useState(null),[a,o]=n.useState(!0),i=Pt(),d=n.useCallback(x=>p(void 0,null,function*(){try{if(!(yield je(x)))return console.log("Auto-completion disabled for non-teacher user:",x),!1;const{data:f,error:h}=yield y.from("user_preferences").select("auto_complete_courses").eq("user_id",x).maybeSingle();if(h)return console.error("Error checking teacher preferences:",h),!1;const D=(f==null?void 0:f.auto_complete_courses)===!0;return console.log(`Auto-completion for user ${x} is ${D?"enabled":"disabled"}`),D}catch(S){return console.error("Error checking auto-completion setting:",S),!1}}),[]),u=n.useCallback(x=>`auto-complete-${x}-${new Date().getDate()}`,[]),l=n.useCallback(x=>p(void 0,null,function*(){if(x)try{if(!(yield d(x))){console.log("Auto-completion is disabled for user:",x);return}const f=u(x);(yield Zs(x,f,!1))?(console.log("Successfully auto-completed all courses"),(yield er(x,f,!1))?console.log("Successfully completed all modules and lessons"):console.error("Failed to complete modules and lessons")):console.error("Failed to auto-complete courses")}catch(S){console.error("Error in auto-completion:",S)}}),[d,u]),c=n.useCallback(()=>{try{const x=sessionStorage.getItem("cached_user_session");if(x){const S=JSON.parse(x);if(S.user)return console.log("Recovering from cached session due to service unavailability"),S.user}}catch(x){console.error("Failed to parse cached session:",x)}return null},[]);n.useEffect(()=>{p(void 0,null,function*(){try{o(!0);try{const f=c();if(f){console.log("Using cached session for faster initial load"),r(f),o(!1),setTimeout(()=>p(void 0,null,function*(){try{const{data:{session:I},error:W}=yield y.auth.getSession();if(I!=null&&I.user){r(I.user);try{sessionStorage.setItem("cached_user_session",JSON.stringify(I))}catch(T){console.error("Failed to cache session:",T)}I.user.id&&setTimeout(()=>{l(I.user.id).catch(T=>{console.error("Error in handleAutoCompletion:",T)})},5e3)}}catch(I){console.error("Background session refresh error:",I)}}),1e3);return}const{data:{session:h},error:D}=yield F(()=>y.auth.getSession());if(D&&Y(D)){const I=c();if(I){r(I),o(!1);return}}if(h!=null&&h.user){r(h.user);try{sessionStorage.setItem("cached_user_session",JSON.stringify(h))}catch(I){console.error("Failed to cache session:",I)}h.user.id&&("requestIdleCallback"in window?window.requestIdleCallback(()=>{l(h.user.id).catch(I=>{console.error("Error in handleAutoCompletion:",I)})},{timeout:5e3}):setTimeout(()=>{l(h.user.id).catch(I=>{console.error("Error in handleAutoCompletion:",I)})},3e3))}else r(null)}catch(f){if(console.error("Error getting session:",f),Y(f)){const h=c();h?r(h):g.error("Unable to connect to the server. Please check your internet connection.",{duration:5e3})}else g.error("Unable to connect to the server. Please check your internet connection.",{duration:5e3})}}finally{o(!1)}});const S=y.auth.onAuthStateChange((f,h)=>{f==="SIGNED_IN"?h!=null&&h.user&&(r(h.user),h.user.id&&setTimeout(()=>{l(h.user.id).catch(D=>{console.error("Error in handleAutoCompletion:",D)})},2e3)):f==="SIGNED_OUT"&&r(null),o(!1)});return()=>{S.data.subscription.unsubscribe()}},[l,c]);const E=(x,S)=>p(void 0,null,function*(){o(!0);try{const f=yield F(()=>y.auth.signInWithPassword({email:x,password:S}));return f.error?(console.error("Sign-in error:",f.error),f.error.message.includes("Invalid login credentials")?g.error("Invalid email or password"):Y(f.error)?g.error("Network error. Supabase service is currently unavailable.",{description:"We are using cached data where possible."}):g.error(f.error.message||"Failed to sign in")):f.data.user&&l(f.data.user.id),f}catch(f){throw console.error("Sign-in error:",f),Y(f)?g.error("Network error. Supabase service is currently unavailable."):g.error(f.message||"Failed to sign in"),f}finally{o(!1)}}),w=(h,D,...I)=>p(void 0,[h,D,...I],function*(x,S,f={}){var W;o(!0);try{f&&(f.requested_role="student");const T=yield F(()=>y.auth.signUp({email:x,password:S,options:{data:f,emailRedirectTo:`${window.location.origin}/auth/callback`}}));if(T.error)console.error("Sign-up error:",T.error),T.error.message.includes("already registered")?g.error("This email is already registered"):Y(T.error)?g.error("Network error. Supabase service is currently unavailable."):g.error(T.error.message||"Failed to sign up");else if(T.data.user)if(((W=T.data.user.identities)==null?void 0:W.length)===0)g.info("A confirmation email has been sent to your email address. Please check your inbox and confirm your email to complete registration.");else{l(T.data.user.id),setTimeout(()=>{i("/dashboard")},1e3);try{const{assignStudentRole:G}=yield b(()=>p(void 0,null,function*(){const{assignStudentRole:me}=yield import("./userRoleService.Crm_K_HM.js");return{assignStudentRole:me}}),__vite__mapDeps([0,1,2,3]));yield G(T.data.user.id)}catch(G){console.error("Error assigning role:",G)}setTimeout(()=>{g.success("Welcome! Your account has been created successfully.")},1500)}return T}catch(T){throw console.error("Sign-up error:",T),Y(T)?g.error("Network error. Supabase service is currently unavailable."):g.error(T.message||"Failed to sign up"),T}finally{o(!1)}}),P=()=>p(void 0,null,function*(){try{console.log("Starting sign out process"),r(null);const x=g.loading("Signing out...");i("/login");try{yield y.auth.signOut(),console.log("Sign out API call completed"),g.success("You have been signed out",{id:x})}catch(S){console.error("Error during Supabase sign out:",S),g.error("Error during sign out, but you have been signed out locally",{id:x})}}catch(x){console.error("Error in sign out flow:",x),g.error("Failed to sign out: "+(x.message||"Unknown error")),r(null),i("/login")}}),k=n.useMemo(()=>({user:s,signIn:E,signUp:w,signOut:P,loading:a,setUser:r}),[s,a]);return e.jsx(ot.Provider,{value:k,children:t})},nt=n.createContext(void 0);function it(){const t=n.useContext(nt);if(!t)throw new Error("useTheme must be used within a ThemeProvider");return t}function sr({children:t,defaultTheme:s="system",storageKey:r="vite-ui-theme"}){const[a,o]=J.useState(()=>localStorage.getItem(r)||s);J.useEffect(()=>{const d=window.document.documentElement;if(d.classList.remove("light","dark"),a==="system"){const u=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";d.classList.add(u),d.dataset.actualTheme=u,u==="dark"?document.body.classList.add("dark-mode"):document.body.classList.remove("dark-mode");return}d.classList.add(a),d.dataset.actualTheme=a,a==="dark"?document.body.classList.add("dark-mode"):document.body.classList.remove("dark-mode")},[a]),J.useEffect(()=>{const d=window.matchMedia("(prefers-color-scheme: dark)"),u=()=>{if(a==="system"){const l=window.document.documentElement,c=d.matches?"dark":"light";l.classList.remove("light","dark"),l.classList.add(c),l.dataset.actualTheme=c,c==="dark"?document.body.classList.add("dark-mode"):document.body.classList.remove("dark-mode")}};return d.addEventListener("change",u),()=>d.removeEventListener("change",u)},[a]),J.useEffect(()=>{const d=document.documentElement,u=document.querySelector('meta[name="theme-color"]'),l=d.classList.contains("dark");u&&u.setAttribute("content",l?"#0C0C0C":"#FFFFFF"),d.style.setProperty("--theme-transition-duration","0.2s");const c=()=>{d.style.setProperty("--theme-transition-duration","0s")},E=()=>{d.style.setProperty("--theme-transition-duration","0.2s")};window.addEventListener("pageshow",c);const w=setTimeout(E,100);return()=>{window.removeEventListener("pageshow",c),clearTimeout(w)}},[a]);const i=J.useMemo(()=>({theme:a,setTheme:d=>{localStorage.setItem(r,d),o(d)}}),[a,r]);return e.jsx(nt.Provider,{value:i,children:t})}function rr(){const[t,s]=n.useState(!1);return n.useEffect(()=>{if(typeof window=="undefined")return;const r=window.matchMedia("(prefers-reduced-motion: reduce)");s(r.matches);const a=o=>{s(o.matches)};return r.addEventListener?(r.addEventListener("change",a),()=>r.removeEventListener("change",a)):(r.addListener(a),()=>r.removeListener(a))},[]),t}const lt={shouldReduceMotion:!1,enableAnimations:!0,setEnableAnimations:()=>{},transitions:{default:{type:"tween",duration:.2,ease:"easeOut"},spring:{type:"spring",stiffness:400,damping:30},microInteraction:{type:"tween",duration:.1}}},ct=n.createContext(lt),dt=()=>n.useContext(ct),ar=({children:t})=>{const s=rr(),[r,a]=n.useState(localStorage.getItem("enableAnimations")!=="false");n.useEffect(()=>{localStorage.setItem("enableAnimations",r.toString())},[r]);const o=s||!r,i=o?{default:{type:"tween",duration:.1,ease:"easeOut"},spring:{type:"tween",stiffness:0,damping:0},microInteraction:{type:"tween",duration:.05}}:lt.transitions;return e.jsx(ct.Provider,{value:{shouldReduceMotion:o,enableAnimations:r,setEnableAnimations:a,transitions:i},children:t})},ut=Mt,or=Vt,nr=Ut,mt=n.forwardRef((a,r)=>{var o=a,{className:t}=o,s=R(o,["className"]);return e.jsx(Ve,v({ref:r,className:_("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t)},s))});mt.displayName=Ve.displayName;const Ne=n.forwardRef((o,a)=>{var i=o,{className:t,children:s}=i,r=R(i,["className","children"]);return e.jsxs(nr,{children:[e.jsx(mt,{}),e.jsxs(ze,L(v({ref:a,className:_("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t)},r),{children:[s,e.jsxs(Dt,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[e.jsx(Fe,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Close"})]})]}))]})});Ne.displayName=ze.displayName;const ht=r=>{var a=r,{className:t}=a,s=R(a,["className"]);return e.jsx("div",v({className:_("flex flex-col space-y-1.5 text-center sm:text-left",t)},s))};ht.displayName="DialogHeader";const ir=r=>{var a=r,{className:t}=a,s=R(a,["className"]);return e.jsx("div",v({className:_("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t)},s))};ir.displayName="DialogFooter";const Ee=n.forwardRef((a,r)=>{var o=a,{className:t}=o,s=R(o,["className"]);return e.jsx(qe,v({ref:r,className:_("text-lg font-semibold leading-none tracking-tight",t)},s))});Ee.displayName=qe.displayName;const pt=n.forwardRef((a,r)=>{var o=a,{className:t}=o,s=R(o,["className"]);return e.jsx(Be,v({ref:r,className:_("text-sm text-muted-foreground",t)},s))});pt.displayName=Be.displayName;const lr=Is("inline-flex items-center justify-center whitespace-nowrap font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-[0.98] touch-manipulation",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90 hover:shadow-md",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90 hover:shadow-md",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:shadow-md",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:shadow-md",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline p-0 h-auto shadow-none"},size:{default:"h-10 px-4 py-2 text-sm rounded-lg",sm:"h-8 px-3 py-1.5 text-xs rounded-md",lg:"h-12 px-6 py-3 text-base rounded-lg",icon:"h-10 w-10 rounded-lg",mobile:"h-11 px-4 py-2.5 text-sm rounded-lg min-w-[44px]","mobile-lg":"h-12 px-6 py-3 text-base rounded-lg min-w-[44px]"}},defaultVariants:{variant:"default",size:"default"}}),O=n.forwardRef((d,i)=>{var u=d,{className:t,variant:s,size:r,asChild:a=!1}=u,o=R(u,["className","variant","size","asChild"]);const l=a?zt:"button";return e.jsx(l,v({className:_(lr({variant:s,size:r,className:t})),ref:i},o))});O.displayName="Button";const cr={theme:"system",setTheme:()=>null},dr=n.createContext(cr),ur=()=>{const t=n.useContext(dr);if(t===void 0)throw new Error("useTheme must be used within a ThemeProvider");return t};function mr({step:t,className:s="",width:r=300,height:a=200}){const{theme:o}=ur(),{shouldReduceMotion:i}=dt(),d=o==="dark",u={hidden:{opacity:0},show:{opacity:1,transition:{staggerChildren:i?.05:.1,delayChildren:.1}}},l={hidden:{opacity:0,y:i?5:10},show:{opacity:1,y:0,transition:{duration:i?.2:.4,ease:"easeOut"}}},c={primary:"#E63946",secondary:"#C1121F",accent:"#9D0208",background:d?"#1C1C1C":"#FFFFFF",outline:d?"#333333":"#E2E8F0",text:d?"#CCCCCC":"#64748B",success:"#E63946"};return t==="welcome"?e.jsxs(m.svg,{width:r,height:a,viewBox:"0 0 300 200",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:s,variants:u,initial:"hidden",animate:"show",children:[e.jsx(m.rect,{x:"75",y:"40",width:"150",height:"120",rx:"10",fill:c.background,stroke:c.outline,strokeWidth:"2",variants:l}),e.jsx(m.circle,{cx:"150",cy:"80",r:"25",fill:c.primary,variants:l}),e.jsx(m.path,{d:"M140 80L148 88L160 70",stroke:"white",strokeWidth:"3",strokeLinecap:"round",strokeLinejoin:"round",variants:l}),e.jsx(m.rect,{x:"110",y:"120",width:"80",height:"8",rx:"4",fill:c.text,variants:l}),e.jsx(m.rect,{x:"125",y:"135",width:"50",height:"8",rx:"4",fill:c.accent,variants:l}),e.jsx(m.circle,{cx:"220",cy:"50",r:"15",fill:c.accent,opacity:"0.6",variants:l}),e.jsx(m.circle,{cx:"80",cy:"150",r:"10",fill:c.secondary,opacity:"0.6",variants:l})]}):t==="profile"?e.jsxs(m.svg,{width:r,height:a,viewBox:"0 0 300 200",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:s,variants:u,initial:"hidden",animate:"show",children:[e.jsx(m.circle,{cx:"150",cy:"70",r:"40",fill:c.background,stroke:c.outline,strokeWidth:"2",variants:l}),e.jsx(m.circle,{cx:"150",cy:"60",r:"15",fill:c.text,variants:l}),e.jsx(m.path,{d:"M130 90C130 79.5 139 75 150 75C161 75 170 79.5 170 90",stroke:c.text,strokeWidth:"2",variants:l}),e.jsx(m.rect,{x:"100",y:"120",width:"100",height:"10",rx:"5",fill:c.outline,variants:l}),e.jsx(m.rect,{x:"100",y:"140",width:"80",height:"10",rx:"5",fill:c.outline,variants:l}),e.jsx(m.rect,{x:"100",y:"160",width:"60",height:"10",rx:"5",fill:c.primary,variants:l}),e.jsx(m.circle,{cx:"220",cy:"50",r:"15",fill:c.accent,opacity:"0.6",variants:l}),e.jsx(m.circle,{cx:"80",cy:"150",r:"10",fill:c.secondary,opacity:"0.6",variants:l})]}):t==="courses"?e.jsxs(m.svg,{width:r,height:a,viewBox:"0 0 300 200",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:s,variants:u,initial:"hidden",animate:"show",children:[e.jsx(m.rect,{x:"60",y:"50",width:"80",height:"100",rx:"8",fill:c.background,stroke:c.outline,strokeWidth:"2",variants:l}),e.jsx(m.rect,{x:"70",y:"70",width:"60",height:"8",rx:"4",fill:c.text,variants:l}),e.jsx(m.rect,{x:"70",y:"85",width:"40",height:"6",rx:"3",fill:c.text,opacity:"0.6",variants:l}),e.jsx(m.rect,{x:"70",y:"120",width:"60",height:"6",rx:"3",fill:c.primary,variants:l}),e.jsx(m.rect,{x:"160",y:"50",width:"80",height:"100",rx:"8",fill:c.background,stroke:c.outline,strokeWidth:"2",variants:l}),e.jsx(m.rect,{x:"170",y:"70",width:"60",height:"8",rx:"4",fill:c.text,variants:l}),e.jsx(m.rect,{x:"170",y:"85",width:"40",height:"6",rx:"3",fill:c.text,opacity:"0.6",variants:l}),e.jsx(m.rect,{x:"170",y:"120",width:"60",height:"6",rx:"3",fill:c.accent,variants:l}),e.jsx(m.circle,{cx:"100",cy:"40",r:"10",fill:c.secondary,opacity:"0.6",variants:l}),e.jsx(m.circle,{cx:"200",cy:"160",r:"15",fill:c.primary,opacity:"0.4",variants:l})]}):t==="complete"?e.jsxs(m.svg,{width:r,height:a,viewBox:"0 0 300 200",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:s,variants:u,initial:"hidden",animate:"show",children:[e.jsx(m.circle,{cx:"150",cy:"100",r:"50",fill:c.background,stroke:c.outline,strokeWidth:"2",variants:l}),e.jsx(m.circle,{cx:"150",cy:"100",r:"40",fill:c.success,opacity:"0.2",variants:l}),e.jsx(m.path,{d:"M130 100L145 115L170 85",stroke:c.success,strokeWidth:"6",strokeLinecap:"round",strokeLinejoin:"round",variants:l}),e.jsx(m.circle,{cx:"220",cy:"60",r:"15",fill:c.accent,opacity:"0.6",variants:l}),e.jsx(m.circle,{cx:"80",cy:"140",r:"10",fill:c.secondary,opacity:"0.6",variants:l}),e.jsx(m.circle,{cx:"240",cy:"140",r:"8",fill:c.primary,opacity:"0.6",variants:l}),e.jsx(m.circle,{cx:"60",cy:"70",r:"12",fill:c.primary,opacity:"0.4",variants:l})]}):t==="clock"?e.jsxs(m.svg,{width:r,height:a,viewBox:"0 0 300 200",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:s,variants:u,initial:"hidden",animate:"show",children:[e.jsx(m.circle,{cx:"150",cy:"100",r:"60",fill:c.background,stroke:c.outline,strokeWidth:"2",variants:l}),e.jsx(m.circle,{cx:"150",cy:"100",r:"55",fill:c.background,stroke:c.primary,strokeWidth:"2",strokeDasharray:"4 4",variants:l}),e.jsx(m.line,{x1:"150",y1:"100",x2:"150",y2:"70",stroke:c.text,strokeWidth:"3",strokeLinecap:"round",variants:l}),e.jsx(m.line,{x1:"150",y1:"100",x2:"175",y2:"100",stroke:c.primary,strokeWidth:"3",strokeLinecap:"round",variants:l}),e.jsx(m.circle,{cx:"150",cy:"100",r:"5",fill:c.text,variants:l}),e.jsx(m.text,{x:"140",y:"50",fill:c.text,fontSize:"12",fontWeight:"bold",variants:l,children:"12"}),e.jsx(m.text,{x:"195",y:"105",fill:c.text,fontSize:"12",fontWeight:"bold",variants:l,children:"3"}),e.jsx(m.text,{x:"147",y:"155",fill:c.text,fontSize:"12",fontWeight:"bold",variants:l,children:"6"}),e.jsx(m.text,{x:"100",y:"105",fill:c.text,fontSize:"12",fontWeight:"bold",variants:l,children:"9"}),e.jsx(m.circle,{cx:"220",cy:"50",r:"15",fill:c.accent,opacity:"0.6",variants:l}),e.jsx(m.circle,{cx:"80",cy:"150",r:"10",fill:c.secondary,opacity:"0.6",variants:l})]}):t==="learning"?e.jsxs(m.svg,{width:r,height:a,viewBox:"0 0 300 200",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:s,variants:u,initial:"hidden",animate:"show",children:[e.jsx(m.rect,{x:"60",y:"60",width:"50",height:"70",rx:"5",fill:c.background,stroke:c.outline,strokeWidth:"2",variants:l}),e.jsx(m.rect,{x:"65",y:"70",width:"40",height:"5",rx:"2",fill:c.text,variants:l}),e.jsx(m.rect,{x:"65",y:"80",width:"40",height:"5",rx:"2",fill:c.text,variants:l}),e.jsx(m.rect,{x:"65",y:"90",width:"30",height:"5",rx:"2",fill:c.text,variants:l}),e.jsx(m.circle,{cx:"150",cy:"90",r:"30",fill:c.primary,opacity:"0.2",variants:l}),e.jsx(m.path,{d:"M140 90 L150 100 L160 90 L150 80 Z",fill:c.primary,variants:l}),e.jsx(m.rect,{x:"190",y:"60",width:"50",height:"40",rx:"5",fill:c.background,stroke:c.outline,strokeWidth:"2",variants:l}),e.jsx(m.rect,{x:"190",y:"105",width:"50",height:"5",rx:"2",fill:c.accent,variants:l}),e.jsx(m.rect,{x:"190",y:"115",width:"50",height:"5",rx:"2",fill:c.accent,variants:l}),e.jsx(m.rect,{x:"190",y:"125",width:"50",height:"5",rx:"2",fill:c.accent,variants:l}),e.jsx(m.circle,{cx:"215",y:"80",r:"15",fill:c.secondary,opacity:"0.3",variants:l}),e.jsx(m.path,{d:"M210 80 L225 80 M217.5 72.5 L217.5 87.5",stroke:c.secondary,strokeWidth:"2",variants:l}),e.jsx(m.circle,{cx:"220",cy:"40",r:"10",fill:c.accent,opacity:"0.6",variants:l}),e.jsx(m.circle,{cx:"80",cy:"150",r:"15",fill:c.primary,opacity:"0.4",variants:l})]}):null}const be=n.forwardRef((o,a)=>{var i=o,{className:t,type:s}=i,r=R(i,["className","type"]);return e.jsx("input",v({type:s,className:_("flex h-12 w-full rounded-xl border border-input/50 bg-background/80 dark:bg-card/80 backdrop-blur-sm px-4 py-3 text-base text-foreground ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:border-primary focus-visible:ring-1 focus-visible:ring-primary focus-visible:ring-offset-1 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm transition-all duration-200",t),ref:a},r))});be.displayName="Input";const hr=Wt,pr=Gt,ft=n.forwardRef((o,a)=>{var i=o,{className:t,children:s}=i,r=R(i,["className","children"]);return e.jsxs($e,L(v({ref:a,className:_("flex h-11 w-full items-center justify-between rounded-lg border border-input bg-background px-4 py-2.5 text-sm ring-offset-background","placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary/50","disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1","transition-all duration-200 hover:border-primary/30 hover:bg-primary/5","shadow-sm hover:shadow-md",t)},r),{children:[s,e.jsx(Ft,{asChild:!0,children:e.jsx(Ke,{className:"h-4 w-4 opacity-60 transition-transform duration-200 data-[state=open]:rotate-180"})})]}))});ft.displayName=$e.displayName;const gt=n.forwardRef((a,r)=>{var o=a,{className:t}=o,s=R(o,["className"]);return e.jsx(Ye,L(v({ref:r,className:_("flex cursor-default items-center justify-center py-1",t)},s),{children:e.jsx(qt,{className:"h-4 w-4"})}))});gt.displayName=Ye.displayName;const xt=n.forwardRef((a,r)=>{var o=a,{className:t}=o,s=R(o,["className"]);return e.jsx(Qe,L(v({ref:r,className:_("flex cursor-default items-center justify-center py-1",t)},s),{children:e.jsx(Ke,{className:"h-4 w-4"})}))});xt.displayName=Qe.displayName;const yt=n.forwardRef((i,o)=>{var d=i,{className:t,children:s,position:r="popper"}=d,a=R(d,["className","children","position"]);return e.jsx(Bt,{children:e.jsxs(We,L(v({ref:o,className:_("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-xl border bg-popover text-popover-foreground shadow-xl backdrop-blur-sm","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0","data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95","data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2","data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","border-border/20 bg-background/95",r==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:r},a),{children:[e.jsx(gt,{}),e.jsx($t,{className:_("p-2",r==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),e.jsx(xt,{})]}))})});yt.displayName=We.displayName;const fr=n.forwardRef((a,r)=>{var o=a,{className:t}=o,s=R(o,["className"]);return e.jsx(Ge,v({ref:r,className:_("py-1.5 pl-8 pr-2 text-sm font-semibold",t)},s))});fr.displayName=Ge.displayName;const bt=n.forwardRef((o,a)=>{var i=o,{className:t,children:s}=i,r=R(i,["className","children"]);return e.jsxs(He,L(v({ref:a,className:_("relative flex w-full cursor-default select-none items-center rounded-lg py-2.5 pl-10 pr-3 text-sm outline-none","focus:bg-primary/5 focus:text-primary data-[state=checked]:bg-primary/10 data-[state=checked]:text-primary","data-[disabled]:pointer-events-none data-[disabled]:opacity-50","transition-all duration-200 hover:bg-primary/5 hover:text-primary","font-medium text-foreground/80 hover:text-foreground",t)},r),{children:[e.jsx("span",{className:"absolute left-3 flex h-4 w-4 items-center justify-center",children:e.jsx(Kt,{children:e.jsx(Yt,{className:"h-3.5 w-3.5 text-primary"})})}),e.jsx(Qt,{children:s})]}))});bt.displayName=He.displayName;const gr=n.forwardRef((a,r)=>{var o=a,{className:t}=o,s=R(o,["className"]);return e.jsx(Je,v({ref:r,className:_("-mx-1 my-1 h-px bg-muted",t)},s))});gr.displayName=Je.displayName;const xr=1,yr=1e6;let fe=0;function br(){return fe=(fe+1)%Number.MAX_SAFE_INTEGER,fe.toString()}const ge=new Map,Le=t=>{if(ge.has(t))return;const s=setTimeout(()=>{ge.delete(t),ee({type:"REMOVE_TOAST",toastId:t})},yr);ge.set(t,s)},vr=(t,s)=>{switch(s.type){case"ADD_TOAST":return L(v({},t),{toasts:[s.toast,...t.toasts].slice(0,xr)});case"UPDATE_TOAST":return L(v({},t),{toasts:t.toasts.map(r=>r.id===s.toast.id?v(v({},r),s.toast):r)});case"DISMISS_TOAST":{const{toastId:r}=s;return r?Le(r):t.toasts.forEach(a=>{Le(a.id)}),L(v({},t),{toasts:t.toasts.map(a=>a.id===r||r===void 0?L(v({},a),{open:!1}):a)})}case"REMOVE_TOAST":return s.toastId===void 0?L(v({},t),{toasts:[]}):L(v({},t),{toasts:t.toasts.filter(r=>r.id!==s.toastId)})}},ie=[];let le={toasts:[]};function ee(t){le=vr(le,t),ie.forEach(s=>{s(le)})}function wr(s){var t=R(s,[]);const r=br(),a=i=>ee({type:"UPDATE_TOAST",toast:L(v({},i),{id:r})}),o=()=>ee({type:"DISMISS_TOAST",toastId:r});return ee({type:"ADD_TOAST",toast:L(v({},t),{id:r,open:!0,onOpenChange:i=>{i||o()}})}),{id:r,dismiss:o,update:a}}function jr(){const[t,s]=n.useState(le);return n.useEffect(()=>(ie.push(s),()=>{const r=ie.indexOf(s);r>-1&&ie.splice(r,1)}),[t]),L(v({},t),{toast:wr,dismiss:r=>ee({type:"DISMISS_TOAST",toastId:r})})}function vt(){return p(this,null,function*(){const{data:t,error:s}=yield y.from("demographic_questionnaires").select("*").eq("is_active",!0).single();return s?(console.error("Error fetching demographic questionnaire:",s),null):t})}function wt(t,s){return p(this,null,function*(){const{data:r,error:a}=yield y.from("user_demographic_responses").select("*").eq("user_id",t).eq("questionnaire_id",s).single();if(a){if(a.code==="PGRST116")return null;throw console.error("Error fetching user demographic response:",a),a}return r})}function Nr(t,s,r){return p(this,null,function*(){const{data:a,error:o}=yield y.from("user_demographic_responses").upsert({user_id:t,questionnaire_id:s,responses:r,updated_at:new Date().toISOString()}).select().single();if(o)throw console.error("Error saving demographic response:",o),o;return a})}function Er(t){return p(this,null,function*(){const s=yield vt();return s?(yield wt(t,s.id))!==null:!0})}function za(){return p(this,null,function*(){try{const{data:t,error:s}=yield y.rpc("get_demographic_analytics");return!s&&t&&t.length>0?t[0]:(console.log("Using fallback analytics calculation"),yield Re())}catch(t){return console.error("Error fetching demographic analytics:",t),yield Re()}})}function Re(){return p(this,null,function*(){try{const{data:t,error:s}=yield y.from("user_demographic_responses").select("responses");if(s)throw s;const{count:r,error:a}=yield y.from("profiles").select("*",{count:"exact",head:!0});a&&console.warn("Could not get total users count:",a);const o=(t==null?void 0:t.length)||0,i=r&&r>0?Math.round(o/r*100*100)/100:0,d={},u={},l={};return t==null||t.forEach(c=>{const E=c.responses,w=E.country||"Unknown";d[w]=(d[w]||0)+1;const P=E.gender||"Unknown";u[P]=(u[P]||0)+1;const k=E.role_type||"Unknown";l[k]=(l[k]||0)+1}),{total_responses:o,response_breakdown:{by_country:d,by_gender:u,by_role:l},completion_rate:i}}catch(t){return console.error("Error in manual analytics calculation:",t),{total_responses:0,response_breakdown:{by_country:{},by_gender:{},by_role:{}},completion_rate:0}}})}function Fa(){return p(this,null,function*(){const{data:t,error:s}=yield y.from("user_demographic_responses").select(`
      *,
      profiles:user_id (
        first_name,
        last_name
      )
    `).order("completed_at",{ascending:!1});if(s)throw console.error("Error fetching all demographic responses:",s),s;return t||[]})}const _r=["Ghana","Afghanistan","Albania","Algeria","Argentina","Armenia","Australia","Austria","Azerbaijan","Bahrain","Bangladesh","Belarus","Belgium","Bolivia","Bosnia and Herzegovina","Brazil","Bulgaria","Cambodia","Cameroon","Canada","Chile","China","Colombia","Costa Rica","Croatia","Czech Republic","Denmark","Ecuador","Egypt","Estonia","Ethiopia","Finland","France","Georgia","Germany","Greece","Guatemala","Honduras","Hungary","Iceland","India","Indonesia","Iran","Iraq","Ireland","Israel","Italy","Japan","Jordan","Kazakhstan","Kenya","Kuwait","Latvia","Lebanon","Lithuania","Luxembourg","Malaysia","Mexico","Morocco","Netherlands","New Zealand","Nigeria","Norway","Pakistan","Peru","Philippines","Poland","Portugal","Qatar","Romania","Russia","Saudi Arabia","Singapore","Slovakia","Slovenia","South Africa","South Korea","Spain","Sri Lanka","Sweden","Switzerland","Thailand","Turkey","Ukraine","United Arab Emirates","United Kingdom","United States","Uruguay","Venezuela","Vietnam","Other"];function kr(t){return["country","university","location","undergraduate_year"].includes(t.id)?!0:t.options&&t.options.length>5}function Sr(t,s){switch(t){case"country":return _r;default:return s||[]}}function Oe(t){switch(t){case"country":return"Ghana";default:return}}function Cr({onComplete:t}){const{user:s}=te(),{toast:r}=jr(),[a,o]=n.useState(null),[i,d]=n.useState(0),[u,l]=n.useState({}),[c,E]=n.useState(!0),[w,P]=n.useState(!1),[k,x]=n.useState(!1);n.useEffect(()=>{p(this,null,function*(){try{const V=yield vt();if(!V){console.warn("No active demographic questionnaire found"),r({title:"Questionnaire not available",description:"Proceeding without demographic questionnaire."}),t();return}if(o(V),s){const H=yield wt(s.id,V.id);H&&(l(H.responses),x(!0))}}catch(V){console.error("Error loading questionnaire:",V),r({title:"Error loading questionnaire",description:"Proceeding without demographic questionnaire."}),t()}finally{E(!1)}})},[s,r,t]);const f=a?a.questions.filter(A=>A.conditional?u[A.conditional.field]===A.conditional.value:!0):[],h=f[i];(i+1)/f.length*100;const D=A=>{h&&l(V=>L(v({},V),{[h.id]:A}))},I=()=>{if(!h)return!1;const A=u[h.id];return A!==void 0&&A!==""},W=()=>{if(!I()&&(h!=null&&h.required)){r({title:"Answer required",description:"Please answer this question before proceeding.",variant:"destructive"});return}i<f.length-1?d(i+1):G()},T=()=>{i>0&&d(i-1)},G=()=>p(this,null,function*(){if(!(!s||!a)){P(!0);try{yield Nr(s.id,a.id,u),x(!0),r({title:"Questionnaire completed!",description:"Thank you for providing your demographic information."}),setTimeout(()=>{t()},1500)}catch(A){console.error("Error saving responses:",A),r({title:"Error",description:"Failed to save your responses. Please try again.",variant:"destructive"})}finally{P(!1)}}});n.useEffect(()=>{if(h){const A=Oe(h.id),V=u[h.id];A&&!V&&h.id==="country"&&D(A)}},[h==null?void 0:h.id,u]);const me=()=>{var ke;if(!h)return null;const A=u[h.id],V=kr(h)||h.type==="dropdown",H=Sr(h.id,h.options),St=Oe(h.id);if(V&&H.length>0)return e.jsx("div",{className:"w-full",children:e.jsx("div",{className:"relative bg-gray-50 dark:bg-gray-800/50 rounded-md sm:rounded-lg border border-gray-200 dark:border-gray-700 p-2.5 sm:p-4 hover:bg-gray-100 dark:hover:bg-gray-800/70 transition-colors duration-200",children:e.jsxs(hr,{value:A||St||"",onValueChange:D,children:[e.jsx(ft,{className:"w-full h-9 sm:h-12 border-0 bg-transparent focus:ring-2 focus:ring-primary/20 text-sm sm:text-base font-medium",children:e.jsx(pr,{placeholder:`Select your ${h.question.toLowerCase().replace("?","")}`})}),e.jsx(yt,{className:"max-h-48 sm:max-h-60 z-50",children:H.map(M=>e.jsx(bt,{value:M,className:"text-sm sm:text-base py-2 sm:py-3",children:M},M))})]})})});switch(h.type){case"single_choice":return e.jsx("div",{className:"space-y-2 sm:space-y-3",children:(ke=h.options)==null?void 0:ke.map((M,Ct)=>e.jsxs(m.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:Ct*.1},className:_("relative flex items-center justify-between py-2.5 sm:py-4 px-3 sm:px-6 cursor-pointer transition-all duration-200 rounded-md sm:rounded-lg border-2",A===M?"border-primary bg-primary/10 dark:bg-primary/20 shadow-sm":"border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50 hover:bg-gray-100 dark:hover:bg-gray-800/70 hover:border-gray-300 dark:hover:border-gray-600"),onClick:()=>D(M),children:[e.jsx("div",{className:"flex-1 text-sm sm:text-base font-medium text-foreground pr-3",children:M}),e.jsx("div",{className:"flex-shrink-0",children:e.jsx("div",{className:_("w-5 h-5 sm:w-6 sm:h-6 rounded-full border-2 transition-all duration-200 flex items-center justify-center",A===M?"border-primary bg-primary shadow-sm":"border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700"),children:A===M&&e.jsx("div",{className:"w-2.5 h-2.5 sm:w-3 sm:h-3 rounded-full bg-white"})})})]},M))});case"text":return e.jsx("div",{className:"w-full",children:e.jsx("div",{className:"relative bg-gray-50 dark:bg-gray-800/50 rounded-md sm:rounded-lg border border-gray-200 dark:border-gray-700 p-2.5 sm:p-4 hover:bg-gray-100 dark:hover:bg-gray-800/70 transition-colors duration-200",children:e.jsx(be,{type:"text",value:A||"",onChange:M=>D(M.target.value),placeholder:"Enter your answer",className:"w-full h-9 sm:h-12 border-0 bg-transparent focus:ring-2 focus:ring-primary/20 text-sm sm:text-base font-medium placeholder:text-gray-400"})})});case"number":return e.jsx("div",{className:"w-full",children:e.jsx("div",{className:"relative bg-gray-50 dark:bg-gray-800/50 rounded-md sm:rounded-lg border border-gray-200 dark:border-gray-700 p-2.5 sm:p-4 hover:bg-gray-100 dark:hover:bg-gray-800/70 transition-colors duration-200",children:e.jsx(be,{type:"number",value:A||"",onChange:M=>D(parseInt(M.target.value)||""),placeholder:"Enter your age",className:"w-full h-9 sm:h-12 border-0 bg-transparent focus:ring-2 focus:ring-primary/20 text-sm sm:text-base font-medium placeholder:text-gray-400"})})});default:return null}};return c?e.jsx("div",{className:"w-full h-screen overflow-y-auto",children:e.jsx(m.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4},className:"w-full max-w-4xl mx-auto px-3 sm:px-6 lg:px-8 py-6",children:e.jsx("div",{className:"min-h-screen flex flex-col justify-center items-center",children:e.jsxs("div",{className:"bg-white dark:bg-gray-900 rounded-lg sm:rounded-xl shadow-md border border-gray-200 dark:border-gray-700 p-6 sm:p-12 text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-10 w-10 sm:h-12 sm:w-12 border-b-2 border-primary mx-auto mb-4 sm:mb-6"}),e.jsx("h3",{className:"text-base sm:text-xl font-semibold mb-2",children:"Loading Questionnaire"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Please wait while we prepare your questionnaire..."})]})})})}):k?e.jsx("div",{className:"w-full h-screen overflow-y-auto",children:e.jsx(m.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.5},className:"w-full max-w-4xl mx-auto px-3 sm:px-6 lg:px-8 py-6",children:e.jsx("div",{className:"min-h-screen flex flex-col justify-center items-center",children:e.jsxs("div",{className:"bg-white dark:bg-gray-900 rounded-lg sm:rounded-xl shadow-md border border-gray-200 dark:border-gray-700 p-6 sm:p-12 text-center",children:[e.jsx(m.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},children:e.jsx(Ht,{className:"h-14 sm:h-20 w-14 sm:w-20 text-green-500 mx-auto mb-4 sm:mb-6"})}),e.jsx("h3",{className:"text-lg sm:text-2xl font-bold mb-3 sm:mb-4 text-green-600",children:"Questionnaire Completed!"}),e.jsx("p",{className:"text-sm sm:text-lg text-muted-foreground mb-3 sm:mb-4",children:"Thank you for providing your demographic information."}),e.jsx("p",{className:"text-xs sm:text-sm text-muted-foreground",children:"You will be redirected to your dashboard shortly..."})]})})})}):!a||f.length===0?e.jsx("div",{className:"w-full h-screen overflow-y-auto",children:e.jsx(m.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4},className:"w-full max-w-4xl mx-auto px-3 sm:px-6 lg:px-8 py-6",children:e.jsx("div",{className:"min-h-screen flex flex-col justify-center items-center",children:e.jsxs("div",{className:"bg-white dark:bg-gray-900 rounded-lg sm:rounded-xl shadow-md border border-gray-200 dark:border-gray-700 p-6 sm:p-12 text-center",children:[e.jsx(Xe,{className:"h-10 sm:h-16 w-10 sm:w-16 text-amber-500 mx-auto mb-4 sm:mb-6"}),e.jsx("h3",{className:"text-base sm:text-xl font-semibold mb-2",children:"Questionnaire Unavailable"}),e.jsx("p",{className:"text-sm text-muted-foreground mb-4 sm:mb-6",children:"The demographic questionnaire is currently not available."}),e.jsx(O,{onClick:t,variant:"outline",size:"sm",className:"h-9 sm:h-11",children:"Continue to Dashboard"})]})})})}):e.jsx("div",{className:"w-full h-screen overflow-y-auto",children:e.jsxs(m.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4},className:"w-full max-w-4xl mx-auto px-3 sm:px-6 lg:px-8 py-4 sm:py-6",children:[e.jsx("div",{className:"mb-4 sm:mb-6",children:e.jsx("div",{className:"bg-white dark:bg-gray-900 rounded-lg sm:rounded-xl shadow-md border border-gray-200 dark:border-gray-700 p-3 sm:p-6",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-lg sm:text-2xl font-bold text-foreground mb-1 sm:mb-2",children:a.title}),e.jsxs("div",{className:"text-xs sm:text-sm font-medium text-muted-foreground mb-2 sm:mb-4",children:["Question ",i+1," of ",f.length]}),e.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 sm:h-2",children:e.jsx("div",{className:"bg-primary h-1.5 sm:h-2 rounded-full transition-all duration-300 ease-out",style:{width:`${(i+1)/f.length*100}%`}})})]})})}),e.jsx("div",{className:"mb-4 sm:mb-6",children:e.jsx("div",{className:"bg-white dark:bg-gray-900 rounded-lg sm:rounded-xl shadow-md border border-gray-200 dark:border-gray-700 p-3 sm:p-6 lg:p-8",children:e.jsx(Ts,{mode:"wait",children:e.jsxs(m.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.3},className:"space-y-4 sm:space-y-6",children:[e.jsx("div",{className:"text-center mb-4 sm:mb-6",children:e.jsxs("h2",{className:"text-base sm:text-xl font-semibold text-foreground leading-relaxed",children:[h==null?void 0:h.question,(h==null?void 0:h.required)&&e.jsx("span",{className:"text-red-500 ml-1",children:"*"})]})}),e.jsx("div",{className:"space-y-2 sm:space-y-4 max-w-2xl mx-auto",children:me()})]},i)})})}),e.jsx("div",{className:"pb-4 sm:pb-6",children:e.jsx("div",{className:"bg-white dark:bg-gray-900 rounded-lg sm:rounded-xl shadow-md border border-gray-200 dark:border-gray-700 p-3 sm:p-6",children:e.jsxs("div",{className:"flex justify-between items-center gap-3 sm:gap-4",children:[e.jsx(O,{variant:"outline",onClick:T,disabled:i===0,size:"sm",className:"min-w-[80px] sm:min-w-[120px] text-xs sm:text-sm font-medium h-9 sm:h-11",children:"Previous"}),e.jsx("div",{className:"text-center hidden sm:block",children:e.jsxs("span",{className:"text-xs text-muted-foreground",children:[i+1," / ",f.length]})}),e.jsx(O,{onClick:W,disabled:w,size:"sm",className:_("min-w-[80px] sm:min-w-[120px] text-xs sm:text-sm font-medium h-9 sm:h-11",i===f.length-1&&"bg-primary hover:bg-primary/90"),children:w?e.jsxs("div",{className:"flex items-center gap-1 sm:gap-2",children:[e.jsx("div",{className:"animate-spin rounded-full h-3 w-3 sm:h-4 sm:w-4 border-b-2 border-white"}),e.jsx("span",{className:"hidden sm:inline",children:"Submitting..."}),e.jsx("span",{className:"sm:hidden",children:"..."})]}):i===f.length-1?e.jsxs("span",{children:[e.jsx("span",{className:"hidden sm:inline",children:"Complete Questionnaire"}),e.jsx("span",{className:"sm:hidden",children:"Complete"})]}):"Next"})]})})})]})})}function Ir({onComplete:t}){const[s,r]=n.useState(!0),[a,o]=n.useState(0),[i,d]=n.useState(!1),{user:u,setUser:l}=te(),{shouldReduceMotion:c,transitions:E}=dt(),w=()=>{P()},P=()=>p(this,null,function*(){if(u){d(!0);try{const{data:h,error:D}=yield y.auth.updateUser({data:{onboarding_completed:!0,demographic_questionnaire_completed:!0}});if(D)console.error("Error updating user:",D);else if(h.user){const{error:I}=yield y.from("user_preferences").upsert({user_id:u.id,demographic_questionnaire_completed:!0});I&&console.error("Error updating preferences:",I),l==null||l(h.user),localStorage.setItem("onboarding_completed","true"),r(!1),t()}}catch(h){console.error("Error in onboarding completion:",h)}finally{d(!1)}}}),k=[{title:"Welcome to Intravaneous Cannulation Elearning Platform",description:"Let's get you set up to start learning.",illustration:"welcome",content:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-muted-foreground mb-6",children:"Welcome! Before you begin your learning journey, we need you to complete a short questionnaire."}),e.jsx(O,{onClick:()=>S(),children:"Get Started"})]})},{title:"Complete the Demographic Questionnaire",description:"Required step before you can access courses.",illustration:"courses",content:e.jsx(Cr,{onComplete:w})}],x={hidden:{opacity:0,x:c?0:20},visible:{opacity:1,x:0,transition:E.default},exit:{opacity:0,x:c?0:-20,transition:E.default}},S=()=>{a<k.length-1&&o(a+1)};return a===1?e.jsx("div",{className:"fixed inset-0 bg-background z-50",children:k[a].content}):e.jsx(ut,{open:s,onOpenChange:r,children:e.jsxs(Ne,{className:"w-[95vw] max-w-[95vw] sm:max-w-[500px] p-0 overflow-hidden",onInteractOutside:h=>h.preventDefault(),onEscapeKeyDown:h=>h.preventDefault(),children:[e.jsxs(Jt,{children:[e.jsx(Ee,{children:k[a].title}),e.jsx(pt,{children:k[a].description})]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute top-4 left-0 right-0 flex justify-center",children:e.jsx("div",{className:"flex gap-1",children:k.map((h,D)=>e.jsx("div",{className:`h-1.5 rounded-full transition-all duration-300 ${D===a?"w-6 bg-primary":"w-2 bg-primary/30"}`},D))})}),e.jsx("div",{className:"bg-muted/30 p-4 sm:p-6 flex justify-center",children:e.jsx(mr,{step:k[a].illustration,width:window.innerWidth<640?240:280,height:window.innerWidth<640?160:180})}),e.jsxs("div",{className:"p-4 sm:p-6",children:[e.jsx("h2",{className:"text-lg sm:text-xl font-semibold mb-1",children:k[a].title}),e.jsx("p",{className:"text-muted-foreground text-xs sm:text-sm mb-4 sm:mb-6",children:k[a].description}),e.jsx(m.div,{variants:x,initial:"hidden",animate:"visible",exit:"exit",children:k[a].content},a)]})]})]})})}function Tr({children:t}){const{user:s}=te(),[r,a]=n.useState(!1),[o,i]=n.useState(!0);n.useEffect(()=>{p(this,null,function*(){var l;if(!s){i(!1);return}try{i(!0);const c=((l=s.user_metadata)==null?void 0:l.onboarding_completed)===!0,E=localStorage.getItem("onboarding_completed")==="true",w=yield Er(s.id);a(!((c||E)&&w))}catch(c){console.error("Error checking onboarding status:",c),a(!0)}finally{i(!1)}})},[s]);const d=()=>p(this,null,function*(){a(!1),g.success("Welcome to IVCan Study!",{description:"Your profile has been set up successfully."})});return o||!s?e.jsx(e.Fragment,{children:t}):e.jsxs(e.Fragment,{children:[r&&e.jsx(Ir,{onComplete:d}),t]})}function jt(){const[t,s]=n.useState(typeof navigator!="undefined"?navigator.onLine:!0),[r,a]=n.useState(!1);return n.useEffect(()=>{const o=()=>{s(!0),r&&(g.success("You are back online"),a(!1))},i=()=>{s(!1),a(!0),g.error("You are offline. Some features may be limited.")};return window.addEventListener("online",o),window.addEventListener("offline",i),()=>{window.removeEventListener("online",o),window.removeEventListener("offline",i)}},[r]),{isOnline:t,isOffline:!t}}const Ar=()=>{const{isOnline:t,isOffline:s}=jt();return t?null:e.jsxs("div",{className:"fixed bottom-4 right-4 z-50 flex items-center gap-2 rounded-md bg-amber-100 px-3 py-2 text-amber-800 shadow-md dark:bg-amber-900 dark:text-amber-100",children:[s?e.jsx(Xt,{className:"h-5 w-5 text-amber-800 dark:text-amber-100"}):e.jsx(Zt,{className:"h-5 w-5 text-red-600 dark:text-red-400"}),e.jsx("span",{className:"text-sm font-medium",children:s?"You are offline":"Connected"})]})},Nt="ivcan-offline-queue",Lr=100;function z(){try{const t=localStorage.getItem(Nt);return t?JSON.parse(t):[]}catch(t){return console.error("Error retrieving offline queue:",t),[]}}function _e(t){try{const s=t.slice(-Lr);localStorage.setItem(Nt,JSON.stringify(s))}catch(s){console.error("Error saving offline queue:",s),g.error("Unable to save offline operations")}}function ve(t,s){try{const r=z(),a=crypto.randomUUID(),o={id:a,operation:t,params:s,timestamp:Date.now(),retries:0};return r.push(o),_e(r),g.info("Operation queued for when you are back online"),a}catch(r){return console.error("Error queueing offline operation:",r),g.error("Failed to queue operation for offline use"),""}}function Rr(t){try{const s=z(),r=s.filter(a=>a.id!==t);return r.length!==s.length?(_e(r),!0):!1}catch(s){return console.error("Error removing operation from offline queue:",s),!1}}function Or(t){return p(this,null,function*(){const s=z();let r=0,a=0;if(s.length===0)return{successful:r,failed:a};g.info(`Processing ${s.length} offline operations...`);const o=[];for(const i of s)try{yield F(()=>p(this,null,function*(){yield t(i.operation,i.params)}),2),r++}catch(d){console.error(`Failed to process offline operation ${i.id}:`,d),i.retries<3?o.push(L(v({},i),{retries:i.retries+1})):(a++,g.error("Failed to process an offline operation after multiple attempts"))}return _e(o),r>0&&g.success(`Successfully processed ${r} offline operations`),o.length>0&&g.error(`${o.length} operations remain in the offline queue`),{successful:r,failed:a}})}function Pr(t,s,r,a){return p(this,null,function*(){if(a)try{return yield F(r,3)}catch(o){if(console.error(`Online operation failed (${t}):`,o),o instanceof Error&&(o.message.includes("network")||o.message.includes("timeout")||o.message.includes("connection")))return ve(t,s),null;throw o}else return ve(t,s),null})}function Dr(){const{isOnline:t}=jt(),[s,r]=n.useState(0);n.useEffect(()=>{const u=z();r(u.length),t&&u.length>0&&a()},[t]);const a=n.useCallback(u=>p(this,null,function*(){if(!t)return{successful:0,failed:0};const c=yield Or(u||((E,w)=>p(this,null,function*(){return console.log(`Processing operation: ${E}`,w),!0})));return r(z().length),c}),[t]),o=n.useCallback((u,l)=>{const c=ve(u,l);return r(z().length),c},[]),i=n.useCallback(u=>{const l=Rr(u);return r(z().length),l},[]),d=n.useCallback((u,l,c)=>p(this,null,function*(){const E=yield Pr(u,l,c,t);return r(z().length),E}),[t]);return{isOnline:t,queueSize:s,processQueue:a,queueOperation:o,removeOperation:i,executeOperation:d,getQueue:z}}const Et=n.forwardRef((o,a)=>{var i=o,{className:t,children:s}=i,r=R(i,["className","children"]);return e.jsxs(Ze,L(v({ref:a,className:_("relative overflow-hidden",t)},r),{children:[e.jsx(es,{className:"h-full w-full rounded-[inherit]",children:s}),e.jsx(_t,{}),e.jsx(ts,{})]}))});Et.displayName=Ze.displayName;const _t=n.forwardRef((o,a)=>{var i=o,{className:t,orientation:s="vertical"}=i,r=R(i,["className","orientation"]);return e.jsx(et,L(v({ref:a,orientation:s,className:_("flex touch-none select-none transition-colors",s==="vertical"&&"h-full w-2.5 border-l border-l-transparent p-[1px]",s==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",t)},r),{children:e.jsx(ss,{className:"relative flex-1 rounded-full bg-border"})}))});_t.displayName=et.displayName;const Mr=()=>{const{queueSize:t,isOnline:s,processQueue:r,getQueue:a}=Dr(),[o,i]=n.useState(!1),[d,u]=n.useState(!1);if(t===0)return null;const l=()=>p(void 0,null,function*(){if(!s){g.error("Cannot process queue while offline");return}u(!0);try{const c=yield r();g.success(`Processed ${c.successful} operations`),c.failed>0&&g.error(`Failed to process ${c.failed} operations`),c.successful>0&&a().length===0&&i(!1)}catch(c){console.error("Error processing queue:",c),g.error("Failed to process operations queue")}finally{u(!1)}});return e.jsxs(ut,{open:o,onOpenChange:i,children:[e.jsx(or,{asChild:!0,children:e.jsxs(O,{size:"sm",variant:"outline",className:"fixed bottom-4 left-4 z-50 flex items-center gap-2",children:[e.jsx(rs,{className:"h-4 w-4"}),e.jsxs("span",{children:["Pending Operations: ",t]})]})}),e.jsxs(Ne,{className:"sm:max-w-md",children:[e.jsx(ht,{children:e.jsx(Ee,{children:"Offline Operations Queue"})}),e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsxs("p",{className:"text-sm text-muted-foreground",children:["You have ",t," operation",t!==1?"s":""," waiting to be processed.",!s&&" You need to be online to process the queue."]}),e.jsx(Et,{className:"h-60 rounded-md border p-4",children:e.jsx("div",{className:"space-y-2",children:a().map(c=>e.jsxs("div",{className:"rounded-md bg-muted p-2 text-xs",children:[e.jsx("div",{className:"font-medium",children:c.operation}),e.jsx("div",{className:"mt-1 text-muted-foreground",children:new Date(c.timestamp).toLocaleString()}),c.retries>0&&e.jsxs("div",{className:"mt-1 text-destructive",children:["Failed attempts: ",c.retries]})]},c.id))})}),e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx(O,{variant:"outline",onClick:()=>i(!1),children:"Close"}),e.jsx(O,{onClick:l,disabled:!s||d,children:d?"Processing...":"Process Queue"})]})]})]})]})},Ur=()=>null,U={isCheckingConnection:!1,lastCheckTime:0,isConnected:!0,connectionHistory:[!0,!0,!0,!0,!0],consecutiveFailures:0};function ce(){return p(this,null,function*(){if(U.isCheckingConnection)return U.isConnected;U.isCheckingConnection=!0;try{try{if((yield fetch(`${st}/rest/v1/`,{method:"HEAD",headers:{apikey:rt},cache:"no-store"})).ok)return ae(!0),!0}catch(a){}const{error:t}=yield F(()=>p(this,null,function*(){return yield y.from("user_roles").select("count").limit(1)}),3,800);if(!t)return ae(!0),!0;const{error:s}=yield F(()=>p(this,null,function*(){return yield y.auth.getSession()}),2,1e3),r=!s;return r||console.error("All connection check methods failed"),ae(r),r}catch(t){return console.error("Health check failed:",t),ae(!1),!1}finally{U.isCheckingConnection=!1,U.lastCheckTime=Date.now()}})}function ae(t){U.connectionHistory.shift(),U.connectionHistory.push(t),U.isConnected=t,t?U.consecutiveFailures=0:U.consecutiveFailures++}function Vr(){ce(),setInterval(()=>{let t;U.isConnected?t=2*60*1e3:U.consecutiveFailures>5?t=60*1e3:U.consecutiveFailures>2?t=30*1e3:t=15*1e3,Date.now()-U.lastCheckTime>=t&&ce()},10*1e3),window.addEventListener("online",()=>{ce()})}class C extends n.Component{constructor(r){super(r);re(this,"handleRetry",()=>p(this,null,function*(){g.info("Retrying connection...",{description:"Attempting to reconnect to the database"});try{(yield ce())?g.success("Connection restored!",{description:"Successfully reconnected to the database"}):g.error("Connection failed",{description:"Could not establish a database connection"})}catch(r){console.error("Connection check failed:",r),g.error("Connection check failed",{description:"Could not verify database connection status"})}this.setState({hasError:!1,error:null,isSupabaseError:!1,isMountingError:!1})}));this.state={hasError:!1,error:null,isSupabaseError:!1,isMountingError:!1}}static getDerivedStateFromError(r){const a=r.message&&(r.message.includes("unmounted")||r.message.includes("memory leak")||r.message.includes("clean up subscriptions")||r.message.includes("removeChild")||r.message.includes("commitDeletion")||r.message.includes("Cannot update a component"))||r.stack&&(r.stack.includes("commitDeletion")||r.stack.includes("removeChild")),o=r instanceof Ds||r.message&&(r.message.includes("supabase")||r.message.includes("network")||r.message.includes("connection")||r.message.includes("timeout")||r.message.includes("postgrest")||r.message.includes("503")||r.message.includes("service unavailable"))||ue(r);return{hasError:!0,error:r,isSupabaseError:o,isMountingError:a}}componentDidCatch(r,a){console.error("Supabase Error Boundary caught an error:",a),console.debug("Error details:",r),this.state.isMountingError&&console.log("React mounting/unmounting error detected - will recover automatically")}render(){const{hasError:r,error:a,isSupabaseError:o,isMountingError:i}=this.state,{children:d,fallback:u}=this.props;return r&&i&&!o?(setTimeout(()=>{this.setState({hasError:!1,error:null,isSupabaseError:!1,isMountingError:!1})},1e3),e.jsx("div",{className:"p-4 text-center",children:e.jsx("p",{className:"text-sm text-muted-foreground",children:"Recovering..."})})):r?u||(o?e.jsxs("div",{className:"flex min-h-[200px] flex-col items-center justify-center rounded-lg border border-primary/20 bg-primary/5 p-6 text-center dark:border-primary/20 dark:bg-primary/10",children:[e.jsx(as,{className:"h-10 w-10 text-primary dark:text-primary-foreground"}),e.jsx("h3",{className:"mt-4 text-lg font-medium text-primary-foreground dark:text-primary-foreground",children:"Database Service Unavailable"}),e.jsx("p",{className:"mt-2 text-sm text-primary/80 dark:text-primary-foreground/80",children:"We're having trouble connecting to our backend service. This may be temporary."}),e.jsxs("div",{className:"flex gap-4 mt-4",children:[e.jsxs(O,{variant:"outline",className:"bg-white dark:bg-primary/20",onClick:this.handleRetry,children:[e.jsx(Ie,{className:"w-4 h-4 mr-2"}),"Try Again"]}),e.jsx(O,{onClick:()=>window.location.reload(),variant:"ghost",className:"text-primary dark:text-primary-foreground",children:"Reload Page"})]})]}):e.jsxs("div",{className:"flex min-h-[200px] flex-col items-center justify-center rounded-lg border border-primary/20 bg-primary/5 p-6 text-center dark:border-primary/20 dark:bg-primary/10",children:[e.jsx(Xe,{className:"h-10 w-10 text-primary dark:text-primary-foreground"}),e.jsx("h3",{className:"mt-4 text-lg font-medium text-primary-foreground dark:text-primary-foreground",children:"Application Error"}),e.jsx("p",{className:"mt-2 text-sm text-primary/80 dark:text-primary-foreground/80",children:(a==null?void 0:a.message)||"An unexpected error occurred"}),e.jsxs("div",{className:"flex gap-4 mt-4",children:[e.jsxs(O,{variant:"outline",className:"bg-white dark:bg-primary/20",onClick:this.handleRetry,children:[e.jsx(Ie,{className:"w-4 h-4 mr-2"}),"Try Again"]}),e.jsx(O,{onClick:()=>window.location.reload(),variant:"ghost",className:"text-primary dark:text-primary-foreground",children:"Reload Page"})]}),a&&!1]})):d}}const zr=({children:t,requiredRole:s=null})=>{var i;const{user:r,loading:a}=te(),o=tt();if(a)return e.jsx("div",{className:"flex justify-center items-center h-screen bg-background",children:e.jsxs("div",{className:"flex flex-col items-center gap-4",children:[e.jsx(ne,{className:"h-12 w-12 animate-spin text-primary"}),e.jsx("p",{className:"text-muted-foreground",children:"Verifying authentication..."})]})});if(!r){const d=encodeURIComponent(o.pathname+o.search);return e.jsx(ye,{to:`/login?returnTo=${d}`,replace:!0})}return s&&((i=r.user_metadata)==null?void 0:i.role)!==s?e.jsx(ye,{to:"/dashboard",replace:!0}):e.jsx(e.Fragment,{children:t})},Fr=()=>e.jsx("div",{className:"flex items-center justify-center min-h-screen bg-background",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"})}),oe=({children:t,requiredRole:s})=>{const{user:r,loading:a}=te(),o=tt(),[i,d]=n.useState(!1),[u,l]=n.useState(null),[c,E]=n.useState(!0);if(n.useEffect(()=>{a||p(void 0,null,function*(){if(!r){E(!1),l(!1);return}try{const{data:P,error:k}=yield y.from("user_roles").select("role").eq("user_id",r.id).eq("role",s).maybeSingle();if(k)throw k;l(!!P),d(!0)}catch(P){console.error("Error checking user role:",P),g.error("Failed to verify your access permissions"),l(!1)}finally{E(!1)}})},[r,a,s]),a||c)return e.jsx(Fr,{});if(!r){const w=encodeURIComponent(o.pathname+o.search);return e.jsx(ye,{to:`/login?returnTo=${w}`,replace:!0})}return i&&!u?e.jsx("div",{className:"flex justify-center items-center h-screen bg-background",children:e.jsxs("div",{className:"max-w-md w-full p-8 bg-card rounded-xl shadow-sm border border-border text-center",children:[e.jsx(os,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),e.jsx("h1",{className:"text-2xl font-bold mb-2",children:"Access Denied"}),e.jsxs("p",{className:"text-muted-foreground mb-6",children:["You don't have permission to access this page. This area requires ",s," privileges."]}),e.jsxs("div",{className:"flex gap-4 justify-center",children:[e.jsx("button",{onClick:()=>window.history.back(),className:"px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors",children:"Go Back"}),e.jsx("button",{onClick:()=>window.location.href="/dashboard",className:"px-4 py-2 bg-muted text-foreground rounded-lg hover:bg-muted/90 transition-colors",children:"Go to Dashboard"})]})]})}):e.jsx(e.Fragment,{children:t})};function qr(){const{theme:t}=it();return n.useEffect(()=>{const s=()=>{localStorage.removeItem("selectedTheme"),localStorage.setItem("selectedTheme","Red");const a=document.documentElement,o=a.classList.contains("dark");a.style.setProperty("--primary","hsl(0 70% 50%)"),a.style.setProperty("--primary-light","hsl(0 70% 60%)"),a.style.setProperty("--primary-dark","hsl(0 70% 40%)"),a.style.setProperty("--accent","hsl(0 60% 30%)"),document.body.classList.add("red-theme"),document.querySelectorAll("*").forEach(d=>{d instanceof HTMLElement&&((d.style.color==="#25D366"||d.style.color==="rgb(37, 211, 102)")&&(d.style.color="#E63946"),(d.style.backgroundColor==="#25D366"||d.style.backgroundColor==="rgb(37, 211, 102)")&&(d.style.backgroundColor="#E63946"),(d.style.borderColor==="#25D366"||d.style.borderColor==="rgb(37, 211, 102)")&&(d.style.borderColor="#E63946"))}),console.log(`Theme has been forced to Red (${o?"dark":"light"} mode)`)};s();const r=setTimeout(s,100);return window.addEventListener("load",s),()=>{clearTimeout(r),window.removeEventListener("load",s)}},[t]),null}const Br=()=>e.jsx("footer",{className:"w-full py-2 px-4 mt-4 border-t bg-background",children:e.jsx("div",{className:"flex items-center justify-center h-6 text-[11px] text-muted-foreground/70 whitespace-nowrap",children:e.jsxs("span",{children:["©",new Date().getFullYear()," e4mi -  Prince Gabriel"]})})});function kt(t=768){const[s,r]=n.useState(!1);return n.useEffect(()=>{const a=()=>{r(window.innerWidth<t)};return a(),window.addEventListener("resize",a),()=>{window.removeEventListener("resize",a)}},[t]),s}function $r(){const[t,s]=n.useState("md");return n.useEffect(()=>{const r=()=>{const o=window.innerWidth;return o<480?"xs":o<640?"sm":o<768?"md":o<1024?"lg":o<1280?"xl":"2xl"};s(r());const a=()=>{s(r())};return window.addEventListener("resize",a),()=>{window.removeEventListener("resize",a)}},[]),t}const Kr=({sidebarOpen:t,toggleSidebar:s,setSidebarOpen:r})=>{const a=u=>window.location.pathname===u,o=kt(768);$r();const i=u=>{u.preventDefault(),u.stopPropagation(),console.log("Toggle sidebar clicked"),s?s():r&&r(!t)},d=(u,l)=>{window.location.pathname===l&&u.preventDefault()};return e.jsxs("div",{className:"flex items-center gap-2 w-full justify-between md:justify-center relative z-50",children:[o&&(s||r)&&e.jsx("button",{onClick:i,className:"rounded-full bg-[#e63946]/10 text-[#e63946] hover:bg-[#e63946]/20 transition-all duration-200 p-2 h-10 w-10 flex items-center justify-center","aria-label":"Toggle menu",style:{touchAction:"manipulation"},children:t?e.jsx(Fe,{className:"h-4 w-4"}):e.jsx(ns,{className:"h-4 w-4"})}),!o&&e.jsxs("nav",{className:"flex items-center gap-2",role:"navigation","aria-label":"Main navigation",children:[e.jsx(O,{asChild:!0,variant:a("/")?"default":"ghost",size:"sm",className:"rounded-full font-medium relative z-50",children:e.jsxs(q,{to:"/",onClick:u=>d(u,"/"),"aria-label":"Go to home page","aria-current":a("/")?"page":void 0,children:[e.jsx(is,{className:"h-4 w-4 mr-1.5","aria-hidden":"true"}),e.jsx("span",{children:"Home"})]})}),e.jsx(O,{asChild:!0,variant:a("/dashboard")?"default":"ghost",size:"sm",className:"rounded-full font-medium relative z-50",children:e.jsxs(q,{to:"/dashboard",onClick:u=>d(u,"/dashboard"),"aria-label":"Go to programmes dashboard","aria-current":a("/dashboard")?"page":void 0,children:[e.jsx(ls,{className:"h-4 w-4 mr-1.5","aria-hidden":"true"}),e.jsx("span",{children:"Programmes"})]})})]})]})};function Yr(){const{theme:t,setTheme:s}=it();return e.jsx(m.div,{whileTap:{scale:.95},className:"relative",children:e.jsxs(O,{variant:"ghost",size:"icon",onClick:()=>s(t==="light"?"dark":"light"),className:`rounded-full ${t==="light"?"bg-primary/10 shadow-sm hover:bg-primary/20":"bg-white/10 backdrop-blur-sm hover:bg-white/20"} border-none shadow-sm`,children:[e.jsxs(m.div,{initial:{rotate:0},animate:{rotate:t==="dark"?180:0},transition:{duration:.4,type:"spring"},className:"relative w-5 h-5",children:[e.jsx(cs,{className:`absolute inset-0 h-5 w-5 rotate-0 scale-100 ${t==="light"?"text-gray-800":"text-white"} transition-all dark:-rotate-90 dark:scale-0`}),e.jsx(ds,{className:`absolute inset-0 h-5 w-5 rotate-90 scale-0 ${t==="light"?"text-gray-800":"text-white"} transition-all dark:rotate-0 dark:scale-100`})]}),e.jsx("span",{className:"sr-only",children:"Toggle theme"})]})})}const Pe=["/images/hero1.jpg","/images/hero2.jpg","/images/hero3.jpg","/images/hero4.jpg","/images/hero5.jpg","/images/hero6.jpg"],Qr=[{name:"Dr Andrew Donkor",role:"Department of Medical Imaging",institution:"KNUST, Ghana"},{name:"Prof Yaw A. Wiafe",role:"Department of Medical Imaging",institution:"KNUST, Ghana"},{name:"Mr Daniel K. Otabil",role:"Department of Medical Imaging",institution:"KNUST, Ghana"},{name:"Miss Maame Araba Ekubi Acquaye",role:"Department of Medical Imaging",institution:"KNUST, Ghana"},{name:"Mr Opoku Sekyere",role:"Department of Medical Imaging",institution:"KNUST, Ghana"},{name:"Mr Alexander Dzakah Jnr",role:"Department of Medical Imaging",institution:"KNUST, Ghana"},{name:"Mr Kwabena Asamoah Baffour",role:"Department of Medical Imaging",institution:"KNUST, Ghana"},{name:"Miss Adwoa Agyemang Duah",role:"Department of Medical Imaging",institution:"KNUST, Ghana"},{name:"Mr Prince Akrofi Osakonor",role:"Department of Medical Imaging",institution:"KNUST, Ghana"}],Wr=[{icon:e.jsx(gs,{className:"h-8 w-8 text-primary"}),title:"Comprehensive Learning",description:"Access evidence-based modules designed specifically for medical imaging professionals"},{icon:e.jsx(xs,{className:"h-8 w-8 text-primary"}),title:"Learn at Your Pace",description:"Self-paced modules allow you to learn whenever and wherever is convenient for you"},{icon:e.jsx(ys,{className:"h-8 w-8 text-primary"}),title:"Earn Certifications",description:"Receive recognition for your completed modules with downloadable certificates"}],Gr=()=>{const t=kt(),{scrollY:s}=As(),r=Ls(s,[0,1e3],["#fefefe","#fefefe"]),[a,o]=n.useState(0);return n.useEffect(()=>{const i=setInterval(()=>{o(d=>(d+1)%Pe.length)},5e3);return()=>clearInterval(i)},[]),e.jsx("div",{className:"min-h-screen relative",style:{background:r},children:e.jsxs("div",{className:"relative z-10",children:[e.jsx("header",{className:"fixed top-0 left-0 right-0 bg-background/95 dark:bg-background/95 border-b border-border/40 z-50 supports-[backdrop-filter]:bg-background/60 supports-[backdrop-filter]:backdrop-blur-sm",children:e.jsx("div",{className:"container mx-auto px-4 sm:px-6",children:e.jsxs("div",{className:"flex h-14 sm:h-16 items-center justify-between",children:[e.jsxs(m.h1,{initial:{opacity:0},animate:{opacity:1},className:"text-lg sm:text-xl font-extrabold text-primary flex items-center",children:[e.jsx(Te,{className:"h-6 w-6 sm:h-8 sm:w-8 sm:mr-2"}),e.jsx("span",{children:"e4mi"})]}),e.jsxs("div",{className:"flex items-center gap-2 sm:gap-4",children:[e.jsx(Kr,{}),e.jsxs("div",{className:"flex items-center gap-1.5 sm:gap-2",children:[e.jsx(Yr,{}),e.jsx(O,{asChild:!0,variant:"default",size:t?"sm":"default",className:"shadow-sm font-medium",children:e.jsx(q,{to:"/login",children:"Login"})})]})]})]})})}),e.jsxs("main",{children:[e.jsx("section",{className:"pt-32 pb-16 px-4 sm:px-6 lg:px-8 relative overflow-hidden",children:e.jsx("div",{className:"max-w-7xl mx-auto",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-12 items-center",children:[e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{children:[e.jsx(m.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.6},className:"mb-2",children:e.jsx("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary/10 text-primary",children:"Medical Imaging Education"})}),e.jsxs(m.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.1},className:"text-4xl sm:text-5xl font-bold text-foreground leading-tight mb-4",children:["Welcome to the ",e.jsx("span",{className:"text-primary",children:"e4mi"})," platform"]}),e.jsx(m.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.2},className:"text-xl text-muted-foreground",children:"Providing an evidence-based eLearning platform to train the medical imaging workforce"})]}),e.jsxs(m.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.3},className:"flex flex-wrap gap-4",children:[e.jsx(O,{asChild:!0,size:"lg",className:"px-8 py-6 h-auto text-base font-medium relative",children:e.jsxs(q,{to:"/login",state:{showSignup:!0},children:["Get Started",e.jsx(Ae,{className:"ml-2 h-4 w-4"})]})}),e.jsx(O,{asChild:!0,variant:"outline",size:"lg",className:"px-8 py-6 h-auto text-base font-medium",children:e.jsx(q,{to:"/login",children:"Login"})})]})]}),e.jsxs(m.div,{initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.8,delay:.2},className:"relative h-[300px] sm:h-[400px] rounded-xl overflow-hidden shadow-lg",children:[Pe.map((i,d)=>e.jsx(m.div,{className:"absolute inset-0",initial:{opacity:0},animate:{opacity:d===a?1:0},transition:{opacity:{duration:1}},children:e.jsx("img",{src:i,alt:`Medical imaging training ${d+1}`,className:"w-full h-full object-cover rounded-xl"})},d)),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary/20 to-transparent rounded-xl"})]})]})})}),e.jsx("section",{className:"py-16 sm:py-24 px-4 sm:px-6 lg:px-8 bg-muted/30",children:e.jsxs("div",{className:"max-w-7xl mx-auto",children:[e.jsxs("div",{className:"text-center mb-12",children:[e.jsx(m.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0,margin:"-100px"},transition:{duration:.6},className:"text-3xl font-bold text-foreground mb-4",children:"Why Choose Our Platform?"}),e.jsx(m.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0,margin:"-100px"},transition:{duration:.6,delay:.1},className:"w-24 h-1 bg-primary mx-auto mb-6"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:Wr.map((i,d)=>e.jsxs(m.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0,margin:"-100px"},transition:{duration:.5,delay:d*.1},className:"bg-card shadow-sm hover:shadow-md transition-shadow duration-300 rounded-xl p-8 text-center",children:[e.jsx("div",{className:"inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary/10 mb-6",children:i.icon}),e.jsx("h3",{className:"text-xl font-semibold text-foreground mb-3",children:i.title}),e.jsx("p",{className:"text-muted-foreground",children:i.description})]},d))})]})}),e.jsx("section",{className:"py-16 sm:py-24 px-4 sm:px-6 lg:px-8",children:e.jsx("div",{className:"max-w-7xl mx-auto",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[e.jsx(m.div,{initial:{opacity:0,x:-30},whileInView:{opacity:1,x:0},viewport:{once:!0,margin:"-100px"},transition:{duration:.7},children:e.jsx("div",{className:"bg-muted/20 p-1 rounded-2xl",children:e.jsx("div",{className:"relative h-[350px] overflow-hidden rounded-xl",children:e.jsx("img",{src:"/images/hero3.jpg",alt:"Medical imaging training",className:"w-full h-full object-cover"})})})}),e.jsxs(m.div,{initial:{opacity:0,x:30},whileInView:{opacity:1,x:0},viewport:{once:!0,margin:"-100px"},transition:{duration:.7},className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-3xl font-bold text-foreground mb-2",children:"About Our Programmes"}),e.jsx("div",{className:"w-20 h-1 bg-primary mb-6"})]}),e.jsx("h3",{className:"text-xl font-semibold text-foreground",children:"Intravenous Cannulation and Contrast Administration (I-can-IV eLearning) initiative"}),e.jsx("p",{className:"text-muted-foreground",children:"One of our ongoing programmes is the I-can-IV eLearning initiative. The I-can-IV eLearning initiative was developed by the Department of Medical Imaging, KNUST. The intravenous cannulation and contrast administration (I-can-IV eLearning) programme was developed by the Department of Medical Imaging, KNUST."}),e.jsx("p",{className:"text-muted-foreground",children:"Intravenous (IV) cannulation and contrast administration are essential clinical skills required in various medical imaging settings. The I-can-IV eLearning initiative provides medical imaging students and professionals with knowledge and guidance on how to safely and effectively perform IV cannulation."}),e.jsx("p",{className:"text-muted-foreground",children:"The I-can-IV eLearning initiative covers important topics such as overview of IV cannulation, patient preparation and procedure for IV cannulation, contrast administration, post administrative care and IV removal and infection control practices during IV cannulation. Medical imaging students and professionals gaining knowledge of IV cannulation and contrast administration will enhance patient care, reduce the risk of complications and build confidence in clinical practices."}),e.jsx(O,{asChild:!0,className:"mt-4",children:e.jsxs(q,{to:"/login",children:["Start Learning Now",e.jsx(us,{className:"ml-2 h-4 w-4"})]})})]})]})})}),e.jsx("section",{className:"py-16 sm:py-24 px-4 sm:px-6 lg:px-8 bg-muted/30",children:e.jsx("div",{className:"max-w-7xl mx-auto text-center",children:e.jsxs(m.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0,margin:"-100px"},transition:{duration:.6},className:"max-w-3xl mx-auto",children:[e.jsx("h2",{className:"text-3xl font-bold text-foreground mb-4",children:"How to Access"}),e.jsx("div",{className:"w-24 h-1 bg-primary mx-auto mb-6"}),e.jsx("p",{className:"text-lg text-muted-foreground mb-8",children:"To access the I-can-IV eLearning programme, you will need an account. You can register by selecting the Get Started button. If you already have an account, click the Login button."}),e.jsxs("div",{className:"flex flex-wrap justify-center gap-4 mt-8",children:[e.jsx(O,{asChild:!0,size:"lg",className:"shadow-md font-medium px-8 py-6 h-auto text-base",children:e.jsxs(q,{to:"/login",state:{showSignup:!0},children:["Get Started",e.jsx(Ae,{className:"ml-2 h-4 w-4"})]})}),e.jsx(O,{asChild:!0,variant:"outline",size:"lg",className:"shadow-sm font-medium px-8 py-6 h-auto text-base",children:e.jsx(q,{to:"/login",children:"Login"})})]})]})})}),e.jsx("section",{className:"py-16 sm:py-24 px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-7xl mx-auto",children:[e.jsxs("div",{className:"text-center mb-12",children:[e.jsx(m.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0,margin:"-100px"},transition:{duration:.6},className:"text-3xl font-bold text-foreground mb-4",children:"Meet Our Team"}),e.jsx(m.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0,margin:"-100px"},transition:{duration:.6,delay:.1},className:"w-24 h-1 bg-primary mx-auto mb-6"})]}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6",children:Qr.map((i,d)=>e.jsx(m.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},viewport:{once:!0,margin:"-100px"},transition:{duration:.5,delay:d*.05},className:"bg-card shadow-sm hover:shadow-md transition-shadow duration-300 rounded-xl p-6",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"flex-shrink-0 w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center",children:e.jsx(ms,{className:"h-6 w-6 text-primary"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-foreground",children:i.name}),e.jsx("p",{className:"text-muted-foreground text-sm",children:i.role}),e.jsx("p",{className:"text-muted-foreground text-sm",children:i.institution})]})]})},d))})]})}),e.jsx("section",{className:"py-16 sm:py-24 px-4 sm:px-6 lg:px-8 bg-muted/30",children:e.jsx("div",{className:"max-w-7xl mx-auto",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[e.jsxs(m.div,{initial:{opacity:0,x:-30},whileInView:{opacity:1,x:0},viewport:{once:!0,margin:"-100px"},transition:{duration:.7},className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-3xl font-bold text-foreground mb-2",children:"Contact Us"}),e.jsx("div",{className:"w-20 h-1 bg-primary mb-6"})]}),e.jsx("p",{className:"text-lg text-muted-foreground",children:"Have questions about the e4mi platform? Our team is here to help you."}),e.jsxs("div",{className:"space-y-4 mt-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center",children:e.jsx(hs,{className:"h-5 w-5 text-primary"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-muted-foreground",children:"Email"}),e.jsx("p",{className:"text-foreground font-medium",children:"<EMAIL>"})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center",children:e.jsx(ps,{className:"h-5 w-5 text-primary"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-muted-foreground",children:"Phone"}),e.jsx("p",{className:"text-foreground font-medium",children:"+233 (0) 559 917 082"})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center",children:e.jsx(fs,{className:"h-5 w-5 text-primary"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-muted-foreground",children:"Department"}),e.jsx("p",{className:"text-foreground font-medium",children:"Department of Medical Imaging"}),e.jsx("p",{className:"text-muted-foreground",children:"Kwame Nkrumah University of Science and Technology (KNUST), Ghana"})]})]})]})]}),e.jsx(m.div,{initial:{opacity:0,x:30},whileInView:{opacity:1,x:0},viewport:{once:!0,margin:"-100px"},transition:{duration:.7},className:"bg-card shadow-md rounded-xl overflow-hidden",children:e.jsx("div",{className:"aspect-video w-full",children:e.jsx("img",{src:"/images/hero6.jpg",alt:"KNUST Department of Medical Imaging",className:"w-full h-full object-cover"})})})]})})})]}),e.jsx("footer",{className:"bg-card/80 border-t border-border/40 py-12",children:e.jsx("div",{className:"container mx-auto px-4 sm:px-6",children:e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[e.jsxs("div",{className:"mb-6 md:mb-0",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[e.jsx(Te,{className:"h-6 w-6 text-primary"}),e.jsx("span",{className:"text-xl font-bold",children:"e4mi"})]}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"eLearning for Medical Imaging Workforce"})]}),e.jsx("div",{className:"mb-6 md:mb-0 text-center md:text-left",children:e.jsxs("p",{className:"text-sm text-muted-foreground",children:["Department of Medical Imaging",e.jsx("br",{}),"Kwame Nkrumah University of Science and Technology"]})}),e.jsx("div",{className:"text-center md:text-right",children:e.jsxs("p",{className:"text-sm text-muted-foreground",children:["© ",new Date().getFullYear()," KNUST. All rights reserved."]})})]})})})]})})},xe={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8",xl:"h-12 w-12"},De={sm:"text-sm",md:"text-base",lg:"text-lg",xl:"text-xl"},Hr=({size:t="md",variant:s="default",className:r,text:a})=>s==="overlay"?e.jsx("div",{className:"fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50",children:e.jsxs("div",{className:"flex flex-col items-center gap-3",children:[e.jsx(ne,{className:_("animate-spin text-primary",xe[t])}),a&&e.jsx("p",{className:_("text-muted-foreground font-medium",De[t]),children:a})]})}):s==="minimal"?e.jsx(ne,{className:_("animate-spin text-primary",xe[t],r)}):e.jsx("div",{className:_("flex items-center justify-center",r),children:e.jsxs("div",{className:"flex flex-col items-center gap-3",children:[e.jsx(ne,{className:_("animate-spin text-primary",xe[t])}),a&&e.jsx("p",{className:_("text-muted-foreground font-medium",De[t]),children:a})]})}),N=({text:t="Loading..."})=>e.jsx("div",{className:"flex items-center justify-center min-h-screen bg-background",children:e.jsx(Hr,{size:"lg",text:t})}),Jr=n.lazy(()=>b(()=>import("./Login.D9BFbxWH.js"),__vite__mapDeps([4,2,1,3]))),Xr=n.lazy(()=>b(()=>import("./ResetPassword.Cs9USIZW.js"),__vite__mapDeps([5,2,1,3]))),Me=n.lazy(()=>b(()=>import("./Dashboard.Cc5az03q.js"),__vite__mapDeps([6,2,1,7,8,9,10,11,12,13,14,3]))),Zr=n.lazy(()=>b(()=>import("./ModuleContent.gv_-rfab.js"),__vite__mapDeps([15,2,1,7,8,9,10,16,17,18,19,20,21,22,3,23,24,11,12,13,25]))),ea=n.lazy(()=>b(()=>import("./ModuleManagementPage.DXOiTvi6.js"),__vite__mapDeps([26,2,1,7,8,27,28,29,23,24,11,30,13,3]))),ta=n.lazy(()=>b(()=>import("./ModulesPage.C6n5RnQl.js"),__vite__mapDeps([31,2,1,7,8,16,32,33,21,22,3,19,20,23,24,11,13]))),sa=n.lazy(()=>b(()=>import("./ModuleTestPage.BPl7sTnK.js"),__vite__mapDeps([34,2,1,7,8,33,35,23,24,11,29,36,13,3]))),ra=n.lazy(()=>b(()=>import("./LessonContent.CfhFGPRt.js"),__vite__mapDeps([37,2,1,7,8,23,24,11,12,10,13,38,17,18,29,28,22,30,21,3,39,40]))),aa=n.lazy(()=>b(()=>import("./NotFound.D7YCCh6G.js"),__vite__mapDeps([41,2,1]))),oa=n.lazy(()=>b(()=>import("./Admin.CC-kr3nO.js"),__vite__mapDeps([42,2,1,7,8,43,28,36,11,14,3,27,32,10,29,30,33,35,24,44,17,18,45,46,16]))),na=n.lazy(()=>b(()=>import("./CertificatePage.CeFjybHI.js"),__vite__mapDeps([47,1,2,7,8,23,24,11,20,13,3]))),ia=n.lazy(()=>b(()=>import("./EditorDemo.BB8Qx_Xq.js"),__vite__mapDeps([48,2,1,44,43,28,17,18,45,29,36,3]))),la=n.lazy(()=>b(()=>import("./MarkdownEditorDemo.BPMRzgsL.js"),__vite__mapDeps([49,2,1,43,28,50,18,45,29,36,3]))),ca=n.lazy(()=>b(()=>import("./MarkdownDebug.Dlt2-8yI.js"),__vite__mapDeps([51,2,1,50,18,43,17,29,3]))),da=n.lazy(()=>b(()=>import("./TableTest.-_qIgXdX.js"),__vite__mapDeps([52,2,1,17,18,3,40]))),ua=n.lazy(()=>b(()=>import("./MarkdownEditorTest.aGsVYXMm.js"),__vite__mapDeps([53,2,1,54,43,17,18,45,29,36,3]))),ma=n.lazy(()=>b(()=>import("./TableRenderingTest.CUlbKRDO.js"),__vite__mapDeps([55,2,1,7,8,54,43,17,18,45,38,29,36,3]))),ha=n.lazy(()=>b(()=>import("./MarkdownTableTest.BWvqDVg7.js"),__vite__mapDeps([56,2,1,17,18,3]))),pa=n.lazy(()=>b(()=>import("./TableParsingVerification.Cs7ZuQbt.js"),__vite__mapDeps([57,2,1,17,18,3]))),fa=n.lazy(()=>b(()=>import("./AchievementsPage.DYWWrx_E.js"),__vite__mapDeps([58,2,1,7,8,29,43,36,22,20,13,3]))),ga=n.lazy(()=>b(()=>import("./TestLessonUI.BR2dGiiK.js"),__vite__mapDeps([59,2,1,7,8,38,17,18,13,3]))),xa=n.lazy(()=>b(()=>import("./SpacingTestPage.B3CFvju1.js"),__vite__mapDeps([60,2,1,7,8,38,17,18,13,3]))),ya=n.lazy(()=>b(()=>import("./DebugLesson.BBeHXqq7.js"),__vite__mapDeps([61,2,1,23,24,11,3]))),ba=n.lazy(()=>b(()=>import("./ImageDebugTest.DOOC9h5o.js"),__vite__mapDeps([62,2,1,17,18,3]))),va=n.lazy(()=>b(()=>import("./LessonRedirect.LOlzeWdP.js"),__vite__mapDeps([63,2,1,3]))),wa=n.lazy(()=>b(()=>import("./YouTubeEditorTest.DkqlUuny.js"),__vite__mapDeps([64,2,1,44,43,28,17,18,45,29,36,3]))),ja=n.lazy(()=>b(()=>import("./AuthCallback.CF4zthXy.js"),__vite__mapDeps([65,2,1,3]))),Na=n.lazy(()=>b(()=>import("./CompletionDiagnostics.Dd73kFC9.js"),__vite__mapDeps([66,2,1,7,8,29,43,21,22,3]))),Ea=n.lazy(()=>b(()=>import("./TestAnalyticsPage.9U_HeWPy.js"),__vite__mapDeps([67,2,1,7,8,46,29,36,35,16,3]))),_a=()=>{"requestIdleCallback"in window?window.requestIdleCallback(()=>{const t=window.location.pathname;t==="/"?b(()=>import("./Login.D9BFbxWH.js"),__vite__mapDeps([4,2,1,3])):t.includes("/dashboard")?b(()=>import("./Dashboard.Cc5az03q.js"),__vite__mapDeps([6,2,1,7,8,9,10,11,12,13,14,3])):t.includes("/course")&&b(()=>import("./ModuleContent.gv_-rfab.js"),__vite__mapDeps([15,2,1,7,8,9,10,16,17,18,19,20,21,22,3,23,24,11,12,13,25]))},{timeout:5e3}):setTimeout(()=>{const t=window.location.pathname;t==="/"?b(()=>import("./Login.D9BFbxWH.js"),__vite__mapDeps([4,2,1,3])):t.includes("/dashboard")?b(()=>import("./Dashboard.Cc5az03q.js"),__vite__mapDeps([6,2,1,7,8,9,10,11,12,13,14,3])):t.includes("/course")&&b(()=>import("./ModuleContent.gv_-rfab.js"),__vite__mapDeps([15,2,1,7,8,9,10,16,17,18,19,20,21,22,3,23,24,11,12,13,25]))},3e3)},ka=new Rs({defaultOptions:{queries:{refetchOnWindowFocus:!1,retry:!1,staleTime:10*60*1e3,refetchOnMount:!1,retryDelay:1e3},mutations:{retry:!1}}});bs({dsn:"YOUR_SENTRY_DSN",integrations:[new Os],tracesSampleRate:.2,environment:"production"});const Sa="yourdomain.com",Ca=()=>(n.useEffect(()=>{if(window.location.hostname!=="localhost"){const a=document.createElement("script");a.setAttribute("defer",""),a.setAttribute("data-domain",Sa),a.src="https://plausible.io/js/plausible.js",document.body.appendChild(a)}document.documentElement.classList.add("loaded");const t=setTimeout(()=>{const a=document.getElementById("initial-loader");a&&a.parentNode&&(a.parentNode.removeChild(a),console.log("Loader forcibly removed after timeout"))},2e3),s=document.getElementById("initial-loader");s&&(s.style.opacity="0",setTimeout(()=>{s.parentNode&&(s.parentNode.removeChild(s),console.log("Loader removed normally"),clearTimeout(t))},300)),setTimeout(_a,1500);const r=()=>{document.documentElement.classList.add("fully-loaded"),console.log("Document fully loaded")};return window.addEventListener("load",r),()=>{clearTimeout(t),window.removeEventListener("load",r)}},[]),e.jsx(vs,{fallback:e.jsx("div",{children:"Something went wrong. Please refresh the page."}),showDialog:!0,children:e.jsxs(sr,{defaultTheme:"system",children:[e.jsx(qr,{}),e.jsx(ar,{children:e.jsx(ws,{client:ka,children:e.jsxs(Us,{children:[e.jsx(Ms,{}),e.jsx(Ar,{}),e.jsx(Mr,{}),e.jsx(Ur,{}),e.jsx(js,{children:e.jsx(tr,{children:e.jsx(Tr,{children:e.jsxs(Ns,{children:[e.jsx(j,{path:"/",element:e.jsx(Gr,{})}),e.jsx(j,{path:"/login",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading login..."}),children:e.jsx(Jr,{})})}),e.jsx(j,{path:"/reset-password",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading..."}),children:e.jsx(Xr,{})})}),e.jsx(j,{path:"/auth/callback",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Authenticating..."}),children:e.jsx(ja,{})})}),e.jsx(j,{path:"/dashboard",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading dashboard..."}),children:e.jsx(C,{children:e.jsx(zr,{children:e.jsx(Me,{})})})})}),e.jsx(j,{path:"/my-courses",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading courses..."}),children:e.jsx(C,{children:e.jsx(Me,{})})})}),e.jsx(j,{path:"/achievements",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading achievements..."}),children:e.jsx(C,{children:e.jsx(fa,{})})})}),e.jsx(j,{path:"/course/:courseId",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading course..."}),children:e.jsx(C,{children:e.jsx(Zr,{})})})}),e.jsx(j,{path:"/course/:courseId/modules",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading modules..."}),children:e.jsx(C,{children:e.jsx(ta,{})})})}),e.jsx(j,{path:"/course/:courseId/lesson/:lessonId",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading lesson..."}),children:e.jsx(C,{children:e.jsx(ra,{})})})}),e.jsx(j,{path:"/lesson/:lessonSlug",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Redirecting to lesson..."}),children:e.jsx(C,{children:e.jsx(va,{})})})}),e.jsx(j,{path:"/course/:courseId/module/:moduleId",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading module test..."}),children:e.jsx(C,{children:e.jsx(sa,{})})})}),e.jsx(j,{path:"/certificate/:courseId",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading certificate..."}),children:e.jsx(C,{children:e.jsx(na,{})})})}),e.jsx(j,{path:"/editor-demo",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading editor demo..."}),children:e.jsx(C,{children:e.jsx(ia,{})})})}),e.jsx(j,{path:"/markdown-editor-demo",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading markdown editor demo..."}),children:e.jsx(C,{children:e.jsx(la,{})})})}),e.jsx(j,{path:"/markdown-debug",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading markdown debug..."}),children:e.jsx(C,{children:e.jsx(ca,{})})})}),e.jsx(j,{path:"/table-test",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading table test..."}),children:e.jsx(C,{children:e.jsx(da,{})})})}),e.jsx(j,{path:"/markdown-editor-test",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading markdown editor test..."}),children:e.jsx(C,{children:e.jsx(ua,{})})})}),e.jsx(j,{path:"/table-rendering-test",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading table rendering test..."}),children:e.jsx(C,{children:e.jsx(ma,{})})})}),e.jsx(j,{path:"/markdown-table-test",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading markdown table test..."}),children:e.jsx(C,{children:e.jsx(ha,{})})})}),e.jsx(j,{path:"/table-parsing-verification",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading table parsing verification..."}),children:e.jsx(C,{children:e.jsx(pa,{})})})}),e.jsx(j,{path:"/test-lesson-ui",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading lesson UI test..."}),children:e.jsx(C,{children:e.jsx(ga,{})})})}),e.jsx(j,{path:"/spacing-test",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading spacing test..."}),children:e.jsx(C,{children:e.jsx(xa,{})})})}),e.jsx(j,{path:"/debug-lesson",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading debug lesson..."}),children:e.jsx(C,{children:e.jsx(ya,{})})})}),e.jsx(j,{path:"/image-debug-test",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading image debug test..."}),children:e.jsx(C,{children:e.jsx(ba,{})})})}),e.jsx(j,{path:"/youtube-editor-test",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading YouTube editor test..."}),children:e.jsx(C,{children:e.jsx(wa,{})})})}),e.jsx(j,{path:"/admin",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading admin panel..."}),children:e.jsx(C,{children:e.jsx(oe,{requiredRole:"teacher",children:e.jsx(oa,{})})})})}),e.jsx(j,{path:"/module-management/:courseId",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading module management..."}),children:e.jsx(C,{children:e.jsx(oe,{requiredRole:"teacher",children:e.jsx(ea,{})})})})}),e.jsx(j,{path:"/admin/progress",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading diagnostics..."}),children:e.jsx(C,{children:e.jsx(oe,{requiredRole:"admin",children:e.jsx(Na,{})})})})}),e.jsx(j,{path:"/admin/analytics",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading test analytics..."}),children:e.jsx(C,{children:e.jsx(oe,{requiredRole:"teacher",children:e.jsx(Ea,{})})})})}),e.jsx(j,{path:"*",element:e.jsx(n.Suspense,{fallback:e.jsx(N,{text:"Loading..."}),children:e.jsx(aa,{})})})]})})})}),e.jsx(Br,{})]})})})]})}));function Ia(){return p(this,null,function*(){try{const{data:t,error:s}=yield y.from("modules").select("image_url").not("image_url","is",null);if(s)throw s;const r=new Set((t==null?void 0:t.map(d=>d.image_url))||[]),{data:a,error:o}=yield y.storage.from("module-images").list();if(o)throw o;if(!a)return;const i=a.filter(d=>{const u=y.storage.from("module-images").getPublicUrl(d.name).data.publicUrl;return!r.has(u)});for(const d of i)yield y.storage.from("module-images").remove([d.name]),console.log(`Deleted orphaned file: ${d.name}`)}catch(t){console.error("Error cleaning up orphaned module images:",t)}})}class we{static startScheduledCleanup(s=24*60*60*1e3){if(this.isRunning){console.log("Cleanup tasks are already running");return}this.runAllCleanupTasks(),this.runInterval=window.setInterval(()=>{this.runAllCleanupTasks()},s),this.isRunning=!0,console.log(`Scheduled cleanup tasks started, running every ${s/(60*60*1e3)} hours`)}static stopScheduledCleanup(){if(!this.isRunning||this.runInterval===null){console.log("No cleanup tasks are running");return}clearInterval(this.runInterval),this.runInterval=null,this.isRunning=!1,console.log("Scheduled cleanup tasks stopped")}static runAllCleanupTasks(){return p(this,null,function*(){console.log("Running all cleanup tasks...");try{yield this.cleanupModuleImages(),console.log("All cleanup tasks completed successfully")}catch(s){console.error("Error running cleanup tasks:",s)}})}static cleanupModuleImages(){return p(this,null,function*(){console.log("Cleaning up orphaned module images...");try{const{data:{user:s}}=yield y.auth.getUser();if(!s){console.log("User not authenticated, skipping module image cleanup");return}const{data:r}=yield y.from("user_roles").select("role").eq("user_id",s.id).single();if(!r||r.role!=="admin"){console.log("User does not have admin rights, skipping module image cleanup");return}yield Ia(),console.log("Module image cleanup completed")}catch(s){console.error("Error cleaning up module images:",s)}})}}re(we,"isRunning",!1),re(we,"runInterval",null);function Ta(){return p(this,null,function*(){const t=[];for(let s=1;s<=7;s++){const r=`/images/m${s}.jpg`;try{(yield fetch(r,{method:"HEAD"})).ok||(console.warn(`Module image ${r} does not exist`),t.push(r))}catch(a){console.error(`Error checking module image ${r}:`,a),t.push(r)}}try{const s="/images/module-placeholder.jpg";(yield fetch(s,{method:"HEAD"})).ok||(console.warn(`Fallback image ${s} does not exist`),t.push(s))}catch(s){console.error("Error checking fallback image:",s),t.push("/images/module-placeholder.jpg")}return{exists:t.length===0,missing:t}})}function Aa(t){return p(this,null,function*(){const{exists:s,missing:r}=yield Ta();s||(t({title:"Missing module images",description:`Please add the following images to the public/images folder: ${r.join(", ")}`,duration:1e4,variant:"destructive"}),console.error("Missing module images. Add the following files to public/images/:",r))})}const qa=performance.now();Vr();we.startScheduledCleanup(),console.log("Image cleanup tasks scheduled");window.addEventListener("load",()=>{setTimeout(()=>{Aa(g)},2e3)});window.addEventListener("error",t=>{console.error("Runtime error caught:",t.error);const s=document.getElementById("initial-loader");s&&s.parentNode&&(s.parentNode.removeChild(s),console.log("Loader removed due to initialization error"));const r=document.getElementById("root");r&&(r.innerHTML=`
      <div style={{
        fontFamily: "'Poppins', system-ui, sans-serif",
        padding: '2rem',
        textAlign: 'center'
      }}>
        <h2>Application Error</h2>
        <p>The application failed to initialize. Please try refreshing the page.</p>
        <button onclick="window.location.reload()" style="padding: 0.5rem 1rem; background: #E63946; color: white; border: none; border-radius: 4px; cursor: pointer;">
          Refresh Page
        </button>
      </div>
    `)});const de=document.getElementById("root");if(!de){console.error("Root element not found! Make sure there is a div with id 'root' in your HTML.");const t=document.createElement("div");t.id="root",document.body.appendChild(t),console.log("Created missing root element")}try{const t=Es(de||document.body);(()=>{t.render(e.jsx(Ca,{}))})()}catch(t){console.error("Failed to initialize React:",t),de&&(de.innerHTML=`
      <div style={{
        fontFamily: "'Poppins', system-ui, sans-serif",
        padding: '2rem',
        textAlign: 'center'
      }}>
        <h2>Application Error</h2>
        <p>The application failed to initialize. Please try refreshing the page.</p>
        <button onclick="window.location.reload()" style="padding: 0.5rem 1rem; background: #E63946; color: white; border: none; border-radius: 4px; cursor: pointer;">
          Refresh Page
        </button>
      </div>
    `);const s=document.getElementById("initial-loader");s&&s.parentNode&&s.parentNode.removeChild(s)}window.addEventListener("online",()=>{g.success("You are back online")});window.addEventListener("offline",()=>{g.error("You are offline. Some features may be limited.")});{const t=()=>{if("performance"in window&&"getEntriesByType"in performance){const s=performance.getEntriesByType("navigation")[0];if(s){console.log(`Time to Interactive: ${s.domInteractive}ms`),console.log(`DOM Content Loaded: ${s.domContentLoadedEventEnd}ms`),console.log(`Load Complete: ${s.loadEventEnd}ms`);const r=window;"gtag"in r&&typeof r.gtag=="function"&&r.gtag("event","performance",{time_to_interactive:s.domInteractive,dom_content_loaded:s.domContentLoadedEventEnd,load_complete:s.loadEventEnd})}}};window.addEventListener("load",()=>setTimeout(t,100))}export{je as A,O as B,it as C,ut as D,be as I,Kr as M,Mr as O,N as P,Ua as S,Us as T,kt as a,$r as b,_ as c,Da as d,Ma as e,Vs as f,jr as g,Ne as h,ht as i,Ee as j,pt as k,ir as l,st as m,hr as n,ft as o,pr as p,yt as q,bt as r,y as s,F as t,te as u,B as v,za as w,Fa as x,lr as y,Va as z};
