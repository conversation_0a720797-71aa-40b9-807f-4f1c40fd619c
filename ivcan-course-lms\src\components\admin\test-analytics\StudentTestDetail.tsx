import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Download, Loader2, User, Calendar, FileText } from 'lucide-react';
import { getDetailedTestResponse } from '@/services/test-analytics/testAnalyticsService';
import { format } from 'date-fns';
import { ratingDescriptions } from '@/types/module-test';
import TestResponsePDF from './TestResponsePDF';

interface StudentTestDetailProps {
  responseId: string;
  onBack: () => void;
}

const StudentTestDetail: React.FC<StudentTestDetailProps> = ({ responseId, onBack }) => {
  const { data: testResponse, isLoading, error } = useQuery({
    queryKey: ['detailed-test-response', responseId],
    queryFn: () => getDetailedTestResponse(responseId),
    refetchOnWindowFocus: false
  });

  const handleDownloadPDF = async () => {
    if (testResponse) {
      try {
        // Import the PDF generation function
        const { generateTestResponsePDF } = await import('./TestResponsePDF');

        // Generate and download PDF
        await generateTestResponsePDF({
          student: testResponse.student,
          testResponse,
          generatedAt: new Date().toISOString()
        });
      } catch (error) {
        console.error('Error generating PDF:', error);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="w-8 h-8 animate-spin text-primary" />
        <span className="ml-2">Loading test details...</span>
      </div>
    );
  }

  if (error || !testResponse) {
    return (
      <div className="space-y-4">
        <Button variant="ghost" onClick={onBack} className="mb-4">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Analytics
        </Button>
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="text-destructive">Error Loading Test Details</CardTitle>
            <CardDescription>
              Failed to load test response details. Please try again.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  const getRatingLabel = (rating: number) => {
    const option = ratingDescriptions.find(desc => desc.value === rating);
    return option ? option.label : `Rating ${rating}`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={onBack}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Analytics
        </Button>
        <Button onClick={handleDownloadPDF} className="flex items-center">
          <Download className="w-4 h-4 mr-2" />
          Download PDF Report
        </Button>
      </div>

      {/* Student and Test Information */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Student Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="w-5 h-5 mr-2" />
              Student Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <div className="font-medium">{testResponse.student.fullName}</div>
              <div className="text-sm text-muted-foreground">{testResponse.student.email}</div>
            </div>
          </CardContent>
        </Card>

        {/* Test Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="w-5 h-5 mr-2" />
              Test Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <div className="font-medium">{testResponse.test.title}</div>
              <Badge variant={testResponse.test.type === 'pre_test' ? 'default' : 'secondary'}>
                {testResponse.test.type === 'pre_test' ? 'Pre-Test' : 'Post-Test'}
              </Badge>
            </div>
            <div className="text-sm text-muted-foreground">
              <div>{testResponse.test.module.course.title}</div>
              <div>{testResponse.test.module.title}</div>
            </div>
          </CardContent>
        </Card>

        {/* Submission Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              Submission Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <div className="text-sm text-muted-foreground">Submitted</div>
              <div className="font-medium">
                {format(new Date(testResponse.createdAt), 'MMM dd, yyyy')}
              </div>
              <div className="text-sm text-muted-foreground">
                {format(new Date(testResponse.createdAt), 'HH:mm')}
              </div>
            </div>
            <div>
              <div className="text-sm text-muted-foreground">Questions Answered</div>
              <div className="font-medium">{testResponse.responses.length}</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Test Description */}
      {testResponse.test.description && (
        <Card>
          <CardHeader>
            <CardTitle>Test Description</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">{testResponse.test.description}</p>
          </CardContent>
        </Card>
      )}

      {/* Responses */}
      <Card>
        <CardHeader>
          <CardTitle>Student Responses</CardTitle>
          <CardDescription>
            Detailed answers for each question in the test
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {testResponse.responses
              .sort((a, b) => a.question.questionNumber - b.question.questionNumber)
              .map((response, index) => (
                <div key={response.questionId}>
                  <div className="space-y-4">
                    {/* Question */}
                    <div>
                      <div className="flex items-start space-x-3">
                        <Badge variant="outline" className="mt-1">
                          Q{response.question.questionNumber}
                        </Badge>
                        <div className="flex-1">
                          <p className="font-medium leading-relaxed">
                            {response.question.question}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Answer */}
                    <div className="ml-12">
                      <div className="bg-muted/50 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium text-primary">
                              {getRatingLabel(response.rating)}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              Rating: {response.rating} out of {response.question.maxRating}
                            </div>
                          </div>
                          <Badge variant="secondary">
                            {response.rating}/{response.question.maxRating}
                          </Badge>
                        </div>
                        
                        {/* Rating Scale Reference */}
                        <div className="mt-3 pt-3 border-t border-border">
                          <div className="flex justify-between text-xs text-muted-foreground">
                            <span>{response.question.minLabel}</span>
                            <span>{response.question.maxLabel}</span>
                          </div>
                          <div className="flex justify-between mt-1">
                            {Array.from({ length: response.question.maxRating }, (_, i) => (
                              <div
                                key={i + 1}
                                className={`w-6 h-6 rounded-full border-2 flex items-center justify-center text-xs ${
                                  i + 1 === response.rating
                                    ? 'border-primary bg-primary text-white'
                                    : 'border-muted-foreground/30'
                                }`}
                              >
                                {i + 1}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {index < testResponse.responses.length - 1 && (
                    <div className="border-t border-gray-200 my-6" />
                  )}
                </div>
              ))}
          </div>
        </CardContent>
      </Card>

      {/* Hidden PDF Component for generation */}
      <div className="hidden">
        <TestResponsePDF
          student={testResponse.student}
          testResponse={testResponse}
          generatedAt={new Date().toISOString()}
        />
      </div>
    </div>
  );
};

export default StudentTestDetail;
