-- Fix certificates functionality

-- Make sure the completed_at column exists in user_course_enrollment
ALTER TABLE IF EXISTS public.user_course_enrollment 
ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP WITH TIME ZONE;

-- Update existing completed enrollments that don't have a completed_at date
UPDATE public.user_course_enrollment
SET completed_at = updated_at
WHERE status = 'completed' AND completed_at IS NULL;

-- Fix RLS policies to ensure proper access
DROP POLICY IF EXISTS "Anyone can view course enrollments" ON public.user_course_enrollment;
DROP POLICY IF EXISTS "Anyone can insert course enrollments" ON public.user_course_enrollment;
DROP POLICY IF EXISTS "Anyone can update course enrollments" ON public.user_course_enrollment;

-- Create more specific policies
CREATE POLICY "Users can view their own enrollments" 
ON public.user_course_enrollment FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own enrollments" 
ON public.user_course_enrollment FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own enrollments" 
ON public.user_course_enrollment FOR UPDATE 
USING (auth.uid() = user_id);

-- Teachers can view all enrollments
CREATE POLICY "Teachers can view all enrollments" 
ON public.user_course_enrollment FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE profiles.id = auth.uid() AND profiles.role = 'teacher'
  )
);

-- Create or replace the complete_course function
CREATE OR REPLACE FUNCTION public.complete_course(p_user_id UUID, p_course_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_enrollment_exists BOOLEAN;
  v_now TIMESTAMP WITH TIME ZONE := NOW();
BEGIN
  -- Check if enrollment exists
  SELECT EXISTS (
    SELECT 1 FROM public.user_course_enrollment
    WHERE user_id = p_user_id AND course_id = p_course_id
  ) INTO v_enrollment_exists;
  
  IF v_enrollment_exists THEN
    -- Update existing enrollment
    UPDATE public.user_course_enrollment
    SET 
      status = 'completed',
      completed_at = v_now,
      updated_at = v_now
    WHERE 
      user_id = p_user_id AND 
      course_id = p_course_id;
  ELSE
    -- Create new enrollment
    INSERT INTO public.user_course_enrollment (
      user_id,
      course_id,
      status,
      enrolled_at,
      completed_at,
      updated_at
    ) VALUES (
      p_user_id,
      p_course_id,
      'completed',
      v_now,
      v_now,
      v_now
    );
  END IF;
  
  -- Also update user_course_progress
  INSERT INTO public.user_course_progress (
    user_id,
    course_id,
    completed_modules,
    updated_at
  ) VALUES (
    p_user_id,
    p_course_id,
    100,
    v_now
  )
  ON CONFLICT (user_id, course_id) 
  DO UPDATE SET
    completed_modules = 100,
    updated_at = v_now;
    
  RETURN TRUE;
END;
$$;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.complete_course TO authenticated;
GRANT EXECUTE ON FUNCTION public.complete_course TO anon;
GRANT EXECUTE ON FUNCTION public.complete_course TO service_role;
