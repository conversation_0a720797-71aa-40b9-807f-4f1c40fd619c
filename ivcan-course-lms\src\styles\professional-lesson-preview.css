/* Professional Lesson Preview Styles */

.professional-lesson-preview {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  font-size: 16px;
  line-height: 1.7;
  color: var(--foreground);
  max-width: none;
  padding: 2rem;
  background: var(--background);
}

/* Professional Typography */
.professional-lesson-preview h1,
.professional-lesson-preview h2,
.professional-lesson-preview h3,
.professional-lesson-preview h4,
.professional-lesson-preview h5,
.professional-lesson-preview h6 {
  font-weight: 600;
  line-height: 1.3;
  margin: 2.5rem 0 1rem 0;
  color: var(--foreground);
  scroll-margin-top: 2rem;
}

.professional-lesson-preview h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-top: 0;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 3px solid var(--primary);
  background: linear-gradient(135deg, var(--primary), var(--primary)/80%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.professional-lesson-preview h2 {
  font-size: 2rem;
  margin-top: 3rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--border);
  position: relative;
}

.professional-lesson-preview h2::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background: var(--primary);
}

.professional-lesson-preview h3 {
  font-size: 1.5rem;
  color: var(--primary);
}

.professional-lesson-preview h4 {
  font-size: 1.25rem;
  color: var(--muted-foreground);
}

.professional-lesson-preview h5 {
  font-size: 1.1rem;
}

.professional-lesson-preview h6 {
  font-size: 1rem;
  color: var(--muted-foreground);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Professional Paragraphs */
.professional-lesson-preview p {
  margin: 1.5rem 0;
  line-height: 1.8;
  text-align: justify;
  hyphens: auto;
}

.professional-lesson-preview p:first-child {
  margin-top: 0;
}

.professional-lesson-preview p:last-child {
  margin-bottom: 0;
}

/* Professional Lists */
.professional-lesson-preview ul,
.professional-lesson-preview ol {
  margin: 1.5rem 0;
  padding-left: 2rem;
  line-height: 1.7;
}

.professional-lesson-preview li {
  margin: 0.75rem 0;
  position: relative;
}

.professional-lesson-preview ul li::marker {
  color: var(--primary);
  font-weight: bold;
}

.professional-lesson-preview ol li::marker {
  color: var(--primary);
  font-weight: 600;
}

/* Professional Task Lists */
.professional-task-item {
  display: flex;
  align-items: flex-start;
  margin: 1rem 0;
  padding: 0.75rem;
  border-radius: 8px;
  background: var(--muted)/30;
  border: 1px solid var(--border);
  transition: all 0.2s ease;
}

.professional-task-item:hover {
  background: var(--muted)/50;
  border-color: var(--primary)/30;
  transform: translateX(4px);
}

.professional-task-item[data-indent="1"] {
  margin-left: 2rem;
}

.professional-task-item[data-indent="2"] {
  margin-left: 4rem;
}

.task-checkbox-wrapper {
  position: relative;
  margin-right: 1rem;
  margin-top: 0.1rem;
}

.professional-checkbox {
  opacity: 0;
  position: absolute;
  width: 20px;
  height: 20px;
}

.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 20px;
  width: 20px;
  background-color: var(--background);
  border: 2px solid var(--border);
  border-radius: 4px;
  transition: all 0.2s ease;
}

.professional-checkbox:checked + .checkmark {
  background-color: var(--primary);
  border-color: var(--primary);
}

.professional-checkbox:checked + .checkmark::after {
  content: '';
  position: absolute;
  left: 6px;
  top: 2px;
  width: 6px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.task-text {
  flex: 1;
  font-weight: 500;
  transition: all 0.2s ease;
}

.task-text.completed {
  text-decoration: line-through;
  color: var(--muted-foreground);
  opacity: 0.7;
}

/* Professional Tables */
.professional-table-wrapper {
  margin: 0.75rem 0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--border);
}

.professional-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--background);
  font-size: 0.95rem;
}

.table-header-row {
  background: linear-gradient(135deg, var(--primary)/10%, var(--primary)/5%);
  border-bottom: 2px solid var(--primary)/20%;
}

.table-header-cell {
  padding: 1.25rem 1.5rem;
  text-align: left;
  font-weight: 600;
  color: var(--primary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-size: 0.85rem;
  border-right: 1px solid var(--border);
}

.table-header-cell:last-child {
  border-right: none;
}

.table-row {
  transition: all 0.2s ease;
  border-bottom: 1px solid var(--border);
}

.table-row:hover,
.table-row.hovered {
  background: var(--muted)/30;
  transform: scale(1.01);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  padding: 1rem 1.5rem;
  border-right: 1px solid var(--border);
  vertical-align: top;
  line-height: 1.6;
}

.table-cell:last-child {
  border-right: none;
}

/* Professional Accordions */
.professional-accordion {
  margin: 2rem 0;
  border: 1px solid var(--border);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.professional-accordion:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: var(--primary)/30%;
}

.professional-accordion.expanded {
  border-color: var(--primary)/50%;
}

.accordion-details {
  border: none;
}

.accordion-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg, var(--muted)/50%, var(--muted)/30%);
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
  list-style: none;
  border-bottom: 1px solid var(--border);
}

.accordion-summary:hover {
  background: linear-gradient(135deg, var(--muted)/70%, var(--muted)/50%);
}

.accordion-summary::-webkit-details-marker {
  display: none;
}

.accordion-title {
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--foreground);
}

.accordion-icon {
  display: flex;
  align-items: center;
  color: var(--primary);
}

.chevron-down {
  transition: transform 0.3s ease;
}

.accordion-content {
  background: var(--background);
  border-top: 1px solid var(--border);
  animation: accordion-expand 0.3s ease-out;
}

.accordion-inner {
  padding: 2rem;
  line-height: 1.7;
}

/* Accordion animation */
@keyframes accordion-expand {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Ensure accordion content is properly styled */
.professional-accordion .accordion-inner > *:first-child {
  margin-top: 0;
}

.professional-accordion .accordion-inner > *:last-child {
  margin-bottom: 0;
}

/* Professional Callouts */
.professional-callout {
  margin: 2rem 0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border);
  transition: all 0.2s ease;
}

.professional-callout:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.callout-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1.25rem 1.5rem;
  font-weight: 600;
  border-bottom: 1px solid var(--border);
}

.callout-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.callout-title {
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 700;
}

.callout-content {
  padding: 1.5rem;
  line-height: 1.7;
}

/* Callout Types */
.callout-info {
  background: linear-gradient(135deg, hsl(var(--primary) / 0.05), hsl(var(--primary) / 0.02));
  border-left: 4px solid hsl(var(--primary));
}

.callout-info .callout-header {
  background: hsl(var(--primary) / 0.1);
  color: hsl(var(--primary));
}

.callout-warning {
  background: linear-gradient(135deg, hsl(var(--warning) / 0.05), hsl(var(--warning) / 0.02));
  border-left: 4px solid hsl(var(--warning));
}

.callout-warning .callout-header {
  background: hsl(var(--warning) / 0.1);
  color: hsl(var(--warning));
}

.callout-success {
  background: linear-gradient(135deg, hsl(142 76% 36% / 0.05), hsl(142 76% 36% / 0.02));
  border-left: 4px solid hsl(142 76% 36%);
}

.callout-success .callout-header {
  background: hsl(142 76% 36% / 0.1);
  color: hsl(142 76% 36%);
}

.callout-error {
  background: linear-gradient(135deg, hsl(var(--destructive) / 0.05), hsl(var(--destructive) / 0.02));
  border-left: 4px solid hsl(var(--destructive));
}

.callout-error .callout-header {
  background: hsl(var(--destructive) / 0.1);
  color: hsl(var(--destructive));
}

.callout-tip,
.callout-note,
.callout-important,
.callout-caution {
  background: linear-gradient(135deg, hsl(271 91% 65% / 0.05), hsl(271 91% 65% / 0.02));
  border-left: 4px solid hsl(271 91% 65%);
}

.callout-tip .callout-header,
.callout-note .callout-header,
.callout-important .callout-header,
.callout-caution .callout-header {
  background: hsl(271 91% 65% / 0.1);
  color: hsl(271 91% 65%);
}

/* Professional Images */
.professional-image-figure {
  margin: 2.5rem 0;
  text-align: center;
}

.professional-image {
  max-width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  transition: all 0.3s ease;
  cursor: zoom-in;
  border: 1px solid var(--border);
}

.professional-image:hover {
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.18);
}

.image-caption {
  margin-top: 1rem;
  font-size: 0.9rem;
  color: var(--muted-foreground);
  font-style: italic;
  line-height: 1.5;
}

/* Professional Highlights */
.professional-highlight {
  background: linear-gradient(135deg, hsl(var(--warning) / 0.3), hsl(var(--warning) / 0.2));
  border-radius: 4px;
  padding: 0.2em 0.4em;
  color: var(--foreground);
  font-weight: 500;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Professional Code Blocks */
.professional-lesson-preview pre {
  margin: 2rem 0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid var(--border);
}

.professional-lesson-preview code {
  background: var(--muted);
  border-radius: 4px;
  padding: 0.2em 0.4em;
  font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.9em;
  font-weight: 500;
}

/* Professional Blockquotes */
.professional-lesson-preview blockquote {
  margin: 2rem 0;
  padding: 1.5rem 2rem;
  border-left: 4px solid var(--primary);
  background: linear-gradient(135deg, var(--muted)/30%, var(--muted)/10%);
  border-radius: 0 12px 12px 0;
  font-style: italic;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.professional-lesson-preview blockquote::before {
  content: '"';
  position: absolute;
  top: -10px;
  left: 10px;
  font-size: 4rem;
  color: var(--primary);
  opacity: 0.3;
  font-family: serif;
}

/* Professional Links */
.professional-lesson-preview a {
  color: var(--primary);
  text-decoration: none;
  font-weight: 500;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  position: relative;
}

.professional-lesson-preview a:hover {
  border-bottom-color: var(--primary);
  transform: translateY(-1px);
}

/* Professional Horizontal Rules */
.professional-lesson-preview hr {
  margin: 3rem 0;
  border: none;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--border), transparent);
  border-radius: 1px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .professional-lesson-preview {
    padding: 1rem;
    font-size: 15px;
  }
  
  .professional-lesson-preview h1 {
    font-size: 2rem;
  }
  
  .professional-lesson-preview h2 {
    font-size: 1.5rem;
  }
  
  .professional-lesson-preview h3 {
    font-size: 1.25rem;
  }
  
  .professional-table-wrapper {
    overflow-x: auto;
  }
  
  .table-header-cell,
  .table-cell {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
  
  .accordion-summary {
    padding: 1rem 1.5rem;
  }
  
  .accordion-inner {
    padding: 1.5rem;
  }
  
  .callout-header {
    padding: 1rem 1.25rem;
  }
  
  .callout-content {
    padding: 1.25rem;
  }
}

/* Dark Mode Enhancements */
.dark .professional-lesson-preview {
  color: var(--foreground);
}

.dark .professional-image {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.dark .professional-image:hover {
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.4);
}

.dark .professional-table-wrapper {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

.dark .professional-accordion:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.dark .professional-callout:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}
