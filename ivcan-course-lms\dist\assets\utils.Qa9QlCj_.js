var u=(e,t,r)=>new Promise((o,i)=>{var l=n=>{try{c(r.next(n))}catch(a){i(a)}},s=n=>{try{c(r.throw(n))}catch(a){i(a)}},c=n=>n.done?o(n.value):Promise.resolve(n.value).then(l,s);c((r=r.apply(e,t)).next())});import{s as d}from"./index.BLDhDn0D.js";import{a2 as E}from"./vendor.DQpuTRuB.js";const g=e=>{let t="lesson";return e.type==="quiz"?t="quiz":e.type==="assignment"&&(t="assignment"),{id:e.id,module_id:e.module_id,slug:e.slug,title:e.title,duration:e.duration,type:t,requirement:e.requirement||void 0,completed:e.completed||!1,content:e.content||void 0}},h=e=>{if(!e)return!1;try{return!!JSON.parse(e).questions}catch(t){return!1}},y=(e,t,r)=>u(void 0,null,function*(){try{const o=t.toLowerCase().replace(/[^a-z0-9]+/g,"-"),i=JSON.stringify({content:`<p>You will be redirected to an external form. <a href="${r}" target="_blank">Click here</a> if you are not redirected automatically.</p>`,externalRedirectUrl:r}),{data:l,error:s}=yield d.from("lessons").insert([{module_id:e,title:t,slug:o,duration:"15:00",type:"lesson",content:i,completed:!1}]).select();return s?(console.error("Error creating external redirect lesson:",s),null):g(l[0])}catch(o){return console.error("Error in createExternalRedirectLesson:",o),null}}),A=(e,t)=>u(void 0,null,function*(){try{const{data:r,error:o}=yield d.from("modules").select("id").eq("course_id",e);if(o)return console.error("Error counting modules:",o),!1;const i=((r==null?void 0:r.length)||0)+1,{data:l,error:s}=yield d.from("modules").insert([{course_id:e,title:"FINAL EXAMINATION",slug:"final-examination",module_number:i,is_locked:!1,is_completed:!1}]).select();if(s||!l||l.length===0)return console.error("Error creating FINAL EXAMINATION module:",s),!1;const c=l[0].id;if(!(yield y(c,"Exam",t)))return console.error("Error creating Exam lesson"),!1;try{const{data:a,error:f}=yield d.from("modules").select("id").eq("course_id",e);if(!f&&a){const p=a.length,{error:m}=yield d.from("courses").update({total_modules:p}).eq("id",e);m&&console.error("Error updating course total_modules:",m)}}catch(a){console.error("Error updating course total_modules:",a)}return E.success("Successfully added FINAL EXAMINATION module with Exam"),!0}catch(r){return console.error("Error in addFinalExamModule:",r),!1}});export{A as a,g as c,h as i};
