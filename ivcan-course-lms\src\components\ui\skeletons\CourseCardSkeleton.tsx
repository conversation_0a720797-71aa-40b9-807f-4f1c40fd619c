import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

interface CourseCardSkeletonProps {
  className?: string;
}

export function CourseCardSkeleton({ className }: CourseCardSkeletonProps) {
  return (
    <div className={cn("bg-card rounded-2xl overflow-hidden border border-border/40 shadow-sm", className)}>
      {/* Image area */}
      <Skeleton className="w-full h-48" />
      
      {/* Content area */}
      <div className="p-6 space-y-4">
        {/* Title */}
        <Skeleton className="h-7 w-3/4" />
        
        {/* Description */}
        <div className="space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-5/6" />
        </div>
        
        {/* Progress bar */}
        <div className="space-y-2">
          <div className="flex justify-between">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-10" />
          </div>
          <Skeleton className="h-2 w-full" />
        </div>
        
        {/* Footer */}
        <div className="flex justify-between items-center pt-2 border-t border-border/60">
          <Skeleton className="h-5 w-24" />
          <Skeleton className="h-5 w-20" />
        </div>
      </div>
    </div>
  );
}

export function CourseCardSkeletonList({ count = 3 }: { count?: number }) {
  return (
    <div className="grid gap-8">
      {Array(count).fill(0).map((_, index) => (
        <CourseCardSkeleton key={index} />
      ))}
    </div>
  );
}
