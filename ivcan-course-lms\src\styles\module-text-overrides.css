/* Module Text Overrides - Making text smaller and more professional */

/* Module title - smaller */
.module-title {
  font-size: 0.8125rem !important;
  line-height: 1.4 !important;
  font-weight: 600 !important;
  letter-spacing: -0.01em !important;
  color: #333 !important;
}

.dark .module-title {
  color: #e0e0e0 !important;
}

/* Module info text (lessons count and time) - much smaller */
.module-info,
.text-gray-500,
.flex.items-center.gap-3,
.flex.items-center.gap-4.text-gray-500 {
  font-size: 0.65rem !important;
  line-height: 1.3 !important;
  color: #666 !important;
}

.dark .module-info,
.dark .text-gray-500,
.dark .flex.items-center.gap-3,
.dark .flex.items-center.gap-4.text-gray-500 {
  color: #999 !important;
}

/* Module icons - smaller */
.module-info svg,
.text-gray-500 svg,
.flex.items-center.gap-3 svg,
.flex.items-center.gap-4.text-gray-500 svg {
  width: 0.65rem !important;
  height: 0.65rem !important;
  margin-right: 0.125rem !important;
  color: #888 !important;
}

.dark .module-info svg,
.dark .text-gray-500 svg,
.dark .flex.items-center.gap-3 svg,
.dark .flex.items-center.gap-4.text-gray-500 svg {
  color: #aaa !important;
}

/* Module dropdown icon - smaller */
.module-dropdown-icon {
  width: 0.75rem !important;
  height: 0.75rem !important;
}

/* Lesson title - smaller */
.lesson-title {
  font-size: 0.75rem !important;
  line-height: 1.4 !important;
  font-weight: 500 !important;
  color: #333 !important;
}

.dark .lesson-title {
  color: #d1d5db !important;
}

/* Lesson meta - smaller */
.lesson-meta,
.flex.items-center.gap-1\.5,
.flex.items-center.gap-3.mt-1,
.flex.items-center.gap-3.text-gray-500 {
  font-size: 0.65rem !important;
  line-height: 1.3 !important;
  color: #777 !important;
}

.dark .lesson-meta,
.dark .flex.items-center.gap-1\.5,
.dark .flex.items-center.gap-3.mt-1,
.dark .flex.items-center.gap-3.text-gray-500 {
  color: #9ca3af !important;
}

/* Lesson meta icons - smaller */
.lesson-meta svg,
.flex.items-center.gap-1\.5 svg,
.flex.items-center.gap-3.mt-1 svg,
.flex.items-center.gap-3.text-gray-500 svg {
  width: 0.65rem !important;
  height: 0.65rem !important;
  margin-right: 0.125rem !important;
  color: #888 !important;
}

.dark .lesson-meta svg,
.dark .flex.items-center.gap-1\.5 svg,
.dark .flex.items-center.gap-3.mt-1 svg,
.dark .flex.items-center.gap-3.text-gray-500 svg {
  color: #aaa !important;
}

/* Module number - smaller */
.module-number {
  width: 1.5rem !important;
  height: 1.5rem !important;
  font-size: 0.75rem !important;
}

/* Extra small devices */
@media (max-width: 380px) {
  .module-title {
    font-size: 0.75rem !important;
  }
  
  .module-info,
  .text-gray-500,
  .flex.items-center.gap-3,
  .flex.items-center.gap-4.text-gray-500 {
    font-size: 0.625rem !important;
  }
  
  .lesson-title {
    font-size: 0.7rem !important;
  }
  
  .lesson-meta,
  .flex.items-center.gap-1\.5,
  .flex.items-center.gap-3.mt-1,
  .flex.items-center.gap-3.text-gray-500 {
    font-size: 0.625rem !important;
  }
  
  .module-number {
    width: 1.375rem !important;
    height: 1.375rem !important;
    font-size: 0.65rem !important;
  }
}

/* Very small devices */
@media (max-width: 320px) {
  .module-title {
    font-size: 0.7rem !important;
  }
  
  .module-info,
  .text-gray-500,
  .flex.items-center.gap-3,
  .flex.items-center.gap-4.text-gray-500 {
    font-size: 0.6rem !important;
  }
  
  .lesson-title {
    font-size: 0.65rem !important;
  }
  
  .lesson-meta,
  .flex.items-center.gap-1\.5,
  .flex.items-center.gap-3.mt-1,
  .flex.items-center.gap-3.text-gray-500 {
    font-size: 0.6rem !important;
  }
  
  .module-number {
    width: 1.25rem !important;
    height: 1.25rem !important;
    font-size: 0.6rem !important;
  }
} 