var re=Object.defineProperty,ae=Object.defineProperties;var ie=Object.getOwnPropertyDescriptors;var O=Object.getOwnPropertySymbols;var oe=Object.prototype.hasOwnProperty,ne=Object.prototype.propertyIsEnumerable;var V=(a,i,r)=>i in a?re(a,i,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[i]=r,G=(a,i)=>{for(var r in i||(i={}))oe.call(i,r)&&V(a,r,i[r]);if(O)for(var r of O(i))ne.call(i,r)&&V(a,r,i[r]);return a},H=(a,i)=>ae(a,ie(i));var w=(a,i,r)=>new Promise((l,t)=>{var j=u=>{try{k(r.next(u))}catch(f){t(f)}},o=u=>{try{k(r.throw(u))}catch(f){t(f)}},k=u=>u.done?l(u.value):Promise.resolve(u.value).then(j,o);k((r=r.apply(a,i)).next())});import{u as Q,r as q,az as B,j as e,aO as $,af as ee,m as le,aP as J,a9 as K,av as ce,aw as de,E as me,aG as W,aH as ge,$ as ue,ag as xe,aD as he}from"./vendor-react.BcAa1DKr.js";import{L as D}from"./Layout.DRjmVYQG.js";import{P as pe}from"./progress.BMuwHUJX.js";import{a as se,u as te,s as T,c as d,B as R,g as ye}from"./index.BLDhDn0D.js";import{g as fe,a as be}from"./moduleImageUtils.AJjzI8xs.js";import{g as X,a as Z}from"./moduleTestService.BmAHru_J.js";import{a4 as I,a5 as je,a2 as P}from"./vendor.DQpuTRuB.js";import{finishCourse as ke}from"./completionService.BUb78ZaE.js";import{C as Ne}from"./CourseCompletionCelebration.BJZAxw59.js";import{f as Ce}from"./courseApi.BQX5-7u-.js";import{P as A,C as ve}from"./floating-sidebar-container.BxlLgzat.js";import"./vendor-supabase.sufZ44-y.js";import"./achievementService.Vkx_BHml.js";import"./confetti.ShHySCrk.js";import"./utils.Qa9QlCj_.js";import"./enrollmentApi.CstnsQi_.js";const we=({modules:a,courseId:i})=>{const r=Q(),l=se(),{user:t}=te(),[j,o]=q.useState(null),[k,u]=q.useState({}),f=s=>{o(j===s?null:s)},E=(s,h=!1)=>{h||r(`/course/${i}/lesson/${s}`)},b=s=>{console.error(`Failed to load image for module ${s}, falling back to default image`),u(h=>H(G({},h),{[s]:!0}))},{data:c}=B({queryKey:["lesson-completions",t==null?void 0:t.id],queryFn:()=>w(void 0,null,function*(){if(!(t!=null&&t.id))return[];const{data:s,error:h}=yield T.from("user_lesson_progress").select("lesson_id, is_completed").eq("user_id",t.id).eq("is_completed",!0);return h?(console.error("Error fetching lesson completion statuses:",h),[]):s||[]}),enabled:!!(t!=null&&t.id)});return e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:a.map((s,h)=>{var y,U,Y,z;const M=new Set((c==null?void 0:c.map(m=>m.lesson_id))||[]),n=((y=s.lessons)==null?void 0:y.length)||0,p=((Y=(U=s.lessons)==null?void 0:U.filter(m=>M.has(m.id)))==null?void 0:Y.length)||0,g=n>0?p/n*100:0,F=j===s.id;let S=fe(s.module_number||1);k[s.id]&&(S=be());const{data:N}=B({queryKey:["module-pre-test",s.id],queryFn:()=>w(void 0,null,function*(){return X(s.id,"pre_test")}),enabled:!!s.id}),{data:_}=B({queryKey:["module-post-test",s.id],queryFn:()=>w(void 0,null,function*(){return X(s.id,"post_test")}),enabled:!!s.id}),{data:x}=B({queryKey:["pre-test-response",s.id,t==null?void 0:t.id],queryFn:()=>w(void 0,null,function*(){return!s.id||!(t!=null&&t.id)||!N?null:Z(N.id,t.id)}),enabled:!!s.id&&!!(t!=null&&t.id)&&!!N}),{data:C}=B({queryKey:["post-test-response",s.id,t==null?void 0:t.id],queryFn:()=>w(void 0,null,function*(){return!s.id||!(t!=null&&t.id)||!_?null:Z(_.id,t.id)}),enabled:!!s.id&&!!(t!=null&&t.id)&&!!_});return e.jsxs(I.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:h*.1},className:d("group","relative overflow-hidden rounded-xl border border-border/40","bg-white dark:bg-gray-800/50 hover:bg-gray-50 dark:hover:bg-gray-800","transition-all duration-200",s.is_locked&&"opacity-75"),children:[e.jsxs("div",{className:"cursor-pointer",onClick:()=>f(s.id),children:[e.jsxs("div",{className:"relative aspect-video w-full overflow-hidden",children:[e.jsx("img",{src:S,alt:s.title,className:"w-full h-full object-cover transition-transform duration-200 group-hover:scale-105",onError:()=>b(s.id)}),s.is_completed&&e.jsx("div",{className:"absolute top-2 right-2 bg-primary text-white rounded-full p-1",children:e.jsx($,{className:"w-5 h-5"})}),s.is_locked&&e.jsx("div",{className:"absolute inset-0 bg-black/50 flex items-center justify-center",children:e.jsx("span",{className:"text-white font-semibold",children:"Locked"})})]}),e.jsxs("div",{className:"p-2 space-y-2",children:[" ",e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[" ",e.jsx("h3",{className:d("font-semibold text-gray-900 dark:text-white group-hover:text-primary",l?"text-sm":"text-base"),children:s.title}),e.jsx("div",{className:"flex items-center flex-wrap gap-2 text-gray-500 dark:text-gray-400",children:e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(ee,{className:"w-4 h-4"}),e.jsxs("span",{className:d(l?"text-xs":"text-sm"),children:[n," ",n===1?"lesson":"lessons"]})]})})]}),e.jsx(le,{className:d("w-5 h-5 text-gray-400 dark:text-gray-500 group-hover:text-primary","transition-transform duration-200",F&&"rotate-180")})]}),e.jsxs("div",{className:"space-y-1",children:[" ",e.jsxs("div",{className:"flex justify-between items-center text-sm",children:[e.jsxs("span",{className:"text-gray-600 dark:text-gray-400 text-xs",children:[p," of ",n," completed"]}),e.jsxs("span",{className:"font-medium text-gray-900 dark:text-white text-xs",children:[Math.round(g),"%"]})]}),e.jsx(pe,{value:g,className:"h-1.5"})]})]})]}),e.jsx(je,{children:F&&e.jsxs(I.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},transition:{duration:.2},className:"overflow-hidden border-t border-border/40",children:[N&&e.jsxs("div",{className:"p-2 border-b border-border/40",children:[" ",e.jsxs("div",{className:d("flex items-center justify-between p-2 rounded-lg","hover:bg-gray-50 dark:hover:bg-gray-700/50","transition-colors duration-200 cursor-pointer",x?"bg-green-50 dark:bg-green-900/20 border-l-4 border-l-green-500":"bg-yellow-50 dark:bg-yellow-900/20 border-l-4 border-l-yellow-500",s.is_locked&&"cursor-not-allowed opacity-50"),onClick:()=>!s.is_locked&&r(`/course/${i}/module/${s.id}`),children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:d("w-6 h-6 rounded-full flex items-center justify-center",x?"bg-green-500 text-white":"bg-yellow-500 text-white"),children:e.jsx(J,{className:"w-4 h-4"})}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:d("text-sm font-medium",x?"text-green-700 dark:text-green-400":"text-yellow-700 dark:text-yellow-400"),children:N.title||"Pre-Test"}),x?e.jsxs("span",{className:"text-xs text-green-600 dark:text-green-400 flex items-center gap-1 mt-0.5",children:[e.jsx($,{className:"w-3 h-3"}),"Completed"]}):e.jsx("span",{className:"text-xs text-yellow-600 dark:text-yellow-400 mt-0.5",children:"Required before starting module"})]})]}),!s.is_locked&&e.jsx(K,{className:d("w-4 h-4",x?"text-green-500":"text-yellow-500")})]})]}),s.lessons&&s.lessons.length>0&&e.jsxs("div",{className:"p-2 space-y-1",children:[" ",s.lessons.map(m=>{const v=M.has(m.id),L=s.is_locked||m.is_locked;return e.jsxs("div",{onClick:()=>!L&&E(m.slug,L),className:d("flex items-center justify-between p-2 rounded-lg","hover:bg-gray-50 dark:hover:bg-gray-700/50","transition-colors duration-200 cursor-pointer",v?"bg-green-50 dark:bg-green-900/20 border-l-4 border-l-green-500":"bg-transparent",L&&"cursor-not-allowed opacity-50",!x&&N&&!v&&"opacity-50"),children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:d("w-6 h-6 rounded-full flex items-center justify-center",v?"bg-green-500 text-white":L?"bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400":"bg-gray-100 dark:bg-gray-700"),children:v?e.jsx($,{className:"w-4 h-4"}):L?e.jsx(ce,{className:"w-3 h-3"}):e.jsx("span",{className:"text-xs font-medium",children:m.lesson_number||1})}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:d("text-sm",v?"text-green-700 dark:text-green-400 font-medium":"text-gray-700 dark:text-gray-300"),children:m.title}),v&&e.jsxs("span",{className:"text-xs text-green-600 dark:text-green-400 flex items-center gap-1 mt-0.5",children:[e.jsx($,{className:"w-3 h-3"}),"Completed"]})]})]}),!s.is_locked&&e.jsx(K,{className:d("w-4 h-4",v?"text-green-500":"text-gray-400")})]},m.id)})]}),_&&e.jsxs("div",{className:"p-2 border-t border-border/40",children:[" ",e.jsxs("div",{className:d("flex items-center justify-between p-2 rounded-lg","hover:bg-gray-50 dark:hover:bg-gray-700/50","transition-colors duration-200 cursor-pointer",C?"bg-green-50 dark:bg-green-900/20 border-l-4 border-l-green-500":"bg-blue-50 dark:bg-blue-900/20 border-l-4 border-l-blue-500",s.is_locked&&"cursor-not-allowed opacity-50",!((z=s.lessons)!=null&&z.every(m=>m.completed))&&!C&&"opacity-50"),onClick:()=>{var m;!s.is_locked&&((m=s.lessons)!=null&&m.every(v=>v.completed)||C)&&r(`/course/${i}/module/${s.id}`)},children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:d("w-6 h-6 rounded-full flex items-center justify-center",C?"bg-green-500 text-white":"bg-blue-500 text-white"),children:e.jsx(J,{className:"w-4 h-4"})}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:d("text-sm font-medium",C?"text-green-700 dark:text-green-400":"text-blue-700 dark:text-blue-400"),children:_.title||"Post-Test"}),C?e.jsxs("span",{className:"text-xs text-green-600 dark:text-green-400 flex items-center gap-1 mt-0.5",children:[e.jsx($,{className:"w-3 h-3"}),"Completed"]}):e.jsx("span",{className:"text-xs text-blue-600 dark:text-blue-400 mt-0.5",children:"Available after completing all lessons"})]})]}),!s.is_locked&&e.jsx(K,{className:d("w-4 h-4",C?"text-green-500":"text-blue-500")})]})]})]})})]},s.id)})})},Fe=({courseId:a,userId:i,modules:r,courseName:l="this course"})=>{const[t,j]=q.useState(!1),[o,k]=q.useState(!1),[u,f]=q.useState(!1),[E,b]=q.useState(!1),c=de(),s=Q();console.log("SimpleFinishCourseButton: Component rendered with props:",{courseId:a,userId:i,moduleCount:(r==null?void 0:r.length)||0,courseName:l}),q.useEffect(()=>{w(void 0,null,function*(){try{if(!r||r.length===0||!i){console.log("SimpleFinishCourseButton: Missing data",{hasModules:!!r,moduleCount:(r==null?void 0:r.length)||0,hasUserId:!!i});return}console.log("SimpleFinishCourseButton: Checking module completion for:",{courseId:a,userId:i,moduleCount:r.length}),console.log("SimpleFinishCourseButton: Module IDs:",r.map(y=>({id:y.id,title:y.title,is_completed:y.is_completed})));const p=r.filter(y=>y.is_completed===!0);console.log("SimpleFinishCourseButton: Modules marked as completed in API:",p.length);const{data:g,error:F}=yield T.from("user_module_progress").select("module_id, is_completed").eq("user_id",i).eq("is_completed",!0).in("module_id",r.map(y=>y.id));if(F){console.error("SimpleFinishCourseButton: Error checking module completion:",F);return}const S=(g==null?void 0:g.length)||0,N=r.length,_=S===N;console.log(`SimpleFinishCourseButton: Module completion status from DB: ${S}/${N} completed`),console.log("SimpleFinishCourseButton: Completed module IDs from DB:",g==null?void 0:g.map(y=>y.module_id)),console.log("SimpleFinishCourseButton: All modules completed?",_),k(_);const{data:x}=yield T.from("user_course_enrollment").select("status").eq("user_id",i).eq("course_id",a).single(),C=(x==null?void 0:x.status)==="completed";f(C),console.log(`Course enrollment status: ${x==null?void 0:x.status}`)}catch(p){console.error("Error checking module completion:",p)}})},[r,i,a]);const h=()=>w(void 0,null,function*(){if(!a||!i){P.error("Missing course or user information");return}if(!o){P.error("You need to complete all modules before finishing the course");return}try{j(!0),console.log(`SimpleFinishCourseButton: Attempting to finish course ${a} for user ${i}`),console.log("SimpleFinishCourseButton: Button state before completion:",{allModulesCompleted:o,isCourseCompleted:u,isCompleting:t});const n=ke(a,i),p=new Promise((F,S)=>setTimeout(()=>S(new Error("Course completion timeout after 30 seconds")),3e4));console.log("SimpleFinishCourseButton: Starting course completion with timeout...");const g=yield Promise.race([n,p]);if(console.log("SimpleFinishCourseButton: Course completion result:",g),g.success){console.log("SimpleFinishCourseButton: Course finished successfully"),b(!0),f(!0),P.success(`🎉 Congratulations! You've completed ${l}! Your certificate is ready.`),console.log("SimpleFinishCourseButton: Refreshing queries...");try{yield c.invalidateQueries({queryKey:["courseProgress"]}),yield c.invalidateQueries({queryKey:["courseEnrollment"]}),yield c.invalidateQueries({queryKey:["certificates"]}),yield c.invalidateQueries({queryKey:["user-achievements"]}),console.log("SimpleFinishCourseButton: Queries refreshed successfully")}catch(F){console.error("SimpleFinishCourseButton: Error refreshing queries:",F)}}else console.error("SimpleFinishCourseButton: Failed to finish course:",g.error),P.error(g.error||"Failed to complete course. Please try again.")}catch(n){console.error("SimpleFinishCourseButton: Error finishing course:",n);let p="Failed to complete course. Please try again.";n instanceof Error&&(n.message.includes("timeout")?p="Course completion is taking too long. Please check your connection and try again.":n.message.includes("network")&&(p="Network error. Please check your connection and try again.")),P.error(p)}finally{console.log("SimpleFinishCourseButton: Setting isCompleting to false"),j(!1)}}),M=()=>{b(!1),s(`/certificate/${a}`)};return u?e.jsxs("div",{className:"flex items-center justify-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800",children:[e.jsx(me,{className:"h-5 w-5 text-green-600 dark:text-green-400 mr-2"}),e.jsx("span",{className:"text-green-700 dark:text-green-300 font-medium",children:"Course Completed!"}),e.jsx(R,{variant:"outline",size:"sm",onClick:()=>s(`/certificate/${a}`),className:"ml-3 border-green-300 text-green-700 hover:bg-green-100 dark:border-green-700 dark:text-green-300 dark:hover:bg-green-900/30",children:"View Certificate"})]}):e.jsxs(e.Fragment,{children:[e.jsx(Ne,{courseId:a,courseName:l,isVisible:E,onClose:M}),e.jsx("div",{className:"flex items-center justify-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(W,{className:"h-5 w-5 text-blue-600 dark:text-blue-400"}),e.jsx("span",{className:"text-blue-700 dark:text-blue-300 font-medium",children:o?"All modules completed! Ready to finish the course?":`Debug: ${r.filter(n=>n.is_completed).length}/${r.length} modules completed`}),e.jsx(R,{onClick:h,disabled:t||!o,className:o?"bg-blue-600 hover:bg-blue-700 text-white":"bg-gray-400 text-gray-700 cursor-not-allowed",children:t?e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"animate-spin border-2 border-current border-t-transparent rounded-full w-4 h-4 mr-2"}),"Completing..."]}):e.jsxs(e.Fragment,{children:[e.jsx(W,{className:"h-4 w-4 mr-2"}),o?"Finish Course":"Complete All Modules First"]})})]})})]})},ze=()=>{const{courseId:a}=ge(),i=Q(),{toast:r}=ye();se();const{user:l}=te(),{data:t,isLoading:j}=B({queryKey:["course-details",a],queryFn:()=>w(void 0,null,function*(){if(!a)return null;const{data:b,error:c}=yield T.from("courses").select("*").eq("id",a).single();if(c)throw r({title:"Error fetching course",description:c.message,variant:"destructive"}),c;return b}),enabled:!!a}),{data:o,isLoading:k}=B({queryKey:["course-modules",a,l==null?void 0:l.id],queryFn:()=>Ce(a||"",l==null?void 0:l.id),enabled:!!a});if(j||k)return e.jsx(D,{children:e.jsx(A,{pageType:"module",children:e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(ue,{className:"w-8 h-8 animate-spin text-primary"})})})});if(!t||!o)return e.jsx(D,{children:e.jsx(A,{pageType:"module",children:e.jsxs("div",{className:"text-center p-8",children:[e.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Course Not Found"}),e.jsx("p",{className:"mb-6",children:"The course you're looking for doesn't exist or you don't have permission to access it."}),e.jsx(R,{onClick:()=>i("/dashboard"),children:"Return to Dashboard"})]})})});const f=o.reduce((b,c)=>{var s;return b+(((s=c.lessons)==null?void 0:s.reduce((h,M)=>{const n=parseInt(M.duration.split(":")[0]);return h+(isNaN(n)?0:n)},0))||0)},0),E=o.reduce((b,c)=>{var s;return b+(((s=c.lessons)==null?void 0:s.length)||0)},0);return e.jsx(D,{children:e.jsx(A,{pageType:"module",children:e.jsxs(ve,{spacing:"md",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold mb-2",children:t.title}),e.jsx("p",{className:"text-muted-foreground",children:"Course Modules"})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"flex items-center gap-3 p-3 rounded-lg bg-gray-50/80 dark:bg-gray-900/50 transition-all duration-200 hover:bg-gray-100/80 dark:hover:bg-gray-700/30",children:[e.jsx("div",{className:"flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 dark:bg-primary/20",children:e.jsx(xe,{className:"w-5 h-5 text-primary"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Duration"}),e.jsxs("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:[f," minutes"]})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 rounded-lg bg-gray-50/80 dark:bg-gray-900/50 transition-all duration-200 hover:bg-gray-100/80 dark:hover:bg-gray-700/30",children:[e.jsx("div",{className:"flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 dark:bg-primary/20",children:e.jsx(ee,{className:"w-5 h-5 text-primary"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Modules"}),e.jsxs("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:[o.length," total"]})]})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 rounded-lg bg-gray-50/80 dark:bg-gray-900/50 transition-all duration-200 hover:bg-gray-100/80 dark:hover:bg-gray-700/30",children:[e.jsx("div",{className:"flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 dark:bg-primary/20",children:e.jsx(he,{className:"w-5 h-5 text-primary"})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Lessons"}),e.jsxs("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:[E," total"]})]})]})]}),e.jsx(we,{modules:o,courseId:t.id}),l&&o&&t&&e.jsx("div",{className:"mt-6",children:e.jsx(Fe,{courseId:t.id,userId:l.id,modules:o,courseName:t.title})})]})})})};export{ze as default};
