-- Migration for auto-completion functionality

-- Ensure the complete_course function exists
CREATE OR REPLACE FUNCTION public.complete_course(p_user_id UUID, p_course_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_enrollment_exists BOOLEAN;
  v_now TIMESTAMP WITH TIME ZONE := NOW();
BEGIN
  -- Check if enrollment exists
  SELECT EXISTS (
    SELECT 1 FROM public.user_course_enrollment
    WHERE user_id = p_user_id AND course_id = p_course_id
  ) INTO v_enrollment_exists;
  
  IF v_enrollment_exists THEN
    -- Update existing enrollment
    UPDATE public.user_course_enrollment
    SET 
      status = 'completed',
      completed_at = v_now,
      updated_at = v_now
    WHERE 
      user_id = p_user_id AND 
      course_id = p_course_id;
  ELSE
    -- Create new enrollment
    INSERT INTO public.user_course_enrollment (
      user_id,
      course_id,
      status,
      enrolled_at,
      completed_at,
      updated_at
    ) VALUES (
      p_user_id,
      p_course_id,
      'completed',
      v_now,
      v_now,
      v_now
    );
  END IF;
  
  -- Also update user_course_progress
  INSERT INTO public.user_course_progress (
    user_id,
    course_id,
    completed_modules,
    updated_at
  ) VALUES (
    p_user_id,
    p_course_id,
    100,
    v_now
  )
  ON CONFLICT (user_id, course_id) 
  DO UPDATE SET
    completed_modules = 100,
    updated_at = v_now;
    
  RETURN TRUE;
END;
$$;

-- Create a function to mark all courses as completed for a user
CREATE OR REPLACE FUNCTION public.complete_all_courses(p_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_course_id UUID;
  v_now TIMESTAMP WITH TIME ZONE := NOW();
  v_success BOOLEAN := TRUE;
BEGIN
  -- Loop through all courses
  FOR v_course_id IN SELECT id FROM public.courses
  LOOP
    BEGIN
      -- Call the complete_course function for each course
      PERFORM public.complete_course(p_user_id, v_course_id);
    EXCEPTION WHEN OTHERS THEN
      -- Log error but continue with other courses
      RAISE NOTICE 'Error completing course %: %', v_course_id, SQLERRM;
      v_success := FALSE;
    END;
  END LOOP;
  
  RETURN v_success;
END;
$$;

-- Grant execute permission on the functions
GRANT EXECUTE ON FUNCTION public.complete_course TO authenticated;
GRANT EXECUTE ON FUNCTION public.complete_course TO anon;
GRANT EXECUTE ON FUNCTION public.complete_course TO service_role;

GRANT EXECUTE ON FUNCTION public.complete_all_courses TO authenticated;
GRANT EXECUTE ON FUNCTION public.complete_all_courses TO anon;
GRANT EXECUTE ON FUNCTION public.complete_all_courses TO service_role;

-- Ensure RLS policies allow users to access their own enrollments
DROP POLICY IF EXISTS "Users can view their own enrollments" ON public.user_course_enrollment;
CREATE POLICY "Users can view their own enrollments" 
ON public.user_course_enrollment FOR SELECT 
USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own enrollments" ON public.user_course_enrollment;
CREATE POLICY "Users can insert their own enrollments" 
ON public.user_course_enrollment FOR INSERT 
WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own enrollments" ON public.user_course_enrollment;
CREATE POLICY "Users can update their own enrollments" 
ON public.user_course_enrollment FOR UPDATE 
USING (auth.uid() = user_id);

-- Teachers can view all enrollments
DROP POLICY IF EXISTS "Teachers can view all enrollments" ON public.user_course_enrollment;
CREATE POLICY "Teachers can view all enrollments" 
ON public.user_course_enrollment FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE profiles.id = auth.uid() AND profiles.role = 'teacher'
  )
);
