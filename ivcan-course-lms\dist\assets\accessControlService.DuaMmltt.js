var n=(r,s,e)=>new Promise((c,o)=>{var h=a=>{try{t(e.next(a))}catch(l){o(l)}},f=a=>{try{t(e.throw(a))}catch(l){o(l)}},t=a=>a.done?c(a.value):Promise.resolve(a.value).then(h,f);t((e=e.apply(r,s)).next())});import{s as u}from"./index.BLDhDn0D.js";import{getUserRole as i}from"./userRoleService.Crm_K_HM.js";import"./vendor-react.BcAa1DKr.js";import"./vendor.DQpuTRuB.js";import"./vendor-supabase.sufZ44-y.js";function g(r,s){return n(this,null,function*(){if(!r||!s)return{hasAccess:!1,reason:"Missing user ID or module ID"};try{console.log(`[ACCESS CONTROL] Checking module access for user ${r}, module ${s}`);const e=yield i(r);if(console.log(`[ACCESS CONTROL] User role: ${e}`),e==="teacher")return console.log(`[ACCESS CONTROL] Teacher access granted for module ${s}`),{hasAccess:!0,reason:"Teacher access"};const{data:c,error:o}=yield u.rpc("check_module_access",{p_user_id:r,p_module_id:s});return o?(console.error("[ACCESS CONTROL] Error checking module access:",o),{hasAccess:!1,reason:"Database error"}):(console.log(`[ACCESS CONTROL] Module access result for ${s}: ${c}`),{hasAccess:!!c,reason:c?"Access granted":"Previous module not completed"})}catch(e){return console.error("[ACCESS CONTROL] Error in checkModuleAccess:",e),{hasAccess:!1,reason:"Unexpected error"}}})}function E(r,s){return n(this,null,function*(){if(!r||!s)return{hasAccess:!1,reason:"Missing user ID or lesson ID"};try{if((yield i(r))==="teacher")return{hasAccess:!0,reason:"Teacher access"};const{data:c,error:o}=yield u.rpc("check_lesson_access",{p_user_id:r,p_lesson_id:s});return o?(console.error("Error checking lesson access:",o),{hasAccess:!1,reason:"Database error"}):{hasAccess:!!c,reason:c?"Access granted":"Previous lesson not completed or module locked"}}catch(e){return console.error("Error in checkLessonAccess:",e),{hasAccess:!1,reason:"Unexpected error"}}})}function O(r,s){return n(this,null,function*(){if(!r)return!1;try{const{error:e}=yield u.rpc("update_module_locks_for_user",{p_user_id:r,p_course_id:s||null});return e?(console.error("Error updating user access:",e),!1):!0}catch(e){return console.error("Error in updateUserAccess:",e),!1}})}export{E as checkLessonAccess,g as checkModuleAccess,O as updateUserAccess};
