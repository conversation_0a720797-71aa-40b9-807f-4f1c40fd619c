import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// List of countries with Ghana as first option
const countries = [
  "Ghana",
  "Afghanistan", "Albania", "Algeria", "Argentina", "Armenia", "Australia",
  "Austria", "Azerbaijan", "Bahrain", "Bangladesh", "Belarus", "Belgium",
  "Bolivia", "Bosnia and Herzegovina", "Brazil", "Bulgaria", "Cambodia",
  "Cameroon", "Canada", "Chile", "China", "Colombia", "Costa Rica",
  "Croatia", "Czech Republic", "Denmark", "Ecuador", "Egypt", "Estonia",
  "Ethiopia", "Finland", "France", "Georgia", "Germany", "Greece",
  "Guatemala", "Honduras", "Hungary", "Iceland", "India", "Indonesia",
  "Iran", "Iraq", "Ireland", "Israel", "Italy", "Japan", "Jordan",
  "Kazakhstan", "Kenya", "Kuwait", "Latvia", "Lebanon", "Lithuania",
  "Luxembourg", "Malaysia", "Mexico", "Morocco", "Netherlands", "New Zealand",
  "Nigeria", "Norway", "Pakistan", "Peru", "Philippines", "Poland",
  "Portugal", "Qatar", "Romania", "Russia", "Saudi Arabia", "Singapore",
  "Slovakia", "Slovenia", "South Africa", "South Korea", "Spain", "Sri Lanka",
  "Sweden", "Switzerland", "Thailand", "Turkey", "Ukraine", "United Arab Emirates",
  "United Kingdom", "United States", "Uruguay", "Venezuela", "Vietnam", "Other"
];

async function fixQuestionnaireDropdowns() {
  try {
    console.log('🔧 Fixing questionnaire dropdown types...\n');

    // Get current questionnaire
    const { data: questionnaire, error: fetchError } = await supabase
      .from('demographic_questionnaires')
      .select('*')
      .eq('is_active', true)
      .single();

    if (fetchError) {
      console.error('Error fetching questionnaire:', fetchError);
      return;
    }

    console.log('✅ Current questionnaire loaded');
    console.log('   Questions:', questionnaire.questions.length);

    // Create completely new questions array with proper dropdown types
    const newQuestions = [
      {
        "id": "consent",
        "question": "Would you like to proceed with the questionnaire?",
        "type": "single_choice",
        "required": true,
        "options": ["Agree", "Disagree"]
      },
      {
        "id": "country",
        "question": "What country are you from?",
        "type": "dropdown",
        "required": true,
        "options": countries,
        "default": "Ghana"
      },
      {
        "id": "gender",
        "question": "Gender",
        "type": "single_choice",
        "required": true,
        "options": ["Male", "Female"]
      },
      {
        "id": "age",
        "question": "Please state your age",
        "type": "number",
        "required": true
      },
      {
        "id": "formal_training",
        "question": "Have you received any formal training on IV cannulation and contrast administration?",
        "type": "single_choice",
        "required": true,
        "options": ["Yes", "No"]
      },
      {
        "id": "role_type",
        "question": "Are you a medical imaging student or a practitioner?",
        "type": "single_choice",
        "required": true,
        "options": ["Student", "Practitioner", "Other"]
      },
      {
        "id": "student_level",
        "question": "Are you an undergraduate or a postgraduate student?",
        "type": "single_choice",
        "required": false,
        "conditional": {"field": "role_type", "value": "Student"},
        "options": ["Undergraduate", "Postgraduate"]
      },
      {
        "id": "university",
        "question": "Which university do you attend?",
        "type": "dropdown",
        "required": false,
        "conditional": {"field": "role_type", "value": "Student"},
        "options": [
          "University of Ghana (UG)",
          "Kwame Nkrumah University of Science and Technology (KNUST)",
          "University of Cape Coast (UCC)",
          "University of Development Studies (UDS)",
          "University of Health and Allied Sciences (UHAS)",
          "Accra Technical University (ATU)",
          "College of Health and Well-Being, Kintampo (KRHTS)",
          "Other"
        ]
      },
      {
        "id": "undergraduate_program",
        "question": "What program are you reading?",
        "type": "single_choice",
        "required": false,
        "conditional": {"field": "student_level", "value": "Undergraduate"},
        "options": [
          "BSc Diagnostic Radiography",
          "BSc Diagnostic Sonography",
          "BSc Radiation Therapy",
          "BSc Nuclear Medicine Technology",
          "Other"
        ]
      },
      {
        "id": "undergraduate_year",
        "question": "What year are you in?",
        "type": "dropdown",
        "required": false,
        "conditional": {"field": "student_level", "value": "Undergraduate"},
        "options": ["1st Year", "2nd Year", "3rd Year", "4th Year", "5th Year", "6th Year"]
      },
      {
        "id": "postgraduate_program",
        "question": "What program are you reading?",
        "type": "single_choice",
        "required": false,
        "conditional": {"field": "student_level", "value": "Postgraduate"},
        "options": ["MSc", "MPhil", "PhD", "Other"]
      },
      {
        "id": "practitioner_work",
        "question": "What do you do for work?",
        "type": "single_choice",
        "required": false,
        "conditional": {"field": "role_type", "value": "Practitioner"},
        "options": [
          "Diagnostic Radiographer",
          "Diagnostic Sonography",
          "Radiation Therapy",
          "Nuclear Medicine Technology",
          "Other"
        ]
      },
      {
        "id": "workplace",
        "question": "Place of work",
        "type": "single_choice",
        "required": false,
        "conditional": {"field": "role_type", "value": "Practitioner"},
        "options": [
          "Tertiary Hospital",
          "Regional Hospital",
          "District Hospital",
          "Diagnostic Center",
          "Other"
        ]
      },
      {
        "id": "location",
        "question": "Location",
        "type": "dropdown",
        "required": false,
        "conditional": {"field": "role_type", "value": "Practitioner"},
        "options": [
          "Ashanti Region", "Ahafo Region", "Bono Region", "Bono East Region",
          "Central Region", "Eastern Region", "Greater Accra Region",
          "North East Region", "Northern Region", "Oti Region",
          "Savannah Region", "Upper East Region", "Upper West Region",
          "Volta Region", "Western Region", "Western North Region"
        ]
      },
      {
        "id": "experience_years",
        "question": "Years of Experience",
        "type": "single_choice",
        "required": false,
        "conditional": {"field": "role_type", "value": "Practitioner"},
        "options": ["1 Year", "2 Years", "3 Years", "4 Years", "5 Years", "Other"]
      }
    ];

    console.log('📝 Creating new questions structure...');
    console.log('   • Country: text → dropdown (with Ghana default)');
    console.log('   • University: single_choice → dropdown (8 options)');
    console.log('   • Location: single_choice → dropdown (16 regions)');
    console.log('   • Undergraduate Year: single_choice → dropdown (6 years)');

    // Update the questionnaire
    const { error: updateError } = await supabase
      .from('demographic_questionnaires')
      .update({
        questions: newQuestions,
        updated_at: new Date().toISOString()
      })
      .eq('id', questionnaire.id);

    if (updateError) {
      console.error('❌ Error updating questionnaire:', updateError);
      return;
    }

    console.log('\n✅ Questionnaire updated successfully!');

    // Verify the update
    const { data: verifyData, error: verifyError } = await supabase
      .from('demographic_questionnaires')
      .select('questions')
      .eq('is_active', true)
      .single();

    if (verifyError) {
      console.error('❌ Error verifying update:', verifyError);
      return;
    }

    const dropdownQuestions = verifyData.questions.filter(q => q.type === 'dropdown');
    const singleChoiceQuestions = verifyData.questions.filter(q => q.type === 'single_choice');
    
    console.log('\n📊 Verification Results:');
    console.log(`   ✅ Total questions: ${verifyData.questions.length}`);
    console.log(`   ✅ Dropdown questions: ${dropdownQuestions.length}`);
    console.log(`   ✅ Single choice questions: ${singleChoiceQuestions.length}`);
    console.log('   ✅ Dropdown IDs:', dropdownQuestions.map(q => q.id).join(', '));

    // Check country question specifically
    const countryQuestion = verifyData.questions.find(q => q.id === 'country');
    if (countryQuestion && countryQuestion.type === 'dropdown') {
      console.log(`   ✅ Country dropdown: ${countryQuestion.options.length} countries`);
      console.log(`   ✅ Default country: ${countryQuestion.default}`);
      console.log(`   ✅ First 3 countries: ${countryQuestion.options.slice(0, 3).join(', ')}`);
    }

    console.log('\n🎉 Dropdown conversion completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   • Country: Now dropdown with 90+ countries, Ghana default');
    console.log('   • University: Now dropdown for better UX');
    console.log('   • Location: Now dropdown for 16 Ghana regions');
    console.log('   • Undergraduate Year: Now dropdown for 6 year options');
    console.log('   • Other questions: Remain as radio buttons (2-5 options)');

  } catch (error) {
    console.error('❌ Error fixing questionnaire:', error);
  }
}

fixQuestionnaireDropdowns();
