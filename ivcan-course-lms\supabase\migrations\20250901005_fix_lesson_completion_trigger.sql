-- Fix the trigger on user_lesson_progress to use is_completed instead of completed

-- First drop the existing trigger
DROP TRIGGER IF EXISTS on_lesson_completion ON public.user_lesson_progress;
DROP TRIGGER IF EXISTS update_module_progress ON public.user_lesson_progress;

-- Fix the update_module_completion function to use is_completed
CREATE OR REPLACE FUNCTION update_module_completion()
RETURNS TRIGGER AS $$
DECLARE
  module_id_val UUID;
  user_id_val UUID;
  total_lessons INTEGER;
  completed_lessons INTEGER;
BEGIN
  -- Get the user ID
  user_id_val := NEW.user_id;
  
  -- Get the module ID for this lesson
  SELECT module_id INTO module_id_val FROM public.lessons WHERE id = NEW.lesson_id;
  
  IF module_id_val IS NULL THEN
    RAISE NOTICE 'No module found for lesson %', NEW.lesson_id;
    RETURN NEW;
  END IF;
  
  -- Count total lessons for this module
  SELECT COUNT(*) INTO total_lessons FROM public.lessons WHERE module_id = module_id_val;
  
  -- Count completed lessons for this user and module
  SELECT COUNT(*) INTO completed_lessons 
  FROM public.user_lesson_progress ulp
  JOIN public.lessons l ON ulp.lesson_id = l.id
  WHERE l.module_id = module_id_val 
    AND ulp.user_id = user_id_val 
    AND ulp.is_completed = TRUE;
  
  RAISE NOTICE 'Module % has % completed lessons out of %', module_id_val, completed_lessons, total_lessons;
  
  -- If all lessons are completed, mark the module as completed
  IF completed_lessons = total_lessons THEN
    UPDATE public.modules
    SET 
      is_completed = TRUE,
      updated_at = NOW()
    WHERE id = module_id_val;
    
    RAISE NOTICE 'Marked module % as completed', module_id_val;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate the triggers using is_completed
CREATE TRIGGER on_lesson_completion
  AFTER INSERT OR UPDATE OF is_completed ON public.user_lesson_progress
  FOR EACH ROW
  WHEN (NEW.is_completed = TRUE)
  EXECUTE FUNCTION update_module_completion();

-- Make sure the update_module_progress trigger also uses is_completed
CREATE TRIGGER update_module_progress
  AFTER INSERT OR UPDATE OF is_completed ON public.user_lesson_progress
  FOR EACH ROW
  EXECUTE FUNCTION public.update_module_progress(); 