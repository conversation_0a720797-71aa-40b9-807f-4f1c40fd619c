var w=(a,h,s)=>new Promise((p,i)=>{var f=r=>{try{n(s.next(r))}catch(c){i(c)}},u=r=>{try{n(s.throw(r))}catch(c){i(c)}},n=r=>r.done?p(r.value):Promise.resolve(r.value).then(f,u);n((s=s.apply(a,h)).next())});import{r as t,u as P,j as e,Y as g,av as k,au as C,ap as S,aq as M,E}from"./vendor-react.BcAa1DKr.js";import{a2 as m,a4 as o}from"./vendor.DQpuTRuB.js";import{B as b,I as j,s as I}from"./index.BLDhDn0D.js";import"./vendor-supabase.sufZ44-y.js";const z=()=>{const[a,h]=t.useState(""),[s,p]=t.useState(""),[i,f]=t.useState(!1),[u,n]=t.useState(!1),[r,c]=t.useState(!1),[x,N]=t.useState(!0),y=P();t.useEffect(()=>{window.location.hash?c(!0):m.error("Invalid or expired reset link. Please request a new one.")},[]),t.useEffect(()=>{s&&N(a===s)},[a,s]);const v=d=>w(void 0,null,function*(){if(d.preventDefault(),a!==s){m.error("Passwords do not match");return}if(a.length<6){m.error("Password must be at least 6 characters");return}n(!0);try{const{error:l}=yield I.auth.updateUser({password:a});if(l)throw l;m.success("Password updated successfully"),y("/login")}catch(l){console.error("Error resetting password:",l),m.error(l.message||"Failed to reset password. Please try again.")}finally{n(!1)}});return r?e.jsxs("div",{className:"min-h-screen flex flex-col lg:flex-row bg-background",children:[e.jsxs("div",{className:"hidden lg:flex lg:w-1/2 bg-primary/10 flex-col items-center justify-center p-12 relative overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/5 to-primary/30 z-0"}),e.jsx("div",{className:"absolute top-0 left-0 w-full h-full",children:Array.from({length:5}).map((d,l)=>e.jsx(o.div,{className:"absolute bg-primary/10 rounded-full",style:{width:Math.random()*300+50,height:Math.random()*300+50,top:`${Math.random()*100}%`,left:`${Math.random()*100}%`,filter:"blur(60px)"},animate:{y:[0,Math.random()*40-20,0],x:[0,Math.random()*40-20,0],opacity:[.3,.6,.3]},transition:{duration:8+Math.random()*5,repeat:1/0,ease:"easeInOut"}},l))}),e.jsxs("div",{className:"relative z-10 text-center space-y-8",children:[e.jsx(o.div,{initial:{scale:0},animate:{scale:1},transition:{duration:.6,type:"spring"},className:"flex justify-center",children:e.jsx("div",{className:"p-4 rounded-full bg-primary/20 backdrop-blur-md",children:e.jsx(k,{className:"w-24 h-24 text-primary"})})}),e.jsxs(o.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3,duration:.8},className:"space-y-4",children:[e.jsx("h1",{className:"text-3xl font-bold text-foreground",children:"Secure Password Reset"}),e.jsx("p",{className:"text-lg text-muted-foreground max-w-md mx-auto",children:"Create a strong new password for your account to continue your learning journey"})]})]})]}),e.jsx("div",{className:"w-full lg:w-1/2 flex items-center justify-center p-4 sm:p-6 lg:p-8",children:e.jsx(o.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"w-full max-w-md",children:e.jsxs("div",{className:"bg-card p-8 rounded-2xl shadow-sm border border-border space-y-6",children:[e.jsxs("button",{onClick:()=>y("/login"),className:"inline-flex items-center px-3 py-1.5 bg-card border border-border rounded-md text-sm font-medium text-foreground hover:bg-primary hover:text-white hover:border-primary transition-colors",children:[e.jsx(C,{className:"w-4 h-4 mr-1.5"}),"Back to Login"]}),e.jsxs("div",{className:"text-center mb-4",children:[e.jsx("h2",{className:"text-2xl font-bold text-foreground",children:"Reset Your Password"}),e.jsx("p",{className:"mt-2 text-muted-foreground text-sm",children:"Create a new secure password for your account"})]}),e.jsxs("form",{className:"space-y-6",onSubmit:v,children:[e.jsxs(o.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.1},className:"space-y-1.5",children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-foreground",children:"New Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(j,{id:"password",name:"password",type:i?"text":"password",required:!0,value:a,onChange:d=>h(d.target.value),className:"pr-10 py-6 bg-card/50",placeholder:"Create a secure password"}),e.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center text-muted-foreground hover:text-foreground transition-colors",onClick:()=>f(!i),children:i?e.jsx(S,{className:"h-5 w-5"}):e.jsx(M,{className:"h-5 w-5"})})]}),a&&a.length<6&&e.jsxs("p",{className:"text-xs text-amber-500 mt-1 flex items-center",children:[e.jsx(g,{className:"h-3 w-3 mr-1"}),"Password must be at least 6 characters"]})]}),e.jsxs(o.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.2},className:"space-y-1.5",children:[e.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-foreground",children:"Confirm New Password"}),e.jsxs("div",{className:"relative",children:[e.jsx(j,{id:"confirmPassword",name:"confirmPassword",type:i?"text":"password",required:!0,value:s,onChange:d=>p(d.target.value),className:`pr-10 py-6 bg-card/50 ${s&&!x?"border-red-500 focus:ring-red-500":""}`,placeholder:"Confirm your password"}),s&&x&&e.jsx(E,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-green-500"})]}),s&&!x&&e.jsxs("p",{className:"text-xs text-red-500 mt-1 flex items-center",children:[e.jsx(g,{className:"h-3 w-3 mr-1"}),"Passwords do not match"]})]}),e.jsx(o.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},children:e.jsxs(b,{type:"submit",className:"w-full relative overflow-hidden group h-11",disabled:u||!x||a.length<6,children:[e.jsx("span",{className:"absolute inset-0 w-full h-full bg-gradient-to-r from-primary/80 to-primary transition-all duration-300 transform group-hover:scale-102 opacity-0 group-hover:opacity-100"}),e.jsx("span",{className:"relative z-10 flex items-center justify-center",children:u?"Updating...":"Update Password"})]})})]})]})})})]}):e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background p-4 sm:p-6 lg:p-8",children:e.jsxs(o.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"max-w-md w-full space-y-8 bg-card p-8 rounded-xl shadow-sm border border-border",children:[e.jsx("div",{className:"flex justify-center",children:e.jsx("div",{className:"p-3 bg-red-100 dark:bg-red-900/30 rounded-full",children:e.jsx(g,{className:"h-8 w-8 text-red-500"})})}),e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-2xl font-bold text-foreground",children:"Invalid Reset Link"}),e.jsx("p",{className:"mt-2 text-muted-foreground",children:"This password reset link is invalid or has expired."})]}),e.jsx("div",{className:"mt-6",children:e.jsx(b,{onClick:()=>y("/login"),className:"w-full font-medium bg-primary hover:bg-primary/90 text-white",children:"Back to Login"})})]})})};export{z as default};
