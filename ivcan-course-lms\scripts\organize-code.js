/**
 * Code Organization Script
 * 
 * This script helps organize the codebase by:
 * 1. Identifying unused files
 * 2. Suggesting better organization for components
 * 3. Checking for duplicate code
 * 
 * Usage:
 * node scripts/organize-code.js [options]
 * 
 * Options:
 *   --check-unused    Check for unused files
 *   --check-duplicates Check for duplicate code
 *   --suggest-organization Suggest better organization
 *   --fix             Apply suggested fixes (USE WITH CAUTION)
 *   --help            Show this help message
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Parse command line arguments
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    checkUnused: false,
    checkDuplicates: false,
    suggestOrganization: false,
    fix: false,
    help: false
  };

  for (const arg of args) {
    if (arg === '--check-unused') options.checkUnused = true;
    else if (arg === '--check-duplicates') options.checkDuplicates = true;
    else if (arg === '--suggest-organization') options.suggestOrganization = true;
    else if (arg === '--fix') options.fix = true;
    else if (arg === '--help') options.help = true;
  }

  // If no specific options are provided, enable all checks
  if (!options.checkUnused && !options.checkDuplicates && !options.suggestOrganization) {
    options.checkUnused = true;
    options.checkDuplicates = true;
    options.suggestOrganization = true;
  }

  return options;
}

// Show help message
function showHelp() {
  console.log(`
Code Organization Script
------------------------

Usage: node scripts/organize-code.js [options]

Options:
  --check-unused           Check for unused files
  --check-duplicates       Check for duplicate code
  --suggest-organization   Suggest better organization
  --fix                    Apply suggested fixes (USE WITH CAUTION)
  --help                   Show this help message

Examples:
  node scripts/organize-code.js                     # Run all checks
  node scripts/organize-code.js --check-unused      # Only check for unused files
  node scripts/organize-code.js --fix               # Apply suggested fixes
  `);
}

// Check for unused files
async function checkUnusedFiles(fix = false) {
  console.log('Checking for unused files...');
  
  try {
    // Get all TypeScript/JavaScript files
    const srcDir = path.join(__dirname, '..', 'src');
    const allFiles = getAllFiles(srcDir, ['.ts', '.tsx', '.js', '.jsx']);
    
    // Get all imports from the codebase
    const importedFiles = getImportedFiles(srcDir);
    
    // Find files that are not imported anywhere
    const potentiallyUnusedFiles = allFiles.filter(file => {
      // Skip index files, types, and direct React component files
      if (path.basename(file) === 'index.ts' || 
          path.basename(file) === 'index.tsx' ||
          path.basename(file).includes('.d.ts') ||
          path.basename(file) === 'App.tsx' ||
          path.basename(file) === 'main.tsx') {
        return false;
      }
      
      // Check if the file is imported anywhere
      const relativePath = path.relative(srcDir, file).replace(/\\/g, '/');
      const isImported = importedFiles.some(importPath => 
        importPath.includes(relativePath) || 
        importPath.includes(relativePath.replace(/\.(ts|tsx|js|jsx)$/, ''))
      );
      
      return !isImported;
    });
    
    if (potentiallyUnusedFiles.length === 0) {
      console.log('No unused files found.');
      return [];
    }
    
    console.log(`Found ${potentiallyUnusedFiles.length} potentially unused files:`);
    potentiallyUnusedFiles.forEach(file => {
      console.log(`- ${path.relative(path.join(__dirname, '..'), file)}`);
    });
    
    if (fix) {
      console.log('\nWARNING: Fixing unused files is not implemented yet.');
      console.log('Please manually review and remove unused files if necessary.');
    }
    
    return potentiallyUnusedFiles;
  } catch (error) {
    console.error('Error checking for unused files:', error);
    return [];
  }
}

// Check for duplicate code
async function checkDuplicateCode(fix = false) {
  console.log('Checking for duplicate code...');
  
  try {
    // Get all TypeScript/JavaScript files
    const srcDir = path.join(__dirname, '..', 'src');
    const allFiles = getAllFiles(srcDir, ['.ts', '.tsx', '.js', '.jsx']);
    
    // Group files by their content hash
    const filesByHash = {};
    
    for (const file of allFiles) {
      const content = fs.readFileSync(file, 'utf8');
      const hash = getSimpleHash(content);
      
      if (!filesByHash[hash]) {
        filesByHash[hash] = [];
      }
      
      filesByHash[hash].push(file);
    }
    
    // Find duplicate files (same hash)
    const duplicates = Object.values(filesByHash).filter(files => files.length > 1);
    
    if (duplicates.length === 0) {
      console.log('No duplicate files found.');
      return [];
    }
    
    console.log(`Found ${duplicates.length} sets of duplicate files:`);
    duplicates.forEach((files, index) => {
      console.log(`\nDuplicate set #${index + 1}:`);
      files.forEach(file => {
        console.log(`- ${path.relative(path.join(__dirname, '..'), file)}`);
      });
    });
    
    if (fix) {
      console.log('\nWARNING: Fixing duplicate files is not implemented yet.');
      console.log('Please manually review and consolidate duplicate files if necessary.');
    }
    
    return duplicates;
  } catch (error) {
    console.error('Error checking for duplicate code:', error);
    return [];
  }
}

// Suggest better organization
async function suggestOrganization(fix = false) {
  console.log('Suggesting better organization...');
  
  try {
    const srcDir = path.join(__dirname, '..', 'src');
    const componentsDir = path.join(srcDir, 'components');
    
    // Check if components are organized by feature
    const componentFiles = getAllFiles(componentsDir, ['.tsx']);
    const componentsByFeature = {};
    
    for (const file of componentFiles) {
      const relativePath = path.relative(componentsDir, file);
      const parts = relativePath.split(path.sep);
      
      if (parts.length === 1) {
        // This is a component directly in the components directory
        const componentName = path.basename(file, path.extname(file));
        
        // Try to determine which feature it belongs to
        const content = fs.readFileSync(file, 'utf8');
        const feature = determineFeature(content, componentName);
        
        if (feature) {
          if (!componentsByFeature[feature]) {
            componentsByFeature[feature] = [];
          }
          
          componentsByFeature[feature].push(file);
        }
      }
    }
    
    // Print suggestions
    const suggestions = [];
    
    for (const [feature, files] of Object.entries(componentsByFeature)) {
      if (files.length > 1) {
        console.log(`\nSuggestion: Create a '${feature}' directory for these components:`);
        files.forEach(file => {
          console.log(`- ${path.basename(file)}`);
        });
        
        suggestions.push({
          feature,
          files
        });
      }
    }
    
    if (suggestions.length === 0) {
      console.log('No organization suggestions found.');
      return [];
    }
    
    if (fix) {
      console.log('\nWARNING: Fixing organization is not implemented yet.');
      console.log('Please manually organize components if necessary.');
    }
    
    return suggestions;
  } catch (error) {
    console.error('Error suggesting organization:', error);
    return [];
  }
}

// Helper function to get all files with specific extensions
function getAllFiles(dir, extensions) {
  let results = [];
  
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      results = results.concat(getAllFiles(filePath, extensions));
    } else {
      const ext = path.extname(filePath);
      if (extensions.includes(ext)) {
        results.push(filePath);
      }
    }
  }
  
  return results;
}

// Helper function to get all imported files
function getImportedFiles(dir) {
  const allFiles = getAllFiles(dir, ['.ts', '.tsx', '.js', '.jsx']);
  const importedFiles = new Set();
  
  for (const file of allFiles) {
    const content = fs.readFileSync(file, 'utf8');
    const importMatches = content.match(/from\s+['"]([^'"]+)['"]/g) || [];
    
    for (const match of importMatches) {
      const importPath = match.replace(/from\s+['"](.+)['"]/, '$1');
      
      if (!importPath.startsWith('.')) {
        // Skip external imports
        continue;
      }
      
      importedFiles.add(importPath);
    }
  }
  
  return Array.from(importedFiles);
}

// Helper function to get a simple hash of a string
function getSimpleHash(str) {
  let hash = 0;
  
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  
  return hash.toString(16);
}

// Helper function to determine which feature a component belongs to
function determineFeature(content, componentName) {
  // Check for common feature indicators in the content
  const featureIndicators = {
    'course': /course|lesson|module|curriculum/i,
    'auth': /auth|login|register|password|user/i,
    'admin': /admin|dashboard|manage/i,
    'ui': /button|input|form|modal|dialog|card|layout/i,
    'profile': /profile|account|settings/i,
    'quiz': /quiz|question|answer|assessment/i
  };
  
  for (const [feature, regex] of Object.entries(featureIndicators)) {
    if (regex.test(content) || regex.test(componentName)) {
      return feature;
    }
  }
  
  return null;
}

// Main function
async function main() {
  const options = parseArgs();
  
  if (options.help) {
    showHelp();
    return;
  }
  
  console.log('Code Organization Script');
  console.log('------------------------\n');
  
  if (options.checkUnused) {
    await checkUnusedFiles(options.fix);
    console.log('');
  }
  
  if (options.checkDuplicates) {
    await checkDuplicateCode(options.fix);
    console.log('');
  }
  
  if (options.suggestOrganization) {
    await suggestOrganization(options.fix);
    console.log('');
  }
  
  console.log('Done!');
}

// Run the script
main().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
