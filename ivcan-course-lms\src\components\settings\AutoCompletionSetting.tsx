import React, { useState, useEffect } from 'react';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { toast } from 'sonner';
import { AlertCircle, CheckCircle, RefreshCw, Lock, ShieldAlert } from 'lucide-react';
import { useQueryClient } from '@tanstack/react-query';
import { verifyTeacherRole } from '@/services/auth/roleService';
import { markAllCoursesAsCompleted, markAllModulesAndLessonsAsCompleted } from '@/services/course/autoCompletionService';
import { logEvent } from '@/services/analytics/analyticsService';

const AutoCompletionSetting = () => {
  const { user } = useAuth();
  const [autoCompleteEnabled, setAutoCompleteEnabled] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [resetting, setResetting] = useState(false);
  const [isTeacher, setIsTeacher] = useState(false);
  const [isRunningAutoComplete, setIsRunningAutoComplete] = useState(false);
  const queryClient = useQueryClient();

  useEffect(() => {
    const fetchUserData = async () => {
      if (!user) return;

      try {
        setLoading(true);

        // Check if user is a teacher using the secure service
        const isUserTeacher = await verifyTeacherRole(user.id);
        setIsTeacher(isUserTeacher);

        // Only fetch preferences if user is a teacher
        if (isUserTeacher) {
          const { data, error } = await supabase
            .from('user_preferences')
            .select('auto_complete_courses')
            .eq('user_id', user.id)
            .maybeSingle();

          if (error) {
            console.error('Error fetching user preferences:', error);
            toast.error('Failed to load your preferences');
          } else {
            setAutoCompleteEnabled(data?.auto_complete_courses || false);
          }
        } else {
          // Not a teacher, auto-completion is always disabled
          setAutoCompleteEnabled(false);
        }
      } catch (error) {
        console.error('Unexpected error:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [user]);

  const handleToggleAutoComplete = async () => {
    if (!user || !isTeacher) {
      toast.error('Only teachers can change this setting');
      return;
    }

    try {
      setSaving(true);
      const newValue = !autoCompleteEnabled;

      // Log the change
      logEvent('auto_completion_setting_changed', {
        userId: user.id,
        newValue
      });

      // Update or insert user preferences
      const { error } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: user.id,
          auto_complete_courses: newValue,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id'
        });

      if (error) {
        console.error('Error saving preferences:', error);
        toast.error('Failed to save your preferences');
      } else {
        setAutoCompleteEnabled(newValue);
        toast.success(`Auto-completion ${newValue ? 'enabled' : 'disabled'} successfully`);
      }
    } catch (error) {
      console.error('Unexpected error:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setSaving(false);
    }
  };

  // Add a function to manually trigger auto-completion
  const handleManualAutoComplete = async () => {
    if (!user || !isTeacher) {
      toast.error('Only teachers can use this feature');
      return;
    }

    try {
      setIsRunningAutoComplete(true);
      toast.loading('Running auto-completion...');

      // Generate a verification token
      const verificationToken = `auto-complete-${user.id}-${new Date().getDate()}`;

      // Mark all courses as completed with verification token
      // Pass true for isManualTrigger since this is manually triggered
      const courseSuccess = await markAllCoursesAsCompleted(user.id, verificationToken, true);

      if (courseSuccess) {
        // Mark all modules and lessons as completed
        const moduleSuccess = await markAllModulesAndLessonsAsCompleted(user.id, verificationToken, true);

        if (moduleSuccess) {
          toast.success('Auto-completion successful');

          // Invalidate queries to refresh data
          queryClient.invalidateQueries({ queryKey: ['courseModules'] });
          queryClient.invalidateQueries({ queryKey: ['dashboard-courses'] });
          queryClient.invalidateQueries({ queryKey: ['courses'] });
        } else {
          toast.error('Failed to complete modules and lessons');
        }
      } else {
        toast.error('Failed to auto-complete courses');
      }
    } catch (error) {
      console.error('Error in manual auto-completion:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsRunningAutoComplete(false);
    }
  };

  const handleResetModuleCompletion = async () => {
    if (!user) return;

    try {
      setResetting(true);
      toast.loading('Resetting progress...');

      // Log the reset
      logEvent('progress_reset', {
        userId: user.id
      });

      // Call the reset_module_completion function
      const { data, error } = await supabase.rpc('reset_module_completion', {
        p_user_id: user.id
      });

      if (error) {
        console.error('Error resetting module completion:', error);
        toast.error('Failed to reset your progress');
      } else {
        toast.success('Your progress has been reset successfully');

        // Invalidate queries to refresh data
        queryClient.invalidateQueries({ queryKey: ['courseModules'] });
        queryClient.invalidateQueries({ queryKey: ['dashboard-courses'] });
        queryClient.invalidateQueries({ queryKey: ['courses'] });
      }
    } catch (error) {
      console.error('Unexpected error:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setResetting(false);
    }
  };

  if (loading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <div className="h-5 w-5 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
            Loading preferences...
          </CardTitle>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Course Progress Settings</CardTitle>
        <CardDescription>
          Control how your course progress is managed
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex flex-col space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="auto-complete" className="text-base font-medium">
                Auto-complete courses {!isTeacher && <span className="text-xs text-muted-foreground ml-2">(Teachers only)</span>}
              </Label>
              <p className="text-sm text-muted-foreground">
                Automatically mark all courses, modules, and lessons as completed when you log in
              </p>
            </div>
            <Switch
              id="auto-complete"
              checked={autoCompleteEnabled}
              onCheckedChange={handleToggleAutoComplete}
              disabled={saving || !isTeacher}
            />
          </div>

          {!isTeacher && (
            <div className="rounded-lg border p-4 bg-blue-50 dark:bg-blue-950/20">
              <div className="flex gap-2 items-start">
                <Lock className="h-5 w-5 text-blue-600 dark:text-blue-500 flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-800 dark:text-blue-400">Teacher-only feature</h4>
                  <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                    Auto-completion is only available to teachers for testing purposes. Students must complete courses normally.
                  </p>
                </div>
              </div>
            </div>
          )}

          {autoCompleteEnabled && (
            <div className="rounded-lg border p-4 bg-yellow-50 dark:bg-yellow-950/20">
              <div className="flex gap-2 items-start">
                <AlertCircle className="h-5 w-5 text-yellow-600 dark:text-yellow-500 flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="font-medium text-yellow-800 dark:text-yellow-400">Auto-completion is enabled</h4>
                  <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                    This will automatically mark all courses, modules, and lessons as completed when you log in.
                    This is typically used for testing purposes.
                  </p>
                </div>
              </div>
            </div>
          )}

          {!autoCompleteEnabled && (
            <div className="rounded-lg border p-4 bg-red-50 dark:bg-red-950/20">
              <div className="flex gap-2 items-start">
                <CheckCircle className="h-5 w-5 text-red-600 dark:text-red-500 flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="font-medium text-red-800 dark:text-red-400">Normal progress tracking</h4>
                  <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                    Your course progress will be tracked normally as you complete lessons and modules.
                  </p>
                </div>
              </div>
            </div>
          )}

          {isTeacher && (
            <div className="mt-4 border-t pt-4">
              <h4 className="font-medium mb-2">Manual Auto-Completion</h4>
              <Button
                variant="outline"
                onClick={handleManualAutoComplete}
                disabled={isRunningAutoComplete}
                className="flex items-center gap-2 w-full"
              >
                <RefreshCw className={`h-4 w-4 ${isRunningAutoComplete ? 'animate-spin' : ''}`} />
                {isRunningAutoComplete ? 'Running Auto-Completion...' : 'Run Auto-Completion Now'}
              </Button>
              <p className="text-xs text-muted-foreground mt-1">
                Manually trigger auto-completion without waiting for login.
              </p>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter>
        <Button
          variant="outline"
          className="w-full"
          onClick={handleResetModuleCompletion}
          disabled={resetting}
        >
          {resetting ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Resetting progress...
            </>
          ) : (
            <>
              <RefreshCw className="mr-2 h-4 w-4" />
              Reset all progress
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default AutoCompletionSetting;
