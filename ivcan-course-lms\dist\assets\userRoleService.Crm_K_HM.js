var l=(e,o,r)=>new Promise((t,s)=>{var f=n=>{try{c(r.next(n))}catch(u){s(u)}},m=n=>{try{c(r.throw(n))}catch(u){s(u)}},c=n=>n.done?t(n.value):Promise.resolve(n.value).then(f,m);c((r=r.apply(e,o)).next())});import{t as p,s as i}from"./index.BLDhDn0D.js";import{a2 as a}from"./vendor.DQpuTRuB.js";import"./vendor-react.BcAa1DKr.js";import"./vendor-supabase.sufZ44-y.js";const g=new Map,w=30*60*1e3;function $(e){return l(this,null,function*(){if(!e)return null;const o=g.get(e);if(o&&Date.now()-o.timestamp<w)return o.role;try{const{data:r,error:t}=yield p(()=>l(this,null,function*(){return yield i.from("user_roles").select("role").eq("user_id",e).maybeSingle()}));if(t)return console.error("Error getting user role:",t),null;const s=(r==null?void 0:r.role)||null;return g.set(e,{role:s,timestamp:Date.now()}),s}catch(r){return console.error("Error in getUserRole:",r),null}})}function y(e,o){return l(this,null,function*(){if(!e||!o)return!1;try{console.log(`Assigning role '${o}' to user '${e}'`);const{data:r,error:t}=yield i.from("user_roles").select("*").eq("user_id",e).maybeSingle();if(t)return console.error("Error checking existing role:",t),a.error(`Failed to check existing role: ${t.message}`),!1;let s;return r?(console.log("User already has a role, updating"),s=yield i.from("user_roles").update({role:o}).eq("user_id",e)):(console.log("Creating new role for user"),s=yield i.from("user_roles").insert([{user_id:e,role:o}])),s.error?(console.error("Error assigning role:",s.error),a.error(`Failed to assign role: ${s.error.message}`),!1):(g.delete(e),a.success(`Role '${o}' assigned successfully`),!0)}catch(r){return console.error("Error in assignRole:",r),a.error(`Failed to assign role: ${r.message||"Unknown error"}`),!1}})}function x(e){return l(this,null,function*(){return yield y(e,"student")})}export{y as assignRole,x as assignStudentRole,$ as getUserRole};
