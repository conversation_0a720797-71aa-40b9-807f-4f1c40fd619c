/**
 * Comprehensive test suite for markdown lesson text implementation
 * Tests security, performance, GFM support, and user experience
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { EnhancedMarkdownEditor } from '@/components/ui/enhanced-markdown-editor';
import { MarkdownPreview } from '@/components/ui/markdown-preview';
import { contentSecurity, validateContentSecurity } from '@/lib/content-security';
import { contentCache } from '@/lib/content-cache';
import { tiptapToMarkdown } from '@/lib/enhanced-markdown-serializer';
import { markdownToHtml } from '@/lib/content-converter';

// Mock dependencies
vi.mock('@/lib/tiptap-image-upload', () => ({
  uploadEditorImage: vi.fn().mockResolvedValue('https://example.com/image.jpg'),
  validateImageFile: vi.fn().mockReturnValue({ valid: true }),
  createImagePreview: vi.fn().mockResolvedValue('data:image/jpeg;base64,test')
}));

vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn()
  }
}));

describe('Markdown Implementation Security Tests', () => {
  beforeEach(() => {
    contentCache.clear();
  });

  describe('Content Security Service', () => {
    it('should sanitize malicious script tags', () => {
      const maliciousContent = '<script>alert("xss")</script><p>Safe content</p>';
      const sanitized = contentSecurity.sanitizeHtml(maliciousContent);
      
      expect(sanitized).not.toContain('<script>');
      expect(sanitized).not.toContain('alert');
      expect(sanitized).toContain('Safe content');
    });

    it('should remove dangerous event handlers', () => {
      const maliciousContent = '<img src="test.jpg" onerror="alert(\'xss\')" onclick="malicious()">';
      const sanitized = contentSecurity.sanitizeHtml(maliciousContent);
      
      expect(sanitized).not.toContain('onerror');
      expect(sanitized).not.toContain('onclick');
      expect(sanitized).toContain('src="test.jpg"');
    });

    it('should validate iframe sources', () => {
      const youtubeEmbed = '<iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ"></iframe>';
      const maliciousEmbed = '<iframe src="javascript:alert(\'xss\')"></iframe>';
      
      const sanitizedYoutube = contentSecurity.sanitizeHtml(youtubeEmbed);
      const sanitizedMalicious = contentSecurity.sanitizeHtml(maliciousEmbed);
      
      expect(sanitizedYoutube).toContain('youtube.com');
      expect(sanitizedMalicious).not.toContain('javascript:');
    });

    it('should validate content structure and encoding', () => {
      const validContent = 'This is valid UTF-8 content with émojis 🎉';
      const invalidContent = '<script>alert("xss")</script>';
      
      const validResult = validateContentSecurity(validContent);
      const invalidResult = validateContentSecurity(invalidContent);
      
      expect(validResult.valid).toBe(true);
      expect(invalidResult.valid).toBe(false);
      expect(invalidResult.errors).toContain(expect.stringContaining('dangerous pattern'));
    });

    it('should enforce content size limits', () => {
      const largeContent = 'a'.repeat(2000000); // 2MB content
      
      expect(() => {
        contentSecurity.sanitizeHtml(largeContent);
      }).toThrow('Content exceeds maximum length');
    });
  });

  describe('Content Caching', () => {
    it('should cache processed markdown content', () => {
      const content = '# Test Heading\n\nThis is test content.';
      
      // First call should be a cache miss
      const result1 = markdownToHtml(content);
      const stats1 = contentCache.getStats();
      
      // Second call should be a cache hit
      const result2 = markdownToHtml(content);
      const stats2 = contentCache.getStats();
      
      expect(result1).toBe(result2);
      expect(stats2.hits).toBeGreaterThan(stats1.hits);
      expect(stats2.hitRate).toBeGreaterThan(0);
    });

    it('should invalidate cache when content changes', () => {
      const content1 = '# Original Content';
      const content2 = '# Modified Content';
      
      const result1 = markdownToHtml(content1);
      const result2 = markdownToHtml(content2);
      
      expect(result1).not.toBe(result2);
      expect(result1).toContain('Original');
      expect(result2).toContain('Modified');
    });

    it('should handle cache size limits with LRU eviction', () => {
      const cache = contentCache;
      const initialSize = cache.getStats().size;
      
      // Fill cache beyond capacity
      for (let i = 0; i < 250; i++) {
        markdownToHtml(`# Content ${i}\n\nTest content ${i}`);
      }
      
      const finalStats = cache.getStats();
      expect(finalStats.size).toBeLessThanOrEqual(finalStats.maxSize);
    });
  });
});

describe('GFM Support Tests', () => {
  it('should support task lists', () => {
    const taskListMarkdown = `
- [x] Completed task
- [ ] Incomplete task
- [x] Another completed task
    `;
    
    const html = markdownToHtml(taskListMarkdown);
    
    expect(html).toContain('checked');
    expect(html).toContain('type="checkbox"');
  });

  it('should support tables with proper formatting', () => {
    const tableMarkdown = `
| Header 1 | Header 2 | Header 3 |
|----------|----------|----------|
| Cell 1   | Cell 2   | Cell 3   |
| Cell 4   | Cell 5   | Cell 6   |
    `;
    
    const html = markdownToHtml(tableMarkdown);
    
    expect(html).toContain('<table');
    expect(html).toContain('<thead');
    expect(html).toContain('<tbody');
    expect(html).toContain('<th');
    expect(html).toContain('<td');
  });

  it('should support strikethrough text', () => {
    const strikethroughMarkdown = 'This is ~~strikethrough~~ text.';
    const html = markdownToHtml(strikethroughMarkdown);
    
    expect(html).toContain('<del>') || expect(html).toContain('<s>');
  });

  it('should support highlighted text', () => {
    const highlightMarkdown = 'This is ==highlighted== text.';
    const html = markdownToHtml(highlightMarkdown);
    
    expect(html).toContain('highlighted');
  });

  it('should support code blocks with syntax highlighting', () => {
    const codeBlockMarkdown = `
\`\`\`javascript
function hello() {
  console.log("Hello, world!");
}
\`\`\`
    `;
    
    const html = markdownToHtml(codeBlockMarkdown);
    
    expect(html).toContain('<pre');
    expect(html).toContain('<code');
    expect(html).toContain('javascript');
  });
});

describe('Enhanced Markdown Editor Tests', () => {
  it('should render with all toolbar buttons', () => {
    render(<EnhancedMarkdownEditor />);
    
    // Check for formatting buttons
    expect(screen.getByTitle('Bold (Ctrl+B)')).toBeInTheDocument();
    expect(screen.getByTitle('Italic (Ctrl+I)')).toBeInTheDocument();
    expect(screen.getByTitle('Strikethrough')).toBeInTheDocument();
    expect(screen.getByTitle('Highlight')).toBeInTheDocument();
    
    // Check for list buttons
    expect(screen.getByTitle('Bullet List')).toBeInTheDocument();
    expect(screen.getByTitle('Numbered List')).toBeInTheDocument();
    expect(screen.getByTitle('Task List')).toBeInTheDocument();
    
    // Check for media buttons
    expect(screen.getByTitle('Add Link')).toBeInTheDocument();
    expect(screen.getByTitle('Add Image')).toBeInTheDocument();
    expect(screen.getByTitle('Add Table')).toBeInTheDocument();
  });

  it('should switch between editor, preview, and split modes', async () => {
    const user = userEvent.setup();
    render(<EnhancedMarkdownEditor initialContent="# Test Content" />);
    
    // Check initial editor mode
    expect(screen.getByText('Edit')).toHaveClass('bg-default') || 
           expect(screen.getByText('Edit')).toHaveAttribute('data-state', 'active');
    
    // Switch to preview mode
    await user.click(screen.getByText('Preview'));
    expect(screen.getByText('Test Content')).toBeInTheDocument();
    
    // Switch to split mode
    await user.click(screen.getByText('Split'));
    // Should show both editor and preview
  });

  it('should handle auto-save functionality', async () => {
    const mockOnSave = vi.fn().mockResolvedValue(undefined);
    render(
      <EnhancedMarkdownEditor 
        autoSave={true}
        autoSaveInterval={100}
        onSave={mockOnSave}
      />
    );
    
    // Simulate content change
    // Note: This would require more complex TipTap editor interaction
    // For now, we'll test the save function directly
    
    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalled();
    }, { timeout: 200 });
  });

  it('should handle image upload with validation', async () => {
    const user = userEvent.setup();
    render(<EnhancedMarkdownEditor courseId="test-course" moduleId="test-module" />);
    
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const input = screen.getByTitle('Add Image').closest('div')?.querySelector('input[type="file"]');
    
    if (input) {
      await user.upload(input, file);
      // Should trigger image upload process
    }
  });
});

describe('Performance Tests', () => {
  it('should handle large content efficiently', () => {
    const largeContent = Array(1000).fill('# Heading\n\nParagraph content with **bold** and *italic* text.\n\n').join('');
    
    const startTime = performance.now();
    const html = markdownToHtml(largeContent);
    const endTime = performance.now();
    
    expect(html).toBeTruthy();
    expect(endTime - startTime).toBeLessThan(1000); // Should process in under 1 second
  });

  it('should benefit from caching on repeated processing', () => {
    const content = '# Test Content\n\nThis is test content for performance testing.';
    
    // First processing (cache miss)
    const start1 = performance.now();
    markdownToHtml(content);
    const end1 = performance.now();
    
    // Second processing (cache hit)
    const start2 = performance.now();
    markdownToHtml(content);
    const end2 = performance.now();
    
    const firstTime = end1 - start1;
    const secondTime = end2 - start2;
    
    expect(secondTime).toBeLessThan(firstTime);
  });
});

describe('Accessibility Tests', () => {
  it('should have proper ARIA labels and keyboard navigation', () => {
    render(<EnhancedMarkdownEditor />);
    
    // Check for accessible toolbar buttons
    const boldButton = screen.getByTitle('Bold (Ctrl+B)');
    expect(boldButton).toHaveAttribute('type', 'button');
    
    // Check for keyboard shortcuts in titles
    expect(boldButton).toHaveAttribute('title', expect.stringContaining('Ctrl+B'));
  });

  it('should support keyboard navigation in editor', () => {
    render(<EnhancedMarkdownEditor />);
    
    // The TipTap editor should be focusable
    const editorContent = document.querySelector('.ProseMirror');
    expect(editorContent).toBeTruthy();
  });
});

describe('Integration Tests', () => {
  it('should maintain content consistency between editor and preview', async () => {
    const user = userEvent.setup();
    const testContent = '# Test\n\n- [x] Task 1\n- [ ] Task 2\n\n**Bold text**';
    
    render(<EnhancedMarkdownEditor initialContent={testContent} />);
    
    // Switch to preview mode
    await user.click(screen.getByText('Preview'));
    
    // Check that content is properly rendered
    expect(screen.getByText('Test')).toBeInTheDocument();
    expect(screen.getByText('Bold text')).toBeInTheDocument();
  });

  it('should handle version history when enabled', () => {
    render(<EnhancedMarkdownEditor showVersionHistory={true} />);
    
    expect(screen.getByText('History')).toBeInTheDocument();
  });
});
