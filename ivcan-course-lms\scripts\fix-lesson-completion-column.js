/**
 * <PERSON><PERSON><PERSON> to fix user_lesson_progress table schema issues
 * 
 * This script standardizes the column naming to use 'is_completed' 
 * instead of 'completed' to ensure consistency across the application.
 */

const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.VITE_SUPABASE_SERVICE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('Missing required environment variables: VITE_SUPABASE_URL or VITE_SUPABASE_SERVICE_KEY');
  process.exit(1);
}

// Create Supabase client with admin rights
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function fixLessonCompletionColumn() {
  try {
    console.log('Checking for lesson completion column inconsistencies...');

    // Check for both column names in the user_lesson_progress table
    const { data: columns, error: columnError } = await supabase
      .from('information_schema.columns')
      .select('column_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'user_lesson_progress');

    if (columnError) {
      throw new Error(`Error fetching column information: ${columnError.message}`);
    }

    const columnNames = columns.map(col => col.column_name);
    const hasCompletedColumn = columnNames.includes('completed');
    const hasIsCompletedColumn = columnNames.includes('is_completed');

    console.log('Current columns:', columnNames.join(', '));
    console.log(`Has 'completed' column: ${hasCompletedColumn}`);
    console.log(`Has 'is_completed' column: ${hasIsCompletedColumn}`);

    if (hasCompletedColumn && !hasIsCompletedColumn) {
      // Need to rename 'completed' to 'is_completed'
      console.log("Renaming 'completed' column to 'is_completed'...");
      
      const { error: renameError } = await supabase.rpc(
        'execute_sql',
        { 
          query: 'ALTER TABLE public.user_lesson_progress RENAME COLUMN completed TO is_completed;' 
        }
      );

      if (renameError) {
        throw new Error(`Error renaming column: ${renameError.message}`);
      }
      
      console.log("✅ Successfully renamed 'completed' to 'is_completed'");
    } else if (!hasCompletedColumn && !hasIsCompletedColumn) {
      // Neither column exists, create the 'is_completed' column
      console.log("Creating 'is_completed' column...");
      
      const { error: addError } = await supabase.rpc(
        'execute_sql',
        { 
          query: 'ALTER TABLE public.user_lesson_progress ADD COLUMN is_completed BOOLEAN DEFAULT false;' 
        }
      );

      if (addError) {
        throw new Error(`Error adding column: ${addError.message}`);
      }
      
      console.log("✅ Successfully added 'is_completed' column");
    } else if (hasCompletedColumn && hasIsCompletedColumn) {
      // Both columns exist, we need to migrate data and drop the old column
      console.log("Both 'completed' and 'is_completed' columns exist. Migrating data...");
      
      // Copy data from 'completed' to 'is_completed' where 'is_completed' is null
      const { error: updateError } = await supabase.rpc(
        'execute_sql',
        { 
          query: `
            UPDATE public.user_lesson_progress 
            SET is_completed = completed 
            WHERE is_completed IS NULL AND completed IS NOT NULL;
          `
        }
      );

      if (updateError) {
        throw new Error(`Error updating data: ${updateError.message}`);
      }
      
      // Drop the 'completed' column
      const { error: dropError } = await supabase.rpc(
        'execute_sql',
        { 
          query: 'ALTER TABLE public.user_lesson_progress DROP COLUMN completed;' 
        }
      );

      if (dropError) {
        throw new Error(`Error dropping column: ${dropError.message}`);
      }
      
      console.log("✅ Successfully migrated data and dropped 'completed' column");
    } else {
      console.log("✅ Table schema is already using the standardized 'is_completed' column");
    }

    // Add an index on user_id and lesson_id for better query performance
    console.log("Creating index on (user_id, lesson_id) if it doesn't exist...");
    
    const { error: indexError } = await supabase.rpc(
      'execute_sql',
      { 
        query: `
          CREATE INDEX IF NOT EXISTS idx_user_lesson_progress_user_lesson 
          ON public.user_lesson_progress (user_id, lesson_id);
        `
      }
    );

    if (indexError) {
      throw new Error(`Error creating index: ${indexError.message}`);
    }
    
    console.log("✅ Successfully created or confirmed index on (user_id, lesson_id)");

    // Create index on is_completed for filtering
    console.log("Creating index on is_completed if it doesn't exist...");
    
    const { error: completedIndexError } = await supabase.rpc(
      'execute_sql',
      { 
        query: `
          CREATE INDEX IF NOT EXISTS idx_user_lesson_progress_is_completed 
          ON public.user_lesson_progress (is_completed);
        `
      }
    );

    if (completedIndexError) {
      throw new Error(`Error creating index on is_completed: ${completedIndexError.message}`);
    }
    
    console.log("✅ Successfully created or confirmed index on is_completed");

    console.log("✅ All schema fixes applied successfully");
  } catch (error) {
    console.error('Error fixing lesson completion column:', error.message);
    process.exit(1);
  }
}

// Run the fix function
fixLessonCompletionColumn().then(() => {
  console.log('Script completed successfully');
  process.exit(0);
}); 