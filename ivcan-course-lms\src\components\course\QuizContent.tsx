import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Loader2, CheckCircle, XCircle, HelpCircle, ThumbsUp } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { useAuth } from '@/context/AuthContext';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { completeLessonProgress } from '@/services/course/progressApi';

interface QuizOption {
  text: string;
  isCorrect: boolean;
}

interface QuizQuestion {
  id: string;
  question: string;
  options: string[];
  correctAnswer?: number;
  type?: string;
  minRating?: number;
  maxRating?: number;
  minLabel?: string;
  maxLabel?: string;
}

interface QuizContentProps {
  lessonId: string;
  content: string;
  onComplete?: () => void;
}

const QuizContent: React.FC<QuizContentProps> = ({ lessonId, content, onComplete }) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [quizData, setQuizData] = useState<{ questions: QuizQuestion[], quizType?: string } | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState<number[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [quizCompleted, setQuizCompleted] = useState(false);
  const [score, setScore] = useState(0);
  const [showResults, setShowResults] = useState(false);
  const [isQuestionnaire, setIsQuestionnaire] = useState(false);

  useEffect(() => {
    try {
      console.log('QuizContent - Raw content:', content);
      const parsedContent = JSON.parse(content);
      console.log('QuizContent - Parsed content:', parsedContent);

      // Validate questions array
      if (!parsedContent.questions || !Array.isArray(parsedContent.questions)) {
        console.error('QuizContent - Invalid quiz data: questions array is missing or not an array');
        setQuizData(null);
        return;
      }

      // Check if any questions exist
      if (parsedContent.questions.length === 0) {
        console.error('QuizContent - Invalid quiz data: no questions found');
        setQuizData(null);
        return;
      }

      // Log each question for debugging
      parsedContent.questions.forEach((q, i) => {
        console.log(`QuizContent - Question ${i}:`, q);
      });

      setQuizData(parsedContent);
      setSelectedAnswers(new Array(parsedContent.questions.length).fill(-1));

      // Check if this is a questionnaire
      const isQuestionnaireType =
        parsedContent.quizType === 'questionnaire' ||
        parsedContent.questions.some(q => q.type === 'rating_scale');

      console.log('QuizContent - Is questionnaire:', isQuestionnaireType);
      setIsQuestionnaire(isQuestionnaireType);
    } catch (error) {
      console.error('Failed to parse quiz content:', error);
      setQuizData(null);
    }
  }, [content]);

  const completeQuizMutation = useMutation({
    mutationFn: async () => {
      if (!user) throw new Error('User not authenticated');

      return await completeLessonProgress(lessonId, user.id);
    },
    onSuccess: () => {
      // Invalidate all relevant queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ['lesson', lessonId],
      });
      // Get courseId from URL path
      const pathParts = window.location.pathname.split('/');
      const courseIdIndex = pathParts.indexOf('course') + 1;
      const courseId = courseIdIndex > 0 && courseIdIndex < pathParts.length ? pathParts[courseIdIndex] : null;

      if (courseId) {
        queryClient.invalidateQueries({
          queryKey: ['courseModules', courseId],
        });
      }

      queryClient.invalidateQueries({
        queryKey: ['dashboard-courses', user?.id],
      });

      // Also invalidate the main dashboard query to update progress
      queryClient.invalidateQueries({
        queryKey: ['courses'],
      });

      if (onComplete) onComplete();

      toast({
        title: "Quiz completed!",
        description: `You scored ${score} out of ${quizData?.questions.length} questions.`,
      });
    },
    onError: (error) => {
      console.error('Error saving quiz progress:', error);
      toast({
        title: "Error",
        description: "Failed to save your quiz progress.",
        variant: "destructive",
      });
    }
  });

  const handleAnswerSelect = (answerIndex: number) => {
    const newAnswers = [...selectedAnswers];
    newAnswers[currentQuestionIndex] = answerIndex;
    setSelectedAnswers(newAnswers);
  };

  const handleNext = () => {
    // Check if all questions have been answered
    if (selectedAnswers[currentQuestionIndex] === -1) {
      toast({
        title: "Please select an answer",
        description: "You need to select an answer before proceeding.",
        variant: "destructive",
      });
      return;
    }

    if (currentQuestionIndex < (quizData?.questions.length || 0) - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      // For questionnaires, we don't calculate a score based on correct answers
      if (isQuestionnaire) {
        console.log('Completing questionnaire...');
        setScore(quizData?.questions.length || 0); // All answers are valid in a questionnaire
        setQuizCompleted(true);
        setShowResults(true);

        if (user) {
          completeQuizMutation.mutate();
        }
      } else {
        // For standard quizzes, calculate score based on correct answers
        let correctAnswers = 0;
        quizData?.questions.forEach((question, index) => {
          if (selectedAnswers[index] === question.correctAnswer) {
            correctAnswers++;
          }
        });

        const finalScore = correctAnswers;
        setScore(finalScore);
        setQuizCompleted(true);
        setShowResults(true);

        if (user) {
          completeQuizMutation.mutate();
        }
      }
    }
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const resetQuiz = () => {
    setCurrentQuestionIndex(0);
    setSelectedAnswers(new Array(quizData?.questions.length || 0).fill(-1));
    setQuizCompleted(false);
    setShowResults(false);
  };

  if (!quizData) {
    return (
      <Card className="my-6">
        <CardContent className="flex items-center justify-center py-6">
          <div className="text-center">
            <HelpCircle className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
            <p className="text-muted-foreground">Quiz content could not be loaded</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const currentQuestion = quizData.questions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / quizData.questions.length) * 100;

  if (showResults) {
    // Different results display for questionnaires vs standard quizzes
    if (isQuestionnaire) {
      return (
        <Card className="my-6">
          <CardHeader>
            <CardTitle>Questionnaire Complete</CardTitle>
            <CardDescription>
              Thank you for completing this questionnaire
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="text-center py-6">
              <ThumbsUp className="h-16 w-16 text-red-500 mx-auto mb-4" />
              <p className="text-lg font-medium">Your responses have been recorded</p>
              <p className="text-muted-foreground mt-2">
                Thank you for providing your feedback. Your responses will help us improve our services.
              </p>
            </div>
          </CardContent>
          <CardFooter className="flex justify-center">
            <Button variant="default" onClick={onComplete}>
              Continue
            </Button>
          </CardFooter>
        </Card>
      );
    }

    // Standard quiz results with correct/incorrect answers
    return (
      <Card className="my-6">
        <CardHeader>
          <CardTitle>Quiz Results</CardTitle>
          <CardDescription>
            You scored {score} out of {quizData.questions.length} questions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {quizData.questions.map((question, qIndex) => (
            <div key={question.id || qIndex} className="border rounded-lg p-4">
              <div className="flex items-start mb-3">
                <div className="flex-shrink-0 mr-3">
                  {selectedAnswers[qIndex] === question.correctAnswer ? (
                    <CheckCircle className="h-5 w-5 text-red-500" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500" />
                  )}
                </div>
                <div>
                  <p className="font-medium mb-2">Question {qIndex + 1}: {question.question}</p>
                  <div className="space-y-2">
                    {question.options.map((option, oIndex) => (
                      <div
                        key={oIndex}
                        className={`px-3 py-2 text-sm rounded ${
                          oIndex === question.correctAnswer
                            ? 'bg-red-100 border border-red-200'
                            : selectedAnswers[qIndex] === oIndex
                              ? 'bg-red-100/50 border border-red-200'
                              : 'bg-gray-50 border border-gray-200'
                        }`}
                      >
                        {typeof option === 'string' ? option : option.text || ''}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={resetQuiz}>
            Retry Quiz
          </Button>
          <Button variant="default" onClick={onComplete}>
            Complete
          </Button>
        </CardFooter>
      </Card>
    );
  }

  // Determine the title based on quiz type
  const contentTitle = isQuestionnaire ? "Questionnaire" : "Quiz";

  try {
    return (
      <Card className="my-6">
        <CardHeader>
          <div className="flex justify-between items-center mb-2">
            <CardTitle className="text-lg">{contentTitle}</CardTitle>
            <span className="text-sm text-muted-foreground">
              Question {currentQuestionIndex + 1} of {quizData.questions.length}
            </span>
          </div>
          <Progress value={progress} className="h-2" />
        </CardHeader>
        <CardContent>
          {currentQuestion ? (
            <div className="space-y-6">
              <h3 className="text-lg font-medium">{currentQuestion.question}</h3>

              {/* For rating scale questions in questionnaires */}
              {isQuestionnaire ? (
                <div className="pt-2">
                  <div className="flex justify-between text-sm mb-2">
                    <span>{currentQuestion.minLabel || 'Poor'}</span>
                    <span>{currentQuestion.maxLabel || 'Excellent'}</span>
                  </div>
                  <RadioGroup
                    value={selectedAnswers[currentQuestionIndex] === -1 ? '' : selectedAnswers[currentQuestionIndex].toString()}
                    onValueChange={(value) => handleAnswerSelect(parseInt(value))}
                    className="flex justify-between gap-1"
                  >
                    {Array.isArray(currentQuestion.options) && currentQuestion.options.map((option, index) => (
                      <div key={index} className="flex flex-col items-center">
                        <RadioGroupItem value={index.toString()} id={`option-${index}`} className="mb-1" />
                        <Label htmlFor={`option-${index}`} className="text-xs">
                          {typeof option === 'string' ? option : (option && option.text) || index + 1}
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                </div>
              ) : (
                /* Standard multiple choice questions */
                <RadioGroup
                  value={selectedAnswers[currentQuestionIndex] === -1 ? '' : selectedAnswers[currentQuestionIndex].toString()}
                  onValueChange={(value) => handleAnswerSelect(parseInt(value))}
                  className="space-y-3"
                >
                  {Array.isArray(currentQuestion.options) && currentQuestion.options.map((option, index) => (
                    <div key={index} className="flex items-center space-x-2 border rounded-md p-3 hover:bg-gray-50 transition-colors">
                      <RadioGroupItem value={index.toString()} id={`option-${index}`} />
                      <Label htmlFor={`option-${index}`} className="flex-1 cursor-pointer">
                        {typeof option === 'string' ? option : (option && option.text) || ''}
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              )}
            </div>
          ) : (
            <div className="flex justify-center py-4">
              <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentQuestionIndex === 0}
          >
            Previous
          </Button>
          <Button
            onClick={handleNext}
            disabled={selectedAnswers[currentQuestionIndex] === -1 || isSubmitting}
          >
            {currentQuestionIndex === quizData.questions.length - 1 ? 'Finish' : 'Next'}
          </Button>
        </CardFooter>
      </Card>
    );
  } catch (error) {
    console.error('Error rendering quiz content:', error);

    // Fallback UI in case of rendering errors
    return (
      <Card className="my-6">
        <CardHeader>
          <CardTitle className="text-lg">Error Loading {contentTitle}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <HelpCircle className="h-12 w-12 text-red-500 mx-auto mb-3" />
            <p className="text-muted-foreground">
              There was an error loading this content. Please try refreshing the page.
            </p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => window.location.reload()}
            >
              Refresh Page
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }
};

export default QuizContent;
