
import React, { useState, useCallback } from 'react';
import { Upload, X } from 'lucide-react';
import { fileToDataUrl } from '@/lib/file-upload';

interface FileDropZoneProps {
  onFileSelected: (dataUrl: string, file: File) => void;
  accept?: string;
  fileType: 'image' | 'video';
  currentUrl?: string;
  onClear?: () => void;
}

const FileDropZone: React.FC<FileDropZoneProps> = ({
  onFileSelected,
  accept = 'image/*,video/*',
  fileType,
  currentUrl,
  onClear
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback(async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0];
      await processFile(file);
    }
  }, [onFileSelected]);

  const handleFileChange = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      await processFile(file);
    }
  }, [onFileSelected]);

  const processFile = async (file: File) => {
    try {
      setIsUploading(true);

      // Basic validation
      if (fileType === 'image' && !file.type.startsWith('image/')) {
        throw new Error('Please upload an image file');
      }
      if (fileType === 'video' && !file.type.startsWith('video/')) {
        throw new Error('Please upload a video file');
      }

      const dataUrl = await fileToDataUrl(file);
      onFileSelected(dataUrl, file);
    } catch (error) {
      console.error('Error processing file:', error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleClearFile = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (onClear) onClear();
  };

  return (
    <div
      className={`border-2 border-dashed rounded-lg p-6 transition-colors ${
        isDragging
          ? 'border-primary bg-primary/5 dark:bg-primary/10'
          : 'border-border hover:border-primary/50'
      } ${isUploading ? 'opacity-70' : ''}`}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      onClick={() => document.getElementById(`file-input-${fileType}`)?.click()}
    >
      <input
        id={`file-input-${fileType}`}
        type="file"
        accept={accept}
        className="hidden"
        onChange={handleFileChange}
      />

      <div className="flex flex-col items-center justify-center gap-2">
        {currentUrl ? (
          <div className="relative w-full">
            {fileType === 'image' && (
              <img
                src={currentUrl}
                alt="Uploaded file"
                className="rounded max-h-[200px] mx-auto object-contain mb-2"
              />
            )}
            {fileType === 'video' && (
              <video
                src={currentUrl.startsWith('data:') ? currentUrl : undefined}
                controls
                className="rounded max-h-[200px] w-full mb-2"
              >
                {!currentUrl.startsWith('data:') && (
                  <source src={currentUrl} type="video/mp4" />
                )}
                Your browser does not support video playback.
              </video>
            )}
            <button
              className="absolute top-0 right-0 bg-destructive text-destructive-foreground p-1 rounded-full"
              onClick={handleClearFile}
            >
              <X size={16} />
            </button>
          </div>
        ) : (
          <>
            <Upload className={`${isUploading ? 'animate-bounce' : ''} h-12 w-12 text-muted-foreground/70`} />
            <p className="text-sm text-center font-medium">
              {isUploading
                ? 'Processing...'
                : `Drag & drop your ${fileType} here or click to browse`}
            </p>
            <p className="text-xs text-muted-foreground text-center">
              {fileType === 'image'
                ? 'JPEG, PNG, GIF, SVG up to 5MB'
                : 'MP4, WebM up to 50MB'}
            </p>
          </>
        )}
      </div>
    </div>
  );
};

export default FileDropZone;
