/**
 * User Role Service
 * 
 * A unified service for managing user roles in the application.
 * This service provides functions for checking, assigning, and managing user roles.
 */

import { supabase } from '@/integrations/supabase/client';
import { executeWithRetry } from '@/lib/connection-manager';
import { toast } from 'sonner';

// Define the UserRole type
export type UserRole = 'student' | 'teacher' | null;

// Cache for user roles to avoid repeated fetches
const userRoleCache = new Map<string, { role: UserRole; timestamp: number }>();
const CACHE_EXPIRY = 30 * 60 * 1000; // 30 minutes

/**
 * Get a user's role with caching
 * @param userId The user ID to check
 * @returns The user's role or null if not found
 */
export async function getUserRole(userId: string): Promise<UserRole> {
  if (!userId) return null;
  
  // Check cache first
  const cachedRole = userRoleCache.get(userId);
  if (cachedRole && (Date.now() - cachedRole.timestamp) < CACHE_EXPIRY) {
    return cachedRole.role;
  }
  
  try {
    const { data, error } = await executeWithRetry(async () => {
      return await supabase
        .from('user_roles')
        .select('role')
        .eq('user_id', userId)
        .maybeSingle();
    });
    
    if (error) {
      console.error('Error getting user role:', error);
      return null;
    }
    
    const role = (data?.role as UserRole) || null;
    
    // Update cache
    userRoleCache.set(userId, { role, timestamp: Date.now() });
    
    return role;
  } catch (error) {
    console.error('Error in getUserRole:', error);
    return null;
  }
}

/**
 * Check if a user has a specific role
 * @param userId The user ID to check
 * @param role The role to check for
 * @returns True if the user has the role, false otherwise
 */
export async function hasRole(userId: string, role: string): Promise<boolean> {
  if (!userId || !role) return false;
  
  try {
    const userRole = await getUserRole(userId);
    return userRole === role;
  } catch (error) {
    console.error('Error in hasRole:', error);
    return false;
  }
}

/**
 * Verify if a user is a teacher
 * @param userId The user ID to check
 * @returns True if the user is a teacher, false otherwise
 */
export async function verifyTeacherRole(userId: string): Promise<boolean> {
  return await hasRole(userId, 'teacher');
}

/**
 * Assign a role to a user
 * @param userId The user ID to assign the role to
 * @param role The role to assign
 * @returns True if successful, false otherwise
 */
export async function assignRole(userId: string, role: UserRole): Promise<boolean> {
  if (!userId || !role) return false;
  
  try {
    console.log(`Assigning role '${role}' to user '${userId}'`);
    
    // Check if the user already has a role
    const { data: existingRole, error: checkError } = await supabase
      .from('user_roles')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();
    
    if (checkError) {
      console.error('Error checking existing role:', checkError);
      toast.error(`Failed to check existing role: ${checkError.message}`);
      return false;
    }
    
    let result;
    
    if (existingRole) {
      // Update existing role
      console.log('User already has a role, updating');
      result = await supabase
        .from('user_roles')
        .update({ role })
        .eq('user_id', userId);
    } else {
      // Insert new role
      console.log('Creating new role for user');
      result = await supabase
        .from('user_roles')
        .insert([{ user_id: userId, role }]);
    }
    
    if (result.error) {
      console.error('Error assigning role:', result.error);
      toast.error(`Failed to assign role: ${result.error.message}`);
      return false;
    }
    
    // Clear cache for this user
    userRoleCache.delete(userId);
    
    toast.success(`Role '${role}' assigned successfully`);
    return true;
  } catch (error: any) {
    console.error('Error in assignRole:', error);
    toast.error(`Failed to assign role: ${error.message || 'Unknown error'}`);
    return false;
  }
}

/**
 * Assign the teacher role to a user
 * @param userId The user ID to make a teacher
 * @returns True if successful, false otherwise
 */
export async function assignTeacherRole(userId: string): Promise<boolean> {
  return await assignRole(userId, 'teacher');
}

/**
 * Assign the student role to a user
 * @param userId The user ID to make a student
 * @returns True if successful, false otherwise
 */
export async function assignStudentRole(userId: string): Promise<boolean> {
  return await assignRole(userId, 'student');
}

/**
 * Remove a user's role
 * @param userId The user ID to remove the role from
 * @returns True if successful, false otherwise
 */
export async function removeRole(userId: string): Promise<boolean> {
  if (!userId) return false;
  
  try {
    console.log(`Removing role from user '${userId}'`);
    
    const { error } = await supabase
      .from('user_roles')
      .delete()
      .eq('user_id', userId);
    
    if (error) {
      console.error('Error removing role:', error);
      toast.error(`Failed to remove role: ${error.message}`);
      return false;
    }
    
    // Clear cache for this user
    userRoleCache.delete(userId);
    
    toast.success('Role removed successfully');
    return true;
  } catch (error: any) {
    console.error('Error in removeRole:', error);
    toast.error(`Failed to remove role: ${error.message || 'Unknown error'}`);
    return false;
  }
}
