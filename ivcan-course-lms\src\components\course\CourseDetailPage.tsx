import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Clock, BookOpen, FileText, CheckCircle, Info, BarChart2, PieChart, ChevronRight } from 'lucide-react';
import { Course, Module } from '@/services/course/types';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { useIsMobile } from '@/hooks/use-mobile';
import { useScreenSize } from '@/hooks/use-mobile';
import { ResponsiveImage } from '@/components/ui/responsive-image';
import { getCoursePlaceholderImage, getCourseImageSource } from '@/utils/imageUtils';
import { Progress } from '@/components/ui/progress';
import { MarkdownPreview } from '@/components/ui/markdown-preview';

import '@/styles/course-detail.css';

interface CourseDetailPageProps {
  course: Course;
  modules: Module[];
  isEnrolled: boolean;
  isCourseCompleted?: boolean;
  onStartCourse: () => void;
  isStarting: boolean;
}

const CourseDetailPage: React.FC<CourseDetailPageProps> = ({
  course,
  modules,
  isEnrolled,
  isCourseCompleted = false,
  onStartCourse,
  isStarting
}) => {
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const screenSize = useScreenSize();
  const isVerySmall = screenSize === 'xs';

  // Get all lessons from all modules
  const allLessons = modules.flatMap(module => module.lessons || []);

  // Filter lectures (regular lessons)
  const lectures = allLessons.filter(lesson => lesson.type === 'lesson');

  // Filter quizzes
  const quizzes = allLessons.filter(lesson => lesson.type === 'quiz');

  // Get image source
  const getImageSource = () => {
    try {
      // First try image_url, then try image property if it exists
      const imageUrl = course.image_url || (course as any).image;
      return getCourseImageSource(imageUrl, course.title);
    } catch (error) {
      console.error('Error getting image source:', error);
      return getCoursePlaceholderImage(course.title);
    }
  };

  // Calculate total duration
  const getTotalDuration = () => {
    return allLessons.reduce((acc, lesson) => {
      // Extract minutes from duration strings like "15:00 mins" or "25:00 mins"
      const minutes = parseInt(lesson.duration?.split(':')[0] || '0');
      return acc + (isNaN(minutes) ? 0 : minutes);
    }, 0);
  };

  // Get total lessons count
  const getTotalLessons = () => {
    return allLessons.length;
  };

  // Calculate progress percentage
  const calculateProgress = () => {
    if (!modules.length) return 0;

    const completedModules = modules.filter(module => module.is_completed).length;
    return Math.round((completedModules / modules.length) * 100);
  };

  // Get completed modules count
  const getCompletedModulesCount = () => {
    return modules.filter(module => module.is_completed).length;
  };

  return (
    <div className={cn(
      "course-detail-container animate-in fade-in duration-500",
      isMobile ? "px-0 pt-0 pb-1" : "px-4 sm:px-6 pt-6 pb-8"
    )}>
      {/* Course hero image */}
      <div className="relative w-full overflow-hidden rounded-t-xl">
        <div className="course-image-container">
          {getImageSource() ? (
            <ResponsiveImage
              src={getImageSource() || ''}
              alt={course.title}
              className="course-image"
              objectFit="cover"
              aspectRatio="21/9"
              loadingStrategy="eager"
              placeholderColor="rgba(0,0,0,0.05)"
              containerClassName="w-full h-full"
              fallback={getCoursePlaceholderImage(course.title)}
              overlayText={course.title}
              overlayTextClassName="course-header-title"
              onError={(e) => {
                console.error(`Image failed to load for course "${course.title || 'Untitled'}":`);
                const target = e.target as HTMLImageElement;
                target.src = getCoursePlaceholderImage(course.title) || '';
              }}
            />
          ) : (
            <div className="course-image-fallback">
              <div className="course-header-content">
                <h1 className="course-header-title">{course.title}</h1>
                {course.description && (
                  <p className="course-header-description">{course.description}</p>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Course content section */}
      <div className={cn(
        "course-content-section",
        isMobile ? "mt-1 px-1" : "mt-6"
      )}>
        <div className={cn(
          "grid",
          isMobile ? "grid-cols-1 gap-2" : "grid-cols-1 lg:grid-cols-3 xl:grid-cols-4 gap-6"
        )}>
          {/* Sidebar - 1/4 width on desktop (first on larger screens) */}
          {!isMobile && (
            <div className="xl:col-span-1 lg:col-span-1 space-y-6 lg:sticky lg:top-20 self-start">
              {/* Course stats card */}
              <div className={cn(
                "course-stats-container bg-white dark:bg-gray-800/90 rounded-xl border border-gray-100 dark:border-gray-800/50 shadow-sm overflow-hidden transition-all duration-300 hover:shadow-md hover:border-primary/20",
              )}>
                <div className="bg-gradient-to-r from-red-50 to-red-100/50 dark:from-red-950/20 dark:to-red-900/10 px-6 py-4 border-b border-gray-100/80 dark:border-gray-800/50">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                    <BarChart2 className="w-5 h-5 mr-2 text-red-500 dark:text-red-400" />
                    Course Details
                  </h2>
                </div>

                <div className="p-5 space-y-4">
                  <div className="flex items-center p-3 bg-gray-50/80 dark:bg-gray-800/80 rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700/80">
                    <Clock className="w-5 h-5 text-red-500 dark:text-red-400 mr-3" />
                    <div>
                      <p className="text-sm font-medium dark:text-gray-200">Total Duration</p>
                      <p className="text-xs text-gray-600 dark:text-gray-400 mt-0.5">{getTotalDuration()} minutes</p>
                    </div>
                  </div>

                  <div className="flex items-center p-3 bg-gray-50/80 dark:bg-gray-800/80 rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700/80">
                    <BookOpen className="w-5 h-5 text-red-500 dark:text-red-400 mr-3" />
                    <div>
                      <p className="text-sm font-medium dark:text-gray-200">Course Modules</p>
                      <p className="text-xs text-gray-600 dark:text-gray-400 mt-0.5">{modules.length} modules</p>
                    </div>
                  </div>

                  <div className="flex items-center p-3 bg-gray-50/80 dark:bg-gray-800/80 rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700/80">
                    <FileText className="w-5 h-5 text-red-500 dark:text-red-400 mr-3" />
                    <div>
                      <p className="text-sm font-medium dark:text-gray-200">Total Lessons</p>
                      <p className="text-xs text-gray-600 dark:text-gray-400 mt-0.5">{getTotalLessons()} lessons</p>
                    </div>
                  </div>
                </div>

                {/* Progress tracking */}
                {isEnrolled && (
                  <div className="border-t border-gray-100/80 dark:border-gray-800/50 p-5">
                    <h3 className="text-sm font-semibold mb-3 flex items-center text-gray-900 dark:text-white">
                      <PieChart className="w-4 h-4 text-red-500 dark:text-red-400 mr-2" />
                      Your Progress
                    </h3>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-gray-600 dark:text-gray-400">Completion</span>
                        <span className="font-medium text-gray-900 dark:text-white">{calculateProgress()}%</span>
                      </div>
                      <Progress value={calculateProgress()} className="h-2 bg-gray-100 dark:bg-gray-700" />
                    </div>
                    <p className="text-xs text-gray-600 dark:text-gray-400 mt-2">
                      {getCompletedModulesCount()} of {modules.length} modules completed
                    </p>
                  </div>
                )}

                {/* Action buttons */}
                <div className="border-t border-gray-100/80 dark:border-gray-800/50 p-5">
                  {!isEnrolled ? (
                    <Button
                      onClick={onStartCourse}
                      className="w-full bg-red-500 hover:bg-red-600 text-white"
                      disabled={isStarting}
                    >
                      {isStarting ? 'Starting...' : 'Start Course'}
                    </Button>
                  ) : isCourseCompleted ? (
                    <div className="flex flex-col items-center space-y-2">
                      <Button
                        variant="outline"
                        className="w-full h-10 rounded-lg border-2 border-green-200 hover:bg-green-50 dark:hover:bg-green-900/20 text-green-700 dark:text-green-300 transition-all duration-300"
                        onClick={() => navigate(`/course/${course.id}/lesson/${allLessons[0]?.slug || ''}`)}
                      >
                        Review Course
                      </Button>
                      <div className="flex items-center rounded-full bg-green-50 dark:bg-green-900/20 border border-green-100 dark:border-green-800/40 text-green-600 dark:text-green-400 py-1.5 px-3 text-xs">
                        <CheckCircle className="w-3.5 h-3.5 mr-1.5" />
                        <span>Course Completed</span>
                      </div>
                    </div>
                  ) : (
                    <Button
                      className="w-full bg-red-500 hover:bg-red-600 text-white"
                      onClick={() => navigate(`/course/${course.id}/lesson/${allLessons[0]?.slug || ''}`)}
                    >
                      Continue Learning
                    </Button>
                  )}
                </div>
              </div>
            </div>
          )}

          <div className={cn(
            isMobile ? "space-y-3" : "xl:col-span-3 lg:col-span-2 space-y-6"
          )}>
            {/* Course description */}
            <div className={cn(
              "bg-white dark:bg-gray-800/90 rounded-xl border border-gray-100/80 dark:border-gray-800/50 overflow-hidden",
              isMobile ? "p-2 shadow-none" : "shadow-sm transition-all duration-300 hover:shadow-md hover:border-primary/20"
            )}>
              <div className="bg-gradient-to-r from-red-50 to-red-100/50 dark:from-red-950/20 dark:to-red-900/10 px-6 py-4 border-b border-gray-100/80 dark:border-gray-800/50">
                <h2 className={cn(
                  "flex items-center text-gray-900 dark:text-white font-semibold",
                  isMobile ? "text-sm" : "text-lg"
                )}>
                  <Info className={cn(
                    "mr-2 text-red-500 dark:text-red-400",
                    isMobile ? "w-3.5 h-3.5" : "w-5 h-5"
                  )} />
                  About this course
                </h2>
              </div>

              <div className={cn(
                "px-6 py-5",
                isMobile && "text-sm"
              )}>
                {course.description ? (
                  <div className="prose prose-sm sm:prose-base prose-gray dark:prose-invert max-w-none">
                    <MarkdownPreview content={course.description} />
                  </div>
                ) : (
                  <p className="text-gray-600 dark:text-gray-400">No description available for this course.</p>
                )}
              </div>
            </div>

            {/* Stats for mobile view */}
            {isMobile && (
              <div className="grid grid-cols-3 gap-2">
                <div className="p-3 bg-white dark:bg-gray-800/90 rounded-lg border border-gray-100/80 dark:border-gray-800/50 flex flex-col items-center justify-center text-center">
                  <Clock className="w-4 h-4 text-red-500 dark:text-red-400 mb-1" />
                  <div className="text-[10px] text-gray-500 dark:text-gray-400">Duration</div>
                  <div className="text-xs font-medium text-gray-900 dark:text-white">{getTotalDuration()} min</div>
                </div>
                <div className="p-3 bg-white dark:bg-gray-800/90 rounded-lg border border-gray-100/80 dark:border-gray-800/50 flex flex-col items-center justify-center text-center">
                  <BookOpen className="w-4 h-4 text-red-500 dark:text-red-400 mb-1" />
                  <div className="text-[10px] text-gray-500 dark:text-gray-400">Modules</div>
                  <div className="text-xs font-medium text-gray-900 dark:text-white">{modules.length}</div>
                </div>
                <div className="p-3 bg-white dark:bg-gray-800/90 rounded-lg border border-gray-100/80 dark:border-gray-800/50 flex flex-col items-center justify-center text-center">
                  <FileText className="w-4 h-4 text-red-500 dark:text-red-400 mb-1" />
                  <div className="text-[10px] text-gray-500 dark:text-gray-400">Lessons</div>
                  <div className="text-xs font-medium text-gray-900 dark:text-white">{getTotalLessons()}</div>
                </div>
              </div>
            )}

            {/* Action buttons for mobile */}
            {isMobile && (
              <div className="p-3 bg-white dark:bg-gray-800/90 rounded-lg border border-gray-100/80 dark:border-gray-800/50">
                {!isEnrolled ? (
                  <Button
                    onClick={onStartCourse}
                    className="w-full bg-red-500 hover:bg-red-600 text-white"
                    disabled={isStarting}
                  >
                    {isStarting ? 'Starting...' : 'Start Course'}
                  </Button>
                ) : isCourseCompleted ? (
                  <div className="flex flex-col space-y-2">
                    <Button
                      variant="outline"
                      className="w-full h-10 rounded-lg border-2 border-green-200 hover:bg-green-50 dark:hover:bg-green-900/20 text-green-700 dark:text-green-300 transition-all duration-300"
                      onClick={() => navigate(`/course/${course.id}/lesson/${allLessons[0]?.slug || ''}`)}
                    >
                      Review Course
                    </Button>
                    <div className="flex items-center justify-center rounded-full bg-green-50 dark:bg-green-900/20 border border-green-100 dark:border-green-800/40 text-green-600 dark:text-green-400 py-1.5 px-3 text-xs">
                      <CheckCircle className="w-3.5 h-3.5 mr-1.5" />
                      <span>Course Completed</span>
                    </div>
                  </div>
                ) : (
                  <Button
                    className="w-full bg-red-500 hover:bg-red-600 text-white"
                    onClick={() => navigate(`/course/${course.id}/lesson/${allLessons[0]?.slug || ''}`)}
                  >
                    Continue Learning
                  </Button>
                )}
              </div>
            )}

            {/* View Modules Button */}
            <Button
              onClick={() => navigate(`/course/${course.id}/modules`)}
              className={cn(
                "w-full md:w-auto bg-red-500 hover:bg-red-600 text-white",
                "px-4 sm:px-6 py-2 sm:py-3 rounded-lg flex items-center justify-center"
              )}
            >
              <div className="flex items-center gap-2">
                <BookOpen className="w-5 h-5" />
                <div className="flex flex-col items-start">
                  <span className="text-xs sm:text-sm font-normal opacity-90">Start Learning</span>
                  <span className="text-sm sm:text-base font-semibold">View Course Modules</span>
                </div>
                <ChevronRight className="w-5 h-5 ml-2" />
              </div>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseDetailPage;
