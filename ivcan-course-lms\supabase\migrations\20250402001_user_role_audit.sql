
-- Create a table to track user role changes
CREATE TABLE IF NOT EXISTS public.user_role_changes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users NOT NULL,
  changed_by UUID REFERENCES auth.users NOT NULL,
  old_role TEXT,
  new_role TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Enable row-level security
ALTER TABLE public.user_role_changes ENABLE ROW LEVEL SECURITY;

-- Create policy for teachers to view any role change audit records
CREATE POLICY "Teachers can view role change audit logs"
  ON public.user_role_changes
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.user_roles
      WHERE user_id = auth.uid() AND role = 'teacher'
    )
  );

-- Create policy for teachers to insert role change audit records
CREATE POLICY "Teachers can insert role change audit records"
  ON public.user_role_changes
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.user_roles
      WHERE user_id = auth.uid() AND role = 'teacher'
    )
  );

-- Make sure realtime is enabled for courses, modules, and lessons tables for real-time sync
ALTER TABLE public.courses REPLICA IDENTITY FULL;
ALTER TABLE public.modules REPLICA IDENTITY FULL;
ALTER TABLE public.lessons REPLICA IDENTITY FULL;

-- Add these tables to the realtime publication
ALTER PUBLICATION supabase_realtime ADD TABLE public.courses;
ALTER PUBLICATION supabase_realtime ADD TABLE public.modules;
ALTER PUBLICATION supabase_realtime ADD TABLE public.lessons;
