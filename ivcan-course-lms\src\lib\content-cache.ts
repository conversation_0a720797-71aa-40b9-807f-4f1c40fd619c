/**
 * Content caching service for markdown processing
 * Provides intelligent caching with LRU eviction and content invalidation
 */

interface CacheEntry {
  content: string;
  html: string;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
  hash: string;
}

interface CacheStats {
  hits: number;
  misses: number;
  size: number;
  maxSize: number;
  hitRate: number;
}

export class ContentCache {
  private cache = new Map<string, CacheEntry>();
  private maxSize: number;
  private maxAge: number; // in milliseconds
  private stats = {
    hits: 0,
    misses: 0
  };

  constructor(maxSize = 100, maxAge = 30 * 60 * 1000) { // 30 minutes default
    this.maxSize = maxSize;
    this.maxAge = maxAge;
  }

  /**
   * Generate a hash for content to use as cache key
   */
  private generateHash(content: string, options?: any): string {
    const optionsStr = options ? JSON.stringify(options) : '';
    const combined = content + optionsStr;
    
    // Simple hash function (for production, consider using a proper hash library)
    let hash = 0;
    for (let i = 0; i < combined.length; i++) {
      const char = combined.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(36);
  }

  /**
   * Get cached content if available and valid
   */
  get(content: string, options?: any): string | null {
    const hash = this.generateHash(content, options);
    const entry = this.cache.get(hash);

    if (!entry) {
      this.stats.misses++;
      return null;
    }

    // Check if entry is expired
    const now = Date.now();
    if (now - entry.timestamp > this.maxAge) {
      this.cache.delete(hash);
      this.stats.misses++;
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = now;
    this.stats.hits++;

    return entry.html;
  }

  /**
   * Store processed content in cache
   */
  set(content: string, html: string, options?: any): void {
    const hash = this.generateHash(content, options);
    const now = Date.now();

    // If cache is full, remove least recently used entry
    if (this.cache.size >= this.maxSize && !this.cache.has(hash)) {
      this.evictLRU();
    }

    const entry: CacheEntry = {
      content,
      html,
      timestamp: now,
      accessCount: 1,
      lastAccessed: now,
      hash
    };

    this.cache.set(hash, entry);
  }

  /**
   * Remove least recently used entry
   */
  private evictLRU(): void {
    let oldestEntry: CacheEntry | null = null;
    let oldestKey: string | null = null;

    for (const [key, entry] of this.cache.entries()) {
      if (!oldestEntry || entry.lastAccessed < oldestEntry.lastAccessed) {
        oldestEntry = entry;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  /**
   * Clear expired entries
   */
  cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.maxAge) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.cache.delete(key));
  }

  /**
   * Clear all cached content
   */
  clear(): void {
    this.cache.clear();
    this.stats.hits = 0;
    this.stats.misses = 0;
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const total = this.stats.hits + this.stats.misses;
    return {
      hits: this.stats.hits,
      misses: this.stats.misses,
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: total > 0 ? this.stats.hits / total : 0
    };
  }

  /**
   * Invalidate cache entries that match a pattern
   */
  invalidate(pattern?: RegExp): void {
    if (!pattern) {
      this.clear();
      return;
    }

    const keysToDelete: string[] = [];
    for (const [key, entry] of this.cache.entries()) {
      if (pattern.test(entry.content)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
  }

  /**
   * Get cache entry details for debugging
   */
  getEntries(): Array<{ key: string; entry: CacheEntry }> {
    return Array.from(this.cache.entries()).map(([key, entry]) => ({ key, entry }));
  }

  /**
   * Preload content into cache
   */
  preload(contentList: Array<{ content: string; html: string; options?: any }>): void {
    contentList.forEach(({ content, html, options }) => {
      this.set(content, html, options);
    });
  }

  /**
   * Get memory usage estimate (rough)
   */
  getMemoryUsage(): number {
    let totalSize = 0;
    for (const entry of this.cache.values()) {
      totalSize += entry.content.length + entry.html.length;
    }
    return totalSize;
  }
}

// Global cache instance
export const contentCache = new ContentCache(200, 60 * 60 * 1000); // 200 entries, 1 hour TTL

// Auto-cleanup every 10 minutes
if (typeof window !== 'undefined') {
  setInterval(() => {
    contentCache.cleanup();
  }, 10 * 60 * 1000);
}

/**
 * Cached markdown processing function
 */
export function getCachedMarkdown(
  content: string,
  processor: (content: string) => string,
  options?: any
): string {
  try {
    // Try to get from cache first
    const cached = contentCache.get(content, options);
    if (cached) {
      return cached;
    }

    // Process and cache
    const processed = processor(content);
    contentCache.set(content, processed, options);

    return processed;
  } catch (error) {
    console.error('Error in getCachedMarkdown:', error);
    // Fallback to direct processing without caching
    return processor(content);
  }
}

/**
 * Debounced cache invalidation for real-time editing
 */
export class DebouncedCacheInvalidator {
  private timeouts = new Map<string, NodeJS.Timeout>();
  private delay: number;

  constructor(delay = 1000) {
    this.delay = delay;
  }

  /**
   * Schedule cache invalidation with debouncing
   */
  scheduleInvalidation(key: string, pattern?: RegExp): void {
    // Clear existing timeout
    const existingTimeout = this.timeouts.get(key);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Set new timeout
    const timeout = setTimeout(() => {
      contentCache.invalidate(pattern);
      this.timeouts.delete(key);
    }, this.delay);

    this.timeouts.set(key, timeout);
  }

  /**
   * Cancel scheduled invalidation
   */
  cancelInvalidation(key: string): void {
    const timeout = this.timeouts.get(key);
    if (timeout) {
      clearTimeout(timeout);
      this.timeouts.delete(key);
    }
  }

  /**
   * Clear all scheduled invalidations
   */
  clearAll(): void {
    for (const timeout of this.timeouts.values()) {
      clearTimeout(timeout);
    }
    this.timeouts.clear();
  }
}

// Global debounced invalidator
export const cacheInvalidator = new DebouncedCacheInvalidator(2000); // 2 second delay
