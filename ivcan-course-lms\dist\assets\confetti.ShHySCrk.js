import{r,j as s}from"./vendor-react.BcAa1DKr.js";import{a4 as h}from"./vendor.DQpuTRuB.js";const l=({count:e=150,duration:i=3,colors:o=["#10b981","#3b82f6","#8b5cf6","#f59e0b","#ec4899"],active:a=!0})=>{const[n,d]=r.useState([]);return r.useEffect(()=>{if(!a)return;const t=Array.from({length:e},(f,m)=>({id:m,x:Math.random()*100,y:-10,size:Math.random()*8+2,color:o[Math.floor(Math.random()*o.length)],rotation:Math.random()*360,delay:Math.random()*.5}));d(t)},[a,o,e]),!a||n.length===0?null:s.jsx("div",{className:"absolute inset-0 overflow-hidden pointer-events-none",children:n.map(t=>s.jsx(h.div,{initial:{x:`${t.x}%`,y:`${t.y}%`,rotate:t.rotation,opacity:1},animate:{y:"100%",rotate:t.rotation+Math.random()*360,opacity:0},transition:{duration:i*(.8+Math.random()*.4),ease:"easeOut",delay:t.delay},className:"absolute",style:{width:`${t.size}px`,height:`${t.size}px`,backgroundColor:t.color,borderRadius:Math.random()>.5?"50%":"2px",left:`${t.x}%`,top:`${t.y}%`}},t.id))})};export{l as C};
