var F=Object.defineProperty;var y=Object.getOwnPropertySymbols;var O=Object.prototype.hasOwnProperty,V=Object.prototype.propertyIsEnumerable;var T=(l,e,s)=>e in l?F(l,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):l[e]=s,N=(l,e)=>{for(var s in e||(e={}))O.call(e,s)&&T(l,s,e[s]);if(y)for(var s of y(e))V.call(e,s)&&T(l,s,e[s]);return l};var A=(l,e,s)=>T(l,typeof e!="symbol"?e+"":e,s);var H=(l,e,s)=>new Promise((t,r)=>{var o=c=>{try{d(s.next(c))}catch(g){r(g)}},u=c=>{try{d(s.throw(c))}catch(g){r(g)}},d=c=>c.done?t(c.value):Promise.resolve(c.value).then(o,u);d((s=s.apply(l,e)).next())});import{r as h,bv as J,j as i,bQ as K,aq as W,bw as G,bx as Q,bA as _,bF as X,bG as Z,bJ as ee,$ as te,aN as se,b0 as ie,bK as re,bL as ae,bR as le}from"./vendor-react.BcAa1DKr.js";import{al as ne,an as oe,ao as ce,at as ue,au as de,av as he,aw as me,ar as ge,as as pe,aq as be,aE as fe,ap as xe,ax as ke,ay as ze,a2 as x}from"./vendor.DQpuTRuB.js";import{T as we,a as Le,b as M,c as E}from"./tabs.B0SF6qIv.js";import{c as j,D as Te,h as je,i as Ce,j as $e,I as Ie,l as ve,B as C}from"./index.BLDhDn0D.js";import{L as ye}from"./label.D4YlnUPk.js";import{M as Ne}from"./markdown-preview.Bw0U-NJA.js";import{m as R}from"./content-converter.L-GziWIP.js";import{u as Ae}from"./tiptap-image-upload.B_U9gNrF.js";class He{constructor(e={}){A(this,"options");this.options=N({tightLists:!1,bulletListMarker:"-",orderedListMarker:".",codeBlockLanguagePrefix:""},e)}serialize(e){return this.serializeNode(e).trim()}serializeNode(e){const s=this.getNodeSerializer(e.type.name);return s?s(e):this.serializeChildren(e)}getNodeSerializer(e){return{doc:t=>this.serializeChildren(t),paragraph:t=>this.serializeParagraph(t),heading:t=>this.serializeHeading(t),text:t=>this.serializeText(t),hardBreak:()=>`\\
`,horizontalRule:()=>`
---
`,bulletList:t=>this.serializeBulletList(t),orderedList:t=>this.serializeOrderedList(t),listItem:t=>this.serializeListItem(t),taskList:t=>this.serializeTaskList(t),taskItem:t=>this.serializeTaskItem(t),strong:t=>`**${this.serializeChildren(t)}**`,em:t=>`*${this.serializeChildren(t)}*`,code:t=>`\`${t.textContent}\``,strike:t=>`~~${this.serializeChildren(t)}~~`,underline:t=>`<u>${this.serializeChildren(t)}</u>`,highlight:t=>`==${this.serializeChildren(t)}==`,link:t=>this.serializeLink(t),image:t=>this.serializeImage(t),codeBlock:t=>this.serializeCodeBlock(t),blockquote:t=>this.serializeBlockquote(t),table:t=>this.serializeTable(t),tableRow:t=>this.serializeTableRow(t),tableCell:t=>this.serializeTableCell(t),tableHeader:t=>this.serializeTableHeader(t)}[e]||null}serializeChildren(e){let s="";return e.forEach(t=>{s+=this.serializeNode(t)}),s}serializeParagraph(e){const s=this.serializeChildren(e);return s.trim()?this.isNodeInListItem(e)?s:s+`

`:""}serializeHeading(e){const s=e.attrs.level||1,t=this.serializeChildren(e);return`${"#".repeat(s)} ${t}

`}serializeText(e){let s=e.text||"";return s=s.replace(/([\\`*_{}[\]()#+\-.!])/g,"\\$1"),s}serializeBulletList(e){return this.serializeListItems(e,this.options.bulletListMarker)+`
`}serializeOrderedList(e){return this.serializeListItems(e,this.options.orderedListMarker,!0)+`
`}serializeListItems(e,s,t=!1){let r="",o=1;return e.forEach(u=>{if(u.type.name==="listItem"){const d=t?`${o}${s} `:`${s} `,c=this.serializeListItem(u,d);r+=c,o++}}),r}serializeListItem(e,s="- "){let t="",r=!0;return e.forEach(o=>{const u=this.serializeNode(o);if(r)t+=s+u.replace(/^\n+/,""),r=!1;else{const d=u.replace(/^/gm,"  ");t+=d}}),t+`
`}serializeTaskList(e){let s="";return e.forEach(t=>{t.type.name==="taskItem"&&(s+=this.serializeTaskItem(t))}),s+`
`}serializeTaskItem(e){const t=e.attrs.checked||!1?"[x]":"[ ]",r=this.serializeChildren(e);return`- ${t} ${r}
`}serializeLink(e){const s=e.attrs.href||"",t=e.attrs.title,r=this.serializeChildren(e);return t?`[${r}](${s} "${t}")`:`[${r}](${s})`}serializeImage(e){const s=e.attrs.src||"",t=e.attrs.alt||"",r=e.attrs.title;return r?`![${t}](${s} "${r}")`:`![${t}](${s})`}serializeCodeBlock(e){const s=e.attrs.language||"",t=e.textContent||"";return`\`\`\`${this.options.codeBlockLanguagePrefix+s}
${t}
\`\`\`

`}serializeBlockquote(e){return this.serializeChildren(e).split(`
`).map(o=>o.trim()?`> ${o}`:">").join(`
`)+`

`}serializeTable(e){let s="",t="",r="",o="",u=!0;return e.forEach(d=>{if(d.type.name==="tableRow"){const c=this.serializeTableRow(d);if(u){t=c;const g=this.getTableColumnCount(d);r="|"+" --- |".repeat(g)+`
`,u=!1}else o+=c}}),s=t+r+o+`
`,s}serializeTableRow(e){let s="|";return e.forEach(t=>{if(t.type.name==="tableCell"||t.type.name==="tableHeader"){const r=this.serializeTableCell(t);s+=` ${r} |`}}),s+`
`}serializeTableCell(e){return this.serializeChildren(e).trim().replace(/\|/g,"\\|").replace(/\n/g," ")}serializeTableHeader(e){return this.serializeTableCell(e)}getTableColumnCount(e){let s=0;return e.forEach(t=>{(t.type.name==="tableCell"||t.type.name==="tableHeader")&&s++}),s}isNodeInListItem(e){return!1}}function Me(l,e){return new He(e).serialize(l)}function Ee(l){if(!l)return"";let e=l;return e=e.replace(/<h([1-6])>(.*?)<\/h[1-6]>/gi,(s,t,r)=>"#".repeat(parseInt(t))+" "+r+`

`),e=e.replace(/<strong>(.*?)<\/strong>/gi,"**$1**"),e=e.replace(/<b>(.*?)<\/b>/gi,"**$1**"),e=e.replace(/<em>(.*?)<\/em>/gi,"*$1*"),e=e.replace(/<i>(.*?)<\/i>/gi,"*$1*"),e=e.replace(/<del>(.*?)<\/del>/gi,"~~$1~~"),e=e.replace(/<s>(.*?)<\/s>/gi,"~~$1~~"),e=e.replace(/<u>(.*?)<\/u>/gi,"<u>$1</u>"),e=e.replace(/<code>(.*?)<\/code>/gi,"`$1`"),e=e.replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gi,"[$2]($1)"),e=e.replace(/<img[^>]*src="([^"]*)"[^>]*alt="([^"]*)"[^>]*>/gi,"![$2]($1)"),e=e.replace(/<img[^>]*alt="([^"]*)"[^>]*src="([^"]*)"[^>]*>/gi,"![$1]($2)"),e=e.replace(new RegExp("<ul[^>]*>(.*?)<\\/ul>","gis"),(s,t)=>t.replace(new RegExp("<li[^>]*>(.*?)<\\/li>","gis"),`- $1
`)+`
`),e=e.replace(new RegExp("<ol[^>]*>(.*?)<\\/ol>","gis"),(s,t)=>{let r=1;return t.replace(new RegExp("<li[^>]*>(.*?)<\\/li>","gis"),()=>`${r++}. $1
`)+`
`}),e=e.replace(new RegExp("<blockquote[^>]*>(.*?)<\\/blockquote>","gis"),(s,t)=>t.split(`
`).map(r=>r.trim()?`> ${r}`:">").join(`
`)+`

`),e=e.replace(new RegExp("<p[^>]*>(.*?)<\\/p>","gis"),`$1

`),e=e.replace(/<br[^>]*>/gi,`
`),e=e.replace(/<[^>]*>/g,""),e=e.replace(/\n{3,}/g,`

`),e.trim()}function Oe({initialContent:l="",onChange:e,placeholder:s="Start writing...",className:t="",minHeight:r=300,courseId:o,moduleId:u}){const[d,c]=h.useState("editor"),[g,L]=h.useState(l),[$,I]=h.useState(!1),[B,z]=h.useState(!1),[w,v]=h.useState(""),k=h.useRef(null),a=J({extensions:[ne,oe.configure({HTMLAttributes:{class:"rounded max-w-full h-auto"},inline:!1}),ce.configure({openOnClick:!1,HTMLAttributes:{class:"text-blue-600 underline"}}),ue.configure({resizable:!0,HTMLAttributes:{class:"border-collapse border border-gray-300"}}),de,he.configure({HTMLAttributes:{class:"bg-gray-100 font-semibold"}}),me.configure({HTMLAttributes:{class:"border border-gray-300 px-2 py-1"}}),ge.configure({HTMLAttributes:{class:"task-list"}}),pe.configure({HTMLAttributes:{class:"task-item flex items-start gap-2"},nested:!0}),be.configure({HTMLAttributes:{class:"bg-yellow-200 dark:bg-yellow-800 px-1 rounded"}}),fe.configure({HTMLAttributes:{class:"line-through"}}),xe,ke.configure({controls:!0,nocookie:!0,HTMLAttributes:{class:"rounded-lg overflow-hidden my-4"}}),ze.configure({placeholder:s})],content:R(l),onUpdate:({editor:n})=>{try{const p=n.state.doc,b=Me(p);L(b),e==null||e(b)}catch(p){const b=n.getHTML(),f=Ee(b);L(f),e==null||e(f)}},editorProps:{attributes:{class:j("prose max-w-none focus:outline-none p-4",`min-h-[${r}px]`)}}});h.useEffect(()=>{if(a&&l!==g){const n=R(l);a.commands.setContent(n),L(l)}},[l,a,g]);const U=h.useCallback(()=>{k.current&&k.current.click()},[]),S=h.useCallback(()=>{const n=prompt("Enter image URL:");n&&a&&(a.chain().focus().setImage({src:n}).run(),x.success("Image added!"))},[a]),Y=h.useCallback(n=>H(this,null,function*(){var b;const p=(b=n.target.files)==null?void 0:b[0];if(!(!p||!a)){if(!p.type.startsWith("image/")){x.error("Please select an image file");return}if(p.size>10*1024*1024){x.error("Image size must be less than 10MB");return}I(!0);try{const f=yield Ae(p,o,u);a.chain().focus().setImage({src:f}).run(),x.success("Image uploaded and added!")}catch(f){console.error("Error uploading image:",f),x.error("Failed to upload image. Please try again.")}finally{I(!1),k.current&&(k.current.value="")}}}),[a,o,u]),P=h.useCallback(()=>{const n=prompt("Enter URL:");n&&a&&a.chain().focus().setLink({href:n}).run()},[a]),q=h.useCallback(()=>{a&&a.chain().focus().insertTable({rows:3,cols:3,withHeaderRow:!0}).run()},[a]),D=h.useCallback(()=>{z(!0)},[]);if(!a)return null;const m=({onClick:n,isActive:p=!1,children:b,title:f})=>i.jsx(C,{type:"button",variant:"ghost",size:"sm",onClick:n,className:j("h-8 w-8 p-0",p?"bg-muted":""),title:f,children:b});return i.jsxs("div",{className:j("border rounded-lg bg-card",t),children:[i.jsxs(we,{value:d,onValueChange:n=>c(n),children:[i.jsx("div",{className:"flex items-center justify-between border-b p-2",children:i.jsxs(Le,{className:"bg-transparent",children:[i.jsxs(M,{value:"editor",className:"flex items-center gap-1",children:[i.jsx(K,{className:"h-3 w-3"}),"Write"]}),i.jsxs(M,{value:"preview",className:"flex items-center gap-1",children:[i.jsx(W,{className:"h-3 w-3"}),"Preview"]})]})}),i.jsxs(E,{value:"editor",className:"p-0 m-0",children:[i.jsxs("div",{className:"flex items-center gap-1 p-2 border-b bg-muted/30",children:[i.jsx(m,{onClick:()=>a.chain().focus().toggleBold().run(),isActive:a.isActive("bold"),title:"Bold",children:i.jsx(G,{className:"h-4 w-4"})}),i.jsx(m,{onClick:()=>a.chain().focus().toggleItalic().run(),isActive:a.isActive("italic"),title:"Italic",children:i.jsx(Q,{className:"h-4 w-4"})}),i.jsx(m,{onClick:()=>a.chain().focus().toggleCode().run(),isActive:a.isActive("code"),title:"Code",children:i.jsx(_,{className:"h-4 w-4"})}),i.jsx("div",{className:"w-px h-6 bg-gray-300 mx-1"}),i.jsx(m,{onClick:()=>a.chain().focus().toggleBulletList().run(),isActive:a.isActive("bulletList"),title:"List",children:i.jsx(X,{className:"h-4 w-4"})}),i.jsx(m,{onClick:()=>a.chain().focus().toggleOrderedList().run(),isActive:a.isActive("orderedList"),title:"Numbered List",children:i.jsx(Z,{className:"h-4 w-4"})}),i.jsx("div",{className:"w-px h-6 bg-gray-300 mx-1"}),i.jsx(m,{onClick:P,title:"Add Link",children:i.jsx(ee,{className:"h-4 w-4"})}),i.jsx(m,{onClick:U,title:"Upload Image",disabled:$,children:$?i.jsx(te,{className:"h-4 w-4 animate-spin"}):i.jsx(se,{className:"h-4 w-4"})}),i.jsx(m,{onClick:S,title:"Add Image from URL",children:i.jsx(ie,{className:"h-4 w-4"})}),i.jsx(m,{onClick:q,title:"Add Table",children:i.jsx(re,{className:"h-4 w-4"})}),i.jsx(m,{onClick:D,title:"Add YouTube Video",children:i.jsx(ae,{className:"h-4 w-4"})})]}),i.jsx(le,{editor:a,style:{minHeight:`${r}px`}})]}),i.jsx(E,{value:"preview",className:"p-4 m-0",style:{minHeight:`${r}px`},children:i.jsx("div",{className:"github-markdown-preview",children:i.jsx(Ne,{content:g,className:"prose max-w-none",allowHtml:!0})})})]}),i.jsx("input",{ref:k,type:"file",accept:"image/*",onChange:Y,style:{display:"none"}}),i.jsx(Te,{open:B,onOpenChange:z,children:i.jsxs(je,{className:"sm:max-w-md",children:[i.jsx(Ce,{children:i.jsx($e,{children:"Insert YouTube Video"})}),i.jsx("div",{className:"space-y-4",children:i.jsxs("div",{children:[i.jsx(ye,{htmlFor:"youtube-url",children:"YouTube URL"}),i.jsx(Ie,{id:"youtube-url",placeholder:"https://www.youtube.com/watch?v=...",value:w,onChange:n=>v(n.target.value)})]})}),i.jsxs(ve,{children:[i.jsx(C,{variant:"outline",onClick:()=>z(!1),children:"Cancel"}),i.jsx(C,{onClick:()=>{w&&(a==null||a.chain().focus().setYoutubeVideo({src:w}).run(),x.success("YouTube video added!")),z(!1),v("")},disabled:!w,children:"Insert Video"})]})]})})]})}export{Oe as S};
