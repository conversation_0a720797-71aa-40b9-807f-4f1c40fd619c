<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Table Rendering Test</title>
    <style>
        /* CSS Variables */
        :root {
            --background: 0 0% 100%;
            --foreground: 222.2 84% 4.9%;
            --muted: 210 40% 96.1%;
            --border: 214.3 31.8% 91.4%;
        }

        .dark {
            --background: 222.2 84% 4.9%;
            --foreground: 210 40% 98%;
            --muted: 217.2 32.6% 17.5%;
            --border: 217.2 32.6% 17.5%;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
            padding: 2rem;
            line-height: 1.6;
        }

        /* Table Styles - Unified Markdown */
        .markdown-preview table,
        .professional-prose table {
            border-collapse: collapse !important;
            border: 1px solid hsl(var(--border)) !important;
            width: 100% !important;
            margin: 1rem 0 !important;
            border-radius: 8px !important;
            overflow: hidden !important;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
        }

        .markdown-preview table th,
        .markdown-preview table td,
        .professional-prose table th,
        .professional-prose table td {
            border: 1px solid hsl(var(--border)) !important;
            padding: 0.75rem 1rem !important;
            text-align: left !important;
            vertical-align: top !important;
        }

        .markdown-preview table th,
        .professional-prose table th {
            background-color: hsl(var(--muted)) !important;
            font-weight: 600 !important;
            color: hsl(var(--foreground)) !important;
        }

        .markdown-preview table td,
        .professional-prose table td {
            background-color: hsl(var(--background)) !important;
            color: hsl(var(--foreground)) !important;
        }

        .markdown-preview table tr:nth-child(even) td,
        .professional-prose table tr:nth-child(even) td {
            background-color: hsl(var(--muted) / 0.3) !important;
        }

        .markdown-preview table tr:hover td,
        .professional-prose table tr:hover td {
            background-color: hsl(var(--muted) / 0.5) !important;
            transition: background-color 0.2s ease !important;
        }

        .test-section {
            margin: 2rem 0;
            padding: 1rem;
            border: 1px solid hsl(var(--border));
            border-radius: 8px;
        }

        .toggle-dark {
            background: hsl(var(--muted));
            border: 1px solid hsl(var(--border));
            color: hsl(var(--foreground));
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <h1>Table Rendering Test</h1>
    
    <button class="toggle-dark" onclick="toggleDarkMode()">Toggle Dark Mode</button>
    
    <div class="test-section">
        <h2>MarkdownPreview Table (.markdown-preview)</h2>
        <div class="markdown-preview">
            <table>
                <thead>
                    <tr>
                        <th>Feature</th>
                        <th>Status</th>
                        <th>Notes</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Bold text</strong></td>
                        <td>✅ Working</td>
                        <td>Properly formatted</td>
                    </tr>
                    <tr>
                        <td><em>Italic text</em></td>
                        <td>✅ Working</td>
                        <td>Looks great</td>
                    </tr>
                    <tr>
                        <td><del>Strikethrough</del></td>
                        <td>✅ Working</td>
                        <td>Now supported!</td>
                    </tr>
                    <tr>
                        <td><code>Code</code></td>
                        <td>✅ Working</td>
                        <td>Inline code works</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="test-section">
        <h2>Professional Prose Table (.professional-prose)</h2>
        <div class="professional-prose">
            <table>
                <thead>
                    <tr>
                        <th>Language</th>
                        <th>Example</th>
                        <th>Documentation</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>JavaScript</td>
                        <td><code>console.log('Hello')</code></td>
                        <td><a href="https://developer.mozilla.org">MDN</a></td>
                    </tr>
                    <tr>
                        <td>Python</td>
                        <td><code>print("Hello")</code></td>
                        <td><a href="https://python.org">Python.org</a></td>
                    </tr>
                    <tr>
                        <td>TypeScript</td>
                        <td><code>const msg: string = "Hello"</code></td>
                        <td><a href="https://typescriptlang.org">TypeScript</a></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="test-section">
        <h2>Large Table for Responsive Testing</h2>
        <div class="professional-prose">
            <table>
                <thead>
                    <tr>
                        <th>Column 1</th>
                        <th>Column 2</th>
                        <th>Column 3</th>
                        <th>Column 4</th>
                        <th>Column 5</th>
                        <th>Column 6</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Data 1.1</td>
                        <td>Data 1.2</td>
                        <td>Data 1.3</td>
                        <td>Data 1.4</td>
                        <td>Data 1.5</td>
                        <td>Data 1.6</td>
                    </tr>
                    <tr>
                        <td>Data 2.1</td>
                        <td>Data 2.2</td>
                        <td>Data 2.3</td>
                        <td>Data 2.4</td>
                        <td>Data 2.5</td>
                        <td>Data 2.6</td>
                    </tr>
                    <tr>
                        <td>Data 3.1</td>
                        <td>Data 3.2</td>
                        <td>Data 3.3</td>
                        <td>Data 3.4</td>
                        <td>Data 3.5</td>
                        <td>Data 3.6</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        function toggleDarkMode() {
            document.body.classList.toggle('dark');
        }
    </script>
</body>
</html>
