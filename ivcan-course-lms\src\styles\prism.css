/**
 * Custom Prism.js theme for syntax highlighting
 * Based on the VS Code dark theme
 */

code[class*="language-"],
pre[class*="language-"] {
  color: #d4d4d4;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  direction: ltr;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  line-height: 1.5;
  tab-size: 4;
  hyphens: none;
}

pre[class*="language-"] {
  padding: 1em;
  margin: 0.5em 0;
  overflow: auto;
  border-radius: 0.375rem;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
  background: #1e1e1e;
}

/* Dark theme */
.dark code[class*="language-"],
.dark pre[class*="language-"] {
  color: #d4d4d4;
}

.dark :not(pre) > code[class*="language-"],
.dark pre[class*="language-"] {
  background: #1e1e1e;
}

/* Light theme */
:root:not(.dark) code[class*="language-"],
:root:not(.dark) pre[class*="language-"] {
  color: #1e1e1e;
}

:root:not(.dark) :not(pre) > code[class*="language-"],
:root:not(.dark) pre[class*="language-"] {
  background: #f5f5f5;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
  color: #6a9955;
}

.token.punctuation {
  color: #d4d4d4;
}

.token.namespace {
  opacity: 0.7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol,
.token.deleted {
  color: #b5cea8;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
  color: #ce9178;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string {
  color: #d4d4d4;
}

.token.atrule,
.token.attr-value,
.token.keyword {
  color: #569cd6;
}

.token.function,
.token.class-name {
  color: #dcdcaa;
}

.token.regex,
.token.important,
.token.variable {
  color: #d16969;
}

.token.important,
.token.bold {
  font-weight: bold;
}

.token.italic {
  font-style: italic;
}

.token.entity {
  cursor: help;
}

/* Light theme overrides */
:root:not(.dark) .token.comment,
:root:not(.dark) .token.prolog,
:root:not(.dark) .token.doctype,
:root:not(.dark) .token.cdata {
  color: #008000;
}

:root:not(.dark) .token.punctuation {
  color: #393a34;
}

:root:not(.dark) .token.property,
:root:not(.dark) .token.tag,
:root:not(.dark) .token.boolean,
:root:not(.dark) .token.number,
:root:not(.dark) .token.constant,
:root:not(.dark) .token.symbol,
:root:not(.dark) .token.deleted {
  color: #36acaa;
}

:root:not(.dark) .token.selector,
:root:not(.dark) .token.attr-name,
:root:not(.dark) .token.string,
:root:not(.dark) .token.char,
:root:not(.dark) .token.builtin,
:root:not(.dark) .token.inserted {
  color: #0000ff;
}

:root:not(.dark) .token.operator,
:root:not(.dark) .token.entity,
:root:not(.dark) .token.url,
:root:not(.dark) .language-css .token.string,
:root:not(.dark) .style .token.string {
  color: #393a34;
}

:root:not(.dark) .token.atrule,
:root:not(.dark) .token.attr-value,
:root:not(.dark) .token.keyword {
  color: #0000ff;
}

:root:not(.dark) .token.function,
:root:not(.dark) .token.class-name {
  color: #795e26;
}

:root:not(.dark) .token.regex,
:root:not(.dark) .token.important,
:root:not(.dark) .token.variable {
  color: #ee9900;
}
