var v=(s,h,n)=>new Promise((j,d)=>{var m=a=>{try{b(n.next(a))}catch(u){d(u)}},t=a=>{try{b(n.throw(a))}catch(u){d(u)}},b=a=>a.done?j(a.value):Promise.resolve(a.value).then(m,t);b((n=n.apply(s,h)).next())});import{r as T,j as e,bK as C,bt as N,br as D,E as L,Y as E}from"./vendor-react.BcAa1DKr.js";import{L as R}from"./Layout.DRjmVYQG.js";import{U as f}from"./unified-markdown-editor.BzxuP8op.js";import{M}from"./markdown-preview.Bw0U-NJA.js";import{L as y}from"./LessonContent.B2xCUk6H.js";import{C as i,a as r,b as l,c,d as o}from"./card.B9V6b2DK.js";import{T as U,a as S,b as x,c as p}from"./tabs.B0SF6qIv.js";import{B as g}from"./badge.C87ZuIxl.js";import{B as k}from"./index.BLDhDn0D.js";import{a2 as w}from"./vendor.DQpuTRuB.js";import"./content-converter.L-GziWIP.js";import"./tiptap-image-upload.B_U9gNrF.js";import"./vendor-supabase.sufZ44-y.js";const q=()=>{const[s,h]=T.useState(`# Table Rendering Test

This page tests table rendering consistency between the unified markdown editor and lesson content display.

## Simple Table

| Feature | Status | Notes |
|---------|--------|-------|
| **Bold text** | ✅ Working | Properly formatted |
| *Italic text* | ✅ Working | Looks great |
| ~~Strikethrough~~ | ✅ Working | Now supported! |
| \`Code\` | ✅ Working | Inline code works |

## Complex Table with Alignment

| Left Aligned | Center Aligned | Right Aligned | Mixed Content |
|:-------------|:--------------:|--------------:|:--------------|
| This is left | This is center | This is right | **Bold** |
| Another row | *Italic text* | 123.45 | \`code\` |
| Long content that might wrap | ~~Strike~~ | $99.99 | [Link](https://example.com) |

## Table with Code and Links

| Language | Example | Documentation |
|----------|---------|---------------|
| JavaScript | \`console.log('Hello')\` | [MDN](https://developer.mozilla.org) |
| Python | \`print("Hello")\` | [Python.org](https://python.org) |
| TypeScript | \`const msg: string = "Hello"\` | [TypeScript](https://typescriptlang.org) |

## Large Table for Responsive Testing

| Column 1 | Column 2 | Column 3 | Column 4 | Column 5 | Column 6 |
|----------|----------|----------|----------|----------|----------|
| Data 1.1 | Data 1.2 | Data 1.3 | Data 1.4 | Data 1.5 | Data 1.6 |
| Data 2.1 | Data 2.2 | Data 2.3 | Data 2.4 | Data 2.5 | Data 2.6 |
| Data 3.1 | Data 3.2 | Data 3.3 | Data 3.4 | Data 3.5 | Data 3.6 |
| Data 4.1 | Data 4.2 | Data 4.3 | Data 4.4 | Data 4.5 | Data 4.6 |

## Table with Special Characters

| Symbol | Unicode | HTML Entity | Description |
|--------|---------|-------------|-------------|
| © | U+00A9 | &copy; | Copyright |
| ® | U+00AE | &reg; | Registered |
| ™ | U+2122 | &trade; | Trademark |
| € | U+20AC | &euro; | Euro |
| £ | U+00A3 | &pound; | Pound |

## Nested Content Table

| Type | Example | Result |
|------|---------|--------|
| **Bold** | \`**text**\` | **text** |
| *Italic* | \`*text*\` | *text* |
| ~~Strike~~ | \`~~text~~\` | ~~text~~ |
| Code | \`\\\`code\\\`\` | \`code\` |

---

**Test Instructions:**
1. Check that tables render consistently in all three views
2. Verify borders, padding, and styling are applied correctly
3. Test responsive behavior on mobile devices
4. Ensure hover effects work properly
5. Check dark mode compatibility`),n=()=>v(void 0,null,function*(){try{yield navigator.clipboard.writeText(s),w.success("Markdown copied to clipboard!")}catch(d){w.error("Failed to copy to clipboard")}}),j=()=>{const d=new Blob([s],{type:"text/markdown"}),m=URL.createObjectURL(d),t=document.createElement("a");t.href=m,t.download="table-rendering-test.md",document.body.appendChild(t),t.click(),document.body.removeChild(t),URL.revokeObjectURL(m),w.success("Markdown file downloaded!")};return e.jsx(R,{children:e.jsxs("div",{className:"container mx-auto py-8 px-4 max-w-7xl",children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs("h1",{className:"text-4xl font-bold mb-4 flex items-center gap-3",children:[e.jsx(C,{className:"h-10 w-10 text-primary"}),"Table Rendering Test"]}),e.jsx("p",{className:"text-lg text-muted-foreground mb-6",children:"Compare table rendering between the unified markdown editor and lesson content display to ensure consistency."}),e.jsxs("div",{className:"flex flex-wrap gap-2 mb-6",children:[e.jsx(g,{variant:"default",children:"Table Rendering"}),e.jsx(g,{variant:"secondary",children:"Unified Markdown"}),e.jsx(g,{variant:"outline",children:"Lesson Content"}),e.jsx(g,{variant:"outline",children:"Responsive Design"})]}),e.jsxs("div",{className:"flex gap-2 mb-6",children:[e.jsxs(k,{variant:"outline",size:"sm",onClick:n,className:"flex items-center gap-2",children:[e.jsx(N,{className:"h-4 w-4"}),"Copy Markdown"]}),e.jsxs(k,{variant:"outline",size:"sm",onClick:j,className:"flex items-center gap-2",children:[e.jsx(D,{className:"h-4 w-4"}),"Download"]})]})]}),e.jsxs(U,{defaultValue:"comparison",className:"space-y-6",children:[e.jsxs(S,{className:"grid w-full grid-cols-4",children:[e.jsx(x,{value:"comparison",children:"Side-by-Side"}),e.jsx(x,{value:"editor",children:"Editor Only"}),e.jsx(x,{value:"preview",children:"Preview Only"}),e.jsx(x,{value:"lesson",children:"Lesson Content"})]}),e.jsx(p,{value:"comparison",className:"space-y-6",children:e.jsxs("div",{className:"grid gap-6 lg:grid-cols-2",children:[e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsxs(l,{className:"flex items-center gap-2",children:[e.jsx(L,{className:"h-5 w-5 text-green-600"}),"Unified Markdown Editor"]}),e.jsx(c,{children:"Tables as they appear in the editor with live preview"})]}),e.jsx(o,{children:e.jsx(f,{initialContent:s,onChange:h,placeholder:"Edit the table markdown...",minHeight:600,autoFocus:!1,showToolbar:!0,showPreview:!0,mode:"split",theme:"github"})})]}),e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsxs(l,{className:"flex items-center gap-2",children:[e.jsx(E,{className:"h-5 w-5 text-yellow-600"}),"Lesson Content Display"]}),e.jsx(c,{children:"Tables as they appear in actual lesson pages (using LessonContent component)"})]}),e.jsx(o,{children:e.jsx("div",{className:"border rounded-lg p-4 bg-card min-h-[600px] overflow-auto",children:e.jsx(y,{content:s})})})]})]})}),e.jsx(p,{value:"editor",className:"space-y-6",children:e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsx(l,{children:"Unified Markdown Editor"}),e.jsx(c,{children:"Full editor interface with toolbar and preview options"})]}),e.jsx(o,{children:e.jsx(f,{initialContent:s,onChange:h,placeholder:"Edit the table markdown...",minHeight:700,autoFocus:!1,showToolbar:!0,showPreview:!0,mode:"editor",theme:"github"})})]})}),e.jsx(p,{value:"preview",className:"space-y-6",children:e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsx(l,{children:"Markdown Preview Component"}),e.jsx(c,{children:"Raw MarkdownPreview component with professional-prose styling"})]}),e.jsx(o,{children:e.jsx("div",{className:"border rounded-lg p-6 bg-card min-h-[600px]",children:e.jsx(M,{content:s,className:"professional-prose",allowHtml:!0})})})]})}),e.jsx(p,{value:"lesson",className:"space-y-6",children:e.jsxs(i,{children:[e.jsxs(r,{children:[e.jsx(l,{children:"Lesson Content Component"}),e.jsx(c,{children:"Exact same component used in actual lesson pages"})]}),e.jsx(o,{children:e.jsx("div",{className:"border rounded-lg p-4 bg-card min-h-[600px]",children:e.jsx(y,{content:s})})})]})})]}),e.jsxs(i,{className:"mt-8",children:[e.jsx(r,{children:e.jsxs(l,{className:"flex items-center gap-2",children:[e.jsx(C,{className:"h-5 w-5"}),"Table Rendering Test Results"]})}),e.jsx(o,{children:e.jsxs("div",{className:"grid gap-4 md:grid-cols-2",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-green-600 mb-2",children:"✅ Expected to Work"}),e.jsxs("ul",{className:"space-y-1 text-sm",children:[e.jsx("li",{children:"• Consistent table borders and styling"}),e.jsx("li",{children:"• Proper header background colors"}),e.jsx("li",{children:"• Alternating row colors"}),e.jsx("li",{children:"• Hover effects on table rows"}),e.jsx("li",{children:"• Responsive table scrolling"}),e.jsx("li",{children:"• Dark mode compatibility"}),e.jsx("li",{children:"• Proper text formatting in cells"}),e.jsx("li",{children:"• Consistent padding and spacing"})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-blue-600 mb-2",children:"🔍 Test Checklist"}),e.jsxs("ul",{className:"space-y-1 text-sm",children:[e.jsx("li",{children:"• Compare all four views side-by-side"}),e.jsx("li",{children:"• Test on mobile devices (responsive)"}),e.jsx("li",{children:"• Toggle dark/light mode"}),e.jsx("li",{children:"• Check table overflow behavior"}),e.jsx("li",{children:"• Verify text formatting in cells"}),e.jsx("li",{children:"• Test hover interactions"}),e.jsx("li",{children:"• Check border consistency"}),e.jsx("li",{children:"• Validate color scheme adherence"})]})]})]})})]})]})})};export{q as default};
