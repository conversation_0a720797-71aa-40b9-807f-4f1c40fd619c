# Database Schema Documentation

This document provides an overview of the database schema for the IVCAN Course LMS application.

## Overview

The database is designed to support a learning management system with the following core features:
- User authentication and role-based access control
- Course creation and management
- Module and lesson organization
- User progress tracking
- Quizzes and assessments
- Achievements and gamification
- Notifications and messaging

## Schema Diagram

```
┌─────────────────┐       ┌─────────────────┐       ┌─────────────────┐
│     Users       │       │     Courses     │       │     Modules     │
├─────────────────┤       ├─────────────────┤       ├─────────────────┤
│ id              │       │ id              │       │ id              │
│ email           │       │ title           │       │ course_id       │
│ password        │       │ slug            │       │ title           │
│ metadata        │       │ description     │       │ slug            │
└─────────────────┘       │ instructor      │       │ module_number   │
        │                 │ image_url       │       │ is_locked       │
        │                 │ total_modules   │       │ is_published    │
        │                 │ created_by      │       └─────────────────┘
        │                 └─────────────────┘               │
        │                         │                         │
        │                         │                         │
┌─────────────────┐       ┌─────────────────┐       ┌─────────────────┐
│    Profiles     │       │  Enrollments    │       │    Lessons      │
├─────────────────┤       ├─────────────────┤       ├─────────────────┤
│ id              │       │ id              │       │ id              │
│ user_id         │       │ user_id         │       │ module_id       │
│ first_name      │       │ course_id       │       │ title           │
│ last_name       │       │ status          │       │ slug            │
│ avatar_url      │       │ enrolled_at     │       │ content         │
│ bio             │       │ completed_at    │       │ duration        │
└─────────────────┘       └─────────────────┘       │ type            │
        │                         │                 │ lesson_number   │
        │                         │                 └─────────────────┘
        │                         │                         │
        │                         │                         │
┌─────────────────┐       ┌─────────────────┐       ┌─────────────────┐
│   User Roles    │       │ Course Progress │       │ Lesson Progress │
├─────────────────┤       ├─────────────────┤       ├─────────────────┤
│ id              │       │ id              │       │ id              │
│ user_id         │       │ user_id         │       │ user_id         │
│ role            │       │ course_id       │       │ lesson_id       │
└─────────────────┘       │ hours_spent     │       │ is_completed    │
                          │ last_accessed_at│       │ progress_percent│
                          └─────────────────┘       └─────────────────┘
```

## Tables

### User-Related Tables

#### `auth.users` (Managed by Supabase Auth)
- Stores user authentication information
- Fields: id, email, password (hashed), metadata

#### `profiles`
- Stores additional user information
- Fields: id, first_name, last_name, avatar_url, bio, completed_lessons, total_points

#### `user_roles`
- Defines user roles (student, teacher)
- Fields: id, user_id, role

#### `role_requests`
- Tracks requests for teacher role
- Fields: id, user_id, requested_role, status, requested_at, processed_at, processed_by, notes

#### `role_audit`
- Audit trail for role changes
- Fields: id, user_id, changed_by, old_role, new_role, created_at

#### `user_preferences`
- User application preferences
- Fields: id, user_id, theme, auto_complete_courses, email_notifications

### Course-Related Tables

#### `courses`
- Main course information
- Fields: id, title, slug, description, instructor, image_url, total_modules, completed_modules, is_published, created_by

#### `modules`
- Course modules
- Fields: id, course_id, title, slug, description, module_number, is_locked, is_published

#### `lessons`
- Module lessons
- Fields: id, module_id, title, slug, description, content, duration, type, lesson_number, requirement, video_url, image_url, is_published

#### `quiz_questions`
- Questions for quizzes
- Fields: id, lesson_id, question, question_type, points, question_order

#### `quiz_answers`
- Answers for quiz questions
- Fields: id, question_id, answer_text, is_correct, answer_order

#### `course_tags`
- Tags for categorizing courses
- Fields: id, name

#### `course_tag_relations`
- Relates courses to tags
- Fields: id, course_id, tag_id

### Progress-Related Tables

#### `user_course_enrollment`
- Tracks user enrollment in courses
- Fields: id, user_id, course_id, status, enrolled_at, completed_at

#### `user_course_progress`
- Tracks overall course progress
- Fields: id, user_id, course_id, hours_spent, last_accessed_at

#### `user_module_progress`
- Tracks module completion
- Fields: id, user_id, module_id, is_completed, completed_at

#### `user_lesson_progress`
- Tracks lesson progress
- Fields: id, user_id, lesson_id, is_completed, progress_percent, time_spent, last_position, completed_at

#### `user_quiz_attempts`
- Tracks quiz attempts
- Fields: id, user_id, lesson_id, score, max_score, passed, started_at, completed_at

#### `user_quiz_answers`
- Stores user answers to quiz questions
- Fields: id, attempt_id, question_id, answer_id, text_answer, is_correct, points_earned

### Achievement-Related Tables

#### `achievements`
- Defines available achievements
- Fields: id, name, description, icon, points

#### `user_achievements`
- Tracks user achievements
- Fields: id, user_id, achievement_id, completed_at

### Notification-Related Tables

#### `notifications`
- Stores user notifications
- Fields: id, user_id, type, title, message, data, read, created_at, read_at

#### `notification_settings`
- User notification preferences
- Fields: id, user_id, course_updates, new_achievements, learning_reminders, progress_reports

#### `feedback`
- User feedback on courses and lessons
- Fields: id, user_id, course_id, lesson_id, rating, comment

## Key Functions

### Role Management
- `has_role(user_id, role)`: Checks if a user has a specific role
- `assign_role(user_id, role)`: Assigns a role to a user
- `approve_role_request(request_id, notes)`: Approves a role request
- `reject_role_request(request_id, notes)`: Rejects a role request

### Progress Tracking
- `update_course_enrollment_status()`: Updates enrollment status based on module completion
- `update_module_progress()`: Updates module progress based on lesson completion
- `get_user_course_progress(user_id, course_id)`: Gets detailed course progress

### Notifications
- `mark_notification_read(notification_id)`: Marks a notification as read
- `mark_all_notifications_read()`: Marks all notifications as read
- `create_notification(user_id, type, title, message, data)`: Creates a notification
- `notify_teachers_on_role_request()`: Notifies teachers about new role requests
- `notify_user_on_role_request_processed()`: Notifies users when their role request is processed

## Security

The database uses Row Level Security (RLS) policies to ensure that users can only access data they are authorized to see. Key security principles:

1. Students can only view and modify their own data
2. Teachers can view all data but can only modify their own courses
3. All tables have appropriate RLS policies
4. Sensitive operations use security definer functions

## Applying the Schema

To apply this database schema, run:

```bash
npm run db:reset
```

This will reset the database and apply all migrations in the correct order.

**Warning**: This will delete all existing data. Use with caution in production environments.
