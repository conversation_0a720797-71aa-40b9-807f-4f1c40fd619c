-- Migration: Fix Function Search Path Security Issues
-- Created: 2025-01-02
-- Description: Fixes all database functions to include proper search_path parameter for security
-- This addresses the "Function Search Path Mutable" warnings from Supabase linter

-- =============================================
-- SECURITY DEFINER FUNCTIONS WITH SEARCH PATH
-- =============================================

-- 1. Fix update_timestamp function
CREATE OR REPLACE FUNCTION public.update_timestamp()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;

-- 2. Fix handle_new_user_role function
CREATE OR REPLACE FUNCTION public.handle_new_user_role()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Insert a default role for new users
  INSERT INTO public.user_roles (user_id, role)
  VALUES (NEW.id, 'student')
  ON CONFLICT (user_id) DO NOTHING;

  RETURN NEW;
END;
$$;

-- 3. Fix get_lesson_navigation function
CREATE OR REPLACE FUNCTION public.get_lesson_navigation(lesson_id_param UUID)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'previous_lesson', (
      SELECT json_build_object('id', id, 'slug', slug, 'title', title)
      FROM public.lessons
      WHERE module_id = (SELECT module_id FROM public.lessons WHERE id = lesson_id_param)
        AND order_index < (SELECT order_index FROM public.lessons WHERE id = lesson_id_param)
      ORDER BY order_index DESC
      LIMIT 1
    ),
    'next_lesson', (
      SELECT json_build_object('id', id, 'slug', slug, 'title', title)
      FROM public.lessons
      WHERE module_id = (SELECT module_id FROM public.lessons WHERE id = lesson_id_param)
        AND order_index > (SELECT order_index FROM public.lessons WHERE id = lesson_id_param)
      ORDER BY order_index ASC
      LIMIT 1
    )
  ) INTO result;

  RETURN result;
END;
$$;

-- 4. Fix health_check function (already exists but ensure proper search_path)
CREATE OR REPLACE FUNCTION public.health_check()
RETURNS jsonb
LANGUAGE SQL
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT jsonb_build_object(
    'status', 'ok',
    'timestamp', now(),
    'version', current_setting('server_version')
  );
$$;

-- 5. Fix get_tables_with_rls function (already exists but ensure proper search_path)
CREATE OR REPLACE FUNCTION public.get_tables_with_rls()
RETURNS TABLE (
  table_schema text,
  table_name text,
  rls_enabled boolean
)
LANGUAGE SQL
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT
    n.nspname AS table_schema,
    c.relname AS table_name,
    c.relrowsecurity AS rls_enabled
  FROM pg_class c
  JOIN pg_namespace n ON n.oid = c.relnamespace
  WHERE c.relkind = 'r'  -- Only tables
    AND n.nspname = 'public'  -- Only public schema
  ORDER BY table_schema, table_name;
$$;

-- 6. Fix update_module_completion function
CREATE OR REPLACE FUNCTION public.update_module_completion(
  p_user_id UUID,
  p_module_id UUID,
  p_is_completed BOOLEAN DEFAULT TRUE
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  INSERT INTO public.user_module_progress (
    user_id,
    module_id,
    is_completed,
    completed_at,
    updated_at
  ) VALUES (
    p_user_id,
    p_module_id,
    p_is_completed,
    CASE WHEN p_is_completed THEN NOW() ELSE NULL END,
    NOW()
  )
  ON CONFLICT (user_id, module_id)
  DO UPDATE SET
    is_completed = p_is_completed,
    completed_at = CASE WHEN p_is_completed THEN NOW() ELSE NULL END,
    updated_at = NOW();

  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in update_module_completion: %', SQLERRM;
    RETURN FALSE;
END;
$$;

-- 7. Fix update_module_progress_cache function
CREATE OR REPLACE FUNCTION public.update_module_progress_cache()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Update the module progress cache when lesson progress changes
  INSERT INTO public.user_module_progress (
    user_id,
    module_id,
    is_completed,
    updated_at
  )
  SELECT
    NEW.user_id,
    l.module_id,
    (
      SELECT COUNT(*) = COUNT(CASE WHEN ulp.is_completed THEN 1 END)
      FROM public.lessons l2
      LEFT JOIN public.user_lesson_progress ulp ON l2.id = ulp.lesson_id AND ulp.user_id = NEW.user_id
      WHERE l2.module_id = l.module_id
    ),
    NOW()
  FROM public.lessons l
  WHERE l.id = NEW.lesson_id
  ON CONFLICT (user_id, module_id)
  DO UPDATE SET
    is_completed = EXCLUDED.is_completed,
    updated_at = NOW();

  RETURN NEW;
END;
$$;

-- 8. Fix refresh_schema_cache function
CREATE OR REPLACE FUNCTION public.refresh_schema_cache()
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- This is a no-op function that serves as a signal to the client to refresh the schema cache
  NULL;
END;
$$;

-- 9. Fix set_completed_at function
CREATE OR REPLACE FUNCTION public.set_completed_at()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  IF NEW.is_completed = TRUE AND OLD.is_completed = FALSE THEN
    NEW.completed_at = NOW();
  ELSIF NEW.is_completed = FALSE THEN
    NEW.completed_at = NULL;
  END IF;

  RETURN NEW;
END;
$$;

-- 10. Fix complete_course function (already has search_path but ensure it's correct)
-- This function was already properly configured in 20250417001_create_complete_course_function.sql

-- 11. Fix exec_sql function
CREATE OR REPLACE FUNCTION public.exec_sql(sql text)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  EXECUTE sql;
END;
$$;

-- 12. Fix execute_sql function
CREATE OR REPLACE FUNCTION public.execute_sql(sql_query TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Execute the SQL query
  EXECUTE sql_query;
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error executing SQL: %', SQLERRM;
    RETURN FALSE;
END;
$$;

-- 13. Fix mark_lesson_completed function
CREATE OR REPLACE FUNCTION public.mark_lesson_completed(
  p_user_id UUID,
  p_lesson_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  INSERT INTO public.user_lesson_progress (
    user_id,
    lesson_id,
    is_completed,
    completed_at,
    updated_at
  ) VALUES (
    p_user_id,
    p_lesson_id,
    TRUE,
    NOW(),
    NOW()
  )
  ON CONFLICT (user_id, lesson_id)
  DO UPDATE SET
    is_completed = TRUE,
    completed_at = NOW(),
    updated_at = NOW();

  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in mark_lesson_completed: %', SQLERRM;
    RETURN FALSE;
END;
$$;

-- 14. Fix check_module_completion function
CREATE OR REPLACE FUNCTION public.check_module_completion(
  p_user_id UUID,
  p_module_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  total_lessons INTEGER;
  completed_lessons INTEGER;
BEGIN
  -- Count total lessons in the module
  SELECT COUNT(*) INTO total_lessons
  FROM public.lessons
  WHERE module_id = p_module_id;

  -- Count completed lessons for the user
  SELECT COUNT(*) INTO completed_lessons
  FROM public.user_lesson_progress ulp
  JOIN public.lessons l ON l.id = ulp.lesson_id
  WHERE l.module_id = p_module_id
    AND ulp.user_id = p_user_id
    AND ulp.is_completed = TRUE;

  -- Return true if all lessons are completed
  RETURN (total_lessons > 0 AND completed_lessons = total_lessons);
END;
$$;

-- 15. Fix mark_notification_read function
CREATE OR REPLACE FUNCTION public.mark_notification_read(
  p_notification_id UUID,
  p_user_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  UPDATE public.notifications
  SET
    is_read = TRUE,
    read_at = NOW(),
    updated_at = NOW()
  WHERE id = p_notification_id
    AND user_id = p_user_id;

  RETURN FOUND;
END;
$$;

-- 16. Fix mark_all_notifications_read function
CREATE OR REPLACE FUNCTION public.mark_all_notifications_read(p_user_id UUID)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  updated_count INTEGER;
BEGIN
  UPDATE public.notifications
  SET
    is_read = TRUE,
    read_at = NOW(),
    updated_at = NOW()
  WHERE user_id = p_user_id
    AND is_read = FALSE;

  GET DIAGNOSTICS updated_count = ROW_COUNT;
  RETURN updated_count;
END;
$$;

-- 17. Fix has_role function
CREATE OR REPLACE FUNCTION public.has_role(p_user_id UUID, p_role TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM public.user_roles
    WHERE user_id = p_user_id
      AND role = p_role
  );
END;
$$;

-- 18. Fix assign_role function
CREATE OR REPLACE FUNCTION public.assign_role(p_user_id UUID, p_role TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  INSERT INTO public.user_roles (user_id, role, assigned_at)
  VALUES (p_user_id, p_role, NOW())
  ON CONFLICT (user_id)
  DO UPDATE SET
    role = p_role,
    assigned_at = NOW();

  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in assign_role: %', SQLERRM;
    RETURN FALSE;
END;
$$;

-- 19. Fix handle_new_user function
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Create a profile for the new user
  INSERT INTO public.profiles (
    id,
    email,
    first_name,
    last_name,
    created_at,
    updated_at
  ) VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'first_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'last_name', ''),
    NOW(),
    NOW()
  );

  -- Assign default role
  INSERT INTO public.user_roles (user_id, role, assigned_at)
  VALUES (NEW.id, 'student', NOW());

  RETURN NEW;
END;
$$;

-- =============================================
-- GRANT PERMISSIONS
-- =============================================

-- Grant execute permissions to appropriate roles
GRANT EXECUTE ON FUNCTION public.health_check() TO anon, authenticated;
GRANT EXECUTE ON FUNCTION public.get_tables_with_rls() TO service_role;
GRANT EXECUTE ON FUNCTION public.get_lesson_navigation(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_module_completion(UUID, UUID, BOOLEAN) TO authenticated;
GRANT EXECUTE ON FUNCTION public.refresh_schema_cache() TO authenticated;
GRANT EXECUTE ON FUNCTION public.exec_sql(text) TO service_role;
GRANT EXECUTE ON FUNCTION public.execute_sql(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.mark_lesson_completed(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.check_module_completion(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.mark_notification_read(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.mark_all_notifications_read(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.has_role(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.assign_role(UUID, TEXT) TO service_role;

-- =============================================
-- COMMENTS FOR DOCUMENTATION
-- =============================================

COMMENT ON FUNCTION public.update_timestamp() IS 'Trigger function to automatically update timestamp columns';
COMMENT ON FUNCTION public.handle_new_user_role() IS 'Trigger function to assign default role to new users';
COMMENT ON FUNCTION public.get_lesson_navigation(UUID) IS 'Returns navigation information for a lesson';
COMMENT ON FUNCTION public.health_check() IS 'Returns database health status';
COMMENT ON FUNCTION public.get_tables_with_rls() IS 'Returns list of tables with RLS status';
COMMENT ON FUNCTION public.update_module_completion(UUID, UUID, BOOLEAN) IS 'Updates module completion status for a user';
COMMENT ON FUNCTION public.update_module_progress_cache() IS 'Trigger function to update module progress cache';
COMMENT ON FUNCTION public.refresh_schema_cache() IS 'Signal to refresh schema cache on client side';
COMMENT ON FUNCTION public.set_completed_at() IS 'Trigger function to set completion timestamp';
COMMENT ON FUNCTION public.exec_sql(text) IS 'Execute arbitrary SQL with proper permissions (service role only)';
COMMENT ON FUNCTION public.execute_sql(TEXT) IS 'Execute SQL query with error handling';
COMMENT ON FUNCTION public.mark_lesson_completed(UUID, UUID) IS 'Mark a lesson as completed for a user';
COMMENT ON FUNCTION public.check_module_completion(UUID, UUID) IS 'Check if all lessons in a module are completed';
COMMENT ON FUNCTION public.mark_notification_read(UUID, UUID) IS 'Mark a notification as read';
COMMENT ON FUNCTION public.mark_all_notifications_read(UUID) IS 'Mark all notifications as read for a user';
COMMENT ON FUNCTION public.has_role(UUID, TEXT) IS 'Check if a user has a specific role';
COMMENT ON FUNCTION public.assign_role(UUID, TEXT) IS 'Assign a role to a user (service role only)';
COMMENT ON FUNCTION public.handle_new_user() IS 'Trigger function to handle new user registration';
