import { supabase } from '@/integrations/supabase/client';
import {
  StudentProfile,
  TestSubmissionSummary,
  StudentTestAnalytics,
  DetailedTestResponse,
  TestAnalyticsOverview,
  OverallAnalyticsExport,
  TestAnalyticsSummary,
  QuestionAnalytics
} from '@/types/test-analytics';
import { ratingDescriptions } from '@/types/module-test';

/**
 * Get all students who have completed tests with their submission summaries
 */
export const getTestAnalyticsOverview = async (): Promise<TestAnalyticsOverview> => {
  try {
    console.log('Fetching test analytics overview...');

    // First, get all test responses
    const { data: responses, error } = await supabase
      .from('module_test_responses')
      .select('id, test_id, user_id, responses, created_at, updated_at');

    if (error) {
      console.error('Error fetching test analytics:', error);
      throw error;
    }

    console.log('Fetched responses:', responses?.length || 0);

    if (!responses || responses.length === 0) {
      console.log('No responses found');
      return {
        totalStudents: 0,
        totalTestsCompleted: 0,
        studentsWithTests: []
      };
    }

    // Get user profiles separately
    const userIds = [...new Set(responses.map((r: any) => r.user_id))];
    console.log('Found user IDs:', userIds.length);

    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, first_name, last_name')
      .in('id', userIds);

    if (profilesError) {
      console.error('Error fetching profiles:', profilesError);
    }

    // Get test details separately
    const testIds = [...new Set(responses.map((r: any) => r.test_id))];
    const { data: tests, error: testsError } = await supabase
      .from('module_tests')
      .select(`
        id,
        title,
        type,
        module_id,
        modules (
          id,
          title,
          course_id,
          courses (
            id,
            title
          )
        )
      `)
      .in('id', testIds);

    if (testsError) {
      console.error('Error fetching tests:', testsError);
    }

    // Create maps for quick lookup
    const profileMap = new Map(profiles?.map(p => [p.id, p]) || []);
    const testMap = new Map(tests?.map(t => [t.id, t]) || []);

    // Group responses by student
    const studentMap = new Map<string, StudentTestAnalytics>();

    responses.forEach((response: any) => {
      const userId = response.user_id;
      const profile = profileMap.get(userId);
      const test = testMap.get(response.test_id);

      if (!test) {
        console.warn('Test not found for response:', response.id);
        return;
      }

      const module = test.modules;
      const course = module?.courses;

      // Create student profile
      const student: StudentProfile = {
        id: userId,
        email: 'User', // We'll use a placeholder for now
        firstName: profile?.first_name,
        lastName: profile?.last_name,
        fullName: `${profile?.first_name || ''} ${profile?.last_name || ''}`.trim() || `User ${userId.slice(0, 8)}`
      };

      // Create test submission summary
      const submission: TestSubmissionSummary = {
        id: response.id,
        testId: test.id,
        testTitle: test.title,
        testType: test.type,
        moduleId: module?.id || '',
        moduleTitle: module?.title || 'Unknown Module',
        courseId: course?.id || '',
        courseTitle: course?.title || 'Unknown Course',
        submittedAt: response.created_at,
        responseCount: JSON.parse(response.responses || '[]').length
      };

      // Add to student map
      if (studentMap.has(userId)) {
        const existing = studentMap.get(userId)!;
        existing.testSubmissions.push(submission);
        existing.totalTestsCompleted++;
      } else {
        studentMap.set(userId, {
          student,
          testSubmissions: [submission],
          totalTestsCompleted: 1
        });
      }
    });

    const studentsWithTests = Array.from(studentMap.values());
    console.log('Processed students:', studentsWithTests.length);

    return {
      totalStudents: studentsWithTests.length,
      totalTestsCompleted: responses.length,
      studentsWithTests
    };
  } catch (error) {
    console.error('Error in getTestAnalyticsOverview:', error);
    throw error;
  }
};

/**
 * Get detailed test response for a specific submission
 */
export const getDetailedTestResponse = async (responseId: string): Promise<DetailedTestResponse> => {
  try {
    // Get the test response
    const { data: response, error } = await supabase
      .from('module_test_responses')
      .select('id, test_id, user_id, responses, created_at, updated_at')
      .eq('id', responseId)
      .single();

    if (error) {
      console.error('Error fetching detailed test response:', error);
      throw error;
    }

    if (!response) {
      throw new Error('Test response not found');
    }

    // Get user profile separately
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, first_name, last_name')
      .eq('id', response.user_id)
      .single();

    if (profileError) {
      console.error('Error fetching profile:', profileError);
    }

    // Get test details separately
    const { data: test, error: testError } = await supabase
      .from('module_tests')
      .select(`
        id,
        title,
        type,
        description,
        questions,
        module_id,
        modules (
          id,
          title,
          course_id,
          courses (
            id,
            title
          )
        )
      `)
      .eq('id', response.test_id)
      .single();

    if (testError) {
      console.error('Error fetching test:', testError);
      throw testError;
    }

    const module = test.modules;
    const course = module.courses;

    // Parse responses and questions
    const userResponses = JSON.parse(response.responses || '[]');
    const testQuestions = JSON.parse(test.questions || '[]');

    // Map responses to questions
    const responsesWithQuestions = userResponses.map((userResponse: any) => {
      const question = testQuestions.find((q: any) => q.id === userResponse.questionId);
      return {
        questionId: userResponse.questionId,
        rating: userResponse.rating,
        question: question || {
          id: userResponse.questionId,
          question: 'Question not found',
          questionNumber: 0,
          type: 'rating',
          minRating: 1,
          maxRating: 4,
          minLabel: 'Strongly disagree',
          maxLabel: 'Strongly agree'
        }
      };
    });

    const detailedResponse: DetailedTestResponse = {
      id: response.id,
      testId: response.test_id,
      userId: response.user_id,
      student: {
        id: response.user_id,
        email: 'User', // We'll use a placeholder for now
        firstName: profile?.first_name,
        lastName: profile?.last_name,
        fullName: `${profile?.first_name || ''} ${profile?.last_name || ''}`.trim() || `User ${response.user_id.slice(0, 8)}`
      },
      test: {
        id: test.id,
        title: test.title,
        type: test.type,
        description: test.description,
        module: {
          id: module.id,
          title: module.title,
          course: {
            id: course.id,
            title: course.title
          }
        }
      },
      responses: responsesWithQuestions,
      createdAt: response.created_at,
      updatedAt: response.updated_at
    };

    return detailedResponse;
  } catch (error) {
    console.error('Error in getDetailedTestResponse:', error);
    throw error;
  }
};

/**
 * Get all test responses for a specific student
 */
export const getStudentTestResponses = async (userId: string): Promise<DetailedTestResponse[]> => {
  try {
    const { data: responses, error } = await supabase
      .from('module_test_responses')
      .select('id')
      .eq('user_id', userId);

    if (error) {
      console.error('Error fetching student test responses:', error);
      throw error;
    }

    if (!responses || responses.length === 0) {
      return [];
    }

    // Get detailed responses for each submission
    const detailedResponses = await Promise.all(
      responses.map(response => getDetailedTestResponse(response.id))
    );

    return detailedResponses;
  } catch (error) {
    console.error('Error in getStudentTestResponses:', error);
    throw error;
  }
};

/**
 * Get overall analytics export with percentage breakdowns for all tests
 */
export const getOverallAnalyticsExport = async (): Promise<OverallAnalyticsExport> => {
  try {
    console.log('Starting getOverallAnalyticsExport...');

    // Get all test responses
    const { data: responses, error } = await supabase
      .from('module_test_responses')
      .select('id, test_id, user_id, responses, created_at');

    if (error) {
      console.error('Error fetching responses for analytics:', error);
      throw error;
    }

    if (!responses || responses.length === 0) {
      return {
        generatedAt: new Date().toISOString(),
        totalStudents: 0,
        totalResponses: 0,
        testSummaries: []
      };
    }

    // Get all unique test IDs
    const testIds = [...new Set(responses.map(r => r.test_id))];

    // Get test details with questions
    const { data: tests, error: testsError } = await supabase
      .from('module_tests')
      .select(`
        id,
        title,
        type,
        questions,
        module_id,
        modules (
          id,
          title,
          course_id,
          courses (
            id,
            title
          )
        )
      `)
      .in('id', testIds);

    if (testsError) {
      console.error('Error fetching tests for analytics:', testsError);
      throw testsError;
    }

    // Process each test
    const testSummaries: TestAnalyticsSummary[] = tests?.map(test => {
      const testResponses = responses.filter(r => r.test_id === test.id);
      const questions = JSON.parse(test.questions || '[]');

      // Process each question
      const questionAnalytics: QuestionAnalytics[] = questions.map((question: any) => {
        const questionResponses = testResponses
          .map(r => JSON.parse(r.responses || '[]'))
          .flat()
          .filter((resp: any) => resp.questionId === question.id);

        // Count responses for each rating
        const ratingCounts = new Map<number, number>();
        questionResponses.forEach((resp: any) => {
          const rating = resp.rating;
          ratingCounts.set(rating, (ratingCounts.get(rating) || 0) + 1);
        });

        // Calculate percentages and create breakdown
        const totalQuestionResponses = questionResponses.length;
        const optionBreakdown = ratingDescriptions.map(option => {
          const count = ratingCounts.get(option.value) || 0;
          const percentage = totalQuestionResponses > 0 ? (count / totalQuestionResponses) * 100 : 0;

          return {
            rating: option.value,
            label: option.label,
            count,
            percentage: Math.round(percentage * 100) / 100 // Round to 2 decimal places
          };
        });

        // Calculate average rating
        const totalRatingSum = questionResponses.reduce((sum: number, resp: any) => sum + resp.rating, 0);
        const averageRating = totalQuestionResponses > 0 ? totalRatingSum / totalQuestionResponses : 0;

        return {
          questionId: question.id,
          questionText: question.question,
          questionNumber: question.questionNumber,
          totalResponses: totalQuestionResponses,
          optionBreakdown,
          averageRating: Math.round(averageRating * 100) / 100
        };
      });

      return {
        testId: test.id,
        testTitle: test.title,
        testType: test.type,
        moduleTitle: test.modules?.title || 'Unknown Module',
        courseTitle: test.modules?.courses?.title || 'Unknown Course',
        totalResponses: testResponses.length,
        questions: questionAnalytics
      };
    }) || [];

    // Calculate unique students
    const uniqueStudents = new Set(responses.map(r => r.user_id)).size;

    return {
      generatedAt: new Date().toISOString(),
      totalStudents: uniqueStudents,
      totalResponses: responses.length,
      testSummaries
    };
  } catch (error) {
    console.error('Error in getOverallAnalyticsExport:', error);
    throw error;
  }
};
