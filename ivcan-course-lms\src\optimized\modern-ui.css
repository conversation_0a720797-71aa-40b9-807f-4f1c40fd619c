/* Modern UI Styles */

/* Typography with improved responsive sizing */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Poppins', system-ui, sans-serif;
  font-weight: 700;
  letter-spacing: -0.02em;
  line-height: 1.2;
}

h1 {
  font-size: 2rem;
  line-height: 1.2;
}

h2 {
  font-size: 1.75rem;
  line-height: 1.25;
}

h3 {
  font-size: 1.5rem;
  line-height: 1.3;
}

h4 {
  font-size: 1.25rem;
  line-height: 1.35;
}

h5 {
  font-size: 1.125rem;
  line-height: 1.4;
}

h6 {
  font-size: 1rem;
  line-height: 1.5;
}

/* Responsive typography for larger screens */
@media (min-width: 640px) {
  h1 {
    font-size: 2.25rem;
  }

  h2 {
    font-size: 1.875rem;
  }
}

@media (min-width: 768px) {
  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  h3 {
    font-size: 1.5rem;
  }
}

body {
  font-family: 'Poppins', system-ui, sans-serif;
  font-weight: 400;
  line-height: 1.6;
  letter-spacing: -0.01em;
  text-size-adjust: 100%; /* Prevent text size adjustment on orientation change */
  -webkit-text-size-adjust: 100%;
}

/* Modern Card Styles - Simplified for unified card system */
.modern-card {
  @apply rounded-xl bg-card border border-border/40 shadow-sm;
  @apply transition-all duration-200 hover:shadow-md hover:border-border/60;
  @apply min-h-[64px] sm:min-h-0 touch-manipulation active:scale-[0.98];
  @apply w-full; /* Ensure cards take full width on mobile */
}

/* Card grid for responsive layouts */

/* Modern Button Styles - Simplified for unified button system */

/* Mobile-friendly icon button */

/* Modern Input Styles with improved mobile usability */

/* Modern form group for better spacing */

/* Modern form label */

/* Modern form helper text */

/* Modern Badge Styles */

/* Modern Sidebar */

/* Modern Dashboard Stats */

/* Modern Course Card */

/* Modern Progress Bar */

/* Modern Layout with improved mobile responsiveness */

/* Modern section with responsive spacing */

/* Modern page with responsive spacing */

/* Modern Search with improved mobile usability */

/* Modern search icon */

/* Modern search clear button */

/* Modern Tabs with improved mobile usability */

/* Modern Dropdown with improved mobile usability */

/* Modern Tooltip with improved mobile usability */

/* Modern Animations optimized for performance */
.fade-in {
  animation: fadeIn 0.3s ease-out forwards;
  will-change: opacity; /* Optimize for animation */
}

/* Reduced motion alternatives */
@media (prefers-reduced-motion: reduce) {
  .fade-in {
    animation: fadeIn 0.15s ease-out forwards !important;
  }
}

/* Mobile-optimized animations */

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

/* Modern UI utilities */

/* Modern spacing that works better on mobile */

/* Improved mobile text sizes */
.text-mobile-xl {
  font-size: clamp(1.25rem, 5vw, 1.5rem);
  line-height: 1.3;
}

.text-mobile-lg {
  font-size: clamp(1.125rem, 4vw, 1.25rem);
  line-height: 1.4;
}

.text-mobile-base {
  font-size: clamp(1rem, 3.5vw, 1.125rem);
  line-height: 1.5;
}

.text-mobile-sm {
  font-size: clamp(0.875rem, 3vw, 1rem);
  line-height: 1.5;
}

/* Modern grid layouts */

/* Modern flex layouts */

/* Modern card styles */

/* Modern button styles */

/* Modern input styles */

/* Modern list styles */

/* Modern navigation styles */

/* Modern loader styles */

@keyframes loader-spin {
  to { transform: rotate(360deg); }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  :root {
    --card-bg: rgba(255,255,255,0.05);
    --card-border: rgba(255,255,255,0.1);
    --input-bg: rgba(255,255,255,0.05);
    --input-border: rgba(255,255,255,0.1);
    --list-bg: rgba(255,255,255,0.05);
    --list-border: rgba(255,255,255,0.1);
    --list-active-bg: rgba(255,255,255,0.1);
    --nav-bg: rgba(0,0,0,0.8);
    --nav-border: rgba(255,255,255,0.1);
    --loader-track: rgba(255,255,255,0.1);
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Touch-specific optimizations */

/* Enhanced focus styles */
