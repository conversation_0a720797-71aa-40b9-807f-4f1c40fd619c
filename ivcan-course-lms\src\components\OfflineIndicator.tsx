import React from 'react';
import useOnlineStatus from '@/hooks/useOnlineStatus';
import { ExclamationTriangleIcon, WifiIcon } from '@heroicons/react/24/outline';

/**
 * Component to display network status indicators
 */
const OfflineIndicator: React.FC = () => {
  const { isOnline, isOffline } = useOnlineStatus();

  // Don't render anything if online
  if (isOnline) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50 flex items-center gap-2 rounded-md bg-amber-100 px-3 py-2 text-amber-800 shadow-md dark:bg-amber-900 dark:text-amber-100">
      {isOffline ? (
        <ExclamationTriangleIcon className="h-5 w-5 text-amber-800 dark:text-amber-100" />
      ) : (
        <WifiIcon className="h-5 w-5 text-red-600 dark:text-red-400" />
      )}
      <span className="text-sm font-medium">
        {isOffline ? 'You are offline' : 'Connected'}
      </span>
    </div>
  );
};

export default OfflineIndicator;