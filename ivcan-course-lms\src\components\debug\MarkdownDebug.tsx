import React, { useState } from 'react';
import { EnhancedMarkdownPreview } from '@/components/ui/enhanced-markdown-preview';
import { TiptapMarkdownEditor } from '@/components/ui/tiptap-markdown-editor';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';

const TEST_CONTENT = `# Markdown Debug Test

## Tables Test

| Feature | Status | Priority | Notes |
|---------|--------|----------|-------|
| Tables | ✅ Working | High | Should display with borders and styling |
| Code Blocks | ✅ Working | High | Syntax highlighting should work |
| Task Lists | ✅ Working | Medium | Checkboxes should be styled |
| Inline Code | \`working\` | Low | Should have background color |

## Code Blocks Test

### JavaScript Example
\`\`\`javascript
function greetUser(name) {
  console.log(\`Hello, \${name}!\`);
  return \`Welcome to the course, \${name}\`;
}

const user = "Student";
greetUser(user);
\`\`\`

### Python Example
\`\`\`python
def calculate_grade(score, total):
    percentage = (score / total) * 100
    if percentage >= 90:
        return "A"
    elif percentage >= 80:
        return "B"
    elif percentage >= 70:
        return "C"
    else:
        return "F"

print(calculate_grade(85, 100))
\`\`\`

### SQL Example
\`\`\`sql
SELECT
    c.course_name,
    COUNT(e.user_id) as enrolled_students,
    AVG(p.completion_percentage) as avg_completion
FROM courses c
LEFT JOIN enrollments e ON c.id = e.course_id
LEFT JOIN progress p ON e.id = p.enrollment_id
GROUP BY c.id, c.course_name
ORDER BY enrolled_students DESC;
\`\`\`

## Accordion Test

<details>
<summary>📚 Course Content Structure</summary>

This section explains how course content is organized:

1. **Courses** - Top level containers
2. **Modules** - Logical groupings of lessons
3. **Lessons** - Individual learning units

### Code Example
\`\`\`typescript
interface Course {
  id: string;
  title: string;
  modules: Module[];
}

interface Module {
  id: string;
  title: string;
  lessons: Lesson[];
}
\`\`\`

</details>

<details>
<summary>🔧 Technical Implementation</summary>

The markdown editor uses several technologies:

- **TipTap** for rich text editing
- **Prism.js** for syntax highlighting
- **DOMPurify** for security
- **Tailwind CSS** for styling

| Component | Purpose | Status |
|-----------|---------|--------|
| TipTap Editor | Rich text editing | ✅ |
| Markdown Preview | Content display | ✅ |
| Table Support | Data presentation | ✅ |
| Code Highlighting | Syntax coloring | ✅ |

</details>

## Task Lists Test

### Course Progress
- [x] Set up development environment
- [x] Install dependencies
- [x] Configure TipTap editor
- [x] Add table support
- [x] Fix code block rendering
- [ ] Add more advanced features
- [ ] Write comprehensive tests
- [ ] Deploy to production

### Feature Checklist
- [x] **Tables** - Properly styled with borders
- [x] **Code Blocks** - Syntax highlighting works
- [x] **Inline Code** - Background styling applied
- [x] **Task Lists** - Checkboxes render correctly
- [ ] **Math Equations** - LaTeX support (future)
- [ ] **Diagrams** - Mermaid integration (future)

## Inline Elements Test

Here's some text with \`inline code\`, **bold text**, *italic text*, and ~~strikethrough~~.

You can also have [links](https://example.com) and images (if uploaded).

## Callouts Test

> [!INFO]
> This is an informational callout. It should have a blue color scheme and an info icon.

> [!WARNING]
> This is a warning callout. It should have a yellow/orange color scheme and a warning icon.

> [!ERROR]
> This is an error callout. It should have a red color scheme and an error icon.

> [!SUCCESS]
> This is a success callout. It should have a green color scheme and a success icon.
`;

export default function MarkdownDebug() {
  const [showRawHtml, setShowRawHtml] = useState(false);
  const [rawHtml, setRawHtml] = useState('');
  const [editorContent, setEditorContent] = useState(TEST_CONTENT);

  const handleShowRaw = () => {
    // Get the rendered HTML from the preview component
    const previewElement = document.querySelector('.professional-lesson-preview');
    if (previewElement) {
      setRawHtml(previewElement.innerHTML);
      setShowRawHtml(true);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">Markdown Debug Tool</h1>
        <p className="text-muted-foreground mb-4">
          This tool helps debug the markdown rendering to ensure tables, code blocks, and other features work correctly in both the editor and preview modes.
        </p>

        <div className="flex gap-2 mb-4">
          <Button onClick={handleShowRaw}>Show Raw HTML</Button>
          <Button variant="outline" onClick={() => setShowRawHtml(false)}>Hide Raw HTML</Button>
          <Button variant="outline" onClick={() => setEditorContent(TEST_CONTENT)}>Reset Content</Button>
        </div>
      </div>

      <Tabs defaultValue="preview" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="preview">Preview Only</TabsTrigger>
          <TabsTrigger value="editor">TipTap Editor</TabsTrigger>
          <TabsTrigger value="source">Source Code</TabsTrigger>
        </TabsList>

        <TabsContent value="preview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Enhanced Markdown Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <EnhancedMarkdownPreview
                content={editorContent}
                enableImageZoom={true}
                enableCodeCopy={true}
                enableExport={false}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="editor" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>TipTap Markdown Editor</CardTitle>
            </CardHeader>
            <CardContent>
              <TiptapMarkdownEditor
                initialContent={editorContent}
                onChange={setEditorContent}
                placeholder="Start typing to test the editor..."
                minHeight={600}
                autoFocus={false}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="source" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Current Markdown Source</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-muted p-4 rounded-lg text-sm overflow-auto max-h-96">
                <code>{editorContent}</code>
              </pre>
            </CardContent>
          </Card>

          {showRawHtml && (
            <Card>
              <CardHeader>
                <CardTitle>Raw HTML Output</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="bg-muted p-4 rounded-lg text-sm overflow-auto max-h-96">
                  <code>{rawHtml}</code>
                </pre>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
