import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

/**
 * Direct Database Access Service
 *
 * This service provides direct access to the database tables
 * It's designed to be used by teachers to perform CRUD operations
 */

// =====================
// USER ROLES
// =====================

/**
 * Assigns the teacher role to a user
 */
export async function assignTeacherRole(userId: string): Promise<boolean> {
  try {
    console.log(`Assigning teacher role to user: ${userId}`);

    // Check if the user already has a role
    const { data: existingRole, error: checkError } = await supabase
      .from('user_roles')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();

    if (checkError) {
      console.error('Error checking existing role:', checkError);
      toast.error(`Failed to check existing role: ${checkError.message}`);
      return false;
    }

    let result;

    if (existingRole) {
      // Update existing role
      console.log('User already has a role, updating to teacher');
      result = await supabase
        .from('user_roles')
        .update({ role: 'teacher' })
        .eq('user_id', userId);
    } else {
      // Insert new role
      console.log('Creating new teacher role for user');
      result = await supabase
        .from('user_roles')
        .insert([{ user_id: userId, role: 'teacher' }]);
    }

    if (result.error) {
      console.error('Error assigning teacher role:', result.error);
      toast({
        title: "Error",
        description: `Failed to assign teacher role: ${result.error.message}`,
        variant: "destructive",
      });
      return false;
    }

    toast({
      title: "Success",
      description: "Teacher role assigned successfully",
    });
    return true;
  } catch (error: any) {
    console.error('Error in assignTeacherRole:', error);
    toast({
      title: "Error",
      description: `An unexpected error occurred: ${error.message}`,
      variant: "destructive",
    });
    return false;
  }
}

// Types
export interface CourseData {
  id?: string;
  title: string;
  slug: string;
  description: string;
  instructor: string;
}

export interface ModuleData {
  id?: string;
  course_id: string;
  title: string;
  slug: string;
  module_number: number;
  is_locked?: boolean;
}

export interface LessonData {
  id?: string;
  module_id: string;
  title: string;
  slug: string;
  duration: string;
  type: string;
  content?: string;
  requirement?: string;
}

// =====================
// COURSES
// =====================

/**
 * Creates a new course
 */
export async function createCourse(courseData: CourseData): Promise<CourseData | null> {
  try {
    console.log('Creating course:', courseData);

    const { data, error } = await supabase
      .from('courses')
      .insert([{
        title: courseData.title,
        slug: courseData.slug,
        description: courseData.description,
        instructor: courseData.instructor,
        total_modules: 0,
        completed_modules: 0
      }])
      .select();

    if (error) {
      console.error('Error creating course:', error);
      toast({
        title: "Error",
        description: `Failed to create course: ${error.message}`,
        variant: "destructive",
      });
      return null;
    }

    toast({
      title: "Success",
      description: "Course created successfully",
    });
    return data[0];
  } catch (error: any) {
    console.error('Error in createCourse:', error);
    toast({
      title: "Error",
      description: `An unexpected error occurred: ${error.message}`,
      variant: "destructive",
    });
    return null;
  }
}

/**
 * Updates an existing course
 */
export async function updateCourse(courseId: string, courseData: Partial<CourseData>): Promise<CourseData | null> {
  try {
    console.log(`Updating course ${courseId}:`, courseData);

    const { data, error } = await supabase
      .from('courses')
      .update({
        title: courseData.title,
        slug: courseData.slug,
        description: courseData.description,
        instructor: courseData.instructor,
        updated_at: new Date().toISOString()
      })
      .eq('id', courseId)
      .select();

    if (error) {
      console.error('Error updating course:', error);
      toast({
        title: "Error",
        description: `Failed to update course: ${error.message}`,
        variant: "destructive",
      });
      return null;
    }

    toast({
      title: "Success",
      description: "Course updated successfully",
    });
    return data[0];
  } catch (error: any) {
    console.error('Error in updateCourse:', error);
    toast({
      title: "Error",
      description: `An unexpected error occurred: ${error.message}`,
      variant: "destructive",
    });
    return null;
  }
}

/**
 * Deletes a course
 */
export async function deleteCourse(courseId: string): Promise<boolean> {
  try {
    console.log(`Deleting course ${courseId}`);

    const { error } = await supabase
      .from('courses')
      .delete()
      .eq('id', courseId);

    if (error) {
      console.error('Error deleting course:', error);
      toast({
        title: "Error",
        description: `Failed to delete course: ${error.message}`,
        variant: "destructive",
      });
      return false;
    }

    toast({
      title: "Success",
      description: "Course deleted successfully",
    });
    return true;
  } catch (error: any) {
    console.error('Error in deleteCourse:', error);
    toast({
      title: "Error",
      description: `An unexpected error occurred: ${error.message}`,
      variant: "destructive",
    });
    return false;
  }
}

// =====================
// MODULES
// =====================

/**
 * Creates a new module
 */
export async function createModule(moduleData: ModuleData): Promise<ModuleData | null> {
  try {
    console.log('Creating module:', moduleData);

    const { data, error } = await supabase
      .from('modules')
      .insert([{
        course_id: moduleData.course_id,
        title: moduleData.title,
        slug: moduleData.slug,
        module_number: moduleData.module_number,
        is_locked: moduleData.is_locked || false,
        is_completed: false
        // Removed is_published field as it doesn't exist in the schema
      }])
      .select();

    if (error) {
      console.error('Error creating module:', error);
      toast({
        title: "Error",
        description: `Failed to create module: ${error.message}`,
        variant: "destructive",
      });
      return null;
    }

    toast({
      title: "Success",
      description: "Module created successfully",
    });
    return data[0];
  } catch (error: any) {
    console.error('Error in createModule:', error);
    toast({
      title: "Error",
      description: `An unexpected error occurred: ${error.message}`,
      variant: "destructive",
    });
    return null;
  }
}

/**
 * Updates an existing module
 */
export async function updateModule(moduleId: string, moduleData: Partial<ModuleData>): Promise<ModuleData | null> {
  try {
    console.log(`Updating module ${moduleId}:`, moduleData);

    const { data, error } = await supabase
      .from('modules')
      .update({
        title: moduleData.title,
        slug: moduleData.slug,
        module_number: moduleData.module_number,
        is_locked: moduleData.is_locked,
        updated_at: new Date().toISOString()
      })
      .eq('id', moduleId)
      .select();

    if (error) {
      console.error('Error updating module:', error);
      toast({
        title: "Error",
        description: `Failed to update module: ${error.message}`,
        variant: "destructive",
      });
      return null;
    }

    toast({
      title: "Success",
      description: "Module updated successfully",
    });
    return data[0];
  } catch (error: any) {
    console.error('Error in updateModule:', error);
    toast({
      title: "Error",
      description: `An unexpected error occurred: ${error.message}`,
      variant: "destructive",
    });
    return null;
  }
}

/**
 * Deletes a module
 */
export async function deleteModule(moduleId: string): Promise<boolean> {
  try {
    console.log(`Deleting module ${moduleId}`);

    // First, get the course_id for this module
    const { data: moduleData, error: fetchError } = await supabase
      .from('modules')
      .select('course_id')
      .eq('id', moduleId)
      .single();

    if (fetchError) {
      console.error('Error fetching module data:', fetchError);
      toast({
        title: "Error",
        description: `Failed to delete module: ${fetchError.message}`,
        variant: "destructive",
      });
      return false;
    }

    const courseId = moduleData.course_id;

    // Delete the module
    const { error } = await supabase
      .from('modules')
      .delete()
      .eq('id', moduleId);

    if (error) {
      console.error('Error deleting module:', error);
      toast({
        title: "Error",
        description: `Failed to delete module: ${error.message}`,
        variant: "destructive",
      });
      return false;
    }

    // Update the course's total_modules count
    try {
      console.log('Updating course total_modules count after deletion');

      // Get the current count of modules for this course
      const { data: moduleCount, error: countError } = await supabase
        .from('modules')
        .select('id')
        .eq('course_id', courseId);

      if (countError) {
        console.error('Error counting modules:', countError);
      } else {
        // Update the course with the new count
        const totalModules = moduleCount.length;
        console.log(`Updating course ${courseId} with ${totalModules} total modules`);

        const { error: updateError } = await supabase
          .from('courses')
          .update({ total_modules: totalModules })
          .eq('id', courseId);

        if (updateError) {
          console.error('Error updating course total_modules:', updateError);
        } else {
          console.log('Course total_modules updated successfully');
        }
      }
    } catch (updateError) {
      console.error('Error updating course total_modules:', updateError);
      // Continue anyway as this is not critical
    }

    toast({
      title: "Success",
      description: "Module deleted successfully",
    });
    return true;
  } catch (error: any) {
    console.error('Error in deleteModule:', error);
    toast({
      title: "Error",
      description: `An unexpected error occurred: ${error.message}`,
      variant: "destructive",
    });
    return false;
  }
}

// =====================
// LESSONS
// =====================

/**
 * Creates a new lesson
 */
export async function createLesson(lessonData: LessonData): Promise<LessonData | null> {
  try {
    console.log('Creating lesson:', lessonData);

    const { data, error } = await supabase
      .from('lessons')
      .insert([{
        module_id: lessonData.module_id,
        title: lessonData.title,
        slug: lessonData.slug,
        duration: lessonData.duration,
        type: lessonData.type,
        content: lessonData.content || null,
        requirement: lessonData.requirement || null,
        completed: false
      }])
      .select();

    if (error) {
      console.error('Error creating lesson:', error);
      toast({
        title: "Error",
        description: `Failed to create lesson: ${error.message}`,
        variant: "destructive",
      });
      return null;
    }

    toast({
      title: "Success",
      description: "Lesson created successfully",
    });
    return data[0];
  } catch (error: any) {
    console.error('Error in createLesson:', error);
    toast({
      title: "Error",
      description: `An unexpected error occurred: ${error.message}`,
      variant: "destructive",
    });
    return null;
  }
}

/**
 * Updates an existing lesson
 */
export async function updateLesson(lessonId: string, lessonData: Partial<LessonData>): Promise<LessonData | null> {
  try {
    console.log(`Updating lesson ${lessonId}:`, lessonData);

    const { data, error } = await supabase
      .from('lessons')
      .update({
        title: lessonData.title,
        slug: lessonData.slug,
        duration: lessonData.duration,
        type: lessonData.type,
        content: lessonData.content || null,
        requirement: lessonData.requirement || null,
        updated_at: new Date().toISOString()
      })
      .eq('id', lessonId)
      .select();

    if (error) {
      console.error('Error updating lesson:', error);
      toast({
        title: "Error",
        description: `Failed to update lesson: ${error.message}`,
        variant: "destructive",
      });
      return null;
    }

    toast({
      title: "Success",
      description: "Lesson updated successfully",
    });
    return data[0];
  } catch (error: any) {
    console.error('Error in updateLesson:', error);
    toast({
      title: "Error",
      description: `An unexpected error occurred: ${error.message}`,
      variant: "destructive",
    });
    return null;
  }
}

/**
 * Deletes a lesson
 */
export async function deleteLesson(lessonId: string): Promise<boolean> {
  try {
    console.log(`Deleting lesson ${lessonId}`);

    const { error } = await supabase
      .from('lessons')
      .delete()
      .eq('id', lessonId);

    if (error) {
      console.error('Error deleting lesson:', error);
      toast({
        title: "Error",
        description: `Failed to delete lesson: ${error.message}`,
        variant: "destructive",
      });
      return false;
    }

    toast({
      title: "Success",
      description: "Lesson deleted successfully",
    });
    return true;
  } catch (error: any) {
    console.error('Error in deleteLesson:', error);
    toast({
      title: "Error",
      description: `An unexpected error occurred: ${error.message}`,
      variant: "destructive",
    });
    return false;
  }
}

// =====================
// COURSE COMPLETION
// =====================

/**
 * Directly marks a course as completed for a user
 * This function uses a more permissive approach to bypass RLS issues
 */
export async function directMarkCourseAsCompleted(courseId: string, userId: string): Promise<boolean> {
  try {
    console.log(`[directMarkCourseAsCompleted] Marking course ${courseId} as completed for user ${userId}`);

    // First check if the enrollment exists
    const { data: enrollment, error: checkError } = await supabase
      .from('user_course_enrollment')
      .select('*')
      .eq('user_id', userId)
      .eq('course_id', courseId)
      .maybeSingle();

    console.log('[directMarkCourseAsCompleted] Enrollment check result:', enrollment);

    if (checkError) {
      console.error('[directMarkCourseAsCompleted] Error checking enrollment:', checkError);
      toast.error('Failed to check enrollment status');
      return false;
    }

    const now = new Date().toISOString();

    if (!enrollment) {
      // Create new enrollment with completed status
      console.log('[directMarkCourseAsCompleted] Creating new enrollment');

      const { error: insertError } = await supabase
        .from('user_course_enrollment')
        .insert([{
          user_id: userId,
          course_id: courseId,
          status: 'completed',
          enrolled_at: now,
          completed_at: now,
          updated_at: now
        }]);

      if (insertError) {
        console.error('[directMarkCourseAsCompleted] Error creating enrollment:', insertError);
        toast.error('Failed to create course completion record');
        return false;
      }
    } else {
      // Update existing enrollment
      console.log('[directMarkCourseAsCompleted] Updating existing enrollment');

      const { error: updateError } = await supabase
        .from('user_course_enrollment')
        .update({
          status: 'completed',
          completed_at: now,
          updated_at: now
        })
        .eq('id', enrollment.id);

      if (updateError) {
        console.error('[directMarkCourseAsCompleted] Error updating enrollment:', updateError);
        toast.error('Failed to mark course as completed');
        return false;
      }
    }

    // Update course progress
    console.log('[directMarkCourseAsCompleted] Updating course progress');

    const { error: progressError } = await supabase
      .from('user_course_progress')
      .upsert([{
        user_id: userId,
        course_id: courseId,
        completed_modules: 100,
        updated_at: now
      }], { onConflict: 'user_id,course_id' });

    if (progressError) {
      console.error('[directMarkCourseAsCompleted] Error updating progress:', progressError);
      // Continue anyway as this is not critical
    }

    console.log('[directMarkCourseAsCompleted] Course successfully marked as completed');
    toast.success('Course successfully marked as completed!');
    return true;
  } catch (error) {
    console.error('[directMarkCourseAsCompleted] Unexpected error:', error);
    toast.error('An unexpected error occurred. Please try again.');
    return false;
  }
}
