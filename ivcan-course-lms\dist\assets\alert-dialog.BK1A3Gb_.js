var w=Object.defineProperty,R=Object.defineProperties;var v=Object.getOwnPropertyDescriptors;var m=Object.getOwnPropertySymbols;var f=Object.prototype.hasOwnProperty,p=Object.prototype.propertyIsEnumerable;var c=(a,e,t)=>e in a?w(a,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[e]=t,i=(a,e)=>{for(var t in e||(e={}))f.call(e,t)&&c(a,t,e[t]);if(m)for(var t of m(e))p.call(e,t)&&c(a,t,e[t]);return a},x=(a,e)=>R(a,v(e));var d=(a,e)=>{var t={};for(var s in a)f.call(a,s)&&e.indexOf(s)<0&&(t[s]=a[s]);if(a!=null&&m)for(var s of m(a))e.indexOf(s)<0&&p.call(a,s)&&(t[s]=a[s]);return t};import{r as n,j as l,bh as g,bi as N,bj as y,bk as b,bl as u,bm as A,bn as h,bo as z,bp as C}from"./vendor-react.BcAa1DKr.js";import{c as r,y as D}from"./index.BLDhDn0D.js";const I=h,J=C,T=z,j=n.forwardRef((s,t)=>{var o=s,{className:a}=o,e=d(o,["className"]);return l.jsx(g,x(i({className:r("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a)},e),{ref:t}))});j.displayName=g.displayName;const k=n.forwardRef((s,t)=>{var o=s,{className:a}=o,e=d(o,["className"]);return l.jsxs(T,{children:[l.jsx(j,{}),l.jsx(N,i({ref:t,className:r("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a)},e))]})});k.displayName=N.displayName;const E=t=>{var s=t,{className:a}=s,e=d(s,["className"]);return l.jsx("div",i({className:r("flex flex-col space-y-2 text-center sm:text-left",a)},e))};E.displayName="AlertDialogHeader";const F=t=>{var s=t,{className:a}=s,e=d(s,["className"]);return l.jsx("div",i({className:r("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a)},e))};F.displayName="AlertDialogFooter";const H=n.forwardRef((s,t)=>{var o=s,{className:a}=o,e=d(o,["className"]);return l.jsx(y,i({ref:t,className:r("text-lg font-semibold",a)},e))});H.displayName=y.displayName;const O=n.forwardRef((s,t)=>{var o=s,{className:a}=o,e=d(o,["className"]);return l.jsx(b,i({ref:t,className:r("text-sm text-muted-foreground",a)},e))});O.displayName=b.displayName;const P=n.forwardRef((s,t)=>{var o=s,{className:a}=o,e=d(o,["className"]);return l.jsx(u,i({ref:t,className:r(D(),a)},e))});P.displayName=u.displayName;const V=n.forwardRef((s,t)=>{var o=s,{className:a}=o,e=d(o,["className"]);return l.jsx(A,i({ref:t,className:r(D({variant:"outline"}),"mt-2 sm:mt-0",a)},e))});V.displayName=A.displayName;export{I as A,J as a,k as b,E as c,H as d,O as e,F as f,V as g,P as h};
