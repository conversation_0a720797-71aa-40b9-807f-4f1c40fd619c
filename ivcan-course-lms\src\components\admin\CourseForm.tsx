import React, { useState } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Loader2 } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { uploadToStorage } from '@/lib/storage-utils';
import { useQueryClient } from '@tanstack/react-query';
import { ResponsiveImage } from '@/components/ui/responsive-image';

// Define the form schema
const courseFormSchema = z.object({
  title: z.string().min(3, { message: 'Title must be at least 3 characters' }),
  description: z.string().min(10, { message: 'Description must be at least 10 characters' }),
  instructor: z.string().optional(),
  startDate: z.date().optional(),
  image: z.string().optional(),
});

type CourseFormValues = z.infer<typeof courseFormSchema>;

interface CourseFormProps {
  initialData?: CourseFormValues & { id?: string };
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function CourseForm({ initialData, onSuccess, onCancel }: CourseFormProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [imageUrl, setImageUrl] = useState(initialData?.image || '');
  const [imageFile, setImageFile] = useState<File | null>(null);

  // Initialize form with default values
  const form = useForm<CourseFormValues>({
    resolver: zodResolver(courseFormSchema),
    defaultValues: initialData || {
      title: '',
      description: '',
      instructor: '',
      startDate: new Date(),
      image: '',
    },
  });

  // Handle image upload
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageFile(file);
      // Create a preview URL
      const previewUrl = URL.createObjectURL(file);
      setImageUrl(previewUrl);
    }
  };

  // Handle form submission
  const onSubmit = async (data: CourseFormValues) => {
    setIsSubmitting(true);

    try {
      let uploadedImageUrl = imageUrl;

      // Upload image if a new one was selected
      if (imageFile) {
        try {
          const fileName = `course-${Date.now()}-${imageFile.name}`;
          // Use our utility function to handle bucket creation and upload
          uploadedImageUrl = await uploadToStorage('course-images', fileName, imageFile);
        } catch (error: any) {
          throw new Error(`Image upload failed: ${error.message}`);
        }
      }

      // Prepare course data
      const courseData = {
        title: data.title,
        description: data.description,
        instructor: data.instructor || null,
        start_date: data.startDate ? data.startDate.toISOString() : null,
        image: uploadedImageUrl || null,
      };

      // Update or create course
      if (initialData?.id) {
        // Update existing course
        const { error } = await supabase
          .from('courses')
          .update(courseData)
          .eq('id', initialData.id);

        if (error) throw error;

        toast({
          title: 'Course updated',
          description: 'The course has been successfully updated.',
        });
      } else {
        // Create new course
        const { error } = await supabase
          .from('courses')
          .insert([courseData]);

        if (error) throw error;

        toast({
          title: 'Course created',
          description: 'The course has been successfully created.',
        });
      }

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['courses'] });

      // Call success callback
      if (onSuccess) onSuccess();

    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'An error occurred while saving the course.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
      <div className="space-y-4">
        {/* Course Image */}
        <div className="space-y-2">
          <Label htmlFor="image">Course Image</Label>
          <div className="flex flex-col items-center gap-4">
            {imageUrl ? (
              <div className="relative w-full max-w-md aspect-video rounded-lg overflow-hidden border border-border">
                <ResponsiveImage
                  src={imageUrl}
                  alt="Course preview"
                  aspectRatio="16/9"
                  objectFit="cover"
                />
              </div>
            ) : (
              <div className="w-full max-w-md aspect-video rounded-lg bg-muted flex items-center justify-center border border-dashed border-border">
                <p className="text-muted-foreground text-sm">No image selected</p>
              </div>
            )}
            <Input
              id="image"
              type="file"
              accept="image/*"
              onChange={handleImageChange}
              className="max-w-md"
            />
            <p className="text-xs text-muted-foreground">
              Recommended size: 1280x720px (16:9 ratio)
            </p>
          </div>
        </div>

        {/* Course Title */}
        <div className="space-y-2">
          <Label htmlFor="title">
            Course Title <span className="text-destructive">*</span>
          </Label>
          <Input
            id="title"
            {...form.register('title')}
            placeholder="Enter course title"
          />
          {form.formState.errors.title && (
            <p className="text-sm text-destructive">{form.formState.errors.title.message}</p>
          )}
        </div>

        {/* Course Description */}
        <div className="space-y-2">
          <Label htmlFor="description">
            Description <span className="text-destructive">*</span>
          </Label>
          <Textarea
            id="description"
            {...form.register('description')}
            placeholder="Enter course description"
            rows={4}
          />
          {form.formState.errors.description && (
            <p className="text-sm text-destructive">{form.formState.errors.description.message}</p>
          )}
        </div>

        {/* Instructor */}
        <div className="space-y-2">
          <Label htmlFor="instructor">Instructor</Label>
          <Input
            id="instructor"
            {...form.register('instructor')}
            placeholder="Enter instructor name"
          />
        </div>

        {/* Start Date */}
        <div className="space-y-2">
          <Label htmlFor="startDate">Start Date</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !form.getValues('startDate') && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {form.getValues('startDate') ? (
                  format(form.getValues('startDate'), "PPP")
                ) : (
                  <span>Pick a date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={form.getValues('startDate')}
                onSelect={(date) => form.setValue('startDate', date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end gap-2 pt-4 border-t border-border">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            initialData?.id ? 'Update Course' : 'Create Course'
          )}
        </Button>
      </div>
    </form>
  );
}
