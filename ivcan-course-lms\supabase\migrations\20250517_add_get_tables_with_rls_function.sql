-- Add a function to get tables with RLS enabled
CREATE OR REPLACE FUNCTION get_tables_with_rls()
RETURNS TABLE (
  table_name TEXT,
  rls_enabled BOOLEAN
) 
LANGUAGE SQL
SECURITY DEFINER
AS $$
  SELECT 
    tables.table_name::TEXT,
    tables.rls_enabled
  FROM 
    pg_catalog.pg_tables tables
  JOIN 
    pg_catalog.pg_class cls ON cls.relname = tables.tablename
  WHERE 
    tables.schemaname = 'public'
  ORDER BY 
    tables.table_name;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_tables_with_rls() TO authenticated;
GRANT EXECUTE ON FUNCTION get_tables_with_rls() TO service_role; 