-- Create storage buckets for image handling
-- This migration ensures all required storage buckets exist with proper policies

-- Enable RLS for storage.objects if not already enabled
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Create storage buckets if they don't exist
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  ('course-images', 'course-images', true, 52428800, ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']),
  ('module-images', 'module-images', true, 52428800, ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif'])
ON CONFLICT (id) DO UPDATE SET
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Drop existing policies if they exist to avoid conflicts
DROP POLICY IF EXISTS "Allow public access to course-images" ON storage.objects;
DROP POLICY IF EXISTS "Allow public access to module-images" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated uploads to course-images" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated uploads to module-images" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated updates to course-images" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated updates to module-images" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated deletes to course-images" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated deletes to module-images" ON storage.objects;

-- Create policies for public read access
CREATE POLICY "Allow public access to course-images"
ON storage.objects FOR SELECT
USING (bucket_id = 'course-images');

CREATE POLICY "Allow public access to module-images"
ON storage.objects FOR SELECT
USING (bucket_id = 'module-images');

-- Create policies for authenticated uploads
CREATE POLICY "Allow authenticated uploads to course-images"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'course-images' AND
  auth.role() = 'authenticated'
);

CREATE POLICY "Allow authenticated uploads to module-images"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'module-images' AND
  auth.role() = 'authenticated'
);

-- Create policies for authenticated updates
CREATE POLICY "Allow authenticated updates to course-images"
ON storage.objects FOR UPDATE
USING (
  bucket_id = 'course-images' AND
  auth.role() = 'authenticated'
)
WITH CHECK (
  bucket_id = 'course-images' AND
  auth.role() = 'authenticated'
);

CREATE POLICY "Allow authenticated updates to module-images"
ON storage.objects FOR UPDATE
USING (
  bucket_id = 'module-images' AND
  auth.role() = 'authenticated'
)
WITH CHECK (
  bucket_id = 'module-images' AND
  auth.role() = 'authenticated'
);

-- Create policies for authenticated deletes
CREATE POLICY "Allow authenticated deletes to course-images"
ON storage.objects FOR DELETE
USING (
  bucket_id = 'course-images' AND
  auth.role() = 'authenticated'
);

CREATE POLICY "Allow authenticated deletes to module-images"
ON storage.objects FOR DELETE
USING (
  bucket_id = 'module-images' AND
  auth.role() = 'authenticated'
);

-- Add comment for documentation
COMMENT ON TABLE storage.buckets IS 'Storage buckets for course and module images with proper RLS policies';
