import { supabase } from '@/integrations/supabase/client';

/**
 * Directly uploads a file to Supabase storage without checking if the bucket exists
 * This is a fallback method when the regular upload process fails
 * 
 * @param bucketName The name of the bucket to upload to
 * @param filePath The path within the bucket where the file should be stored
 * @param file The file to upload
 * @returns The public URL of the uploaded file
 */
export async function directUploadToStorage(
  bucketName: string,
  filePath: string,
  file: File
): Promise<string> {
  console.log(`[DIRECT UPLOAD] Attempting direct upload to bucket '${bucketName}', file: '${filePath}'`);
  
  try {
    // Try to upload directly without checking if the bucket exists
    const { data, error } = await supabase.storage
      .from(bucketName)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: true // Overwrite if file exists
      });
    
    if (error) {
      console.error(`[DIRECT UPLOAD] Error uploading file:`, error);
      throw error;
    }
    
    console.log(`[DIRECT UPLOAD] File uploaded successfully:`, data);
    
    // Get the public URL
    const { data: { publicUrl } } = supabase.storage
      .from(bucketName)
      .getPublicUrl(filePath);
    
    console.log(`[DIRECT UPLOAD] Generated public URL: ${publicUrl}`);
    return publicUrl;
  } catch (error) {
    console.error('[DIRECT UPLOAD] Failed:', error);
    throw error;
  }
}
