/* iOS Theme Styles with Red Color Scheme */

/* iOS-specific variables */
:root {
  /* iOS color palette */
  --ios-blue: 212 100% 50%;      /* #007AFF */
  --ios-red: 0 70% 50%;          /* #E63946 - Primary red */
  --ios-indigo: 231 100% 60%;    /* #5856D6 */
  --ios-orange: 14 100% 52%;     /* #FF9500 */
  --ios-pink: 349 100% 58%;      /* #FF2D55 */
  --ios-purple: 292 100% 50%;    /* #AF52DE */
  /* Legacy green color removed */
  --ios-teal: 180 100% 39%;      /* #5AC8FA */
  --ios-yellow: 45 100% 50%;     /* #FFCC00 */

  /* iOS UI properties */
  --ios-radius-sm: 8px;
  --ios-radius-md: 12px;
  --ios-radius-lg: 16px;
  --ios-radius-xl: 22px;
  --ios-radius-full: 9999px;

  /* iOS shadows */
  --ios-shadow-sm: 0 2px 6px rgba(0, 0, 0, 0.1);
  --ios-shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
  --ios-shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.06);

  /* Modern fonts */
  --ios-font-family: 'Poppins', system-ui, sans-serif;

  /* Override existing variables with iOS values */
  --radius: 12px;
  --primary: var(--ios-red);

  /* iOS-specific transitions */
  --ios-transition-fast: 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ios-transition-medium: 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ios-transition-slow: 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  /* iOS-style UI Variables */
  --ios-background: #F2F2F7;
  --ios-text: #000000;
  --ios-secondary-text: #6C6C70;
  --ios-border: #C5C5C7;
  --ios-primary: #007AFF;
  --ios-secondary: #5856D6;
  --ios-success: #34C759;
  --ios-warning: #FF9500;
  --ios-danger: #FF3B30;
  --ios-radius: 10px;
}

/* iOS-specific dark mode overrides */
.dark {
  --ios-shadow-sm: 0 2px 6px rgba(0, 0, 0, 0.2);
  --ios-shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --ios-shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.12);
}

/* iOS-specific theme */
.ios {
  /* iOS-style tap highlights */
  -webkit-tap-highlight-color: transparent;

  /* iOS-style momentum scrolling */
  -webkit-overflow-scrolling: touch;

  /* iOS-style buttons */
  --ios-button-height: 44px;
  --ios-button-padding: 16px;
  --ios-button-radius: 10px;
  --ios-button-active-scale: 0.97;

  /* iOS-style inputs */
  --ios-input-height: 44px;
  --ios-input-radius: 10px;

  /* iOS-style safe areas */
  --safe-area-inset-top: env(safe-area-inset-top);
  --safe-area-inset-bottom: env(safe-area-inset-bottom);
}

/* iOS Typography */
body {
  font-family: var(--ios-font-family);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--ios-font-family);
  font-weight: 600;
}

/* iOS-style buttons - Simplified for unified button system */

/* iOS-style cards */

/* iOS-style inputs */

/* iOS-style switches */

/* iOS-style navigation */

.dark .ios-nav {
  background-color: rgba(30, 30, 30, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* iOS-style bottom tab bar */

.dark .ios-tab-bar {
  background-color: rgba(30, 30, 30, 0.8);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .ios-tab-item {
  color: rgba(255, 255, 255, 0.6);
}

.dark .ios-tab-item.active {
  color: hsl(var(--ios-red));
}

/* iOS-style badges */

/* iOS-style lists */

.dark .ios-list {
  background-color: rgba(30, 30, 30, 0.8);
}

.dark .ios-list-item {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* iOS-style segmented control */

/* iOS-style alerts and modals */

.dark .ios-alert {
  background-color: rgba(30, 30, 30, 0.8);
}

.dark .ios-alert-message {
  color: rgba(255, 255, 255, 0.6);
}

.dark .ios-alert-actions {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .ios-alert-button:not(:last-child) {
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .ios-alert-button:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* iOS-style toast notifications */

.dark .ios-toast {
  background-color: rgba(30, 30, 30, 0.8);
}

/* iOS-style loading indicators */

@keyframes ios-spin {
  to {
    transform: rotate(360deg);
  }
}

/* iOS-style pull-to-refresh */

/* iOS-style scrollbars */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

.dark ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* iOS-style bottom sheet */

/* Dark mode adjustments */
