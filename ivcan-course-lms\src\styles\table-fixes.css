/* Table Styling Fixes for Markdown Editors and Previews */

/* Reset conflicting table styles */
table {
  border-collapse: collapse !important;
  border-spacing: 0 !important;
  width: 100% !important;
  margin: 0.75rem 0 !important;
  font-size: 0.95rem !important;
  line-height: 1.6 !important;
}

/* TipTap Editor Table Styles */
.ProseMirror table {
  border-collapse: collapse !important;
  border: 1px solid hsl(var(--border)) !important;
  width: 100% !important;
  margin: 0.75rem 0 !important;
  border-radius: 8px !important;
  overflow: hidden !important;
}

.ProseMirror table th,
.ProseMirror table td {
  border: 1px solid hsl(var(--border)) !important;
  padding: 0.75rem 1rem !important;
  text-align: left !important;
  vertical-align: top !important;
}

.ProseMirror table th {
  background-color: hsl(var(--muted)) !important;
  font-weight: 600 !important;
  color: hsl(var(--foreground)) !important;
}

.ProseMirror table tr {
  border-bottom: 1px solid hsl(var(--border)) !important;
}

.ProseMirror table tr:nth-child(even) {
  background-color: hsl(var(--muted) / 0.3) !important;
}

.ProseMirror table tr:hover {
  background-color: hsl(var(--muted) / 0.5) !important;
  transition: background-color 0.2s ease !important;
}

/* Markdown Preview Table Styles */
.markdown-preview table,
.github-markdown-preview table,
.prose table {
  border-collapse: collapse !important;
  border: 1px solid hsl(var(--border)) !important;
  width: 100% !important;
  margin: 0.75rem 0 !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
}

.markdown-preview table th,
.markdown-preview table td,
.github-markdown-preview table th,
.github-markdown-preview table td,
.prose table th,
.prose table td {
  border: 1px solid hsl(var(--border)) !important;
  padding: 0.75rem 1rem !important;
  text-align: left !important;
  vertical-align: top !important;
}

.markdown-preview table th,
.github-markdown-preview table th,
.prose table th {
  background-color: hsl(var(--muted)) !important;
  font-weight: 600 !important;
  color: hsl(var(--foreground)) !important;
}

.markdown-preview table tr,
.github-markdown-preview table tr,
.prose table tr {
  border-bottom: 1px solid hsl(var(--border)) !important;
}

.markdown-preview table tr:nth-child(even),
.github-markdown-preview table tr:nth-child(even),
.prose table tr:nth-child(even) {
  background-color: hsl(var(--muted) / 0.3) !important;
}

.markdown-preview table tr:hover,
.github-markdown-preview table tr:hover,
.prose table tr:hover {
  background-color: hsl(var(--muted) / 0.5) !important;
  transition: background-color 0.2s ease !important;
}

/* Enhanced Markdown Preview Tables */
.professional-table-wrapper {
  overflow-x: auto !important;
  border-radius: 8px !important;
  border: 1px solid hsl(var(--border)) !important;
  margin: 0.75rem 0 !important;
}

.professional-table {
  border-collapse: collapse !important;
  width: 100% !important;
  margin: 0 !important;
  border: none !important;
}

.professional-table .table-header-cell {
  background-color: hsl(var(--muted)) !important;
  font-weight: 600 !important;
  padding: 0.75rem 1rem !important;
  border: 1px solid hsl(var(--border)) !important;
  color: hsl(var(--foreground)) !important;
}

.professional-table .table-cell {
  padding: 0.75rem 1rem !important;
  border: 1px solid hsl(var(--border)) !important;
  color: hsl(var(--foreground)) !important;
}

.professional-table .table-row:nth-child(even) {
  background-color: hsl(var(--muted) / 0.3) !important;
}

.professional-table .table-row:hover {
  background-color: hsl(var(--muted) / 0.5) !important;
  transition: background-color 0.2s ease !important;
}

/* Responsive table handling */
@media (max-width: 768px) {
  table {
    font-size: 0.875rem !important;
  }
  
  .ProseMirror table th,
  .ProseMirror table td,
  .markdown-preview table th,
  .markdown-preview table td,
  .github-markdown-preview table th,
  .github-markdown-preview table td,
  .prose table th,
  .prose table td,
  .professional-table .table-header-cell,
  .professional-table .table-cell {
    padding: 0.5rem 0.75rem !important;
  }
}

/* Dark mode adjustments */
.dark table {
  border-color: hsl(var(--border)) !important;
}

.dark .ProseMirror table th,
.dark .markdown-preview table th,
.dark .github-markdown-preview table th,
.dark .prose table th,
.dark .professional-table .table-header-cell {
  background-color: hsl(var(--muted)) !important;
  color: hsl(var(--foreground)) !important;
}

.dark .ProseMirror table tr:nth-child(even),
.dark .markdown-preview table tr:nth-child(even),
.dark .github-markdown-preview table tr:nth-child(even),
.dark .prose table tr:nth-child(even),
.dark .professional-table .table-row:nth-child(even) {
  background-color: hsl(var(--muted) / 0.2) !important;
}

.dark .ProseMirror table tr:hover,
.dark .markdown-preview table tr:hover,
.dark .github-markdown-preview table tr:hover,
.dark .prose table tr:hover,
.dark .professional-table .table-row:hover {
  background-color: hsl(var(--muted) / 0.4) !important;
}

/* Ensure table visibility */
table,
.ProseMirror table,
.markdown-preview table,
.github-markdown-preview table,
.prose table,
.professional-table {
  display: table !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Fix table cell content alignment */
table th,
table td,
.ProseMirror table th,
.ProseMirror table td,
.markdown-preview table th,
.markdown-preview table td,
.github-markdown-preview table th,
.github-markdown-preview table td,
.prose table th,
.prose table td,
.professional-table .table-header-cell,
.professional-table .table-cell {
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  hyphens: auto !important;
}

/* Table selection styles for TipTap */
.ProseMirror .selectedCell {
  background-color: hsl(var(--primary) / 0.2) !important;
  position: relative !important;
}

.ProseMirror .selectedCell::after {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-color: hsl(var(--primary) / 0.1) !important;
  pointer-events: none !important;
}

/* Table resize handles */
.ProseMirror .tableWrapper {
  overflow-x: auto !important;
  margin: 1rem 0 !important;
}

.ProseMirror .resize-cursor {
  cursor: col-resize !important;
}

/* Code block consistency fixes */
.ProseMirror pre,
.markdown-preview pre,
.github-markdown-preview pre,
.prose pre {
  background-color: hsl(var(--muted) / 0.7) !important;
  border: 1px solid hsl(var(--border)) !important;
  border-radius: 8px !important;
  padding: 1rem !important;
  overflow-x: auto !important;
  margin: 1rem 0 !important;
}

.ProseMirror pre code,
.markdown-preview pre code,
.github-markdown-preview pre code,
.prose pre code {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  font-family: ui-monospace, SFMono-Regular, 'SF Mono', Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace !important;
  font-size: 0.875rem !important;
  line-height: 1.6 !important;
}

/* Inline code consistency */
.ProseMirror code,
.markdown-preview code,
.github-markdown-preview code,
.prose code {
  background-color: hsl(var(--muted) / 0.7) !important;
  border: 1px solid hsl(var(--border)) !important;
  border-radius: 4px !important;
  padding: 0.125rem 0.25rem !important;
  font-family: ui-monospace, SFMono-Regular, 'SF Mono', Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace !important;
  font-size: 0.875em !important;
}
