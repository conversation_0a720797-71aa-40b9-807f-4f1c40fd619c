/**
 * Verify Navigation Fix Script
 * 
 * This script verifies that the navigation fix has been applied correctly
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Colors for console output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

/**
 * Check if lesson_number column exists and is populated
 */
async function checkLessonNumbers() {
  console.log(`\n${colors.blue}🔍 Checking lesson_number column...${colors.reset}`);
  
  try {
    const { data: lessons, error } = await supabase
      .from('lessons')
      .select('id, slug, title, lesson_number, module_id')
      .limit(5);
    
    if (error) {
      console.error(`${colors.red}❌ Error fetching lessons:${colors.reset}`, error.message);
      return false;
    }
    
    if (!lessons || lessons.length === 0) {
      console.log(`${colors.yellow}⚠️ No lessons found in database${colors.reset}`);
      return false;
    }
    
    // Check if lesson_number exists and is populated
    const hasLessonNumbers = lessons.every(lesson => 
      lesson.lesson_number !== null && lesson.lesson_number !== undefined
    );
    
    if (hasLessonNumbers) {
      console.log(`${colors.green}✅ lesson_number column exists and is populated${colors.reset}`);
      console.log(`${colors.cyan}Sample lessons:${colors.reset}`);
      lessons.forEach(lesson => {
        console.log(`  - ${lesson.title} (lesson_number: ${lesson.lesson_number})`);
      });
      return true;
    } else {
      console.log(`${colors.red}❌ Some lessons are missing lesson_number values${colors.reset}`);
      lessons.forEach(lesson => {
        if (lesson.lesson_number === null || lesson.lesson_number === undefined) {
          console.log(`  - ${lesson.title} (lesson_number: ${lesson.lesson_number})`);
        }
      });
      return false;
    }
  } catch (error) {
    console.error(`${colors.red}❌ Unexpected error:${colors.reset}`, error.message);
    return false;
  }
}

/**
 * Test the navigation functions
 */
async function testNavigationFunctions() {
  console.log(`\n${colors.blue}🔍 Testing navigation functions...${colors.reset}`);
  
  try {
    // Get a sample lesson
    const { data: lessons, error: lessonError } = await supabase
      .from('lessons')
      .select('id, slug, title, lesson_number')
      .limit(1);
    
    if (lessonError || !lessons || lessons.length === 0) {
      console.log(`${colors.yellow}⚠️ No lessons available for testing${colors.reset}`);
      return false;
    }
    
    const testLesson = lessons[0];
    console.log(`${colors.cyan}Testing with lesson: ${testLesson.title} (${testLesson.slug})${colors.reset}`);
    
    // Test the enhanced navigation function
    const { data: navResult, error: navError } = await supabase
      .rpc('get_lesson_navigation_enhanced', { p_current_lesson_slug: testLesson.slug });
    
    if (navError) {
      console.error(`${colors.red}❌ Navigation function error:${colors.reset}`, navError.message);
      return false;
    }
    
    if (navResult) {
      console.log(`${colors.green}✅ Navigation function working correctly${colors.reset}`);
      console.log(`${colors.cyan}Navigation result:${colors.reset}`, JSON.stringify(navResult, null, 2));
      return true;
    } else {
      console.log(`${colors.red}❌ Navigation function returned null${colors.reset}`);
      return false;
    }
  } catch (error) {
    console.error(`${colors.red}❌ Unexpected error testing navigation:${colors.reset}`, error.message);
    return false;
  }
}

/**
 * Test basic course data loading
 */
async function testCourseDataLoading() {
  console.log(`\n${colors.blue}🔍 Testing course data loading...${colors.reset}`);
  
  try {
    // Test fetching courses
    const { data: courses, error: courseError } = await supabase
      .from('courses')
      .select('id, title, slug')
      .limit(3);
    
    if (courseError) {
      console.error(`${colors.red}❌ Error fetching courses:${colors.reset}`, courseError.message);
      return false;
    }
    
    if (!courses || courses.length === 0) {
      console.log(`${colors.yellow}⚠️ No courses found${colors.reset}`);
      return false;
    }
    
    console.log(`${colors.green}✅ Courses loading correctly${colors.reset}`);
    console.log(`${colors.cyan}Found ${courses.length} courses:${colors.reset}`);
    courses.forEach(course => {
      console.log(`  - ${course.title} (${course.slug})`);
    });
    
    // Test fetching modules for first course
    const { data: modules, error: moduleError } = await supabase
      .from('modules')
      .select('id, title, module_number')
      .eq('course_id', courses[0].id)
      .order('module_number');
    
    if (moduleError) {
      console.error(`${colors.red}❌ Error fetching modules:${colors.reset}`, moduleError.message);
      return false;
    }
    
    console.log(`${colors.green}✅ Modules loading correctly${colors.reset}`);
    console.log(`${colors.cyan}Found ${modules?.length || 0} modules for course "${courses[0].title}":${colors.reset}`);
    modules?.forEach(module => {
      console.log(`  - Module ${module.module_number}: ${module.title}`);
    });
    
    return true;
  } catch (error) {
    console.error(`${colors.red}❌ Unexpected error testing course data:${colors.reset}`, error.message);
    return false;
  }
}

/**
 * Main verification function
 */
async function main() {
  console.log(`${colors.blue}🚀 Starting Navigation Fix Verification${colors.reset}`);
  
  try {
    const checks = [
      await checkLessonNumbers(),
      await testNavigationFunctions(),
      await testCourseDataLoading()
    ];
    
    const allPassed = checks.every(check => check === true);
    
    if (allPassed) {
      console.log(`\n${colors.green}🎉 All checks passed! Navigation fix has been applied successfully.${colors.reset}`);
      console.log(`\n${colors.cyan}Next steps:${colors.reset}`);
      console.log(`1. Clear your browser cache`);
      console.log(`2. Refresh the application`);
      console.log(`3. Test the navigation by clicking the "Next Lesson" button`);
      console.log(`4. Check browser console for detailed navigation logs`);
    } else {
      console.log(`\n${colors.red}❌ Some checks failed. Please ensure all database migrations have been applied.${colors.reset}`);
      console.log(`\n${colors.cyan}Troubleshooting:${colors.reset}`);
      console.log(`1. Make sure you've run both SQL migrations in Supabase dashboard`);
      console.log(`2. Check for any SQL errors in the Supabase dashboard`);
      console.log(`3. Verify your database connection`);
    }
    
  } catch (error) {
    console.error(`${colors.red}❌ Verification script failed:${colors.reset}`, error);
    process.exit(1);
  }
}

// Run the verification
main();
