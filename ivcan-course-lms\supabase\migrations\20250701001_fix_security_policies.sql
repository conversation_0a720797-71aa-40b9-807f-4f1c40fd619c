-- Comprehensive security policy fix for all tables
-- This migration consolidates and improves RLS policies across all tables

-- Fix and consolidate user_roles security policies
DROP POLICY IF EXISTS "Anyone can view user_roles" ON public.user_roles;
DROP POLICY IF EXISTS "Teachers can insert user_roles" ON public.user_roles;
DROP POLICY IF EXISTS "Teachers can update user_roles" ON public.user_roles;
DROP POLICY IF EXISTS "Teachers can delete user_roles" ON public.user_roles;
DROP POLICY IF EXISTS "Service role bypass" ON public.user_roles;

-- Create improved user_roles policies with proper security checks
CREATE POLICY "Users can view user_roles" 
ON public.user_roles FOR SELECT 
USING (
  -- Only authenticated users can view roles
  auth.role() = 'authenticated' AND (
    -- Users can view their own role
    auth.uid() = user_id OR
    -- Teachers can view all roles
    EXISTS (
      SELECT 1 FROM public.user_roles
      WHERE user_id = auth.uid() AND role = 'teacher'
    )
  )
);

CREATE POLICY "Only teachers can modify user_roles" 
ON public.user_roles FOR INSERT 
WITH CHECK (
  -- Only allow teachers to create roles
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Only teachers can update user_roles" 
ON public.user_roles FOR UPDATE 
USING (
  -- Only allow teachers to update roles
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Only teachers can delete user_roles" 
ON public.user_roles FOR DELETE 
USING (
  -- Only allow teachers to delete roles
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- Service role bypass policy
CREATE POLICY "Service role bypass for user_roles" 
ON public.user_roles 
USING (auth.jwt() ->> 'role' = 'service_role');

-- Fix user preferences security
DROP POLICY IF EXISTS "Users can view their own preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Users can update their own preferences" ON public.user_preferences;

CREATE POLICY "Users can view their own preferences" 
ON public.user_preferences FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own preferences" 
ON public.user_preferences FOR UPDATE 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own preferences" 
ON public.user_preferences FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Fix course completion security
DROP POLICY IF EXISTS "Users can view their own completions" ON public.user_course_enrollment;
DROP POLICY IF EXISTS "Users can update their own completions" ON public.user_course_enrollment;

CREATE POLICY "Users can view their own course enrollments" 
ON public.user_course_enrollment FOR SELECT 
USING (
  -- Users can view their own enrollments
  auth.uid() = user_id OR
  -- Teachers can view all enrollments
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Users can manage their own course enrollments" 
ON public.user_course_enrollment FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own course enrollments" 
ON public.user_course_enrollment FOR UPDATE 
USING (
  -- Users can update their own enrollments
  auth.uid() = user_id OR
  -- Teachers can update all enrollments
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- Fix lesson progress security
DROP POLICY IF EXISTS "Users can view their own lesson progress" ON public.user_lesson_progress;
DROP POLICY IF EXISTS "Users can insert their own lesson progress" ON public.user_lesson_progress;
DROP POLICY IF EXISTS "Users can update their own lesson progress" ON public.user_lesson_progress;

CREATE POLICY "Users can view their own lesson progress" 
ON public.user_lesson_progress FOR SELECT 
USING (
  -- Users can view their own progress
  auth.uid() = user_id OR
  -- Teachers can view all progress
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Users can insert their own lesson progress" 
ON public.user_lesson_progress FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own lesson progress" 
ON public.user_lesson_progress FOR UPDATE 
USING (auth.uid() = user_id);

-- Fix module progress security
DROP POLICY IF EXISTS "Users can view their own module progress" ON public.user_module_progress;
DROP POLICY IF EXISTS "Users can insert their own module progress" ON public.user_module_progress;
DROP POLICY IF EXISTS "Users can update their own module progress" ON public.user_module_progress;

CREATE POLICY "Users can view their own module progress" 
ON public.user_module_progress FOR SELECT 
USING (
  -- Users can view their own progress
  auth.uid() = user_id OR
  -- Teachers can view all progress
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Users can insert their own module progress" 
ON public.user_module_progress FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own module progress" 
ON public.user_module_progress FOR UPDATE 
USING (auth.uid() = user_id);

-- Creating functions to secure role management

-- Create a more secure function to check if a user has a specific role
CREATE OR REPLACE FUNCTION public.has_role(
  _user_id UUID,
  _role TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Validate inputs to prevent SQL injection
  IF _user_id IS NULL OR _role IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- We use a parameterized query for better security
  RETURN EXISTS (
    SELECT 1
    FROM public.user_roles
    WHERE user_id = _user_id AND role = _role
  );
END;
$$;

-- Grant limited permissions to use this function
REVOKE ALL ON FUNCTION public.has_role FROM public;
GRANT EXECUTE ON FUNCTION public.has_role TO authenticated;

-- Create a more secure function to assign a role to a user
CREATE OR REPLACE FUNCTION public.assign_role(
  _user_id UUID,
  _role TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  _current_user_id UUID;
  _is_teacher BOOLEAN;
BEGIN
  -- Get the current user ID
  _current_user_id := auth.uid();
  
  -- Validate the current user has teacher role
  SELECT EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = _current_user_id AND role = 'teacher'
  ) INTO _is_teacher;
  
  -- Only allow teachers to assign roles
  IF NOT _is_teacher THEN
    RAISE EXCEPTION 'Only teachers can assign roles';
    RETURN FALSE;
  END IF;

  -- Validate inputs
  IF _user_id IS NULL OR _role IS NULL THEN
    RAISE EXCEPTION 'Invalid user ID or role';
    RETURN FALSE;
  END IF;
  
  -- Check if the user already has a role
  IF EXISTS (SELECT 1 FROM public.user_roles WHERE user_id = _user_id) THEN
    -- Update the existing role
    UPDATE public.user_roles
    SET role = _role
    WHERE user_id = _user_id;
  ELSE
    -- Insert a new role
    INSERT INTO public.user_roles (user_id, role)
    VALUES (_user_id, _role);
  END IF;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Error assigning role: %', SQLERRM;
    RETURN FALSE;
END;
$$;

-- Grant limited permissions to use this function
REVOKE ALL ON FUNCTION public.assign_role FROM public;
GRANT EXECUTE ON FUNCTION public.assign_role TO authenticated; 