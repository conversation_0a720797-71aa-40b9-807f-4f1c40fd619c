import{j as e}from"./vendor-react.BcAa1DKr.js";import{M as o}from"./markdown-preview.Bw0U-NJA.js";/* empty css                                    */import"./vendor.DQpuTRuB.js";import"./content-converter.L-GziWIP.js";import"./index.BLDhDn0D.js";import"./vendor-supabase.sufZ44-y.js";const c=()=>{const s=`# Table Rendering Test

This is a test to verify that markdown tables are properly rendered.

## Simple Table

| Column 1 | Column 2 | Column 3 |
|----------|----------|----------|
| Row 1, Col 1 | Row 1, Col 2 | Row 1, Col 3 |
| Row 2, Col 1 | Row 2, Col 2 | Row 2, Col 3 |
| Row 3, Col 1 | Row 3, Col 2 | Row 3, Col 3 |

## Table with Alignment

| Left Aligned | Center Aligned | Right Aligned |
|:-------------|:--------------:|--------------:|
| Left | Center | Right |
| This is left | This is center | This is right |
| More content | More content | More content |

## Table with Formatting

| Feature | Status | **Priority** | Notes |
|---------|--------|-------------|-------|
| Tables | ✅ Working | **High** | Should display with borders |
| Code Blocks | ✅ Working | **High** | \`syntax highlighting\` |
| Task Lists | ✅ Working | *Medium* | Checkboxes styled |
| Links | [Working](https://example.com) | Low | External links |

## Complex Table

| Learning Module | Duration | Difficulty | Prerequisites |
|-----------------|----------|------------|---------------|
| Introduction to Concepts | 45 minutes | Beginner | None |
| Practical Applications | 90 minutes | Intermediate | Module 1 |
| Advanced Techniques | 120 minutes | Advanced | Modules 1-2 |
| Final Project | 180 minutes | Expert | All previous |

This should render as properly formatted tables with professional styling.
`;return e.jsx("div",{className:"professional-lesson-container full-width",children:e.jsxs("div",{className:"professional-prose",children:[e.jsx("h1",{children:"Table Rendering Debug"}),e.jsx("p",{children:"This component tests table rendering in the professional lesson content system."}),e.jsxs("div",{className:"border border-border rounded-lg p-4 mb-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-2",children:"Raw Markdown:"}),e.jsx("pre",{className:"text-sm bg-muted p-2 rounded overflow-x-auto",children:s})]}),e.jsxs("div",{className:"border border-border rounded-lg p-4 mb-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-2",children:"Rendered Output:"}),e.jsx(o,{content:s,className:"professional-prose",allowHtml:!0})]}),e.jsxs("div",{className:"border border-border rounded-lg p-4",children:[e.jsx("h2",{className:"text-lg font-semibold mb-2",children:"Direct HTML Table Test:"}),e.jsx("div",{className:"professional-prose",children:e.jsx("div",{className:"table-wrapper",children:e.jsxs("table",{children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Column 1"}),e.jsx("th",{children:"Column 2"}),e.jsx("th",{children:"Column 3"})]})}),e.jsxs("tbody",{children:[e.jsxs("tr",{children:[e.jsx("td",{children:"Row 1, Col 1"}),e.jsx("td",{children:"Row 1, Col 2"}),e.jsx("td",{children:"Row 1, Col 3"})]}),e.jsxs("tr",{children:[e.jsx("td",{children:"Row 2, Col 1"}),e.jsx("td",{children:"Row 2, Col 2"}),e.jsx("td",{children:"Row 2, Col 3"})]})]})]})})})]})]})})};export{c as default};
