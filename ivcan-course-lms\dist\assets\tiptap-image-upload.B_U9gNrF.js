var m=(e,d,a)=>new Promise((o,t)=>{var l=r=>{try{n(a.next(r))}catch(s){t(s)}},i=r=>{try{n(a.throw(r))}catch(s){t(s)}},n=r=>r.done?o(r.value):Promise.resolve(r.value).then(l,i);n((a=a.apply(e,d)).next())});import{s as c}from"./index.BLDhDn0D.js";function p(e){if(!e)return{valid:!1,message:"No file provided"};if(!e.type.startsWith("image/"))return{valid:!1,message:"Please upload an image file"};if(!["image/jpeg","image/png","image/webp","image/gif"].includes(e.type))return{valid:!1,message:"Please upload a JPEG, PNG, WebP, or GIF image"};const a=5*1024*1024;return e.size>a?{valid:!1,message:`Image is too large. Maximum size is ${a/(1024*1024)}MB`}:{valid:!0}}function E(e){return m(this,null,function*(){if(!e)throw new Error("File is required");if(!e.type.startsWith("image/"))throw new Error("Please select an image file");return new Promise((d,a)=>{const o=new FileReader;o.onload=t=>{var i;const l=(i=t.target)==null?void 0:i.result;typeof l=="string"?d(l):a(new Error("Failed to create image preview"))},o.onerror=()=>{a(new Error("Failed to read image file"))},o.readAsDataURL(e)})})}function $(a){return m(this,arguments,function*(e,d={}){const{courseId:o,moduleId:t,onProgress:l}=d;try{const i=p(e);if(!i.valid)throw new Error(i.message||"Invalid image");const n=Date.now(),r=e.name.split(".").pop()||"jpg";let s;o&&t?s=`editor-images/courses/${o}/modules/${t}/${n}.${r}`:o?s=`editor-images/courses/${o}/${n}.${r}`:s=`editor-images/general/${n}.${r}`;const{data:f,error:g}=yield c.storage.from("course-images").upload(s,e,{cacheControl:"3600",upsert:!1});if(g)throw console.error("Error uploading image:",g),new Error(`Failed to upload image: ${g.message}`);const{data:{publicUrl:u}}=c.storage.from("course-images").getPublicUrl(s);if(!u)throw new Error("Failed to get public URL for uploaded image");return l&&l(100),u}catch(i){throw console.error("Error in uploadEditorImage:",i),i}})}export{E as c,$ as u,p as v};
