export interface TestQuestion {
  id: string;
  question: string;
  questionNumber: number;
}

export interface RatingQuestion extends TestQuestion {
  type: 'rating';
  minRating: number;
  maxRating: number;
  minLabel: string;
  maxLabel: string;
}

export interface ModuleTest {
  id: string;
  moduleId: string;
  title: string;
  type: 'pre_test' | 'post_test';
  description?: string;
  questions: RatingQuestion[];
  createdAt: string;
  updatedAt: string;
}

export interface TestResponse {
  questionId: string;
  rating: number;
}

export interface ModuleTestResponse {
  id: string;
  testId: string;
  userId: string;
  responses: TestResponse[];
  createdAt: string;
  updatedAt: string;
}

export const createDefaultModuleTest = (
  moduleId: string,
  type: 'pre_test' | 'post_test'
): Partial<ModuleTest> => {
  return {
    moduleId,
    type,
    title: type === 'pre_test' ? 'Pre-Intervention Questionnaire' : 'Post-Intervention Questionnaire',
    questions: []
  };
};

export const createDefaultRatingQuestion = (questionNumber: number): RatingQuestion => {
  return {
    id: crypto.randomUUID(),
    type: 'rating',
    question: '',
    questionNumber,
    minRating: 1,
    maxRating: 4,
    minLabel: 'Strongly disagree',
    maxLabel: 'Strongly agree'
  };
};

export const ratingDescriptions = [
  { value: 1, label: 'Strongly disagree' },
  { value: 2, label: 'Disagree' },
  { value: 3, label: 'Agree' },
  { value: 4, label: 'Strongly agree' }
];