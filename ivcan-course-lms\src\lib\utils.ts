
import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function getGreeting(): string {
  const hour = new Date().getHours();
  
  if (hour < 12) {
    return 'Good morning';
  } else if (hour < 18) {
    return 'Good afternoon';
  } else {
    return 'Good evening';
  }
}

export function formatMarkdown(content: string): string {
  if (!content) return '';
  
  // Basic preprocessing for markdown content
  return content
    .replace(/\\n/g, '\n') // Replace escaped newlines with actual newlines
    .trim();
}
