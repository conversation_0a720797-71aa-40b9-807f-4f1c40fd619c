import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Module } from '@/services/courseService';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronRight, CheckCircle2, BookOpen, ChevronDown, ClipboardCheck, Lock } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { getModuleImageByNumber, getModuleFallbackImage } from '@/utils/moduleImageUtils';
import { useQuery } from '@tanstack/react-query';
import { getModuleTestByType, getUserTestResponse } from '@/services/module-test/moduleTestService';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';

interface ModuleListProps {
  modules: Module[];
  courseId: string;
}

const ModuleList: React.FC<ModuleListProps> = ({ modules, courseId }) => {
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const { user } = useAuth();
  const [expandedModuleId, setExpandedModuleId] = useState<string | null>(null);
  const [imageErrors, setImageErrors] = useState<Record<string, boolean>>({});

  const handleModuleClick = (moduleId: string) => {
    setExpandedModuleId(expandedModuleId === moduleId ? null : moduleId);
  };

  const handleLessonClick = (lessonSlug: string, isLocked: boolean = false) => {
    if (!isLocked) {
      navigate(`/course/${courseId}/lesson/${lessonSlug}`);
    }
  };

  const handleImageError = (moduleId: string) => {
    console.error(`Failed to load image for module ${moduleId}, falling back to default image`);
    setImageErrors(prev => ({ ...prev, [moduleId]: true }));
  };

  // Add a new query to fetch lesson completion statuses from user_lesson_progress
  const { data: lessonCompletionStatuses } = useQuery({
    queryKey: ['lesson-completions', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];

      const { data, error } = await supabase
        .from('user_lesson_progress')
        .select('lesson_id, is_completed')
        .eq('user_id', user.id)
        .eq('is_completed', true);

      if (error) {
        console.error('Error fetching lesson completion statuses:', error);
        return [];
      }

      return data || [];
    },
    enabled: !!user?.id
  });

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {modules.map((module, index) => {
        // Create a set of completed lesson IDs for faster lookups
        const completedLessonIds = new Set(
          lessonCompletionStatuses?.map(status => status.lesson_id) || []
        );

        // Calculate module completion based on our user_lesson_progress data
        const totalLessons = module.lessons?.length || 0;
        const completedLessons = module.lessons?.filter(
          lesson => completedLessonIds.has(lesson.id)
        )?.length || 0;
        const progress = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0;
        const isExpanded = expandedModuleId === module.id;

        // Use static module images based on module number
        let moduleImageUrl = getModuleImageByNumber(module.module_number || 1);

        if (imageErrors[module.id]) {
          moduleImageUrl = getModuleFallbackImage();
        }

        // Fetch pre-test data
        const { data: preTest } = useQuery({
          queryKey: ['module-pre-test', module.id],
          queryFn: async () => getModuleTestByType(module.id, 'pre_test'),
          enabled: !!module.id
        });

        // Fetch post-test data
        const { data: postTest } = useQuery({
          queryKey: ['module-post-test', module.id],
          queryFn: async () => getModuleTestByType(module.id, 'post_test'),
          enabled: !!module.id
        });

        // Check if user has completed the pre-test
        const { data: preTestResponse } = useQuery({
          queryKey: ['pre-test-response', module.id, user?.id],
          queryFn: async () => {
            if (!module.id || !user?.id || !preTest) return null;
            return getUserTestResponse(preTest.id, user.id);
          },
          enabled: !!module.id && !!user?.id && !!preTest
        });

        // Check if user has completed the post-test
        const { data: postTestResponse } = useQuery({
          queryKey: ['post-test-response', module.id, user?.id],
          queryFn: async () => {
            if (!module.id || !user?.id || !postTest) return null;
            return getUserTestResponse(postTest.id, user.id);
          },
          enabled: !!module.id && !!user?.id && !!postTest
        });

        return (
          <motion.div
            key={module.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className={cn(
              "group",
              "relative overflow-hidden rounded-xl border border-border/40",
              "bg-white dark:bg-gray-800/50 hover:bg-gray-50 dark:hover:bg-gray-800",
              "transition-all duration-200",
              module.is_locked && "opacity-75"
            )}
          >
            <div
              className="cursor-pointer"
              onClick={() => handleModuleClick(module.id)}
            >
              {/* Module Image */}
              <div className="relative aspect-video w-full overflow-hidden">
                <img
                  src={moduleImageUrl}
                  alt={module.title}
                  className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
                  onError={() => handleImageError(module.id)}
                />
                {module.is_completed && (
                  <div className="absolute top-2 right-2 bg-primary text-white rounded-full p-1">
                    <CheckCircle2 className="w-5 h-5" />
                  </div>
                )}
                {module.is_locked && (
                  <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                    <span className="text-white font-semibold">Locked</span>
                  </div>
                )}
              </div>

              <div className="p-2 space-y-2"> {/* Reduced padding and spacing */}
                <div className="flex items-start justify-between">
                  <div className="space-y-0.5"> {/* Reduced spacing */}
                    <h3 className={cn(
                      "font-semibold text-gray-900 dark:text-white group-hover:text-primary",
                      isMobile ? "text-sm" : "text-base"
                    )}>
                      {module.title}
                    </h3>
                    <div className="flex items-center flex-wrap gap-2 text-gray-500 dark:text-gray-400">
                      <div className="flex items-center gap-1">
                        <BookOpen className="w-4 h-4" />
                        <span className={cn(
                          isMobile ? "text-xs" : "text-sm"
                        )}>
                          {totalLessons} {totalLessons === 1 ? 'lesson' : 'lessons'}
                        </span>
                      </div>
                    </div>
                  </div>
                  <ChevronDown className={cn(
                    "w-5 h-5 text-gray-400 dark:text-gray-500 group-hover:text-primary",
                    "transition-transform duration-200",
                    isExpanded && "rotate-180"
                  )} />
                </div>

                <div className="space-y-1"> {/* Reduced spacing */}
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-600 dark:text-gray-400 text-xs">
                      {completedLessons} of {totalLessons} completed
                    </span>
                    <span className="font-medium text-gray-900 dark:text-white text-xs">
                      {Math.round(progress)}%
                    </span>
                  </div>
                  <Progress value={progress} className="h-1.5" />
                </div>
              </div>
            </div>

            {/* Lessons List */}
            <AnimatePresence>
              {isExpanded && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: "auto", opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="overflow-hidden border-t border-border/40"
                >
                  {/* Pre-test entry */}
                  {preTest && (
                    <div className="p-2 border-b border-border/40"> {/* Reduced padding */}
                      <div
                        className={cn(
                          "flex items-center justify-between p-2 rounded-lg",
                          "hover:bg-gray-50 dark:hover:bg-gray-700/50",
                          "transition-colors duration-200 cursor-pointer",
                          preTestResponse ? "bg-green-50 dark:bg-green-900/20 border-l-4 border-l-green-500" : "bg-yellow-50 dark:bg-yellow-900/20 border-l-4 border-l-yellow-500",
                          module.is_locked && "cursor-not-allowed opacity-50"
                        )}
                        onClick={() => !module.is_locked && navigate(`/course/${courseId}/module/${module.id}`)}
                      >
                        <div className="flex items-center gap-3">
                          <div className={cn(
                            "w-6 h-6 rounded-full flex items-center justify-center",
                            preTestResponse ? "bg-green-500 text-white" : "bg-yellow-500 text-white"
                          )}>
                            <ClipboardCheck className="w-4 h-4" />
                          </div>
                          <div className="flex flex-col">
                            <span className={cn(
                              "text-sm font-medium",
                              preTestResponse ? "text-green-700 dark:text-green-400" : "text-yellow-700 dark:text-yellow-400"
                            )}>
                              {preTest.title || "Pre-Test"}
                            </span>
                            {preTestResponse ? (
                              <span className="text-xs text-green-600 dark:text-green-400 flex items-center gap-1 mt-0.5">
                                <CheckCircle2 className="w-3 h-3" />
                                Completed
                              </span>
                            ) : (
                              <span className="text-xs text-yellow-600 dark:text-yellow-400 mt-0.5">
                                Required before starting module
                              </span>
                            )}
                          </div>
                        </div>
                        {!module.is_locked && (
                          <ChevronRight className={cn(
                            "w-4 h-4",
                            preTestResponse ? "text-green-500" : "text-yellow-500"
                          )} />
                        )}
                      </div>
                    </div>
                  )}

                  {/* Module lessons */}
                  {module.lessons && module.lessons.length > 0 && (
                    <div className="p-2 space-y-1"> {/* Reduced padding and spacing */}
                      {module.lessons.map((lesson) => {
                        // Check if this lesson is completed from our reliable source
                        const isCompleted = completedLessonIds.has(lesson.id);
                        // Check if lesson is locked (either module is locked or lesson itself is locked)
                        const isLessonLocked = module.is_locked || lesson.is_locked;

                        return (
                          <div
                            key={lesson.id}
                            onClick={() => !isLessonLocked && handleLessonClick(lesson.slug, isLessonLocked)}
                            className={cn(
                              "flex items-center justify-between p-2 rounded-lg",
                              "hover:bg-gray-50 dark:hover:bg-gray-700/50",
                              "transition-colors duration-200 cursor-pointer",
                              isCompleted ? "bg-green-50 dark:bg-green-900/20 border-l-4 border-l-green-500" : "bg-transparent",
                              isLessonLocked && "cursor-not-allowed opacity-50",
                              !preTestResponse && preTest && !isCompleted && "opacity-50"
                            )}
                          >
                            <div className="flex items-center gap-3">
                              <div className={cn(
                                "w-6 h-6 rounded-full flex items-center justify-center",
                                isCompleted ? "bg-green-500 text-white" :
                                isLessonLocked ? "bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400" :
                                "bg-gray-100 dark:bg-gray-700"
                              )}>
                                {isCompleted ? (
                                  <CheckCircle2 className="w-4 h-4" />
                                ) : isLessonLocked ? (
                                  <Lock className="w-3 h-3" />
                                ) : (
                                  <span className="text-xs font-medium">{lesson.lesson_number || 1}</span>
                                )}
                              </div>
                              <div className="flex flex-col">
                                <span className={cn(
                                  "text-sm",
                                  isCompleted ? "text-green-700 dark:text-green-400 font-medium" : "text-gray-700 dark:text-gray-300"
                                )}>
                                  {lesson.title}
                                </span>
                                {isCompleted && (
                                  <span className="text-xs text-green-600 dark:text-green-400 flex items-center gap-1 mt-0.5">
                                    <CheckCircle2 className="w-3 h-3" />
                                    Completed
                                  </span>
                                )}
                              </div>
                            </div>
                            {!module.is_locked && (
                              <ChevronRight className={cn(
                                "w-4 h-4",
                                isCompleted ? "text-green-500" : "text-gray-400"
                              )} />
                            )}
                          </div>
                        );
                      })}
                    </div>
                  )}

                  {/* Post-test entry */}
                  {postTest && (
                    <div className="p-2 border-t border-border/40"> {/* Reduced padding */}
                      <div
                        className={cn(
                          "flex items-center justify-between p-2 rounded-lg",
                          "hover:bg-gray-50 dark:hover:bg-gray-700/50",
                          "transition-colors duration-200 cursor-pointer",
                          postTestResponse ? "bg-green-50 dark:bg-green-900/20 border-l-4 border-l-green-500" : "bg-blue-50 dark:bg-blue-900/20 border-l-4 border-l-blue-500",
                          module.is_locked && "cursor-not-allowed opacity-50",
                          !module.lessons?.every(l => l.completed) && !postTestResponse && "opacity-50"
                        )}
                        onClick={() => {
                          if (!module.is_locked && (module.lessons?.every(l => l.completed) || postTestResponse)) {
                            navigate(`/course/${courseId}/module/${module.id}`);
                          }
                        }}
                      >
                        <div className="flex items-center gap-3">
                          <div className={cn(
                            "w-6 h-6 rounded-full flex items-center justify-center",
                            postTestResponse ? "bg-green-500 text-white" : "bg-blue-500 text-white"
                          )}>
                            <ClipboardCheck className="w-4 h-4" />
                          </div>
                          <div className="flex flex-col">
                            <span className={cn(
                              "text-sm font-medium",
                              postTestResponse ? "text-green-700 dark:text-green-400" : "text-blue-700 dark:text-blue-400"
                            )}>
                              {postTest.title || "Post-Test"}
                            </span>
                            {postTestResponse ? (
                              <span className="text-xs text-green-600 dark:text-green-400 flex items-center gap-1 mt-0.5">
                                <CheckCircle2 className="w-3 h-3" />
                                Completed
                              </span>
                            ) : (
                              <span className="text-xs text-blue-600 dark:text-blue-400 mt-0.5">
                                Available after completing all lessons
                              </span>
                            )}
                          </div>
                        </div>
                        {!module.is_locked && (
                          <ChevronRight className={cn(
                            "w-4 h-4",
                            postTestResponse ? "text-green-500" : "text-blue-500"
                          )} />
                        )}
                      </div>
                    </div>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        );
      })}
    </div>
  );
};

export default ModuleList;