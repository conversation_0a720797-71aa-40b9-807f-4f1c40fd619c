import React from 'react';
import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'minimal' | 'overlay';
  className?: string;
  text?: string;
}

const sizeClasses = {
  sm: 'h-4 w-4',
  md: 'h-6 w-6',
  lg: 'h-8 w-8',
  xl: 'h-12 w-12'
};

const textSizeClasses = {
  sm: 'text-sm',
  md: 'text-base',
  lg: 'text-lg',
  xl: 'text-xl'
};

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  variant = 'default',
  className,
  text
}) => {
  if (variant === 'overlay') {
    return (
      <div className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
        <div className="flex flex-col items-center gap-3">
          <Loader2 className={cn('animate-spin text-primary', sizeClasses[size])} />
          {text && (
            <p className={cn('text-muted-foreground font-medium', textSizeClasses[size])}>
              {text}
            </p>
          )}
        </div>
      </div>
    );
  }

  if (variant === 'minimal') {
    return (
      <Loader2 className={cn('animate-spin text-primary', sizeClasses[size], className)} />
    );
  }

  return (
    <div className={cn('flex items-center justify-center', className)}>
      <div className="flex flex-col items-center gap-3">
        <Loader2 className={cn('animate-spin text-primary', sizeClasses[size])} />
        {text && (
          <p className={cn('text-muted-foreground font-medium', textSizeClasses[size])}>
            {text}
          </p>
        )}
      </div>
    </div>
  );
};

// Specific loading states for common use cases
export const PageLoadingSpinner: React.FC<{ text?: string }> = ({ text = 'Loading...' }) => (
  <div className="flex items-center justify-center min-h-screen bg-background">
    <LoadingSpinner size="lg" text={text} />
  </div>
);

export const SectionLoadingSpinner: React.FC<{ text?: string; className?: string }> = ({ 
  text, 
  className 
}) => (
  <div className={cn('flex items-center justify-center min-h-[200px]', className)}>
    <LoadingSpinner size="md" text={text} />
  </div>
);

export const InlineLoadingSpinner: React.FC<{ text?: string; className?: string }> = ({ 
  text, 
  className 
}) => (
  <div className={cn('flex items-center gap-2', className)}>
    <LoadingSpinner size="sm" variant="minimal" />
    {text && <span className="text-sm text-muted-foreground">{text}</span>}
  </div>
);
