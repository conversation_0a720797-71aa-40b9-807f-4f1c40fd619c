var m=(e,o,r)=>new Promise((a,n)=>{var c=i=>{try{t(r.next(i))}catch(l){n(l)}},d=i=>{try{t(r.throw(i))}catch(l){n(l)}},t=i=>i.done?a(i.value):Promise.resolve(i.value).then(c,d);t((r=r.apply(e,o)).next())});import{s}from"./index.BLDhDn0D.js";const g={module1:{name:"Bold Step",description:"Completed Module 1",icon:"bold-step"},module2:{name:"Persistence",description:"Completed Module 2",icon:"persistence"},module3:{name:"Consistency",description:"Completed Module 3",icon:"consistency"},module4:{name:"Perseverance",description:"Completed Module 4",icon:"perseverance"},module5:{name:"Discipline",description:"Completed Module 5",icon:"discipline"},module6:{name:"Last Straw",description:"Completed Module 6",icon:"last-straw"},module7:{name:"Achiever",description:"Completed Module 7",icon:"achiever"},course_completion:{name:"Course Master",description:"Completed all modules in a course",icon:"course-master"}},v=(e,o,r)=>m(void 0,null,function*(){try{if(!e||!o)return console.error("Missing required parameters for awarding badge:",{userId:e,moduleId:o}),!1;const a=`module${r+1}`,n=g[a];if(!n)return console.error(`No badge defined for module index ${r}`),!1;console.log(`Awarding badge "${n.name}" to user ${e} for module ${o}`);const{data:c,error:d}=yield s.from("achievements").select("id").eq("name",n.name).single();if(d&&d.code!=="PGRST116")return console.error("Error checking for achievement:",d),!1;let t=c==null?void 0:c.id;if(!t){console.log(`Creating new achievement: ${n.name}`);const{data:u,error:f}=yield s.from("achievements").insert({name:n.name,description:n.description,icon:n.icon,points:100}).select("id").single();if(f)return console.error("Error creating achievement:",f),!1;t=u.id}const{data:i,error:l}=yield s.from("user_achievements").select("id").eq("user_id",e).eq("achievement_id",t).single();if(l&&l.code!=="PGRST116")return console.error("Error checking for user achievement:",l),!1;if(i)return console.log(`User ${e} already has the "${n.name}" badge`),!0;{const{error:u}=yield s.from("user_achievements").insert({user_id:e,achievement_id:t,completed_at:new Date().toISOString(),is_claimed:!1,module_id:o});return u?(console.error("Error awarding achievement to user:",u),!1):(console.log(`Successfully awarded "${n.name}" badge to user ${e}`),!0)}}catch(a){return console.error("Unexpected error in awardModuleBadge:",a),!1}}),_=(e,o)=>m(void 0,null,function*(){try{if(!e||!o)return console.error("Missing required parameters for awarding course badge:",{userId:e,courseId:o}),!1;const r=g.course_completion;console.log(`Awarding badge "${r.name}" to user ${e} for course ${o}`);const{data:a,error:n}=yield s.from("achievements").select("id").eq("name",r.name).single();if(n&&n.code!=="PGRST116")return console.error("Error checking for achievement:",n),!1;let c=a==null?void 0:a.id;if(!c){console.log(`Creating new achievement: ${r.name}`);const{data:i,error:l}=yield s.from("achievements").insert({name:r.name,description:r.description,icon:r.icon,points:500}).select("id").single();if(l)return console.error("Error creating achievement:",l),!1;c=i.id}const{data:d,error:t}=yield s.from("user_achievements").select("id").eq("user_id",e).eq("achievement_id",c).eq("course_id",o).single();if(t&&t.code!=="PGRST116")return console.error("Error checking for user achievement:",t),!1;if(d)return console.log(`User ${e} already has the "${r.name}" badge for course ${o}`),!0;{const{error:i}=yield s.from("user_achievements").insert({user_id:e,achievement_id:c,completed_at:new Date().toISOString(),is_claimed:!1,course_id:o});return i?(console.error("Error awarding achievement to user:",i),!1):(console.log(`Successfully awarded "${r.name}" badge to user ${e} for course ${o}`),!0)}}catch(r){return console.error("Unexpected error in awardCourseBadge:",r),!1}}),w=e=>m(void 0,null,function*(){try{if(!e)return console.error("Missing userId for getUserAchievements"),[];const{data:o,error:r}=yield s.from("user_achievements").select(`
        id,
        user_id,
        achievement_id,
        completed_at,
        is_claimed,
        module_id,
        course_id,
        achievement:achievements(
          id,
          name,
          description,
          icon,
          points,
          created_at
        )
      `).eq("user_id",e).order("completed_at",{ascending:!1});return r?(console.error("Error fetching user achievements:",r),[]):o}catch(o){return console.error("Unexpected error in getUserAchievements:",o),[]}}),E=(e,o)=>m(void 0,null,function*(){try{if(!e||!o)return console.error("Missing required parameters for claiming badge:",{userId:e,userAchievementId:o}),!1;const{data:r,error:a}=yield s.from("user_achievements").select("id").eq("id",o).eq("user_id",e).single();if(a||!r)return console.error("Error verifying achievement ownership:",a),!1;const{error:n}=yield s.from("user_achievements").update({is_claimed:!0}).eq("id",o);return n?(console.error("Error claiming achievement:",n),!1):(console.log(`Successfully claimed achievement ${o} for user ${e}`),!0)}catch(r){return console.error("Unexpected error in claimBadge:",r),!1}});export{v as a,_ as b,E as c,w as g};
