# Netlify Environment Variables Setup

To fix the authentication issue in the deployed application, you need to set up the following environment variables in the Netlify UI:

1. Go to your Netlify site dashboard
2. Navigate to Site settings > Build & deploy > Environment
3. Add the following environment variables:

| Key | Value |
|-----|-------|
| `VITE_SUPABASE_URL` | `https://jibspqwieubavucdtccv.supabase.co` |
| `VITE_SUPABASE_ANON_KEY` | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMxMjUzNzIsImV4cCI6MjA1ODcwMTM3Mn0.H1cvGLXWvIMibv8dYsYIvY37IRoVR80HyeF1wM93IUI` |
| `VITE_SUPABASE_SERVICE_ROLE_KEY` | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzEyNTM3MiwiZXhwIjoyMDU4NzAxMzcyfQ.I6Vxg6B0FvKPOeKMhAAu75kOf6ct5NBNs-azmAwqNWI` |
| `VITE_APP_ENVIRONMENT` | `production` |

4. Save the changes
5. Trigger a new deployment

These environment variables will be available during the build process and will be used by the application for authentication.
