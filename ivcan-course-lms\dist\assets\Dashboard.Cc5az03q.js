var C=(a,v,u)=>new Promise((h,f)=>{var j=m=>{try{b(u.next(m))}catch(r){f(r)}},x=m=>{try{b(u.throw(m))}catch(r){f(r)}},b=m=>m.done?h(m.value):Promise.resolve(m.value).then(j,x);b((u=u.apply(a,v)).next())});import{u as q,r as R,aw as K,ax as G,j as e,af as _,ay as J,aa as $,_ as X,az as Z,Y as M,aA as ee,aB as re,a8 as te}from"./vendor-react.BcAa1DKr.js";import{L as U}from"./Layout.DRjmVYQG.js";import{R as se,g as L,a as ae}from"./responsive-image.BMcUeCRG.js";import{a as oe,u as F,s as E,c as A,B as le}from"./index.BLDhDn0D.js";import{e as ne,g as ie}from"./enrollmentApi.CstnsQi_.js";import{a2 as k,a4 as y}from"./vendor.DQpuTRuB.js";import{C as de}from"./loading-skeleton.DxUGnZ26.js";import{P as I,C as O}from"./floating-sidebar-container.BxlLgzat.js";import{A as ce,a as me,b as ue}from"./alert.EBLwCiZR.js";import"./skeleton.C0Lb8Xms.js";import"./vendor-supabase.sufZ44-y.js";const ge=a=>{const{id:v,title:u="",description:h,startDate:f,progress:j,image:x,hasVideo:b=!1,isEnrolled:m=!1,course:r}=a,s=(r==null?void 0:r.id)||v,i=(r==null?void 0:r.title)||u||"",o=(r==null?void 0:r.description)||h,l=(r==null?void 0:r.startDate)||f,g=(r==null?void 0:r.progress)!==void 0?r.progress:j,d=(r==null?void 0:r.image)||x,N=(r==null?void 0:r.hasVideo)!==void 0?r.hasVideo:b,c=(r==null?void 0:r.isEnrolled)!==void 0?r.isEnrolled:m;console.log(`CourseCard for "${i}" using ${r?"course object":"direct props"} mode`);const p=q(),S=oe(),[w,V]=R.useState(!1),[B,z]=R.useState(null),{user:t}=F(),Q=K(),D=G({mutationFn:()=>C(void 0,null,function*(){var P;if(console.log("[COURSE CARD] Enrollment mutation triggered with:",{courseId:s,userId:t==null?void 0:t.id,userObject:t,userEmail:t==null?void 0:t.email}),!s)throw new Error("Course ID is missing");if(!(t!=null&&t.id))throw new Error("User ID is missing or user not authenticated");const{data:{session:n}}=yield E.auth.getSession();if(console.log("[COURSE CARD] Current session check:",{hasSession:!!n,sessionUserId:(P=n==null?void 0:n.user)==null?void 0:P.id,contextUserId:t.id}),!(n!=null&&n.user))throw new Error("No active session found");return n.user.id!==t.id&&console.warn("[COURSE CARD] Session user ID mismatch:",{sessionUserId:n.user.id,contextUserId:t.id}),ne(s,t.id,"in_progress")}),onSuccess:()=>{console.log("[COURSE CARD] Enrollment successful"),k.success("Successfully enrolled in course!"),Q.invalidateQueries({queryKey:["dashboard-courses",t==null?void 0:t.id]}),p(`/course/${s}/modules`)},onError:n=>{console.error("[COURSE CARD] Error enrolling in course:",n),k.error(`Failed to enroll in course: ${n.message||"Please try again."}`)}}),W=()=>{if(console.log("[COURSE CARD] Course click handler triggered with:",{courseId:s,userId:t==null?void 0:t.id,isEnrolled:c,userAuthenticated:!!t}),!s){console.error("[COURSE CARD] Course ID is missing"),k.error("Unable to open course: ID is missing");return}if(!t){console.error("[COURSE CARD] User not authenticated"),k.error("Please log in to access this course");return}!c&&(t!=null&&t.id)?(console.log("[COURSE CARD] User not enrolled, enrolling automatically..."),D.mutate()):c?(console.log("[COURSE CARD] User enrolled, navigating to modules"),p(`/course/${s}/modules`)):(console.log("[COURSE CARD] Fallback navigation to course detail"),p(`/course/${s}`))},Y=()=>l==="Active"?"bg-primary/20 text-primary dark:bg-primary/30 dark:text-primary-foreground":l==="Completed"&&c?"bg-primary/30 text-primary dark:bg-primary/40 dark:text-primary-foreground":"bg-muted text-muted-foreground",T=()=>{try{return ae(d,i)}catch(n){return console.error("Error getting image source:",n),L(i)}};R.useEffect(()=>{console.log(`CourseCard "${i}" (id: ${s}) has image:`,!!d),console.log("CourseCard props:",{id:s,title:i,description:o,startDate:l,progress:g,hasVideo:N,isEnrolled:c}),d&&d.startsWith("data:")&&console.log(`Course "${i}" has a data URL image of length:`,d.length)},[s,i,o,l,g,d,N,c]);const H=()=>l==="Active"?"Continue":l==="Completed"&&c?"Review":"Start";try{return e.jsxs("div",{className:A("group flex flex-col sm:flex-row overflow-hidden rounded-2xl border border-border/30 shadow-sm","bg-gradient-to-br from-white via-white to-gray-50/50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800/50","transition-all duration-500 cursor-pointer backdrop-blur-sm",!S&&"hover:-translate-y-2 hover:shadow-xl hover:shadow-primary/10 hover:border-primary/30 dark:hover:border-primary/40"),onClick:W,children:[e.jsxs("div",{className:"relative w-full sm:w-[320px] h-[200px] xs:h-[220px] sm:h-auto bg-gradient-to-br from-primary/8 via-primary/5 to-primary/12 dark:from-primary/15 dark:via-primary/8 dark:to-primary/20",children:[T()&&!w?e.jsx("div",{className:"absolute inset-0",children:e.jsx(se,{src:T()||"",alt:i||"Course",className:"transition-all duration-700 group-hover:scale-110",objectFit:"cover",aspectRatio:S?"16/9":"4/3",loadingStrategy:"eager",placeholderColor:"rgba(0,0,0,0.05)",containerClassName:"w-full h-full",fallback:L(i),onError:()=>V(!0)})}):e.jsxs("div",{className:"absolute inset-0 flex flex-col items-center justify-center",children:[e.jsx("div",{className:"w-12 h-12 sm:w-16 sm:h-16 rounded-2xl bg-gradient-to-br from-primary/20 to-primary/40 dark:from-primary/30 dark:to-primary/50 flex items-center justify-center mb-3 shadow-lg",children:e.jsx(_,{className:"w-6 h-6 sm:w-8 sm:h-8 text-primary dark:text-primary-foreground"})}),e.jsx("div",{className:"text-xl sm:text-2xl font-bold text-primary dark:text-primary-foreground",children:(i==null?void 0:i.charAt(0).toUpperCase())||"C"})]}),N&&e.jsx("div",{className:"absolute top-3 right-3 sm:top-4 sm:right-4 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground p-2 sm:p-2.5 rounded-xl shadow-lg backdrop-blur-sm",children:e.jsx(J,{className:"w-4 h-4 sm:w-5 sm:h-5"})}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent opacity-60"}),e.jsx("div",{className:"absolute bottom-0 left-0 right-0 h-1/2 bg-gradient-to-t from-black/40 via-black/10 to-transparent"})]}),e.jsxs("div",{className:"flex flex-col flex-1 p-4 sm:p-5 md:p-7",children:[e.jsxs("div",{className:"flex justify-between items-start gap-3 sm:gap-4 mb-3 sm:mb-5",children:[e.jsx("h3",{className:"text-xl sm:text-2xl md:text-3xl font-bold line-clamp-2 font-poppins bg-gradient-to-r from-red-500 via-red-600 to-red-700 bg-clip-text text-transparent leading-tight",children:i||"Untitled Course"}),l&&e.jsx("span",{className:A("px-3 sm:px-4 py-1 sm:py-1.5 text-xs font-semibold rounded-full whitespace-nowrap shadow-sm",Y()),children:l})]}),e.jsx("p",{className:"text-sm sm:text-base text-muted-foreground/80 line-clamp-2 sm:line-clamp-3 mb-4 sm:mb-6 flex-1 leading-relaxed",children:o||"Discover comprehensive learning content designed to enhance your knowledge and skills."}),e.jsxs("div",{className:"space-y-3 sm:space-y-4 mb-4 sm:mb-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Learning Progress"}),e.jsxs("span",{className:"text-sm font-bold text-primary bg-primary/10 px-2 py-1 rounded-md",children:[typeof g=="number"?Math.round(g):0,"%"]})]}),e.jsxs("div",{className:"relative h-2 sm:h-2.5 w-full bg-gray-100 dark:bg-gray-800 rounded-full overflow-hidden shadow-inner",children:[e.jsx("div",{className:"h-full bg-gradient-to-r from-primary to-primary/80 rounded-full transition-all duration-500 ease-out shadow-sm",style:{width:`${typeof g=="number"?Math.min(100,Math.max(0,g)):0}%`}}),e.jsx("div",{className:"absolute top-0 h-full bg-gradient-to-r from-primary/50 to-transparent rounded-full blur-sm opacity-60",style:{width:`${typeof g=="number"?Math.min(100,Math.max(0,g)):0}%`}})]})]}),e.jsxs("div",{className:"flex items-center justify-between pt-4 sm:pt-5 border-t border-gray-200/60 dark:border-gray-700/60",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[c&&g>=100&&e.jsxs("span",{className:"inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium rounded-full bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 shadow-sm",children:[e.jsx("svg",{className:"w-3 h-3",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),"Completed"]}),!c&&e.jsx("span",{className:"text-xs text-muted-foreground/70 font-medium",children:"Ready to start learning?"})]}),e.jsxs("button",{onClick:n=>{if(n.stopPropagation(),console.log("[COURSE CARD] Button click handler triggered with:",{courseId:s,userId:t==null?void 0:t.id,isEnrolled:c,userAuthenticated:!!t}),!s){console.error("[COURSE CARD] Course ID is missing"),k.error("Unable to open course: ID is missing");return}if(!t){console.error("[COURSE CARD] User not authenticated"),k.error("Please log in to access this course");return}!c&&(t!=null&&t.id)?(console.log("[COURSE CARD] User not enrolled, enrolling automatically..."),D.mutate()):c?(console.log("[COURSE CARD] User enrolled, navigating to modules"),p(`/course/${s}/modules`)):(console.log("[COURSE CARD] Fallback navigation to course detail"),p(`/course/${s}`))},disabled:D.isPending,className:A("group relative flex items-center justify-center gap-2 sm:gap-3 px-5 sm:px-7 py-2.5 sm:py-3.5","text-sm sm:text-base font-semibold rounded-xl shadow-lg","bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700","text-white transition-all duration-300 transform","hover:scale-105 hover:shadow-xl hover:shadow-red-500/25","active:scale-95 min-w-[130px] sm:min-w-[150px]","disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"),children:[e.jsx("span",{className:"relative z-10 font-medium tracking-wide",children:D.isPending?"Enrolling...":H()}),e.jsx($,{className:"w-4 h-4 sm:w-5 sm:h-5 group-hover:translate-x-1 transition-transform duration-300 relative z-10"}),e.jsx("div",{className:"absolute inset-0 rounded-xl bg-gradient-to-r from-red-400 to-red-500 opacity-0 group-hover:opacity-20 transition-opacity duration-300 blur-xl"})]})]})]})]})}catch(n){return console.error("Error rendering CourseCard:",n),n instanceof Error&&!B&&z(n),e.jsx("div",{className:"modern-card p-5",children:e.jsxs("div",{className:"text-primary",children:[e.jsx("h3",{className:"font-medium",children:"Error displaying course"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"There was an error displaying this course card."}),s&&e.jsxs("button",{onClick:()=>p(`/course/${s}`),className:"mt-3 text-sm text-primary hover:text-primary/80 dark:text-primary-foreground dark:hover:text-primary-foreground/90 hover:underline flex items-center",children:["View Course ",e.jsx($,{className:"ml-1 h-4 w-4"})]})]})})}},pe=()=>C(void 0,null,function*(){try{console.log("=== ENROLLMENT TEST START ===");const{data:{user:a},error:v}=yield E.auth.getUser();if(console.log("Current user:",a),v){console.error("Auth error:",v);return}if(!a){console.error("No authenticated user");return}const u="9a528e76-f04d-4bc6-a080-5909d34d257a",h=a.id;console.log("Testing enrollment for:",{userId:h,courseId:u}),console.log("Test 1: Reading enrollments...");const{data:f,error:j}=yield E.from("user_course_enrollment").select("*").eq("user_id",h);console.log("Read result:",{enrollments:f,readError:j}),console.log("Test 2: Inserting enrollment...");const{data:x,error:b}=yield E.from("user_course_enrollment").insert({user_id:h,course_id:u,status:"in_progress",enrolled_at:new Date().toISOString(),updated_at:new Date().toISOString()}).select();if(console.log("Insert result:",{insertData:x,insertError:b}),x&&x.length>0){console.log("Test 3: Updating enrollment...");const{data:m,error:r}=yield E.from("user_course_enrollment").update({status:"completed",updated_at:new Date().toISOString()}).eq("id",x[0].id).select();console.log("Update result:",{updateData:m,updateError:r})}console.log("=== ENROLLMENT TEST END ===")}catch(a){console.error("Test error:",a)}});window.testEnrollment=pe;const Re=()=>{const{user:a,setUser:v}=F(),[u,h]=R.useState(null),f=X(),j=q();R.useEffect(()=>{var o;if(a){const l=((o=a.user_metadata)==null?void 0:o.demographic_questionnaire_completed)===!0;h(l)}},[a]);const{data:x=[],isLoading:b,error:m,refetch:r}=Z({queryKey:["dashboard-courses",a==null?void 0:a.id],queryFn:()=>C(void 0,null,function*(){try{console.log("Fetching courses for user:",a==null?void 0:a.id);const{data:o,error:l}=yield E.from("courses").select("*");if(l)throw console.error("Error fetching courses:",l),l;return yield Promise.all((o||[]).map(d=>C(void 0,null,function*(){let N=!1,c="not_started",p="Not Started",S=0;if(a!=null&&a.id)try{const w=yield ie(d.id,a.id);if(w)switch(N=!0,c=w.status,w.status){case"in_progress":p="Active";break;case"completed":p="Completed",S=100;break;case"not_started":p="Not Started";break;default:p="Not Started"}}catch(w){console.error("Error fetching enrollment for course:",d.id,w)}return{id:d.id,title:d.title||"Untitled Course",description:d.description||"No description available",progress:S,startDate:p,image:d.image_url||d.image,status:c,isEnrolled:N,hasVideo:!1}})))}catch(o){throw console.error("Error in dashboard query:",o),o}}),enabled:!!(a!=null&&a.id),retry:1}),s=x||[];R.useEffect(()=>{C(void 0,null,function*(){if(f.pathname==="/my-courses"&&s.length>0){const g=s.find(d=>d.isEnrolled)||s[0];j(`/course/${g.id}/modules`)}})},[f.pathname,s,j]);const i={totalCourses:s.length,inProgress:s.filter(o=>o.status==="in_progress").length,completed:s.filter(o=>o.status==="completed").length,enrolled:s.filter(o=>o.isEnrolled).length};return u===null?e.jsx(U,{children:e.jsx(I,{pageType:"dashboard",children:e.jsx(O,{spacing:"lg",children:e.jsx("div",{className:"flex items-center justify-center p-12",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),e.jsx("p",{className:"text-muted-foreground",children:"Loading..."})]})})})})}):u===!1?e.jsx(U,{children:e.jsx(I,{pageType:"dashboard",children:e.jsx(O,{spacing:"lg",children:e.jsxs("div",{className:"flex flex-col items-center justify-center p-12 text-center",children:[e.jsx("div",{className:"w-16 h-16 rounded-full bg-amber-100 dark:bg-amber-900/20 flex items-center justify-center mb-4",children:e.jsx(M,{className:"w-8 h-8 text-amber-600 dark:text-amber-400"})}),e.jsx("h2",{className:"text-2xl font-bold mb-2",children:"Complete Your Profile"}),e.jsx("p",{className:"text-muted-foreground mb-6 max-w-md",children:"Please complete the demographic questionnaire to access your dashboard and courses."}),e.jsx(le,{onClick:()=>window.location.reload(),children:"Complete Questionnaire"})]})})})}):e.jsx(U,{children:e.jsx(I,{pageType:"dashboard",children:e.jsxs(O,{spacing:"lg",children:[e.jsxs(y.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},className:"text-center",children:[e.jsx("h1",{className:"text-3xl md:text-4xl font-bold bg-gradient-to-r from-red-500 to-red-700 bg-clip-text text-transparent mb-2",children:"Welcome to e4mi"}),e.jsx("p",{className:"text-muted-foreground text-lg",children:"Your learning journey starts here"})]}),e.jsxs("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6",children:[e.jsxs(y.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:"group relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/50 p-6 hover:shadow-lg transition-all duration-300 border border-blue-200/50 dark:border-blue-800/50",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("div",{className:"w-10 h-10 rounded-xl bg-blue-500/10 dark:bg-blue-400/20 flex items-center justify-center",children:e.jsx(_,{className:"w-5 h-5 text-blue-600 dark:text-blue-400"})}),e.jsx("span",{className:"text-2xl font-bold text-blue-900 dark:text-blue-100",children:i.totalCourses})]}),e.jsx("p",{className:"text-sm font-medium text-blue-700 dark:text-blue-300",children:"Total Programmes"})]})}),e.jsx("div",{className:"absolute -right-4 -bottom-4 w-20 h-20 bg-blue-500/5 rounded-full"})]}),e.jsxs(y.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"group relative overflow-hidden rounded-2xl bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/50 dark:to-orange-900/50 p-6 hover:shadow-lg transition-all duration-300 border border-orange-200/50 dark:border-orange-800/50",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("div",{className:"w-10 h-10 rounded-xl bg-orange-500/10 dark:bg-orange-400/20 flex items-center justify-center",children:e.jsx(ee,{className:"w-5 h-5 text-orange-600 dark:text-orange-400"})}),e.jsx("span",{className:"text-2xl font-bold text-orange-900 dark:text-orange-100",children:i.inProgress})]}),e.jsx("p",{className:"text-sm font-medium text-orange-700 dark:text-orange-300",children:"In Progress"})]})}),e.jsx("div",{className:"absolute -right-4 -bottom-4 w-20 h-20 bg-orange-500/5 rounded-full"})]}),e.jsxs(y.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"group relative overflow-hidden rounded-2xl bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/50 dark:to-green-900/50 p-6 hover:shadow-lg transition-all duration-300 border border-green-200/50 dark:border-green-800/50",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("div",{className:"w-10 h-10 rounded-xl bg-green-500/10 dark:bg-green-400/20 flex items-center justify-center",children:e.jsx(re,{className:"w-5 h-5 text-green-600 dark:text-green-400"})}),e.jsx("span",{className:"text-2xl font-bold text-green-900 dark:text-green-100",children:i.completed})]}),e.jsx("p",{className:"text-sm font-medium text-green-700 dark:text-green-300",children:"Completed"})]})}),e.jsx("div",{className:"absolute -right-4 -bottom-4 w-20 h-20 bg-green-500/5 rounded-full"})]}),e.jsxs(y.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},className:"group relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/50 dark:to-purple-900/50 p-6 hover:shadow-lg transition-all duration-300 border border-purple-200/50 dark:border-purple-800/50",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("div",{className:"w-10 h-10 rounded-xl bg-purple-500/10 dark:bg-purple-400/20 flex items-center justify-center",children:e.jsx(te,{className:"w-5 h-5 text-purple-600 dark:text-purple-400"})}),e.jsx("span",{className:"text-2xl font-bold text-purple-900 dark:text-purple-100",children:i.enrolled})]}),e.jsx("p",{className:"text-sm font-medium text-purple-700 dark:text-purple-300",children:"Enrolled"})]})}),e.jsx("div",{className:"absolute -right-4 -bottom-4 w-20 h-20 bg-purple-500/5 rounded-full"})]})]}),m&&e.jsxs(ce,{variant:"destructive",className:"mb-6",children:[e.jsx(M,{className:"h-4 w-4"}),e.jsx(me,{children:"Error"}),e.jsx(ue,{children:"Failed to load course data. Please try again later."})]}),e.jsxs(y.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.5},className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-2xl font-bold text-foreground",children:"Your Programmes"}),e.jsxs("div",{className:"text-sm text-muted-foreground",children:[s.length," ",s.length===1?"programme":"programmes"," available"]})]}),e.jsx("div",{className:"grid grid-cols-1 gap-6",children:b?e.jsx("div",{className:"space-y-6",children:Array.from({length:3}).map((o,l)=>e.jsx(y.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:l*.1},children:e.jsx(de,{})},l))}):s.length>0?e.jsx("div",{className:"space-y-6",children:s.map((o,l)=>e.jsx(y.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6+l*.1},children:e.jsx(ge,{course:o})},o.id))}):e.jsxs(y.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},className:"flex flex-col items-center justify-center p-12 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900/50 dark:to-gray-800/50 rounded-2xl border border-border/40 text-center",children:[e.jsx("div",{className:"w-16 h-16 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center mb-4",children:e.jsx(_,{className:"w-8 h-8 text-primary dark:text-primary-foreground"})}),e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"No programmes available"}),e.jsx("p",{className:"text-muted-foreground max-w-md",children:"Check back later for new learning programmes and courses."})]})})]})]})})})};export{Re as default};
