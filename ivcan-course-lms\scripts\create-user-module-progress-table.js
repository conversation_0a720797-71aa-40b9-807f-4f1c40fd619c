// This script creates the user_module_progress table in Supabase
// Run this script with: node scripts/create-user-module-progress-table.js

const { createClient } = require('@supabase/supabase-js');

// Replace these with your Supabase URL and anon key
const SUPABASE_URL = "https://jibspqwieubavucdtccv.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMxMjUzNzIsImV4cCI6MjA1ODcwMTM3Mn0.H1cvGLXWvIMibv8dYsYIvY37IRoVR80HyeF1wM93IUI";

// Create Supabase client with anon key (limited permissions)
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Try to create a service role client if environment variables are available
let serviceRoleClient = null;
if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.log('Using service role client for elevated permissions');
  serviceRoleClient = createClient(SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);
}

async function createUserModuleProgressTable() {
  try {
    const client = serviceRoleClient || supabase;
    console.log('Creating user_module_progress table...');
    
    // Check if the table already exists
    const { data: tables, error: tablesError } = await client
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'user_module_progress');
    
    if (tablesError) {
      console.error('Error checking if table exists:', tablesError);
      process.exit(1);
    }
    
    if (tables && tables.length > 0) {
      console.log('Table user_module_progress already exists');
      return;
    }
    
    console.log('Table does not exist, creating it...');
    
    // Create the table using SQL
    const { error: createError } = await client.rpc('create_user_module_progress_table');
    
    if (createError) {
      console.error('Error creating table:', createError);
      console.log('Trying alternative method...');
      
      // Try to create the table using a direct SQL query
      const createTableSQL = `
        CREATE TABLE IF NOT EXISTS public.user_module_progress (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
          module_id UUID NOT NULL REFERENCES public.modules(id) ON DELETE CASCADE,
          is_completed BOOLEAN NOT NULL DEFAULT FALSE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(user_id, module_id)
        );
        
        -- Add RLS policies
        ALTER TABLE public.user_module_progress ENABLE ROW LEVEL SECURITY;
        
        -- Allow users to view their own progress
        CREATE POLICY "Users can view their own module progress"
          ON public.user_module_progress
          FOR SELECT
          USING (auth.uid() = user_id);
        
        -- Allow users to update their own progress
        CREATE POLICY "Users can update their own module progress"
          ON public.user_module_progress
          FOR UPDATE
          USING (auth.uid() = user_id);
        
        -- Allow users to insert their own progress
        CREATE POLICY "Users can insert their own module progress"
          ON public.user_module_progress
          FOR INSERT
          WITH CHECK (auth.uid() = user_id);
        
        -- Add indexes for performance
        CREATE INDEX IF NOT EXISTS idx_user_module_progress_user_id ON public.user_module_progress(user_id);
        CREATE INDEX IF NOT EXISTS idx_user_module_progress_module_id ON public.user_module_progress(module_id);
        CREATE INDEX IF NOT EXISTS idx_user_module_progress_completion ON public.user_module_progress(is_completed);
      `;
      
      // This won't work with the anon key, but we'll try anyway
      const { error: sqlError } = await client.rpc('exec_sql', { sql: createTableSQL });
      
      if (sqlError) {
        console.error('Error creating table with SQL:', sqlError);
        console.log('Please create the table manually using the Supabase dashboard');
        console.log('SQL to create the table:');
        console.log(createTableSQL);
        process.exit(1);
      }
    }
    
    console.log('Successfully created user_module_progress table');
    
  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  }
}

// Run the function
createUserModuleProgressTable();
