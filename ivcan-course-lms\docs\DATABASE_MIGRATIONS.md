# Database Migrations Guide

This document explains how to work with database migrations in the IVCAN Course LMS project.

## Overview

Database migrations are SQL scripts that modify the database schema or data. They are used to:

- Create new tables
- Add or modify columns
- Create or update functions and triggers
- Apply Row Level Security (RLS) policies
- Seed data

Migrations are stored in the `supabase/migrations` directory and are applied in alphabetical order.

## Using the Migration Script

We provide a standardized migration script that makes it easy to create and apply migrations.

### Prerequisites

1. Install the Supabase CLI:
   ```bash
   npm install -g supabase
   ```

2. Set up your environment variables in `.env`:
   ```
   VITE_SUPABASE_URL=https://your-project-url.supabase.co
   VITE_SUPABASE_ANON_KEY=your-anon-key
   VITE_SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
   ```

### Creating a New Migration

To create a new migration:

```bash
npm run migrate:create -- --name=your_migration_name
```

This will create a new SQL file in the `supabase/migrations` directory with a timestamp prefix.

### Applying Migrations

To apply migrations to your local development database:

```bash
npm run migrate
```

To apply migrations to the remote production database:

```bash
npm run migrate:remote
```

### Advanced Options

The migration script supports several options:

```bash
# Show help
node scripts/migrate.js --help

# Reset local database before applying migrations (USE WITH CAUTION)
node scripts/migrate.js --reset

# Create a new migration
node scripts/migrate.js --create --name=add_new_table
```

## Migration Best Practices

1. **Use Idempotent Migrations**: Make your migrations idempotent (can be run multiple times without error) by using `IF NOT EXISTS` and `IF EXISTS` clauses.

   ```sql
   -- Good
   CREATE TABLE IF NOT EXISTS public.users (id UUID PRIMARY KEY);
   
   -- Bad
   CREATE TABLE public.users (id UUID PRIMARY KEY);
   ```

2. **One Change Per Migration**: Each migration should make one logical change to the database.

3. **Include RLS Policies**: Always include appropriate Row Level Security policies when creating new tables.

   ```sql
   -- Create table
   CREATE TABLE IF NOT EXISTS public.posts (
     id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
     user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
     content TEXT NOT NULL,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
   );
   
   -- Enable RLS
   ALTER TABLE public.posts ENABLE ROW LEVEL SECURITY;
   
   -- Create policies
   CREATE POLICY "Users can view all posts" 
   ON public.posts FOR SELECT 
   USING (true);
   
   CREATE POLICY "Users can insert their own posts" 
   ON public.posts FOR INSERT 
   WITH CHECK (auth.uid() = user_id);
   
   CREATE POLICY "Users can update their own posts" 
   ON public.posts FOR UPDATE 
   USING (auth.uid() = user_id);
   
   CREATE POLICY "Users can delete their own posts" 
   ON public.posts FOR DELETE 
   USING (auth.uid() = user_id);
   ```

4. **Add Comments**: Include comments to explain the purpose of the migration.

   ```sql
   -- Migration to add user profiles table
   -- This table stores additional user information not included in auth.users
   
   CREATE TABLE IF NOT EXISTS public.profiles (
     id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
     display_name TEXT,
     avatar_url TEXT,
     bio TEXT,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
   );
   ```

5. **Test Migrations**: Always test migrations on a development database before applying them to production.

## Troubleshooting

### Common Issues

1. **Migration Failed**: If a migration fails, fix the issue and try again. The migration script will skip migrations that have already been applied.

2. **Supabase CLI Not Found**: Make sure you have installed the Supabase CLI globally:
   ```bash
   npm install -g supabase
   ```

3. **Environment Variables Not Set**: Make sure you have set the required environment variables in your `.env` file.

### Getting Help

If you encounter issues with migrations, check the Supabase documentation or reach out to the development team.

## References

- [Supabase CLI Documentation](https://supabase.com/docs/reference/cli)
- [Supabase Migrations Guide](https://supabase.com/docs/guides/database/migrations)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
