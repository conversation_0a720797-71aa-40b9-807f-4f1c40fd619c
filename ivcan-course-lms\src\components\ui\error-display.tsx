import React from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { AlertCircle, RefreshCw, AlertTriangle, Wifi, WifiOff } from 'lucide-react';

interface ErrorDisplayProps {
  title?: string;
  message?: string;
  variant?: 'default' | 'warning' | 'network' | 'minimal';
  size?: 'sm' | 'md' | 'lg';
  showRetry?: boolean;
  showReload?: boolean;
  onRetry?: () => void;
  onReload?: () => void;
  className?: string;
  children?: React.ReactNode;
}

const sizeClasses = {
  sm: {
    container: 'min-h-[120px] p-4',
    icon: 'h-6 w-6',
    title: 'text-base',
    message: 'text-sm',
    button: 'h-8 px-3 text-sm'
  },
  md: {
    container: 'min-h-[200px] p-6',
    icon: 'h-8 w-8',
    title: 'text-lg',
    message: 'text-sm',
    button: 'h-9 px-4'
  },
  lg: {
    container: 'min-h-[300px] p-8',
    icon: 'h-10 w-10',
    title: 'text-xl',
    message: 'text-base',
    button: 'h-10 px-6'
  }
};

const variantClasses = {
  default: {
    container: 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/20',
    icon: 'text-red-500',
    title: 'text-red-800 dark:text-red-300',
    message: 'text-red-700 dark:text-red-400'
  },
  warning: {
    container: 'border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950/20',
    icon: 'text-amber-500',
    title: 'text-amber-800 dark:text-amber-300',
    message: 'text-amber-700 dark:text-amber-400'
  },
  network: {
    container: 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950/20',
    icon: 'text-blue-500',
    title: 'text-blue-800 dark:text-blue-300',
    message: 'text-blue-700 dark:text-blue-400'
  },
  minimal: {
    container: 'border-border bg-muted/50',
    icon: 'text-muted-foreground',
    title: 'text-foreground',
    message: 'text-muted-foreground'
  }
};

export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  title = 'Something went wrong',
  message = 'An unexpected error occurred. Please try again.',
  variant = 'default',
  size = 'md',
  showRetry = true,
  showReload = false,
  onRetry,
  onReload,
  className,
  children
}) => {
  const sizeClass = sizeClasses[size];
  const variantClass = variantClasses[variant];

  const getIcon = () => {
    switch (variant) {
      case 'warning':
        return <AlertTriangle className={cn(sizeClass.icon, variantClass.icon)} />;
      case 'network':
        return <WifiOff className={cn(sizeClass.icon, variantClass.icon)} />;
      default:
        return <AlertCircle className={cn(sizeClass.icon, variantClass.icon)} />;
    }
  };

  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    }
  };

  const handleReload = () => {
    if (onReload) {
      onReload();
    } else {
      window.location.reload();
    }
  };

  return (
    <div className={cn(
      'flex flex-col items-center justify-center rounded-lg border text-center',
      sizeClass.container,
      variantClass.container,
      className
    )}>
      {getIcon()}
      
      <h3 className={cn('mt-4 font-semibold', sizeClass.title, variantClass.title)}>
        {title}
      </h3>
      
      <p className={cn('mt-2 max-w-md', sizeClass.message, variantClass.message)}>
        {message}
      </p>

      {children && (
        <div className="mt-4">
          {children}
        </div>
      )}

      {(showRetry || showReload) && (
        <div className="flex gap-3 mt-6">
          {showRetry && (
            <Button
              onClick={handleRetry}
              variant="outline"
              size={size}
              className={cn('flex items-center gap-2', sizeClass.button)}
            >
              <RefreshCw className="h-4 w-4" />
              Try Again
            </Button>
          )}
          
          {showReload && (
            <Button
              onClick={handleReload}
              variant="ghost"
              size={size}
              className={cn(sizeClass.button)}
            >
              Reload Page
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

// Specific error components for common use cases
export const NetworkError: React.FC<Omit<ErrorDisplayProps, 'variant' | 'title' | 'message'> & {
  title?: string;
  message?: string;
}> = ({ 
  title = 'Connection Problem',
  message = 'Unable to connect to the server. Please check your internet connection and try again.',
  ...props 
}) => (
  <ErrorDisplay
    {...props}
    variant="network"
    title={title}
    message={message}
  />
);

export const DatabaseError: React.FC<Omit<ErrorDisplayProps, 'variant' | 'title' | 'message'> & {
  title?: string;
  message?: string;
}> = ({ 
  title = 'Database Service Unavailable',
  message = "We're having trouble connecting to our backend service. This may be temporary.",
  ...props 
}) => (
  <ErrorDisplay
    {...props}
    variant="warning"
    title={title}
    message={message}
  />
);

export const NotFoundError: React.FC<Omit<ErrorDisplayProps, 'variant' | 'title' | 'message'> & {
  title?: string;
  message?: string;
}> = ({ 
  title = 'Content Not Found',
  message = 'The content you are looking for could not be found.',
  ...props 
}) => (
  <ErrorDisplay
    {...props}
    variant="minimal"
    title={title}
    message={message}
    showRetry={false}
  />
);

export const PermissionError: React.FC<Omit<ErrorDisplayProps, 'variant' | 'title' | 'message'> & {
  title?: string;
  message?: string;
}> = ({ 
  title = 'Access Denied',
  message = 'You do not have permission to access this content.',
  ...props 
}) => (
  <ErrorDisplay
    {...props}
    variant="warning"
    title={title}
    message={message}
    showRetry={false}
  />
);
