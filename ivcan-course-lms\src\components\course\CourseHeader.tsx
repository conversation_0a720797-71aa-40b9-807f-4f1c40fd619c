
import React from 'react';
import { Link } from 'react-router-dom';
import { ChevronLeft } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';

interface CourseHeaderProps {
  title: string;
  description: string;
}

const CourseHeader: React.FC<CourseHeaderProps> = ({ title, description }) => {
  const isMobile = useIsMobile();

  return (
    <div>
      <Link
        to="/dashboard"
        className={cn(
          "inline-flex items-center font-medium text-muted-foreground hover:text-foreground mb-3 md:mb-4",
          isMobile ? "text-xs" : "text-sm"
        )}
      >
        <ChevronLeft className={cn("mr-1", isMobile ? "w-3 h-3" : "w-4 h-4")} />
        Back to Dashboard
      </Link>

      <div className="mb-6 md:mb-8">
        <h1 className={cn(
          "font-bold tracking-tight",
          isMobile ? "text-xl" : "text-2xl"
        )}>{title}</h1>
        <p className={cn(
          "text-muted-foreground mt-1",
          isMobile ? "text-sm" : "text-base"
        )}>{description}</p>
      </div>
    </div>
  );
};

export default CourseHeader;
