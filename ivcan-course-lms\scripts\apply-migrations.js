/**
 * <PERSON><PERSON><PERSON> to apply Supabase migrations
 * 
 * This script reads all SQL migration files in the supabase/migrations directory
 * and applies them to your Supabase project.
 * 
 * Usage:
 * node scripts/apply-migrations.js
 * 
 * Requirements:
 * - Supabase CLI installed
 * - Logged in to Supabase CLI
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const MIGRATIONS_DIR = path.join(__dirname, '..', 'supabase', 'migrations');
const PROJECT_ID = 'jibspqwieubavucdtccv'; // Your Supabase project ID

// Function to apply migrations
function applyMigrations() {
  try {
    console.log('Applying Supabase migrations...');
    
    // Check if migrations directory exists
    if (!fs.existsSync(MIGRATIONS_DIR)) {
      console.error(`Migrations directory not found: ${MIGRATIONS_DIR}`);
      process.exit(1);
    }
    
    // Get all migration files
    const migrationFiles = fs.readdirSync(MIGRATIONS_DIR)
      .filter(file => file.endsWith('.sql'))
      .sort(); // Sort to ensure migrations are applied in order
    
    if (migrationFiles.length === 0) {
      console.log('No migration files found.');
      return;
    }
    
    console.log(`Found ${migrationFiles.length} migration files.`);
    
    // Apply each migration
    for (const file of migrationFiles) {
      const filePath = path.join(MIGRATIONS_DIR, file);
      console.log(`Applying migration: ${file}`);
      
      try {
        // Read the SQL file
        const sql = fs.readFileSync(filePath, 'utf8');
        
        // Create a temporary file with the SQL content
        const tempFile = path.join(__dirname, 'temp-migration.sql');
        fs.writeFileSync(tempFile, sql);
        
        // Apply the migration using Supabase CLI
        execSync(`supabase db push --db-url postgresql://postgres:postgres@localhost:54322/postgres`, {
          stdio: 'inherit'
        });
        
        // Clean up the temporary file
        fs.unlinkSync(tempFile);
        
        console.log(`Successfully applied migration: ${file}`);
      } catch (error) {
        console.error(`Error applying migration ${file}:`, error.message);
        process.exit(1);
      }
    }
    
    console.log('All migrations applied successfully!');
  } catch (error) {
    console.error('Error applying migrations:', error.message);
    process.exit(1);
  }
}

// Run the function
applyMigrations();
