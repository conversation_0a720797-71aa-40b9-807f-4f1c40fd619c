# Setting Up Google OAuth with Supabase

This guide will help you set up Google OAuth authentication for your Supabase project.

## Prerequisites

- A Supabase project
- A Google Cloud Platform account

## Step 1: Create OAuth Credentials in Google Cloud Console

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Navigate to "APIs & Services" > "Credentials"
4. Click "Create Credentials" and select "OAuth client ID"
5. Select "Web application" as the application type
6. Add a name for your OAuth client
7. Add authorized JavaScript origins:
   - For development: `http://localhost:8080`
   - For production: Your actual domain (e.g., `https://your-app-domain.com`)
8. Add authorized redirect URIs:
   - For development: `http://localhost:8080/auth/callback`
   - For production: `https://your-app-domain.com/auth/callback`
   - Also add your Supabase URL: `https://[YOUR_PROJECT_ID].supabase.co/auth/v1/callback`
9. Click "Create"
10. Note down the Client ID and Client Secret

## Step 2: Configure Supabase Auth Settings

1. Go to your [Supabase Dashboard](https://app.supabase.io/)
2. Select your project
3. Navigate to "Authentication" > "Providers"
4. Find "Google" in the list of providers and click "Edit"
5. Enable the provider by toggling the switch
6. Enter the Client ID and Client Secret from Google Cloud Console
7. Save the changes

## Step 3: Configure Site URL and Redirect URLs in Supabase

1. In your Supabase dashboard, go to "Authentication" > "URL Configuration"
2. Set the Site URL:
   - For development: `http://localhost:8080`
   - For production: Your actual domain (e.g., `https://your-app-domain.com`)
3. Add Redirect URLs:
   - For development: `http://localhost:8080/dashboard`
   - For production: `https://your-app-domain.com/dashboard`
4. Save the changes

## Step 4: Test the Integration

1. Run your application in development mode
2. Navigate to the login page
3. Click the "Log in with Google" button
4. You should be redirected to Google's authentication page
5. After successful authentication, you should be redirected back to your application

## Troubleshooting

If you encounter issues with Google OAuth, check the following:

1. **Redirect URI Mismatch**: Ensure that the redirect URIs in Google Cloud Console match the ones in your application and Supabase settings.
2. **JavaScript Origins**: Make sure your application's domain is listed in the authorized JavaScript origins in Google Cloud Console.
3. **Client ID and Secret**: Verify that the Client ID and Secret in Supabase match those from Google Cloud Console.
4. **Browser Console Errors**: Check the browser console for any error messages that might provide more information.
5. **Supabase Logs**: Check the Supabase logs for authentication-related errors.

## Additional Resources

- [Supabase Auth Documentation](https://supabase.com/docs/guides/auth)
- [Google OAuth Documentation](https://developers.google.com/identity/protocols/oauth2)
