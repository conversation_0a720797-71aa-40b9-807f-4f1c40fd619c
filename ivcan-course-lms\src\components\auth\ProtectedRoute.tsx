import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { Loader2 } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: 'student' | 'teacher' | null;
}

/**
 * A component that protects routes by requiring authentication
 * and optionally a specific role
 */
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children,
  requiredRole = null
}) => {
  const { user, loading } = useAuth();
  const location = useLocation();
  
  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen bg-background">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-12 w-12 animate-spin text-primary" />
          <p className="text-muted-foreground">Verifying authentication...</p>
        </div>
      </div>
    );
  }
  
  // If not authenticated, redirect to login
  if (!user) {
    // Save the current location to redirect back after login
    const returnPath = encodeURIComponent(location.pathname + location.search);
    return <Navigate to={`/login?returnTo=${returnPath}`} replace />;
  }
  
  // If a specific role is required, check for it
  if (requiredRole) {
    // This is a simplified check - in a real app, you'd use the role service
    // For now, we'll just check if the user has the required role in their metadata
    const userRole = user.user_metadata?.role;
    
    if (userRole !== requiredRole) {
      // If the user doesn't have the required role, redirect to dashboard
      return <Navigate to="/dashboard" replace />;
    }
  }
  
  // If authenticated and has the required role (if any), render the children
  return <>{children}</>;
};

export default ProtectedRoute;
