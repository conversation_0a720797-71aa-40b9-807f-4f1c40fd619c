var E=(c,w,l)=>new Promise((f,n)=>{var i=o=>{try{t(l.next(o))}catch(d){n(d)}},b=o=>{try{t(l.throw(o))}catch(d){n(d)}},t=o=>o.done?f(o.value):Promise.resolve(o.value).then(i,b);t((l=l.apply(c,w)).next())});import{u as N,_ as C,r as m,j as s,$ as v,E as P,aW as U}from"./vendor-react.BcAa1DKr.js";import{u as k,s as S}from"./index.BLDhDn0D.js";import{a2 as R}from"./vendor.DQpuTRuB.js";import"./vendor-supabase.sufZ44-y.js";const M=()=>{const c=N(),w=C(),{user:l,setUser:f}=k(),[n,i]=m.useState("loading"),[b,t]=m.useState("Processing your request..."),[o,d]=m.useState(!1),[L,A]=m.useState(null);return m.useEffect(()=>{l&&!o&&(console.log("User already authenticated in AuthCallback, redirecting to dashboard"),d(!0),setTimeout(()=>{c("/dashboard")},100))},[l,c,o]),m.useEffect(()=>{if(o)return;E(void 0,null,function*(){try{console.log("Auth callback triggered"),console.log("URL:",window.location.href),console.log("Location:",w);const g=window.location.hash,u=new URLSearchParams(window.location.search);A({url:window.location.href,hash:g,params:Object.fromEntries(u.entries()),host:window.location.host,origin:window.location.origin}),console.log("Hash:",g),console.log("Search params:",Object.fromEntries(u.entries()));const y=u.get("error"),j=u.get("error_description");if(y){console.error("Auth error from URL:",y,j),i("error"),t(j||`Authentication error: ${y}`);return}let a=null;if(window.location.href.includes("error=")){console.error("OAuth error detected in URL:",window.location.href),i("error"),t("Authentication failed. Please try again.");return}try{console.log("Attempting to get current session");const{data:e,error:r}=yield S.auth.getSession();if(r)throw r;a=e,console.log("Successfully retrieved session:",a)}catch(e){if(console.error("Error getting session:",e),u.has("code"))try{console.log("Attempting to exchange code for session"),yield new Promise(x=>setTimeout(x,1e3));try{console.log("Manually exchanging code for session"),yield S.auth.exchangeCodeForSession(u.get("code")||"")}catch(x){console.error("Error in manual code exchange:",x)}const{data:r,error:p}=yield S.auth.getSession();if(p)throw p;a=r,console.log("Successfully retrieved session after code exchange:",a)}catch(r){console.error("Error exchanging code for session:",r),i("error"),t("Failed to complete authentication. Please try again.");return}else{i("error"),t(e.message||"Failed to get authentication session.");return}}console.log("Session data:",a);let h="/dashboard";try{const e=u.get("state")||new URLSearchParams(g.substring(1)).get("state");if(e)try{const r=JSON.parse(decodeURIComponent(e));r.redirectTo&&(h=r.redirectTo)}catch(r){console.warn("Error parsing state JSON:",r),e.startsWith("/")&&(h=e)}}catch(e){console.warn("Could not parse state parameter:",e)}if(a!=null&&a.session){if(i("success"),t("Authentication successful! Redirecting..."),console.log("User authenticated:",a.session.user),f){console.log("Updating user in auth context"),f(a.session.user);try{localStorage.setItem("supabase.auth.token",JSON.stringify({currentSession:a.session,expiresAt:Math.floor(Date.now()/1e3)+3600}))}catch(x){console.warn("Failed to store session in localStorage:",x)}}else console.warn("setUser function not available in auth context");R.success("Successfully signed in!"),d(!0);const e=window.location.origin,r=h.startsWith("/")?h:`/${h}`,p=`${e}${r}`;console.log("Redirecting to:",p),c(h)}else i("success"),t("Email confirmed! You can now sign in with your credentials."),d(!0),setTimeout(()=>{c("/login")},1e3)}catch(g){console.error("Error handling auth callback:",g),i("error"),t(g.message||"An unexpected error occurred. Please try again.")}})},[c,w,f,o]),s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background",children:s.jsx("div",{className:"max-w-md w-full p-8 bg-card rounded-xl shadow-sm border border-border",children:s.jsxs("div",{className:"flex flex-col items-center justify-center text-center",children:[n==="loading"&&s.jsx(v,{className:"h-12 w-12 text-primary animate-spin mb-4"}),n==="success"&&s.jsx(P,{className:"h-12 w-12 text-red-500 mb-4"}),n==="error"&&s.jsx(U,{className:"h-12 w-12 text-red-500 mb-4"}),s.jsxs("h1",{className:"text-2xl font-bold mb-2",children:[n==="loading"&&"Processing",n==="success"&&"Success",n==="error"&&"Error"]}),s.jsx("p",{className:"text-muted-foreground mb-6",children:b}),n==="error"&&s.jsxs("div",{className:"flex flex-col gap-4",children:[s.jsxs("div",{className:"flex gap-4",children:[s.jsx("button",{onClick:()=>c("/login"),className:"px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors",children:"Go to Login"}),s.jsx("button",{onClick:()=>c("/"),className:"px-4 py-2 bg-muted text-foreground rounded-lg hover:bg-muted/90 transition-colors",children:"Go to Home"})]}),!1]})]})})})};export{M as default};
