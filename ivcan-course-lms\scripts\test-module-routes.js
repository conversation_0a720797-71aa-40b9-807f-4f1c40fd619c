// Test script to verify module test routes are working
// Run with: node scripts/test-module-routes.js

const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = "https://jibspqwieubavucdtccv.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMxMjUzNzIsImV4cCI6MjA1ODcwMTM3Mn0.H1cvGLXWvIMibv8dYsYIvY37IRoVR80HyeF1wM93IUI";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testModuleRoutes() {
  console.log('🔍 Testing module test routes...\n');
  
  try {
    // 1. Get a sample course and module
    console.log('1. Getting sample course and module data...');
    
    const { data: courses, error: courseError } = await supabase
      .from('courses')
      .select('id, title')
      .limit(1);
    
    if (courseError || !courses || courses.length === 0) {
      console.log('❌ No courses found for testing');
      return;
    }
    
    const course = courses[0];
    console.log(`✅ Found course: "${course.title}" (ID: ${course.id})`);
    
    const { data: modules, error: moduleError } = await supabase
      .from('modules')
      .select('id, title, course_id')
      .eq('course_id', course.id)
      .limit(1);
    
    if (moduleError || !modules || modules.length === 0) {
      console.log('❌ No modules found for testing');
      return;
    }
    
    const module = modules[0];
    console.log(`✅ Found module: "${module.title}" (ID: ${module.id})`);
    
    // 2. Check if module has tests
    console.log('\n2. Checking for module tests...');
    
    const { data: preTest, error: preTestError } = await supabase
      .from('module_tests')
      .select('id, title, type')
      .eq('module_id', module.id)
      .eq('type', 'pre_test')
      .single();
    
    if (preTestError && preTestError.code !== 'PGRST116') {
      console.log('⚠️ Error checking pre-test:', preTestError.message);
    } else if (preTest) {
      console.log(`✅ Found pre-test: "${preTest.title}"`);
    } else {
      console.log('ℹ️ No pre-test found for this module');
    }
    
    const { data: postTest, error: postTestError } = await supabase
      .from('module_tests')
      .select('id, title, type')
      .eq('module_id', module.id)
      .eq('type', 'post_test')
      .single();
    
    if (postTestError && postTestError.code !== 'PGRST116') {
      console.log('⚠️ Error checking post-test:', postTestError.message);
    } else if (postTest) {
      console.log(`✅ Found post-test: "${postTest.title}"`);
    } else {
      console.log('ℹ️ No post-test found for this module');
    }
    
    // 3. Generate test URLs
    console.log('\n3. Generated test URLs:');
    const baseUrl = 'http://localhost:5173';
    const moduleTestUrl = `${baseUrl}/course/${course.id}/module/${module.id}`;
    
    console.log(`📍 Module test page: ${moduleTestUrl}`);
    console.log(`📍 Course page: ${baseUrl}/course/${course.id}`);
    
    // 4. Test route patterns
    console.log('\n4. Route pattern verification:');
    console.log('✅ Route pattern: /course/:courseId/module/:moduleId');
    console.log('✅ Example URL matches pattern');
    
    if (preTest || postTest) {
      console.log('\n🎉 Module test routing should work!');
      console.log('\n📝 To test:');
      console.log('1. Open the course page in your browser');
      console.log('2. Click on a pre-test or post-test button');
      console.log('3. Verify you are redirected to the module test page');
      console.log('4. Check that the test interface loads correctly');
    } else {
      console.log('\n⚠️ No tests found for this module');
      console.log('You may need to create tests using the admin interface to fully test the routing');
    }
    
  } catch (error) {
    console.error('❌ Unexpected error during testing:', error);
  }
}

// Run the test
testModuleRoutes();
