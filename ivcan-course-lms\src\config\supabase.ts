/**
 * Centralized Supabase configuration
 *
 * This file provides a single source of truth for Supabase configuration
 * and prevents hardcoded credentials throughout the codebase.
 */

// Check if we're in production mode
const isProduction = import.meta.env.MODE === 'production';

// Get environment variables with error if missing in production
const getEnvVar = (key: string): string => {
  const value = import.meta.env[key];
  if (!value && isProduction) {
    console.error(`${key} is required in production but not set!`);
    throw new Error(`${key} is required in production but not set!`);
  }
  return value || '';
};

// Set environment-specific log levels
export const LOG_LEVEL = isProduction ? 'error' : 'info';

// Supabase configuration
export const SUPABASE_CONFIG = {
  // URL for the Supabase project
  URL: getEnvVar('VITE_SUPABASE_URL'),

  // Anonymous/public API key (limited permissions)
  ANON_KEY: getEnvVar('VITE_SUPABASE_ANON_KEY'),

  // Service role key (admin permissions) - only used in secure contexts
  SERVICE_ROLE_KEY: getEnvVar('VITE_SUPABASE_SERVICE_ROLE_KEY'),

  // Project ID - used for some API operations
  PROJECT_ID: 'jibspqwieubavucdtccv',

  // Default schema
  SCHEMA: 'public',

  // Connection settings
  CONNECTION: {
    TIMEOUT_MS: 10000, // 10 seconds
    RETRY_COUNT: 3,
    CIRCUIT_BREAKER_THRESHOLD: 5, // Number of failures before circuit trips
    CIRCUIT_BREAKER_RESET_TIMEOUT_MS: 30000, // 30 seconds
  },

  // Storage bucket names
  STORAGE: {
    COURSE_IMAGES: 'course-images',
    AVATARS: 'avatars',
    APP_UPLOADS: 'app-uploads',
  }
};

// Check for required configuration
const configValid = SUPABASE_CONFIG.URL && SUPABASE_CONFIG.ANON_KEY;

// Log configuration status
if (configValid) {
  console.log('Supabase configuration loaded successfully.');
  console.log('Environment mode:', import.meta.env.MODE);
  console.log('Supabase URL:', SUPABASE_CONFIG.URL ? 'Set' : 'Not set');
  console.log('Supabase Anon Key:', SUPABASE_CONFIG.ANON_KEY ? 'Set' : 'Not set');
  console.log('Supabase Service Role Key:', SUPABASE_CONFIG.SERVICE_ROLE_KEY ? 'Set' : 'Not set');
} else {
  console.warn('Supabase configuration incomplete. Check your environment variables.');
}

// Utility function for logging
export const logSupabaseError = (operation: string, error: any) => {
  if (isProduction) {
    // In production, log minimal info to avoid leaking sensitive data
    console.error(`Supabase error during ${operation}: ${error?.message || 'Unknown error'}`);
  } else {
    // In development, log more details for debugging
    console.error(`Supabase error during ${operation}:`, error);
  }
};

// Export individual values for convenience
export const SUPABASE_URL = SUPABASE_CONFIG.URL;
export const SUPABASE_ANON_KEY = SUPABASE_CONFIG.ANON_KEY;
export const SUPABASE_SERVICE_ROLE_KEY = SUPABASE_CONFIG.SERVICE_ROLE_KEY;
export const SUPABASE_PROJECT_ID = SUPABASE_CONFIG.PROJECT_ID;
