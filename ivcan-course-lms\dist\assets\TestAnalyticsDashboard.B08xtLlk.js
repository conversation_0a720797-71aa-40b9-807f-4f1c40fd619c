var R=(s,r,t)=>new Promise((o,x)=>{var l=g=>{try{i(t.next(g))}catch(a){x(a)}},m=g=>{try{i(t.throw(g))}catch(a){x(a)}},i=g=>g.done?o(g.value):Promise.resolve(g.value).then(l,m);i((t=t.apply(s,r)).next())});import{r as _,R as W,j as e,az as z,$ as E,at as k,br as G,as as B,aD as H,cd as J,be as V,ab as K,b6 as X,aq as Y}from"./vendor-react.BcAa1DKr.js";import{C as j,a as T,b as S,c as P,d as b}from"./card.B9V6b2DK.js";import{s as D,B as q,I as Z,D as ee,h as te,i as se,j as re,k as ie,l as ne}from"./index.BLDhDn0D.js";import{B as $}from"./badge.C87ZuIxl.js";import{r as F}from"./module-test.BaVeK2uM.js";import{aF as N,ag as L,ah as O,a2 as C,W as ae}from"./vendor.DQpuTRuB.js";import{P as le}from"./progress.BMuwHUJX.js";const de=()=>R(void 0,null,function*(){try{console.log("Fetching test analytics overview...");const{data:s,error:r}=yield D.from("module_test_responses").select("id, test_id, user_id, responses, created_at, updated_at");if(r)throw console.error("Error fetching test analytics:",r),r;if(console.log("Fetched responses:",(s==null?void 0:s.length)||0),!s||s.length===0)return console.log("No responses found"),{totalStudents:0,totalTestsCompleted:0,studentsWithTests:[]};const t=[...new Set(s.map(n=>n.user_id))];console.log("Found user IDs:",t.length);const{data:o,error:x}=yield D.from("profiles").select("id, first_name, last_name").in("id",t);x&&console.error("Error fetching profiles:",x);const l=[...new Set(s.map(n=>n.test_id))],{data:m,error:i}=yield D.from("module_tests").select(`
        id,
        title,
        type,
        module_id,
        modules (
          id,
          title,
          course_id,
          courses (
            id,
            title
          )
        )
      `).in("id",l);i&&console.error("Error fetching tests:",i);const g=new Map((o==null?void 0:o.map(n=>[n.id,n]))||[]),a=new Map((m==null?void 0:m.map(n=>[n.id,n]))||[]),c=new Map;s.forEach(n=>{const p=n.user_id,d=g.get(p),y=a.get(n.test_id);if(!y){console.warn("Test not found for response:",n.id);return}const f=y.modules,w=f==null?void 0:f.courses,I={id:p,email:"User",firstName:d==null?void 0:d.first_name,lastName:d==null?void 0:d.last_name,fullName:`${(d==null?void 0:d.first_name)||""} ${(d==null?void 0:d.last_name)||""}`.trim()||`User ${p.slice(0,8)}`},M={id:n.id,testId:y.id,testTitle:y.title,testType:y.type,moduleId:(f==null?void 0:f.id)||"",moduleTitle:(f==null?void 0:f.title)||"Unknown Module",courseId:(w==null?void 0:w.id)||"",courseTitle:(w==null?void 0:w.title)||"Unknown Course",submittedAt:n.created_at,responseCount:JSON.parse(n.responses||"[]").length};if(c.has(p)){const A=c.get(p);A.testSubmissions.push(M),A.totalTestsCompleted++}else c.set(p,{student:I,testSubmissions:[M],totalTestsCompleted:1})});const u=Array.from(c.values());return console.log("Processed students:",u.length),{totalStudents:u.length,totalTestsCompleted:s.length,studentsWithTests:u}}catch(s){throw console.error("Error in getTestAnalyticsOverview:",s),s}}),oe=s=>R(void 0,null,function*(){try{const{data:r,error:t}=yield D.from("module_test_responses").select("id, test_id, user_id, responses, created_at, updated_at").eq("id",s).single();if(t)throw console.error("Error fetching detailed test response:",t),t;if(!r)throw new Error("Test response not found");const{data:o,error:x}=yield D.from("profiles").select("id, first_name, last_name").eq("id",r.user_id).single();x&&console.error("Error fetching profile:",x);const{data:l,error:m}=yield D.from("module_tests").select(`
        id,
        title,
        type,
        description,
        questions,
        module_id,
        modules (
          id,
          title,
          course_id,
          courses (
            id,
            title
          )
        )
      `).eq("id",r.test_id).single();if(m)throw console.error("Error fetching test:",m),m;const i=l.modules,g=i.courses,a=JSON.parse(r.responses||"[]"),c=JSON.parse(l.questions||"[]"),u=a.map(p=>{const d=c.find(y=>y.id===p.questionId);return{questionId:p.questionId,rating:p.rating,question:d||{id:p.questionId,question:"Question not found",questionNumber:0,type:"rating",minRating:1,maxRating:4,minLabel:"Strongly disagree",maxLabel:"Strongly agree"}}});return{id:r.id,testId:r.test_id,userId:r.user_id,student:{id:r.user_id,email:"User",firstName:o==null?void 0:o.first_name,lastName:o==null?void 0:o.last_name,fullName:`${(o==null?void 0:o.first_name)||""} ${(o==null?void 0:o.last_name)||""}`.trim()||`User ${r.user_id.slice(0,8)}`},test:{id:l.id,title:l.title,type:l.type,description:l.description,module:{id:i.id,title:i.title,course:{id:g.id,title:g.title}}},responses:u,createdAt:r.created_at,updatedAt:r.updated_at}}catch(r){throw console.error("Error in getDetailedTestResponse:",r),r}}),ce=()=>R(void 0,null,function*(){try{console.log("Starting getOverallAnalyticsExport...");const{data:s,error:r}=yield D.from("module_test_responses").select("id, test_id, user_id, responses, created_at");if(r)throw console.error("Error fetching responses for analytics:",r),r;if(!s||s.length===0)return{generatedAt:new Date().toISOString(),totalStudents:0,totalResponses:0,testSummaries:[]};const t=[...new Set(s.map(i=>i.test_id))],{data:o,error:x}=yield D.from("module_tests").select(`
        id,
        title,
        type,
        questions,
        module_id,
        modules (
          id,
          title,
          course_id,
          courses (
            id,
            title
          )
        )
      `).in("id",t);if(x)throw console.error("Error fetching tests for analytics:",x),x;const l=(o==null?void 0:o.map(i=>{var u,n,p;const g=s.filter(d=>d.test_id===i.id),c=JSON.parse(i.questions||"[]").map(d=>{const y=g.map(h=>JSON.parse(h.responses||"[]")).flat().filter(h=>h.questionId===d.id),f=new Map;y.forEach(h=>{const v=h.rating;f.set(v,(f.get(v)||0)+1)});const w=y.length,I=F.map(h=>{const v=f.get(h.value)||0,U=w>0?v/w*100:0;return{rating:h.value,label:h.label,count:v,percentage:Math.round(U*100)/100}}),M=y.reduce((h,v)=>h+v.rating,0),A=w>0?M/w:0;return{questionId:d.id,questionText:d.question,questionNumber:d.questionNumber,totalResponses:w,optionBreakdown:I,averageRating:Math.round(A*100)/100}});return{testId:i.id,testTitle:i.title,testType:i.type,moduleTitle:((u=i.modules)==null?void 0:u.title)||"Unknown Module",courseTitle:((p=(n=i.modules)==null?void 0:n.courses)==null?void 0:p.title)||"Unknown Course",totalResponses:g.length,questions:c}}))||[],m=new Set(s.map(i=>i.user_id)).size;return{generatedAt:new Date().toISOString(),totalStudents:m,totalResponses:s.length,testSummaries:l}}catch(s){throw console.error("Error in getOverallAnalyticsExport:",s),s}}),me=s=>R(void 0,null,function*(){const{student:r,testResponse:t,generatedAt:o}=s,x=l=>{const m=F.find(i=>i.value===l);return m?m.label:`Rating ${l}`};try{const l=document.createElement("div");l.style.position="absolute",l.style.left="-9999px",l.style.top="-9999px",l.style.width="210mm",l.style.backgroundColor="#ffffff",l.style.fontFamily="Arial, sans-serif",l.style.padding="20mm",l.innerHTML=`
      <div style="max-width: 170mm; margin: 0 auto;">
        <!-- Header -->
        <div style="border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px;">
          <h1 style="font-size: 28px; font-weight: bold; color: #333; margin: 0 0 10px 0;">Test Response Report</h1>
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; font-size: 12px; color: #666;">
            <div><strong>Generated:</strong> ${N(new Date(o),"MMM dd, yyyy HH:mm")}</div>
            <div><strong>Report ID:</strong> ${t.id.slice(0,8)}</div>
          </div>
        </div>

        <!-- Student Information -->
        <div style="margin-bottom: 30px;">
          <h2 style="font-size: 18px; font-weight: 600; color: #333; margin: 0 0 15px 0; border-bottom: 1px solid #ccc; padding-bottom: 8px;">
            Student Information
          </h2>
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div><strong>Name:</strong> ${r.fullName}</div>
            <div><strong>Email:</strong> ${r.email}</div>
          </div>
        </div>

        <!-- Test Information -->
        <div style="margin-bottom: 30px;">
          <h2 style="font-size: 18px; font-weight: 600; color: #333; margin: 0 0 15px 0; border-bottom: 1px solid #ccc; padding-bottom: 8px;">
            Test Information
          </h2>
          <div style="line-height: 1.6;">
            <div><strong>Test Title:</strong> ${t.test.title}</div>
            <div><strong>Test Type:</strong> ${t.test.type==="pre_test"?"Pre-Test":"Post-Test"}</div>
            <div><strong>Course:</strong> ${t.test.module.course.title}</div>
            <div><strong>Module:</strong> ${t.test.module.title}</div>
            <div><strong>Submission Date:</strong> ${N(new Date(t.createdAt),"MMM dd, yyyy HH:mm")}</div>
            <div><strong>Questions Answered:</strong> ${t.responses.length}</div>
            ${t.test.description?`
              <div style="margin-top: 15px;">
                <strong>Test Description:</strong>
                <p style="margin: 5px 0; color: #555;">${t.test.description}</p>
              </div>
            `:""}
          </div>
        </div>

        <!-- Responses -->
        <div style="margin-bottom: 30px;">
          <h2 style="font-size: 18px; font-weight: 600; color: #333; margin: 0 0 15px 0; border-bottom: 1px solid #ccc; padding-bottom: 8px;">
            Student Responses
          </h2>
          <div style="space-y: 20px;">
            ${t.responses.sort((d,y)=>d.question.questionNumber-y.question.questionNumber).map(d=>`
                <div style="border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
                  <!-- Question -->
                  <div style="margin-bottom: 12px;">
                    <div style="display: flex; align-items: flex-start; gap: 8px;">
                      <span style="background: #f5f5f5; color: #333; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;">
                        Q${d.question.questionNumber}
                      </span>
                      <p style="font-weight: 500; color: #333; line-height: 1.5; margin: 0; flex: 1;">
                        ${d.question.question}
                      </p>
                    </div>
                  </div>

                  <!-- Answer -->
                  <div style="background: #f9f9f9; border-radius: 6px; padding: 12px; margin-left: 32px;">
                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;">
                      <div>
                        <div style="font-weight: 500; color: #2563eb;">
                          ${x(d.rating)}
                        </div>
                        <div style="font-size: 12px; color: #666;">
                          Rating: ${d.rating} out of ${d.question.maxRating}
                        </div>
                      </div>
                      <div style="background: #dbeafe; color: #1e40af; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;">
                        ${d.rating}/${d.question.maxRating}
                      </div>
                    </div>

                    <!-- Rating Scale Reference -->
                    <div style="margin-top: 12px; padding-top: 12px; border-top: 1px solid #ddd;">
                      <div style="display: flex; justify-content: space-between; font-size: 10px; color: #666; margin-bottom: 4px;">
                        <span>${d.question.minLabel}</span>
                        <span>${d.question.maxLabel}</span>
                      </div>
                      <div style="display: flex; justify-content: space-between;">
                        ${Array.from({length:d.question.maxRating},(y,f)=>`
                          <div style="width: 20px; height: 20px; border-radius: 50%; border: 2px solid ${f+1===d.rating?"#2563eb":"#ccc"}; background: ${f+1===d.rating?"#2563eb":"white"}; display: flex; align-items: center; justify-content: center; font-size: 10px; color: ${f+1===d.rating?"white":"#666"};">
                            ${f+1}
                          </div>
                        `).join("")}
                      </div>
                    </div>
                  </div>
                </div>
              `).join("")}
          </div>
        </div>

        <!-- Footer -->
        <div style="border-top: 1px solid #ccc; padding-top: 15px; text-align: center; font-size: 11px; color: #666;">
          <p style="margin: 0 0 5px 0;">This report was automatically generated by the LMS Test Analytics System</p>
          <p style="margin: 0;">Report generated on ${N(new Date(o),"MMMM dd, yyyy 'at' HH:mm")}</p>
        </div>
      </div>
    `,document.body.appendChild(l);const m=yield L(l,{scale:2,logging:!1,useCORS:!0,backgroundColor:"#ffffff",width:l.offsetWidth,height:l.offsetHeight});document.body.removeChild(l);const i=new O({orientation:"portrait",unit:"mm",format:"a4"}),g=210,a=m.height*g/m.width,c=297;let u=a,n=0;for(i.addImage(m.toDataURL("image/png"),"PNG",0,n,g,a),u-=c;u>=0;)n=u-a,i.addPage(),i.addImage(m.toDataURL("image/png"),"PNG",0,n,g,a),u-=c;const p=`${r.fullName.replace(/\s+/g,"_")}_${t.test.title.replace(/\s+/g,"_")}_${N(new Date(t.createdAt),"yyyy-MM-dd")}.pdf`;i.save(p),C.success(`Test report for ${r.fullName} has been downloaded.`)}catch(l){throw console.error("Error generating PDF:",l),C.error("There was an error creating the PDF report. Please try again."),l}}),Q=({student:s,testResponse:r,generatedAt:t,onDownload:o})=>{const x=_.useRef(null),[l,m]=_.useState(!1),i=a=>{const c=F.find(u=>u.value===a);return c?c.label:`Rating ${a}`},g=()=>R(void 0,null,function*(){if(x.current){m(!0);try{const a=yield L(x.current,{scale:2,logging:!1,useCORS:!0,backgroundColor:"#ffffff"}),c=new O({orientation:"portrait",unit:"mm",format:"a4"}),u=210,n=a.height*u/a.width,p=297;let d=n,y=0;for(c.addImage(a.toDataURL("image/png"),"PNG",0,y,u,n),d-=p;d>=0;)y=d-n,c.addPage(),c.addImage(a.toDataURL("image/png"),"PNG",0,y,u,n),d-=p;const f=`${s.fullName.replace(/\s+/g,"_")}_${r.test.title.replace(/\s+/g,"_")}_${N(new Date(r.createdAt),"yyyy-MM-dd")}.pdf`;c.save(f),C({title:"PDF Generated Successfully",description:`Test report for ${s.fullName} has been downloaded.`}),o&&o()}catch(a){console.error("Error generating PDF:",a),C({title:"PDF Generation Failed",description:"There was an error creating the PDF report. Please try again.",variant:"destructive"})}finally{m(!1)}}});return W.useEffect(()=>{o&&g()},[]),e.jsx("div",{className:"hidden",children:e.jsxs("div",{ref:x,className:"bg-white p-8 max-w-4xl mx-auto",style:{fontFamily:"Arial, sans-serif"},children:[e.jsxs("div",{className:"border-b-2 border-gray-300 pb-6 mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:"Test Response Report"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm text-gray-600",children:[e.jsxs("div",{children:[e.jsx("strong",{children:"Generated:"})," ",N(new Date(t),"MMM dd, yyyy HH:mm")]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Report ID:"})," ",r.id.slice(0,8)]})]})]}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2",children:"Student Information"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("strong",{children:"Name:"})," ",s.fullName]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Email:"})," ",s.email]})]})]}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2",children:"Test Information"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{children:[e.jsx("strong",{children:"Test Title:"})," ",r.test.title]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Test Type:"})," ",r.test.type==="pre_test"?"Pre-Test":"Post-Test"]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Course:"})," ",r.test.module.course.title]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Module:"})," ",r.test.module.title]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Submission Date:"})," ",N(new Date(r.createdAt),"MMM dd, yyyy HH:mm")]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Questions Answered:"})," ",r.responses.length]})]}),r.test.description&&e.jsxs("div",{className:"mt-4",children:[e.jsx("strong",{children:"Test Description:"}),e.jsx("p",{className:"mt-1 text-gray-700",children:r.test.description})]})]}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h2",{className:"text-xl font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2",children:"Student Responses"}),e.jsx("div",{className:"space-y-6",children:r.responses.sort((a,c)=>a.question.questionNumber-c.question.questionNumber).map((a,c)=>e.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[e.jsx("div",{className:"mb-3",children:e.jsxs("div",{className:"flex items-start space-x-2",children:[e.jsxs("span",{className:"bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm font-medium",children:["Q",a.question.questionNumber]}),e.jsx("p",{className:"font-medium text-gray-800 leading-relaxed",children:a.question.question})]})}),e.jsxs("div",{className:"bg-gray-50 rounded p-3 ml-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-blue-700",children:i(a.rating)}),e.jsxs("div",{className:"text-sm text-gray-600",children:["Rating: ",a.rating," out of ",a.question.maxRating]})]}),e.jsxs("div",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium",children:[a.rating,"/",a.question.maxRating]})]}),e.jsxs("div",{className:"mt-3 pt-3 border-t border-gray-200",children:[e.jsxs("div",{className:"flex justify-between text-xs text-gray-500 mb-1",children:[e.jsx("span",{children:a.question.minLabel}),e.jsx("span",{children:a.question.maxLabel})]}),e.jsx("div",{className:"flex justify-between",children:Array.from({length:a.question.maxRating},(u,n)=>e.jsx("div",{className:`w-6 h-6 rounded-full border-2 flex items-center justify-center text-xs ${n+1===a.rating?"border-blue-500 bg-blue-500 text-white":"border-gray-300"}`,children:n+1},n+1))})]})]})]},a.questionId))})]}),e.jsxs("div",{className:"border-t border-gray-300 pt-4 text-center text-sm text-gray-500",children:[e.jsx("p",{children:"This report was automatically generated by the LMS Test Analytics System"}),e.jsxs("p",{children:["Report generated on ",N(new Date(t),"MMMM dd, yyyy 'at' HH:mm")]})]})]})})},xe=Object.freeze(Object.defineProperty({__proto__:null,default:Q,generateTestResponsePDF:me},Symbol.toStringTag,{value:"Module"})),ge=({responseId:s,onBack:r})=>{const{data:t,isLoading:o,error:x}=z({queryKey:["detailed-test-response",s],queryFn:()=>oe(s),refetchOnWindowFocus:!1}),l=()=>R(void 0,null,function*(){if(t)try{const{generateTestResponsePDF:i}=yield ae(()=>R(void 0,null,function*(){const{generateTestResponsePDF:g}=yield Promise.resolve().then(()=>xe);return{generateTestResponsePDF:g}}),void 0);yield i({student:t.student,testResponse:t,generatedAt:new Date().toISOString()})}catch(i){console.error("Error generating PDF:",i)}});if(o)return e.jsxs("div",{className:"flex items-center justify-center p-8",children:[e.jsx(E,{className:"w-8 h-8 animate-spin text-primary"}),e.jsx("span",{className:"ml-2",children:"Loading test details..."})]});if(x||!t)return e.jsxs("div",{className:"space-y-4",children:[e.jsxs(q,{variant:"ghost",onClick:r,className:"mb-4",children:[e.jsx(k,{className:"w-4 h-4 mr-2"}),"Back to Analytics"]}),e.jsx(j,{className:"border-destructive",children:e.jsxs(T,{children:[e.jsx(S,{className:"text-destructive",children:"Error Loading Test Details"}),e.jsx(P,{children:"Failed to load test response details. Please try again."})]})})]});const m=i=>{const g=F.find(a=>a.value===i);return g?g.label:`Rating ${i}`};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs(q,{variant:"ghost",onClick:r,children:[e.jsx(k,{className:"w-4 h-4 mr-2"}),"Back to Analytics"]}),e.jsxs(q,{onClick:l,className:"flex items-center",children:[e.jsx(G,{className:"w-4 h-4 mr-2"}),"Download PDF Report"]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs(j,{children:[e.jsx(T,{children:e.jsxs(S,{className:"flex items-center",children:[e.jsx(B,{className:"w-5 h-5 mr-2"}),"Student Information"]})}),e.jsx(b,{className:"space-y-3",children:e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:t.student.fullName}),e.jsx("div",{className:"text-sm text-muted-foreground",children:t.student.email})]})})]}),e.jsxs(j,{children:[e.jsx(T,{children:e.jsxs(S,{className:"flex items-center",children:[e.jsx(H,{className:"w-5 h-5 mr-2"}),"Test Information"]})}),e.jsxs(b,{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:t.test.title}),e.jsx($,{variant:t.test.type==="pre_test"?"default":"secondary",children:t.test.type==="pre_test"?"Pre-Test":"Post-Test"})]}),e.jsxs("div",{className:"text-sm text-muted-foreground",children:[e.jsx("div",{children:t.test.module.course.title}),e.jsx("div",{children:t.test.module.title})]})]})]}),e.jsxs(j,{children:[e.jsx(T,{children:e.jsxs(S,{className:"flex items-center",children:[e.jsx(J,{className:"w-5 h-5 mr-2"}),"Submission Details"]})}),e.jsxs(b,{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-muted-foreground",children:"Submitted"}),e.jsx("div",{className:"font-medium",children:N(new Date(t.createdAt),"MMM dd, yyyy")}),e.jsx("div",{className:"text-sm text-muted-foreground",children:N(new Date(t.createdAt),"HH:mm")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-muted-foreground",children:"Questions Answered"}),e.jsx("div",{className:"font-medium",children:t.responses.length})]})]})]})]}),t.test.description&&e.jsxs(j,{children:[e.jsx(T,{children:e.jsx(S,{children:"Test Description"})}),e.jsx(b,{children:e.jsx("p",{className:"text-muted-foreground",children:t.test.description})})]}),e.jsxs(j,{children:[e.jsxs(T,{children:[e.jsx(S,{children:"Student Responses"}),e.jsx(P,{children:"Detailed answers for each question in the test"})]}),e.jsx(b,{children:e.jsx("div",{className:"space-y-6",children:t.responses.sort((i,g)=>i.question.questionNumber-g.question.questionNumber).map((i,g)=>e.jsxs("div",{children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsxs($,{variant:"outline",className:"mt-1",children:["Q",i.question.questionNumber]}),e.jsx("div",{className:"flex-1",children:e.jsx("p",{className:"font-medium leading-relaxed",children:i.question.question})})]})}),e.jsx("div",{className:"ml-12",children:e.jsxs("div",{className:"bg-muted/50 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-primary",children:m(i.rating)}),e.jsxs("div",{className:"text-sm text-muted-foreground",children:["Rating: ",i.rating," out of ",i.question.maxRating]})]}),e.jsxs($,{variant:"secondary",children:[i.rating,"/",i.question.maxRating]})]}),e.jsxs("div",{className:"mt-3 pt-3 border-t border-border",children:[e.jsxs("div",{className:"flex justify-between text-xs text-muted-foreground",children:[e.jsx("span",{children:i.question.minLabel}),e.jsx("span",{children:i.question.maxLabel})]}),e.jsx("div",{className:"flex justify-between mt-1",children:Array.from({length:i.question.maxRating},(a,c)=>e.jsx("div",{className:`w-6 h-6 rounded-full border-2 flex items-center justify-center text-xs ${c+1===i.rating?"border-primary bg-primary text-white":"border-muted-foreground/30"}`,children:c+1},c+1))})]})]})})]}),g<t.responses.length-1&&e.jsx("div",{className:"border-t border-gray-200 my-6"})]},i.questionId))})})]}),e.jsx("div",{className:"hidden",children:e.jsx(Q,{student:t.student,testResponse:t,generatedAt:new Date().toISOString()})})]})},ue=s=>R(void 0,null,function*(){try{console.log("Generating overall analytics PDF...");const r=document.createElement("div");r.style.position="absolute",r.style.left="-9999px",r.style.top="-9999px",r.style.width="210mm",r.style.backgroundColor="#ffffff",r.style.fontFamily="Arial, sans-serif",r.style.padding="20mm",r.innerHTML=`
      <div style="max-width: 170mm; margin: 0 auto;">
        <!-- Header -->
        <div style="border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; text-align: center;">
          <h1 style="font-size: 28px; font-weight: bold; color: #333; margin: 0 0 10px 0;">Test Analytics Overview</h1>
          <h2 style="font-size: 18px; color: #666; margin: 0 0 15px 0;">Comprehensive Student Response Analysis</h2>
          <div style="font-size: 12px; color: #666;">
            <div><strong>Generated:</strong> ${N(new Date(s.generatedAt),"MMM dd, yyyy HH:mm")}</div>
            <div><strong>Total Students:</strong> ${s.totalStudents} | <strong>Total Responses:</strong> ${s.totalResponses}</div>
          </div>
        </div>

        <!-- Summary Statistics -->
        <div style="margin-bottom: 30px; background: #f8f9fa; padding: 15px; border-radius: 8px;">
          <h3 style="font-size: 16px; font-weight: 600; color: #333; margin: 0 0 10px 0;">Summary Statistics</h3>
          <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; font-size: 14px;">
            <div><strong>Tests Analyzed:</strong> ${s.testSummaries.length}</div>
            <div><strong>Average Responses per Test:</strong> ${s.testSummaries.length>0?Math.round(s.totalResponses/s.testSummaries.length):0}</div>
            <div><strong>Response Rate:</strong> ${s.totalStudents>0?Math.round(s.totalResponses/(s.totalStudents*s.testSummaries.length)*100):0}%</div>
          </div>
        </div>

        <!-- Test Summaries -->
        ${s.testSummaries.map(c=>`
          <div style="margin-bottom: 40px; page-break-inside: avoid;">
            <!-- Test Header -->
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
              <h3 style="font-size: 18px; font-weight: 600; color: #1565c0; margin: 0 0 5px 0;">${c.testTitle}</h3>
              <div style="font-size: 14px; color: #666;">
                <div><strong>Course:</strong> ${c.courseTitle} | <strong>Module:</strong> ${c.moduleTitle}</div>
                <div><strong>Type:</strong> ${c.testType==="pre_test"?"Pre-Test":"Post-Test"} | <strong>Total Responses:</strong> ${c.totalResponses}</div>
              </div>
            </div>

            <!-- Questions Analysis -->
            ${c.questions.map(u=>`
              <div style="margin-bottom: 25px; border: 1px solid #e0e0e0; border-radius: 8px; padding: 15px;">
                <!-- Question Header -->
                <div style="margin-bottom: 15px;">
                  <div style="display: flex; align-items: flex-start; gap: 8px;">
                    <span style="background: #f5f5f5; color: #333; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;">
                      Q${u.questionNumber}
                    </span>
                    <div style="flex: 1;">
                      <p style="font-weight: 500; color: #333; line-height: 1.4; margin: 0 0 8px 0; font-size: 14px;">
                        ${u.questionText}
                      </p>
                      <div style="font-size: 12px; color: #666;">
                        <strong>Responses:</strong> ${u.totalResponses} | <strong>Average Rating:</strong> ${u.averageRating}/4
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Response Breakdown -->
                <div style="background: #fafafa; padding: 12px; border-radius: 6px;">
                  <h4 style="font-size: 14px; font-weight: 600; color: #333; margin: 0 0 10px 0;">Response Distribution</h4>
                  ${u.optionBreakdown.map(n=>`
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; padding: 6px 0;">
                      <div style="flex: 1;">
                        <span style="font-weight: 500; color: #333;">${n.label}</span>
                      </div>
                      <div style="display: flex; align-items: center; gap: 10px;">
                        <div style="width: 100px; height: 12px; background: #e0e0e0; border-radius: 6px; overflow: hidden;">
                          <div style="width: ${n.percentage}%; height: 100%; background: ${n.rating===1?"#f44336":n.rating===2?"#ff9800":n.rating===3?"#4caf50":"#2196f3"}; transition: width 0.3s ease;"></div>
                        </div>
                        <span style="font-weight: 600; color: #333; min-width: 45px; text-align: right;">
                          ${n.count} (${n.percentage}%)
                        </span>
                      </div>
                    </div>
                  `).join("")}
                </div>
              </div>
            `).join("")}
          </div>
        `).join("")}

        <!-- Footer -->
        <div style="border-top: 1px solid #ccc; padding-top: 15px; text-align: center; font-size: 11px; color: #666; margin-top: 30px;">
          <p style="margin: 0 0 5px 0;">This comprehensive analytics report was automatically generated by the LMS Test Analytics System</p>
          <p style="margin: 0;">Report generated on ${N(new Date(s.generatedAt),"MMMM dd, yyyy 'at' HH:mm")}</p>
        </div>
      </div>
    `,document.body.appendChild(r);const t=yield L(r,{scale:2,logging:!1,useCORS:!0,backgroundColor:"#ffffff",width:r.offsetWidth,height:r.offsetHeight,onclone:c=>{const u=c.querySelector("div");u&&(u.style.fontFamily="Arial, sans-serif")}});document.body.removeChild(r);const o=new O({orientation:"portrait",unit:"mm",format:"a4"}),x=210,l=t.height*x/t.width,m=297;let i=l,g=0;for(o.addImage(t.toDataURL("image/png"),"PNG",0,g,x,l),i-=m;i>=0;)g=i-l,o.addPage(),o.addImage(t.toDataURL("image/png"),"PNG",0,g,x,l),i-=m;const a=`Test_Analytics_Overview_${N(new Date,"yyyy-MM-dd_HH-mm")}.pdf`;o.save(a),C.success("Overall analytics report has been downloaded successfully!")}catch(r){throw console.error("Error generating overall analytics PDF:",r),C.error("There was an error creating the analytics report. Please try again."),r}}),he=({data:s})=>{const r=t=>{switch(t){case 1:return"text-red-700";case 2:return"text-orange-700";case 3:return"text-green-700";case 4:return"text-blue-700";default:return"text-gray-700"}};return e.jsxs("div",{className:"space-y-6 max-h-[70vh] overflow-y-auto",children:[e.jsxs("div",{className:"text-center border-b pb-4",children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Overall Test Analytics Preview"}),e.jsxs("p",{className:"text-muted-foreground",children:["Generated on ",N(new Date(s.generatedAt),"MMM dd, yyyy HH:mm")]})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsx(j,{children:e.jsxs(b,{className:"p-4 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary",children:s.totalStudents}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Total Students"})]})}),e.jsx(j,{children:e.jsxs(b,{className:"p-4 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary",children:s.totalResponses}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Total Responses"})]})}),e.jsx(j,{children:e.jsxs(b,{className:"p-4 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary",children:s.testSummaries.length}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Tests Analyzed"})]})}),e.jsx(j,{children:e.jsxs(b,{className:"p-4 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary",children:s.testSummaries.length>0?Math.round(s.totalResponses/s.testSummaries.length):0}),e.jsx("div",{className:"text-sm text-muted-foreground",children:"Avg per Test"})]})})]}),e.jsxs("div",{className:"space-y-6",children:[s.testSummaries.slice(0,3).map((t,o)=>e.jsxs(j,{children:[e.jsx(T,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx(S,{className:"text-lg",children:t.testTitle}),e.jsxs(P,{children:[t.courseTitle," • ",t.moduleTitle]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx($,{variant:t.testType==="pre_test"?"default":"secondary",children:t.testType==="pre_test"?"Pre-Test":"Post-Test"}),e.jsxs($,{variant:"outline",children:[t.totalResponses," responses"]})]})]})}),e.jsx(b,{children:e.jsxs("div",{className:"space-y-4",children:[t.questions.slice(0,2).map((x,l)=>e.jsxs("div",{className:"border rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-start gap-2 mb-3",children:[e.jsxs($,{variant:"outline",className:"mt-1",children:["Q",x.questionNumber]}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"font-medium text-sm leading-relaxed",children:x.questionText.length>100?`${x.questionText.substring(0,100)}...`:x.questionText}),e.jsxs("p",{className:"text-xs text-muted-foreground mt-1",children:[x.totalResponses," responses • Avg: ",x.averageRating,"/4"]})]})]}),e.jsx("div",{className:"space-y-2",children:x.optionBreakdown.map(m=>e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-24 text-xs font-medium",children:m.label}),e.jsx("div",{className:"flex-1",children:e.jsx(le,{value:m.percentage,className:"h-2"})}),e.jsxs("div",{className:`text-xs font-medium min-w-[60px] text-right ${r(m.rating)}`,children:[m.count," (",m.percentage,"%)"]})]},m.rating))})]},x.questionId)),t.questions.length>2&&e.jsxs("div",{className:"text-center text-sm text-muted-foreground",children:["... and ",t.questions.length-2," more questions"]})]})})]},t.testId)),s.testSummaries.length>3&&e.jsx(j,{children:e.jsx(b,{className:"p-6 text-center",children:e.jsxs("p",{className:"text-muted-foreground",children:["... and ",s.testSummaries.length-3," more tests will be included in the full report"]})})})]}),e.jsx("div",{className:"text-center text-sm text-muted-foreground border-t pt-4",children:e.jsx("p",{children:"This is a preview. The full PDF report will contain detailed analysis for all tests and questions."})})]})},Te=()=>{const[s,r]=_.useState(""),[t,o]=_.useState(null),[x,l]=_.useState(null),[m,i]=_.useState(!1),[g,a]=_.useState(!1),[c,u]=_.useState(null),{data:n,isLoading:p,error:d}=z({queryKey:["test-analytics-overview"],queryFn:de,refetchOnWindowFocus:!1}),y=(n==null?void 0:n.studentsWithTests.filter(h=>h.student.fullName.toLowerCase().includes(s.toLowerCase())||h.student.email.toLowerCase().includes(s.toLowerCase())))||[],f=(h,v)=>{o(h),l(v)},w=()=>{o(null),l(null)},I=()=>R(void 0,null,function*(){console.log("Download Overall Analytics button clicked!"),i(!0);try{console.log("Calling getOverallAnalyticsExport...");const h=yield ce();console.log("Received overall data:",h),u(h),a(!0)}catch(h){console.error("Error generating overall analytics:",h)}finally{i(!1)}}),M=()=>R(void 0,null,function*(){if(c){a(!1);try{console.log("Starting PDF generation..."),yield ue(c),console.log("PDF generation completed!")}catch(h){console.error("PDF generation failed:",h)}u(null)}}),A=()=>{a(!1),u(null)};return p?e.jsxs("div",{className:"flex items-center justify-center p-8",children:[e.jsx(E,{className:"w-8 h-8 animate-spin text-primary"}),e.jsx("span",{className:"ml-2",children:"Loading test analytics..."})]}):d?e.jsx("div",{className:"p-8",children:e.jsx(j,{className:"border-destructive",children:e.jsxs(T,{children:[e.jsx(S,{className:"text-destructive",children:"Error Loading Analytics"}),e.jsx(P,{children:"Failed to load test analytics data. Please try again."})]})})}):t&&x?e.jsx(ge,{responseId:x,onBack:w}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Test Analytics Dashboard"}),e.jsx("p",{className:"text-muted-foreground",children:"Comprehensive analysis of student test responses"})]}),e.jsxs(q,{onClick:I,disabled:m||p||!n,className:"flex items-center gap-2",children:[m?e.jsx(E,{className:"h-4 w-4 animate-spin"}):e.jsx(V,{className:"h-4 w-4"}),m?"Generating Report...":"Download Overall Analytics"]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs(j,{children:[e.jsxs(T,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(S,{className:"text-sm font-medium",children:"Total Students"}),e.jsx(K,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(b,{children:[e.jsx("div",{className:"text-2xl font-bold",children:(n==null?void 0:n.totalStudents)||0}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Students who have completed tests"})]})]}),e.jsxs(j,{children:[e.jsxs(T,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(S,{className:"text-sm font-medium",children:"Total Test Submissions"}),e.jsx(H,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(b,{children:[e.jsx("div",{className:"text-2xl font-bold",children:(n==null?void 0:n.totalTestsCompleted)||0}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Across all students and modules"})]})]}),e.jsxs(j,{children:[e.jsxs(T,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(S,{className:"text-sm font-medium",children:"Average Tests per Student"}),e.jsx(H,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(b,{children:[e.jsx("div",{className:"text-2xl font-bold",children:n!=null&&n.totalStudents?Math.round(n.totalTestsCompleted/n.totalStudents*10)/10:0}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Tests completed per student"})]})]})]}),e.jsxs(j,{children:[e.jsxs(T,{children:[e.jsx(S,{children:"Student Test Records"}),e.jsx(P,{children:"View detailed test responses for individual students"})]}),e.jsxs(b,{children:[e.jsxs("div",{className:"flex items-center space-x-2 mb-4",children:[e.jsx(X,{className:"h-4 w-4 text-muted-foreground"}),e.jsx(Z,{placeholder:"Search students by name or email...",value:s,onChange:h=>r(h.target.value),className:"max-w-sm"})]}),e.jsx("div",{className:"space-y-4",children:y.length===0?e.jsx("div",{className:"text-center py-8 text-muted-foreground",children:s?"No students found matching your search.":"No test submissions found."}):y.map(h=>e.jsxs(j,{className:"border-l-4 border-l-primary",children:[e.jsx(T,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx(S,{className:"text-lg",children:h.student.fullName}),e.jsx(P,{children:h.student.email})]}),e.jsxs($,{variant:"secondary",children:[h.totalTestsCompleted," test",h.totalTestsCompleted!==1?"s":""]})]})}),e.jsx(b,{children:e.jsx("div",{className:"space-y-3",children:h.testSubmissions.map(v=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-muted/50 rounded-lg",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"font-medium",children:v.testTitle}),e.jsxs("div",{className:"text-sm text-muted-foreground",children:[v.courseTitle," • ",v.moduleTitle]}),e.jsxs("div",{className:"text-xs text-muted-foreground",children:["Submitted: ",N(new Date(v.submittedAt),"MMM dd, yyyy HH:mm")]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx($,{variant:v.testType==="pre_test"?"default":"secondary",children:v.testType==="pre_test"?"Pre-Test":"Post-Test"}),e.jsxs(q,{size:"sm",variant:"outline",onClick:()=>f(h,v.id),children:[e.jsx(Y,{className:"h-4 w-4 mr-1"}),"View Details"]})]})]},v.id))})})]},h.student.id))})]})]}),e.jsx(ee,{open:g,onOpenChange:a,children:e.jsxs(te,{className:"max-w-4xl max-h-[90vh]",children:[e.jsxs(se,{children:[e.jsx(re,{children:"Overall Analytics Preview"}),e.jsx(ie,{children:"Review the analytics summary before downloading the full PDF report"})]}),c&&e.jsx(he,{data:c}),e.jsxs(ne,{children:[e.jsx(q,{variant:"outline",onClick:A,children:"Cancel"}),e.jsxs(q,{onClick:M,className:"flex items-center gap-2",children:[e.jsx(G,{className:"h-4 w-4"}),"Download Full PDF Report"]})]})]})})]})};export{Te as T};
