[build]
  command = "node netlify-env-inject.cjs && npm run build:prod"
  publish = "dist"
  environment = { NODE_VERSION = "18" }

[[redirects]]
  from = "/auth/callback"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/auth/callback/*"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  force = true

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Cache-Control = "public, max-age=0, must-revalidate"
    Access-Control-Allow-Origin = "*"

[[headers]]
  for = "/index.html"
  [headers.values]
    Cache-Control = "no-cache"

[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.js"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"

[[headers]]
  for = "/assets/*.js"
  [headers.values]
    Content-Type = "application/javascript; charset=utf-8"

[[headers]]
  for = "*.css"
  [headers.values]
    Content-Type = "text/css; charset=utf-8"

[[headers]]
  for = "/assets/*.css"
  [headers.values]
    Content-Type = "text/css; charset=utf-8"

[[headers]]
  for = "/*.svg"
  [headers.values]
    Content-Type = "image/svg+xml"

[[headers]]
  for = "/assets/*.svg"
  [headers.values]
    Content-Type = "image/svg+xml"
