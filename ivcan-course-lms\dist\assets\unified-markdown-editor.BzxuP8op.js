var ge=Object.defineProperty,pe=Object.defineProperties;var be=Object.getOwnPropertyDescriptors;var K=Object.getOwnPropertySymbols;var xe=Object.prototype.hasOwnProperty,fe=Object.prototype.propertyIsEnumerable;var F=(s,e,r)=>e in s?ge(s,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):s[e]=r,M=(s,e)=>{for(var r in e||(e={}))xe.call(e,r)&&F(s,r,e[r]);if(K)for(var r of K(e))fe.call(e,r)&&F(s,r,e[r]);return s},Q=(s,e)=>pe(s,be(e));var P=(s,e,r)=>F(s,typeof e!="symbol"?e+"":e,r);var G=(s,e,r)=>new Promise((l,i)=>{var n=g=>{try{p(r.next(g))}catch(C){i(C)}},u=g=>{try{p(r.throw(g))}catch(C){i(C)}},p=g=>g.done?l(g.value):Promise.resolve(g.value).then(n,u);p((r=r.apply(s,e)).next())});import{bV as se,j as t,bW as re,m as ae,a9 as ke,bX as le,F as Ce,bY as je,aW as ve,E as we,Y as Ne,aF as ie,r as m,bv as ye,$ as J,bQ as Te,bZ as $e,aq as Le,bR as O,bw as Ae,bx as He,by as Se,bz as Ie,bB as Me,bC as Re,bD as ze,bE as Ue,bF as Be,bG as De,bH as Ee,bI as qe,bA as Fe,b0 as Pe,bJ as Oe,bK as We,bM as Ve,bN as Ye}from"./vendor-react.BcAa1DKr.js";import{aC as ne,aD as oe,ai as Ke,aj as Qe,ak as Ge,az as Je,al as Xe,am as Ze,an as _e,ao as et,ap as tt,aE as st,aq as rt,ar as at,as as lt,at as it,au as nt,av as ot,aw as ct,ay as dt,a2 as W}from"./vendor.DQpuTRuB.js";import{T as X,a as Z,b as A,c as H}from"./tabs.B0SF6qIv.js";import{c as _,D as ut,h as mt,i as ht,j as gt,I as R,l as pt,B as V,T as bt,d as xt,e as ft,f as kt}from"./index.BLDhDn0D.js";import{M as ee}from"./markdown-preview.Bw0U-NJA.js";import{m as te}from"./content-converter.L-GziWIP.js";import{v as Ct,c as jt,u as vt}from"./tiptap-image-upload.B_U9gNrF.js";const wt=({node:s,updateAttributes:e,deleteNode:r})=>{const{open:l,summary:i}=s.attrs;return t.jsx(re,{className:"details-wrapper",children:t.jsxs("details",{open:l,className:"border border-border rounded-lg p-4 my-4 bg-muted/30",onToggle:n=>{e({open:n.target.open})},children:[t.jsxs("summary",{className:"cursor-pointer flex items-center gap-2 font-medium text-foreground hover:text-primary transition-colors",contentEditable:!1,children:[l?t.jsx(ae,{className:"h-4 w-4"}):t.jsx(ke,{className:"h-4 w-4"}),t.jsx("span",{contentEditable:!0,suppressContentEditableWarning:!0,onBlur:n=>{e({summary:n.target.textContent})},children:i||"Click to expand"})]}),t.jsx("div",{className:"mt-3 pl-6",children:t.jsx(le,{})})]})})},Nt=ne.create({name:"details",addOptions(){return{HTMLAttributes:{}}},group:"block",content:"block+",defining:!0,addAttributes(){return{summary:{default:"Click to expand",parseHTML:s=>{const e=s.querySelector("summary");return(e==null?void 0:e.textContent)||"Click to expand"},renderHTML:s=>({})},open:{default:!1,parseHTML:s=>s.hasAttribute("open"),renderHTML:s=>s.open?{open:""}:{}}}},parseHTML(){return[{tag:"details"}]},renderHTML({HTMLAttributes:s,node:e}){const{summary:r,open:l}=e.attrs;return["details",oe(this.options.HTMLAttributes,s,M({},l&&{open:""})),["summary",{},r||"Click to expand"],["div",{},0]]},addNodeView(){return se(wt)},addCommands(){return{setDetails:(s={})=>({commands:e})=>e.wrapIn(this.name,s),toggleDetails:()=>({commands:s})=>s.toggleWrap(this.name),unsetDetails:()=>({commands:s})=>s.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-d":()=>this.editor.commands.toggleDetails()}}}),yt=({node:s,updateAttributes:e})=>{const{type:r,title:l}=s.attrs,n=(u=>{switch(u){case"info":return{icon:t.jsx(ie,{className:"h-5 w-5"}),className:"border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950/30",iconColor:"text-blue-600 dark:text-blue-400",titleColor:"text-blue-800 dark:text-blue-200"};case"warning":return{icon:t.jsx(Ne,{className:"h-5 w-5"}),className:"border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950/30",iconColor:"text-yellow-600 dark:text-yellow-400",titleColor:"text-yellow-800 dark:text-yellow-200"};case"success":return{icon:t.jsx(we,{className:"h-5 w-5"}),className:"border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950/30",iconColor:"text-green-600 dark:text-green-400",titleColor:"text-green-800 dark:text-green-200"};case"error":return{icon:t.jsx(ve,{className:"h-5 w-5"}),className:"border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/30",iconColor:"text-red-600 dark:text-red-400",titleColor:"text-red-800 dark:text-red-200"};case"tip":return{icon:t.jsx(je,{className:"h-5 w-5"}),className:"border-purple-200 bg-purple-50 dark:border-purple-800 dark:bg-purple-950/30",iconColor:"text-purple-600 dark:text-purple-400",titleColor:"text-purple-800 dark:text-purple-200"};case"note":default:return{icon:t.jsx(Ce,{className:"h-5 w-5"}),className:"border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800/30",iconColor:"text-gray-600 dark:text-gray-400",titleColor:"text-gray-800 dark:text-gray-200"}}})(r);return t.jsx(re,{className:"callout-wrapper",children:t.jsxs("div",{className:`border-l-4 rounded-r-lg p-4 my-4 ${n.className}`,children:[t.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[t.jsx("span",{className:n.iconColor,children:n.icon}),t.jsx("span",{className:`font-semibold ${n.titleColor}`,contentEditable:!0,suppressContentEditableWarning:!0,onBlur:u=>{e({title:u.target.textContent})},children:l||r.charAt(0).toUpperCase()+r.slice(1)})]}),t.jsx("div",{className:"pl-8",children:t.jsx(le,{})})]})})},Tt=ne.create({name:"callout",addOptions(){return{HTMLAttributes:{},types:["info","warning","success","error","tip","note"]}},group:"block",content:"block+",defining:!0,addAttributes(){return{type:{default:"info",parseHTML:s=>s.getAttribute("data-type")||"info",renderHTML:s=>({"data-type":s.type})},title:{default:null,parseHTML:s=>s.getAttribute("data-title"),renderHTML:s=>s.title?{"data-title":s.title}:{}}}},parseHTML(){return[{tag:"div[data-callout]"},{tag:"blockquote",getAttrs:s=>{const r=(s.textContent||"").match(/^\[!(INFO|WARNING|SUCCESS|ERROR|TIP|NOTE)\]/i);return r?{type:r[1].toLowerCase()}:!1}}]},renderHTML({HTMLAttributes:s,node:e}){const{type:r,title:l}=e.attrs;return["div",oe(this.options.HTMLAttributes,s,Q(M({"data-callout":"","data-type":r},l&&{"data-title":l}),{class:"callout"})),0]},addNodeView(){return se(yt)},addCommands(){return{setCallout:(s={})=>({commands:e})=>e.wrapIn(this.name,s),toggleCallout:(s="info")=>({commands:e})=>e.toggleWrap(this.name,{type:s}),unsetCallout:()=>({commands:s})=>s.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-c":()=>this.editor.commands.toggleCallout()}}});class ce{constructor(e={}){P(this,"options");P(this,"turndownService");this.options=M({tightLists:!1,preserveWhitespace:!1,gfmTables:!0,taskLists:!0,details:!0,callouts:!0,strikethrough:!0,codeBlocks:!0},e),this.turndownService=new Ke({headingStyle:"atx",hr:"---",bulletListMarker:"-",codeBlockStyle:"fenced",fence:"```",emDelimiter:"*",strongDelimiter:"**",linkStyle:"inlined",linkReferenceStyle:"full",preformattedCode:!1}),this.turndownService.use(Qe),this.addCustomRules()}addCustomRules(){this.options.taskLists&&(this.turndownService.addRule("taskList",{filter:e=>e.nodeName==="UL"&&e.classList.contains("task-list"),replacement:e=>e}),this.turndownService.addRule("taskItem",{filter:e=>e.nodeName==="LI"&&e.classList.contains("task-item"),replacement:(e,r)=>{const l=r.querySelector('input[type="checkbox"]'),i=l!=null&&l.checked?"x":" ",n=e.replace(/^\s*\[[ x]\]\s*/,"").trim();return`- [${i}] ${n}
`}})),this.options.details&&this.turndownService.addRule("details",{filter:"details",replacement:(e,r)=>{var u;const l=r.querySelector("summary"),i=((u=l==null?void 0:l.textContent)==null?void 0:u.trim())||"Details",n=e.replace(i,"").trim();return`<details>
<summary>${i}</summary>

${n}
</details>

`}}),this.options.callouts&&this.turndownService.addRule("callout",{filter:e=>e.nodeName==="DIV"&&e.hasAttribute("data-callout"),replacement:(e,r)=>{const l=r.getAttribute("data-type")||"info",i=r.getAttribute("data-title")||l.charAt(0).toUpperCase()+l.slice(1),u=e.trim().split(`
`).map(p=>`> ${p}`).join(`
`);return`> [!${l.toUpperCase()}] ${i}
${u}

`}}),this.options.strikethrough&&this.turndownService.addRule("strikethrough",{filter:["del","s","strike"],replacement:e=>`~~${e}~~`}),this.options.codeBlocks&&this.turndownService.addRule("codeBlock",{filter:e=>e.nodeName==="PRE"&&e.querySelector("code"),replacement:(e,r)=>{const l=r.querySelector("code"),i=this.extractLanguage(l),n=(l==null?void 0:l.textContent)||e;return`
\`\`\`${i}
${n}
\`\`\`

`}}),this.turndownService.addRule("highlight",{filter:["mark","span.highlight"],replacement:e=>`==${e}==`}),this.turndownService.addRule("underline",{filter:"u",replacement:e=>`<u>${e}</u>`}),this.turndownService.addRule("youtube",{filter:e=>e.nodeName==="DIV"&&e.classList.contains("youtube-embed"),replacement:(e,r)=>{const l=r.querySelector("iframe"),i=(l==null?void 0:l.getAttribute("src"))||"",n=this.extractYouTubeId(i);return n?`[![YouTube](https://img.youtube.com/vi/${n}/0.jpg)](https://www.youtube.com/watch?v=${n})

`:""}}),this.turndownService.addRule("image",{filter:"img",replacement:(e,r)=>{const l=r.getAttribute("src")||"",i=r.getAttribute("alt")||"",n=r.getAttribute("title");return n?`![${i}](${l} "${n}")`:`![${i}](${l})`}})}extractLanguage(e){if(!e)return"";const r=Array.from(e.classList);for(const i of r){if(i.startsWith("language-"))return i.replace("language-","");if(i.startsWith("hljs-"))return i.replace("hljs-","")}const l=e.getAttribute("data-language");return l||""}extractYouTubeId(e){const r=e.match(/(?:youtube\.com\/embed\/|youtu\.be\/)([^?&]+)/);return(r==null?void 0:r[1])||""}serialize(e){const r=this.nodeToHtml(e),l=this.turndownService.turndown(r);return this.cleanMarkdown(l)}nodeToHtml(e){return this.serializeNodeToHtml(e)}serializeNodeToHtml(e){switch(e.type.name){case"doc":return this.serializeChildrenToHtml(e);case"paragraph":const r=this.serializeChildrenToHtml(e);return r?`<p>${r}</p>`:"";case"heading":const l=e.attrs.level||1,i=this.serializeChildrenToHtml(e);return`<h${l}>${i}</h${l}>`;case"bulletList":const n=this.serializeChildrenToHtml(e);return`<ul${e.attrs.tight===!1?' class="task-list"':""}>${n}</ul>`;case"orderedList":return`<ol>${this.serializeChildrenToHtml(e)}</ol>`;case"listItem":return`<li>${this.serializeChildrenToHtml(e)}</li>`;case"taskItem":const C=e.attrs.checked,z=this.serializeChildrenToHtml(e);return`<li class="task-item"><input type="checkbox"${C?" checked":""}> ${z}</li>`;case"codeBlock":case"codeBlockLowlight":const j=e.attrs.language||"",U=e.textContent;return`<pre><code${j?` class="language-${j}"`:""}>${U}</code></pre>`;case"blockquote":return`<blockquote>${this.serializeChildrenToHtml(e)}</blockquote>`;case"horizontalRule":return"<hr>";case"hardBreak":return"<br>";case"image":const w=e.attrs.src||"",$=e.attrs.alt||"",S=e.attrs.title;return`<img src="${w}" alt="${$}"${S?` title="${S}"`:""}>`;case"table":return`<table>${this.serializeChildrenToHtml(e)}</table>`;case"tableRow":return`<tr>${this.serializeChildrenToHtml(e)}</tr>`;case"tableHeader":return`<th>${this.serializeChildrenToHtml(e)}</th>`;case"tableCell":return`<td>${this.serializeChildrenToHtml(e)}</td>`;case"details":const L=e.attrs.summary||"Details",x=this.serializeChildrenToHtml(e);return`<details${e.attrs.open?" open":""}><summary>${L}</summary>${x}</details>`;case"callout":const f=e.attrs.type||"info",k=e.attrs.title||f.charAt(0).toUpperCase()+f.slice(1),I=this.serializeChildrenToHtml(e);return`<div data-callout data-type="${f}" data-title="${k}">${I}</div>`;case"text":let d=e.text||"";if(e.marks)for(const T of e.marks)switch(T.type.name){case"bold":d=`<strong>${d}</strong>`;break;case"italic":d=`<em>${d}</em>`;break;case"underline":d=`<u>${d}</u>`;break;case"strike":d=`<del>${d}</del>`;break;case"code":d=`<code>${d}</code>`;break;case"highlight":d=`<mark>${d}</mark>`;break;case"link":const E=T.attrs.href||"",a=T.attrs.title||"";d=`<a href="${E}"${a?` title="${a}"`:""}>${d}</a>`;break}return d;default:return this.serializeChildrenToHtml(e)}}serializeChildrenToHtml(e){let r="";return e.forEach(l=>{r+=this.serializeNodeToHtml(l)}),r}cleanMarkdown(e){return e=e.replace(/\n{3,}/g,`

`),e=e.replace(/^(#{1,6}\s.+)$/gm,`
$1
`),e=e.replace(/^(\s*[-*+]\s)/gm,"$1"),e=e.replace(/^(\s*-\s*\[[ x]\]\s*)/gm,"$1"),e=e.replace(/\|\s*\|\s*\|/g,"| |"),e=e.replace(/(\n\|.*\|.*\n)/g,`
$1
`),e.trim()}}function $t(s,e){return new ce(e).serialize(s)}function Lt(s,e){return new ce(e).turndownService.turndown(s)}const At=Ge(Je);function Dt({initialContent:s="",onChange:e,placeholder:r="Start writing your content...",className:l="",minHeight:i=400,autoFocus:n=!1,courseId:u,moduleId:p,showToolbar:g=!0,showPreview:C=!0,mode:z="editor",theme:j="github"}){const[U,Y]=m.useState(z),[w,$]=m.useState(s),[S,N]=m.useState(!1),[B,D]=m.useState(!1),[y,L]=m.useState(""),[x,f]=m.useState(""),[k,I]=m.useState(null),[d,T]=m.useState(null),E=m.useRef(null),a=ye({extensions:[Xe.configure({codeBlock:!1}),Ze.configure({lowlight:At,defaultLanguage:"plaintext",HTMLAttributes:{class:"rounded-md bg-muted/70 p-4 overflow-x-auto"}}),_e.configure({HTMLAttributes:{class:"rounded-lg max-w-full h-auto"}}),et.configure({openOnClick:!1,HTMLAttributes:{rel:"noopener noreferrer",target:"_blank",class:"text-blue-600 hover:text-blue-800 underline"}}),tt,st,rt.configure({HTMLAttributes:{class:"bg-yellow-200 dark:bg-yellow-800"}}),at,lt.configure({nested:!0}),it.configure({resizable:!0,HTMLAttributes:{class:"border-collapse border border-border rounded-md overflow-hidden"}}),nt,ot.configure({HTMLAttributes:{class:"bg-muted font-semibold"}}),ct.configure({HTMLAttributes:{class:"border border-border px-3 py-2"}}),Nt,Tt,dt.configure({placeholder:r})],content:te(s),onUpdate:({editor:o})=>{try{const h=o.state.doc,b=$t(h);$(b),e==null||e(b)}catch(h){console.error("Error converting to markdown:",h);const b=o.getHTML(),v=Lt(b);$(v),e==null||e(v)}},editorProps:{attributes:{class:_("prose prose-sm max-w-none focus:outline-none dark:prose-invert",`min-h-[${i}px] p-4`,j==="github"&&"github-markdown-editor",j==="obsidian"&&"obsidian-editor",j==="minimal"&&"minimal-editor"),style:`min-height: ${i}px`}},autofocus:n});m.useEffect(()=>{if(a&&s!==w){const o=te(s);a.commands.setContent(o),$(s)}},[s,a,w]);const c=({onClick:o,disabled:h,title:b,children:v,isActive:he=!1})=>t.jsx(bt,{children:t.jsxs(xt,{children:[t.jsx(ft,{asChild:!0,children:t.jsx(V,{variant:he?"default":"ghost",size:"sm",onClick:o,disabled:h,className:"h-8 w-8 p-0",children:v})}),t.jsx(kt,{children:t.jsx("p",{children:b})})]})}),de=m.useCallback(o=>{var b;const h=(b=o.target.files)==null?void 0:b[0];if(h){const v=Ct(h);v.valid?(I(h),jt(h).then(T).catch(console.error)):W.error(v.message)}},[]),ue=m.useCallback(()=>G(this,null,function*(){if(!(!k||!a))try{D(!0);const o=yield vt(k,{courseId:u,moduleId:p,onProgress:h=>{}});a.chain().focus().setImage({src:o,alt:x||k.name}).run(),N(!1),I(null),T(null),L(""),f(""),W.success("Image uploaded successfully!")}catch(o){console.error("Error uploading image:",o),W.error("Failed to upload image. Please try again.")}finally{D(!1)}}),[k,a,x,u,p]),me=m.useCallback(()=>{!a||!y||(a.chain().focus().setImage({src:y,alt:x||"Image"}).run(),N(!1),L(""),f(""))},[a,y,x]);if(!a)return t.jsx("div",{className:"flex items-center justify-center h-64",children:t.jsx(J,{className:"h-8 w-8 animate-spin"})});const q=()=>g?t.jsxs("div",{className:"border-b border-border p-2 flex flex-wrap gap-1",children:[t.jsxs("div",{className:"flex gap-1 border-r border-border pr-2 mr-2",children:[t.jsx(c,{onClick:()=>a.chain().focus().toggleBold().run(),disabled:!a.can().chain().focus().toggleBold().run(),isActive:a.isActive("bold"),title:"Bold (Ctrl+B)",children:t.jsx(Ae,{className:"h-4 w-4"})}),t.jsx(c,{onClick:()=>a.chain().focus().toggleItalic().run(),disabled:!a.can().chain().focus().toggleItalic().run(),isActive:a.isActive("italic"),title:"Italic (Ctrl+I)",children:t.jsx(He,{className:"h-4 w-4"})}),t.jsx(c,{onClick:()=>a.chain().focus().toggleUnderline().run(),disabled:!a.can().chain().focus().toggleUnderline().run(),isActive:a.isActive("underline"),title:"Underline (Ctrl+U)",children:t.jsx(Se,{className:"h-4 w-4"})}),t.jsx(c,{onClick:()=>a.chain().focus().toggleStrike().run(),disabled:!a.can().chain().focus().toggleStrike().run(),isActive:a.isActive("strike"),title:"Strikethrough",children:t.jsx(Ie,{className:"h-4 w-4"})}),t.jsx(c,{onClick:()=>a.chain().focus().toggleHighlight().run(),disabled:!a.can().chain().focus().toggleHighlight().run(),isActive:a.isActive("highlight"),title:"Highlight",children:t.jsx(Me,{className:"h-4 w-4"})})]}),t.jsxs("div",{className:"flex gap-1 border-r border-border pr-2 mr-2",children:[t.jsx(c,{onClick:()=>a.chain().focus().toggleHeading({level:1}).run(),isActive:a.isActive("heading",{level:1}),title:"Heading 1",children:t.jsx(Re,{className:"h-4 w-4"})}),t.jsx(c,{onClick:()=>a.chain().focus().toggleHeading({level:2}).run(),isActive:a.isActive("heading",{level:2}),title:"Heading 2",children:t.jsx(ze,{className:"h-4 w-4"})}),t.jsx(c,{onClick:()=>a.chain().focus().toggleHeading({level:3}).run(),isActive:a.isActive("heading",{level:3}),title:"Heading 3",children:t.jsx(Ue,{className:"h-4 w-4"})})]}),t.jsxs("div",{className:"flex gap-1 border-r border-border pr-2 mr-2",children:[t.jsx(c,{onClick:()=>a.chain().focus().toggleBulletList().run(),isActive:a.isActive("bulletList"),title:"Bullet List",children:t.jsx(Be,{className:"h-4 w-4"})}),t.jsx(c,{onClick:()=>a.chain().focus().toggleOrderedList().run(),isActive:a.isActive("orderedList"),title:"Numbered List",children:t.jsx(De,{className:"h-4 w-4"})}),t.jsx(c,{onClick:()=>a.chain().focus().toggleTaskList().run(),isActive:a.isActive("taskList"),title:"Task List",children:t.jsx(Ee,{className:"h-4 w-4"})})]}),t.jsxs("div",{className:"flex gap-1 border-r border-border pr-2 mr-2",children:[t.jsx(c,{onClick:()=>a.chain().focus().toggleBlockquote().run(),isActive:a.isActive("blockquote"),title:"Quote",children:t.jsx(qe,{className:"h-4 w-4"})}),t.jsx(c,{onClick:()=>a.chain().focus().toggleCodeBlock().run(),isActive:a.isActive("codeBlock"),title:"Code Block",children:t.jsx(Fe,{className:"h-4 w-4"})}),t.jsx(c,{onClick:()=>N(!0),title:"Insert Image",children:t.jsx(Pe,{className:"h-4 w-4"})}),t.jsx(c,{onClick:()=>{const o=window.prompt("Enter link URL:");o&&a.chain().focus().setLink({href:o}).run()},isActive:a.isActive("link"),title:"Insert Link",children:t.jsx(Oe,{className:"h-4 w-4"})})]}),t.jsxs("div",{className:"flex gap-1 border-r border-border pr-2 mr-2",children:[t.jsx(c,{onClick:()=>a.chain().focus().insertTable({rows:3,cols:3,withHeaderRow:!0}).run(),title:"Insert Table",children:t.jsx(We,{className:"h-4 w-4"})}),t.jsx(c,{onClick:()=>a.chain().focus().setDetails().run(),title:"Insert Collapsible Section",children:t.jsx(ae,{className:"h-4 w-4"})}),t.jsx(c,{onClick:()=>a.chain().focus().setCallout({type:"info"}).run(),title:"Insert Callout",children:t.jsx(ie,{className:"h-4 w-4"})})]}),t.jsxs("div",{className:"flex gap-1",children:[t.jsx(c,{onClick:()=>a.chain().focus().undo().run(),disabled:!a.can().undo(),title:"Undo",children:t.jsx(Ve,{className:"h-4 w-4"})}),t.jsx(c,{onClick:()=>a.chain().focus().redo().run(),disabled:!a.can().redo(),title:"Redo",children:t.jsx(Ye,{className:"h-4 w-4"})})]})]}):null;return t.jsxs("div",{className:_("border border-border rounded-lg overflow-hidden",l),children:[C?t.jsxs(X,{value:U,onValueChange:o=>Y(o),children:[t.jsxs(Z,{className:"grid w-full grid-cols-3",children:[t.jsxs(A,{value:"editor",className:"flex items-center gap-2",children:[t.jsx(Te,{className:"h-4 w-4"}),"Editor"]}),t.jsxs(A,{value:"split",className:"flex items-center gap-2",children:[t.jsx($e,{className:"h-4 w-4"}),"Split"]}),t.jsxs(A,{value:"preview",className:"flex items-center gap-2",children:[t.jsx(Le,{className:"h-4 w-4"}),"Preview"]})]}),t.jsxs(H,{value:"editor",className:"m-0",children:[q(),t.jsx(O,{editor:a})]}),t.jsxs(H,{value:"split",className:"m-0",children:[q(),t.jsxs("div",{className:"grid grid-cols-2 h-full",children:[t.jsx("div",{className:"border-r border-border",children:t.jsx(O,{editor:a})}),t.jsx("div",{className:"h-full overflow-auto",children:t.jsx("div",{className:"p-6",style:{minHeight:`${i}px`},children:t.jsx(ee,{content:w,className:"prose prose-gray max-w-none dark:prose-invert",allowHtml:!0})})})]})]}),t.jsx(H,{value:"preview",className:"p-6 m-0",style:{minHeight:`${i}px`},children:t.jsx("div",{className:`${j}-markdown-preview`,children:t.jsx(ee,{content:w,className:"prose prose-gray max-w-none dark:prose-invert",allowHtml:!0})})})]}):t.jsxs("div",{children:[q(),t.jsx(O,{editor:a})]}),t.jsx(ut,{open:S,onOpenChange:N,children:t.jsxs(mt,{className:"sm:max-w-md",children:[t.jsx(ht,{children:t.jsx(gt,{children:"Insert Image"})}),t.jsx("div",{className:"space-y-4",children:t.jsxs(X,{defaultValue:"upload",children:[t.jsxs(Z,{className:"grid w-full grid-cols-2",children:[t.jsx(A,{value:"upload",children:"Upload"}),t.jsx(A,{value:"url",children:"URL"})]}),t.jsxs(H,{value:"upload",className:"space-y-4",children:[t.jsxs("div",{children:[t.jsx("label",{htmlFor:"image-file",className:"text-sm font-medium",children:"Choose Image File"}),t.jsx(R,{id:"image-file",type:"file",accept:"image/*",ref:E,onChange:de,className:"mt-1"})]}),d&&t.jsxs("div",{className:"space-y-2",children:[t.jsx("img",{src:d,alt:"Preview",className:"max-w-full h-32 object-cover rounded border"}),t.jsx(R,{placeholder:"Alt text (optional)",value:x,onChange:o=>f(o.target.value)})]})]}),t.jsxs(H,{value:"url",className:"space-y-4",children:[t.jsxs("div",{children:[t.jsx("label",{htmlFor:"image-url",className:"text-sm font-medium",children:"Image URL"}),t.jsx(R,{id:"image-url",placeholder:"https://example.com/image.jpg",value:y,onChange:o=>L(o.target.value),className:"mt-1"})]}),t.jsxs("div",{children:[t.jsx("label",{htmlFor:"image-alt-url",className:"text-sm font-medium",children:"Alt Text"}),t.jsx(R,{id:"image-alt-url",placeholder:"Describe the image",value:x,onChange:o=>f(o.target.value),className:"mt-1"})]})]})]})}),t.jsxs(pt,{children:[t.jsx(V,{variant:"outline",onClick:()=>N(!1),children:"Cancel"}),t.jsxs(V,{onClick:k?ue:me,disabled:B||!k&&!y,children:[B&&t.jsx(J,{className:"mr-2 h-4 w-4 animate-spin"}),"Insert Image"]})]})]})})]})}export{Dt as U};
