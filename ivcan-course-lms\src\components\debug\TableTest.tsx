import React from 'react';
import { MarkdownPreview } from '@/components/ui/markdown-preview';
import '@/styles/professional-lesson-content.css';

const TableTest: React.FC = () => {
  const testMarkdown = `# Table Rendering Test

This is a test to verify that markdown tables are properly rendered.

## Simple Table

| Column 1 | Column 2 | Column 3 |
|----------|----------|----------|
| Row 1, Col 1 | Row 1, Col 2 | Row 1, Col 3 |
| Row 2, Col 1 | Row 2, Col 2 | Row 2, Col 3 |
| Row 3, Col 1 | Row 3, Col 2 | Row 3, Col 3 |

## Table with Alignment

| Left Aligned | Center Aligned | Right Aligned |
|:-------------|:--------------:|--------------:|
| Left | Center | Right |
| This is left | This is center | This is right |
| More content | More content | More content |

## Table with Formatting

| Feature | Status | **Priority** | Notes |
|---------|--------|-------------|-------|
| Tables | ✅ Working | **High** | Should display with borders |
| Code Blocks | ✅ Working | **High** | \`syntax highlighting\` |
| Task Lists | ✅ Working | *Medium* | Checkboxes styled |
| Links | [Working](https://example.com) | Low | External links |

## Complex Table

| Learning Module | Duration | Difficulty | Prerequisites |
|-----------------|----------|------------|---------------|
| Introduction to Concepts | 45 minutes | Beginner | None |
| Practical Applications | 90 minutes | Intermediate | Module 1 |
| Advanced Techniques | 120 minutes | Advanced | Modules 1-2 |
| Final Project | 180 minutes | Expert | All previous |

This should render as properly formatted tables with professional styling.
`;

  return (
    <div className="professional-lesson-container full-width">
      <div className="professional-prose">
        <h1>Table Rendering Debug</h1>
        <p>This component tests table rendering in the professional lesson content system.</p>
        
        <div className="border border-border rounded-lg p-4 mb-6">
          <h2 className="text-lg font-semibold mb-2">Raw Markdown:</h2>
          <pre className="text-sm bg-muted p-2 rounded overflow-x-auto">
            {testMarkdown}
          </pre>
        </div>
        
        <div className="border border-border rounded-lg p-4 mb-6">
          <h2 className="text-lg font-semibold mb-2">Rendered Output:</h2>
          <MarkdownPreview
            content={testMarkdown}
            className="professional-prose"
            allowHtml={true}
          />
        </div>

        <div className="border border-border rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-2">Direct HTML Table Test:</h2>
          <div className="professional-prose">
            <div className="table-wrapper">
              <table>
                <thead>
                  <tr>
                    <th>Column 1</th>
                    <th>Column 2</th>
                    <th>Column 3</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Row 1, Col 1</td>
                    <td>Row 1, Col 2</td>
                    <td>Row 1, Col 3</td>
                  </tr>
                  <tr>
                    <td>Row 2, Col 1</td>
                    <td>Row 2, Col 2</td>
                    <td>Row 2, Col 3</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TableTest;
