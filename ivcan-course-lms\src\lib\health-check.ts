import { supabase } from '@/integrations/supabase/client';
import { executeWithRetry } from './connection-manager';
import { SUPABASE_URL, SUPABASE_ANON_KEY } from '@/config/supabase';

/**
 * Global health check status
 */
export const connectionStatus = {
  isCheckingConnection: false,
  lastCheckTime: 0,
  isConnected: true,
  // Last 5 connection attempts (true = success, false = failure)
  connectionHistory: [true, true, true, true, true],
  consecutiveFailures: 0,
};

/**
 * Check tables that should always exist to verify connection
 */
export async function checkSupabaseConnection(): Promise<boolean> {
  if (connectionStatus.isCheckingConnection) {
    return connectionStatus.isConnected;
  }

  connectionStatus.isCheckingConnection = true;

  try {
    // Try multiple approaches to check connection

    // 1. First try a simple health check endpoint
    try {
      const response = await fetch(`${SUPABASE_URL}/rest/v1/`, {
        method: 'HEAD',
        headers: {
          'apikey': SUPABASE_ANON_KEY,
        },
        cache: 'no-store',
      });

      if (response.ok) {
        if (import.meta.env.MODE !== 'production') {
          console.log('Connection check successful via direct API endpoint');
        }
        recordConnectionAttempt(true);
        return true;
      }
    } catch (e) {
      if (import.meta.env.MODE !== 'production') {
        console.log('Direct API endpoint check failed, trying alternatives');
      }
    }

    // 2. Try checking user_roles table which should always exist
    const { error: rolesError } = await executeWithRetry(
      async () => await supabase.from('user_roles').select('count').limit(1),
      3, // Increased retries for better reliability
      800 // Longer delay for better success chance
    ) as { error?: any };

    // If successful, update status
    if (!rolesError) {
      if (import.meta.env.MODE !== 'production') {
        console.log('Connection check successful via user_roles table');
      }
      recordConnectionAttempt(true);
      return true;
    }

    // 3. If that failed, try auth as a final fallback
    const { error: authError } = await executeWithRetry(
      async () => await supabase.auth.getSession(),
      2, // Increased retries
      1000 // Longer delay
    ) as { error?: any };

    // Update status based on final result
    const isConnected = !authError;
    if (isConnected) {
      if (import.meta.env.MODE !== 'production') {
        console.log('Connection check successful via auth session');
      }
    } else {
      if (import.meta.env.MODE === 'production') {
        console.error('All connection check methods failed');
      } else {
        console.log('All connection check methods failed');
      }
    }
    recordConnectionAttempt(isConnected);
    return isConnected;

  } catch (error) {
    console.error('Health check failed:', error);
    recordConnectionAttempt(false);
    return false;
  } finally {
    connectionStatus.isCheckingConnection = false;
    connectionStatus.lastCheckTime = Date.now();
  }
}

/**
 * Record connection attempt result
 */
function recordConnectionAttempt(success: boolean): void {
  // Update history (shift array and add new value at the end)
  connectionStatus.connectionHistory.shift();
  connectionStatus.connectionHistory.push(success);

  // Update connected status
  connectionStatus.isConnected = success;

  // Update consecutive failures counter
  if (success) {
    connectionStatus.consecutiveFailures = 0;
  } else {
    connectionStatus.consecutiveFailures++;
  }
}

/**
 * Start periodic background health checks
 */
export function startPeriodicHealthChecks(): void {
  // Run a health check on initialization
  checkSupabaseConnection();

  // Set up periodic checks with dynamic intervals based on connection status
  setInterval(() => {
    // More frequent checks when disconnected or after failures
    let checkInterval;

    if (!connectionStatus.isConnected) {
      // When disconnected, check more frequently based on consecutive failures
      if (connectionStatus.consecutiveFailures > 5) {
        checkInterval = 60 * 1000; // Every minute for extended outages
      } else if (connectionStatus.consecutiveFailures > 2) {
        checkInterval = 30 * 1000; // Every 30 seconds for multiple failures
      } else {
        checkInterval = 15 * 1000; // Every 15 seconds for initial failures
      }
    } else {
      // When connected, check less frequently
      checkInterval = 2 * 60 * 1000; // Every 2 minutes when everything is working
    }

    const timeSinceLastCheck = Date.now() - connectionStatus.lastCheckTime;

    if (timeSinceLastCheck >= checkInterval) {
      if (import.meta.env.MODE !== 'production') {
        console.log(`Running scheduled connection check (${connectionStatus.isConnected ? 'connected' : 'disconnected'}, ${connectionStatus.consecutiveFailures} failures)`);
      }
      checkSupabaseConnection();
    }
  }, 10 * 1000); // Check every 10 seconds whether we need to run a health check

  // Also check connection when the browser comes back online
  window.addEventListener('online', () => {
    if (import.meta.env.MODE !== 'production') {
      console.log('Browser reports online status, checking connection');
    }
    checkSupabaseConnection();
  });
}