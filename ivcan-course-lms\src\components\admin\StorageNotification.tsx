import React, { useState, useEffect } from 'react';
import { Alert<PERSON>riangle, X, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

export function StorageNotification() {
  const [dismissed, setDismissed] = useState(false);
  const { toast } = useToast();

  // Check if the notification has been dismissed before
  useEffect(() => {
    const isDismissed = localStorage.getItem('storage-notification-dismissed') === 'true';
    setDismissed(isDismissed);
  }, []);

  const handleDismiss = () => {
    localStorage.setItem('storage-notification-dismissed', 'true');
    setDismissed(true);
  };

  if (dismissed) {
    return null;
  }

  return (
    <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <Check className="h-5 w-5 text-red-600" />
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-red-800">New Basic Image Upload System</h3>
          <div className="mt-2 text-sm text-red-700">
            <p>
              We've implemented a completely new, basic image upload system that works reliably.
              Images are automatically resized to 600x400 pixels and stored directly in the database.
              You can now add images to courses and they will appear on the course cards.
            </p>
            <div className="mt-3">
              <Button
                variant="outline"
                size="sm"
                className="mr-2 bg-white text-red-700 border-red-300 hover:bg-red-50"
                onClick={() => {
                  toast({
                    title: 'About the Basic Image System',
                    description: 'Images are now processed with a simple, reliable approach that ensures they appear on course cards. The system handles all the technical details for you.',
                    duration: 5000,
                  });
                }}
              >
                Learn More
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="text-red-700 hover:bg-red-100"
                onClick={handleDismiss}
              >
                Dismiss
              </Button>
            </div>
          </div>
        </div>
        <div className="ml-4 flex-shrink-0">
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
            onClick={handleDismiss}
          >
            <span className="sr-only">Close</span>
            <X className="h-4 w-4 text-red-500" />
          </Button>
        </div>
      </div>
    </div>
  );
}
