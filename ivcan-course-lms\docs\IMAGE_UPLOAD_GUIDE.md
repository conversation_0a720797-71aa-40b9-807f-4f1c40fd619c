# Image Upload Guide for Teachers

## Overview

Teachers can now easily add images to lesson content using the enhanced markdown editor. This guide explains how to upload and manage images in lessons.

## How to Add Images to Lessons

### Method 1: Upload Image Files (Recommended)

1. **Open the Lesson Editor**
   - Navigate to the admin panel
   - Go to the Lessons tab or edit an existing lesson
   - Click on the "Content" tab

2. **Use the Upload Button**
   - In the markdown editor toolbar, look for the upload icon (📤)
   - Click the upload button to open the file picker
   - Select an image file from your computer

3. **Supported File Types**
   - JPEG (.jpg, .jpeg)
   - PNG (.png)
   - WebP (.webp)
   - GIF (.gif)

4. **File Size Limits**
   - Maximum file size: 10MB per image
   - For best performance, keep images under 2MB

5. **Automatic Processing**
   - The image will be automatically uploaded to secure cloud storage
   - A markdown image tag will be inserted at your cursor position
   - The image will be immediately visible in the preview

### Method 2: Add Image from URL

1. **Use the Image Icon**
   - In the markdown editor toolbar, click the image icon (🖼️)
   - Enter the URL of an existing image
   - The image will be inserted into your content

## Image Display in Lessons

### How Images Appear to Students

- Images are automatically optimized for different screen sizes
- Images load lazily for better performance
- Images have proper alt text for accessibility
- Images are displayed with rounded corners and shadows for a professional look

### Image Positioning

- Images are automatically centered in the lesson content
- Images scale responsively to fit different screen sizes
- Images maintain their aspect ratio

## Best Practices

### Image Quality
- Use high-quality images (at least 800px wide for main content images)
- Ensure images are clear and relevant to the lesson content
- Consider using WebP format for better compression

### File Organization
- Images are automatically organized by course and module
- Each uploaded image gets a unique filename to prevent conflicts
- Images are stored securely and backed up automatically

### Accessibility
- Always provide descriptive alt text for images
- Use images to enhance, not replace, written content
- Ensure sufficient contrast if text overlays images

## Troubleshooting

### Upload Issues
- **File too large**: Reduce image size or compress the image
- **Unsupported format**: Convert to JPEG, PNG, WebP, or GIF
- **Upload failed**: Check your internet connection and try again

### Display Issues
- **Image not showing**: Check if the image URL is accessible
- **Image too small/large**: The system automatically optimizes display size
- **Broken image**: Re-upload the image or use a different file

## Technical Details

### Storage
- Images are stored in Supabase Cloud Storage
- All images are publicly accessible via secure URLs
- Images are served through a global CDN for fast loading

### Security
- Only authenticated teachers can upload images
- File type validation prevents malicious uploads
- All uploads are scanned for security

### Performance
- Images are automatically optimized for web display
- Lazy loading improves page load times
- CDN delivery ensures fast global access

## Support

If you encounter any issues with image uploads:

1. Check the file size and format requirements
2. Ensure you have a stable internet connection
3. Try refreshing the page and uploading again
4. Contact technical support if problems persist

## Examples

### Basic Image Upload
```markdown
# My Lesson Title

Here's some content before the image.

![Description of the image](uploaded-image-url-here)

Here's some content after the image.
```

### Multiple Images
```markdown
# Lesson with Multiple Images

First concept with image:
![Concept 1](image-1-url)

Second concept with image:
![Concept 2](image-2-url)
```

The enhanced image upload system makes it easy to create rich, visual lesson content that engages students and enhances learning outcomes.
