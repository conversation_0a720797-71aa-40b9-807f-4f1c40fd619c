import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

interface ConfettiProps {
  count?: number;
  duration?: number;
  colors?: string[];
  active?: boolean;
}

const Confetti: React.FC<ConfettiProps> = ({
  count = 150,
  duration = 3,
  colors = ['#10b981', '#3b82f6', '#8b5cf6', '#f59e0b', '#ec4899'],
  active = true
}) => {
  const [particles, setParticles] = useState<Array<{
    id: number;
    x: number;
    y: number;
    size: number;
    color: string;
    rotation: number;
    delay: number;
  }>>([]);

  useEffect(() => {
    if (!active) return;

    // Generate confetti particles
    const newParticles = Array.from({ length: count }, (_, i) => ({
      id: i,
      x: Math.random() * 100, // Random x position (0-100%)
      y: -10, // Start above the container
      size: Math.random() * 8 + 2, // Random size (2-10px)
      color: colors[Math.floor(Math.random() * colors.length)],
      rotation: Math.random() * 360, // Random initial rotation
      delay: Math.random() * 0.5, // Random delay for staggered animation
    }));

    setParticles(newParticles);

    // Optional: Play a celebration sound
    // Uncomment this when you have the sound file ready
    /*
    const audio = new Audio('/sounds/success.mp3');
    try {
      audio.volume = 0.5;
      audio.play().catch(e => console.log('Audio play failed:', e));
    } catch (error) {
      console.log('Audio error:', error);
    }
    */
  }, [active, colors, count]);

  if (!active || particles.length === 0) return null;

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          initial={{
            x: `${particle.x}%`,
            y: `${particle.y}%`,
            rotate: particle.rotation,
            opacity: 1
          }}
          animate={{
            y: '100%',
            rotate: particle.rotation + Math.random() * 360,
            opacity: 0
          }}
          transition={{
            duration: duration * (0.8 + Math.random() * 0.4), // Slight variation in duration
            ease: 'easeOut',
            delay: particle.delay
          }}
          className="absolute"
          style={{
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            backgroundColor: particle.color,
            borderRadius: Math.random() > 0.5 ? '50%' : '2px', // Mix of circles and squares
            left: `${particle.x}%`,
            top: `${particle.y}%`
          }}
        />
      ))}
    </div>
  );
};

export default Confetti;
