-- Migration: Add lesson_number field to lessons table
-- Created: 2025-01-02
-- Description: Adds lesson_number field for proper sequential ordering of lessons

-- =============================================
-- ADD LESSON_NUMBER FIELD
-- =============================================

-- Check if lesson_number column exists, if not add it
DO $$
BEGIN
  -- Check if lesson_number column exists
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'lessons' 
    AND column_name = 'lesson_number'
  ) THEN
    -- Add lesson_number column
    ALTER TABLE public.lessons ADD COLUMN lesson_number INTEGER;
    RAISE NOTICE 'Added lesson_number column to lessons table';
  ELSE
    RAISE NOTICE 'lesson_number column already exists';
  END IF;
END $$;

-- =============================================
-- POPULATE LESSON_NUMBER FIELD
-- =============================================

-- Update lesson_number based on created_at order within each module
-- This ensures existing lessons get proper sequential numbering
UPDATE public.lessons 
SET lesson_number = lesson_order.row_num
FROM (
  SELECT 
    id,
    ROW_NUMBER() OVER (PARTITION BY module_id ORDER BY created_at ASC) as row_num
  FROM public.lessons
) lesson_order
WHERE lessons.id = lesson_order.id
AND lessons.lesson_number IS NULL;

-- =============================================
-- ADD CONSTRAINTS
-- =============================================

-- Make lesson_number NOT NULL after populating data
ALTER TABLE public.lessons 
ALTER COLUMN lesson_number SET NOT NULL;

-- Add constraint to ensure lesson_number is positive
ALTER TABLE public.lessons 
ADD CONSTRAINT lesson_number_positive 
CHECK (lesson_number > 0);

-- Create unique constraint on module_id + lesson_number
-- This ensures no duplicate lesson numbers within a module
ALTER TABLE public.lessons 
DROP CONSTRAINT IF EXISTS unique_module_lesson_number;

ALTER TABLE public.lessons 
ADD CONSTRAINT unique_module_lesson_number 
UNIQUE (module_id, lesson_number);

-- =============================================
-- CREATE INDEXES
-- =============================================

-- Create index for efficient ordering queries
CREATE INDEX IF NOT EXISTS idx_lessons_module_lesson_number 
ON public.lessons (module_id, lesson_number);

-- Create index for cross-module ordering
CREATE INDEX IF NOT EXISTS idx_lessons_lesson_number 
ON public.lessons (lesson_number);

-- =============================================
-- CREATE HELPER FUNCTIONS
-- =============================================

-- Function to get next lesson number for a module
CREATE OR REPLACE FUNCTION public.get_next_lesson_number(p_module_id UUID)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  next_number INTEGER;
BEGIN
  SELECT COALESCE(MAX(lesson_number), 0) + 1 
  INTO next_number
  FROM public.lessons 
  WHERE module_id = p_module_id;
  
  RETURN next_number;
END;
$$;

-- Function to reorder lessons in a module
CREATE OR REPLACE FUNCTION public.reorder_module_lessons(p_module_id UUID)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Update lesson numbers to be sequential starting from 1
  UPDATE public.lessons 
  SET lesson_number = lesson_order.new_number
  FROM (
    SELECT 
      id,
      ROW_NUMBER() OVER (ORDER BY lesson_number ASC, created_at ASC) as new_number
    FROM public.lessons
    WHERE module_id = p_module_id
  ) lesson_order
  WHERE lessons.id = lesson_order.id
  AND lessons.module_id = p_module_id;
END;
$$;

-- =============================================
-- CREATE TRIGGERS
-- =============================================

-- Trigger to automatically set lesson_number for new lessons
CREATE OR REPLACE FUNCTION public.set_lesson_number()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  -- If lesson_number is not provided, set it to next available number
  IF NEW.lesson_number IS NULL THEN
    NEW.lesson_number := public.get_next_lesson_number(NEW.module_id);
  END IF;
  
  RETURN NEW;
END;
$$;

-- Create trigger
DROP TRIGGER IF EXISTS trigger_set_lesson_number ON public.lessons;
CREATE TRIGGER trigger_set_lesson_number
  BEFORE INSERT ON public.lessons
  FOR EACH ROW
  EXECUTE FUNCTION public.set_lesson_number();

-- =============================================
-- GRANT PERMISSIONS
-- =============================================

-- Grant execute permissions for helper functions
GRANT EXECUTE ON FUNCTION public.get_next_lesson_number(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.reorder_module_lessons(UUID) TO authenticated;

-- =============================================
-- VALIDATION
-- =============================================

-- Validate that all lessons now have lesson_number
DO $$
DECLARE
  missing_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO missing_count
  FROM public.lessons
  WHERE lesson_number IS NULL;
  
  IF missing_count > 0 THEN
    RAISE EXCEPTION 'Found % lessons without lesson_number', missing_count;
  ELSE
    RAISE NOTICE 'All lessons have lesson_number assigned';
  END IF;
END $$;

-- =============================================
-- COMMENTS FOR DOCUMENTATION
-- =============================================

COMMENT ON COLUMN public.lessons.lesson_number IS 'Sequential number of lesson within module (1, 2, 3, ...)';
COMMENT ON FUNCTION public.get_next_lesson_number(UUID) IS 'Returns the next available lesson number for a module';
COMMENT ON FUNCTION public.reorder_module_lessons(UUID) IS 'Reorders all lessons in a module to have sequential numbers starting from 1';
