var j=(i,s,d)=>new Promise((t,y)=>{var x=o=>{try{l(d.next(o))}catch(r){y(r)}},h=o=>{try{l(d.throw(o))}catch(r){y(r)}},l=o=>o.done?t(o.value):Promise.resolve(o.value).then(x,h);l((d=d.apply(i,s)).next())});import{u as J,r as T,ax as se,j as e,E as q,aH as te,az as k,$ as ae,F as re,at as K,aP as Y,ag as U}from"./vendor-react.BcAa1DKr.js";import{L as P}from"./Layout.DRjmVYQG.js";import{u as V,g as W,B as c,c as v,s as ne}from"./index.BLDhDn0D.js";import{s as ie,a as O,g as G}from"./moduleTestService.BmAHru_J.js";import{r as le}from"./module-test.BaVeK2uM.js";import{g as de}from"./courseApi.BQX5-7u-.js";import{a4 as m,a5 as oe}from"./vendor.DQpuTRuB.js";import{C as _,a as z,b as A,c as F,d as R}from"./card.B9V6b2DK.js";import{B as C}from"./badge.C87ZuIxl.js";import{P as B}from"./floating-sidebar-container.BxlLgzat.js";import"./vendor-supabase.sufZ44-y.js";import"./utils.Qa9QlCj_.js";import"./enrollmentApi.CstnsQi_.js";const ce=({test:i,onComplete:s,courseId:d,showNextLessonButton:t=!1})=>{const{user:y}=V(),{toast:x}=W(),h=J(),[l,o]=T.useState(0),[r,Q]=T.useState([]),[n,L]=T.useState(!1),[g,p]=T.useState(!1),f=i.questions[l],M=i.questions.length,b=l===M-1,N=a=>{const w=parseInt(a),D={questionId:f.id,rating:w};Q($=>{const H=$.findIndex(I=>I.questionId===f.id);if(H>=0){const I=[...$];return I[H]=D,I}else return[...$,D]})},u=()=>{var a;return(a=r.find(w=>w.questionId===f.id))==null?void 0:a.rating},E=()=>{b?Z():o(a=>a+1)},S=()=>{o(a=>Math.max(0,a-1))},X=se({mutationFn:()=>j(void 0,null,function*(){if(!y)throw new Error("User not authenticated");return ie(i.id,y.id,r)}),onSuccess:()=>{p(!0),x({title:"Test Completed",description:"Your responses have been submitted successfully."})},onError:a=>{console.error("Error submitting test:",a),x({title:"Error",description:a.message||"Failed to submit test responses",variant:"destructive"}),L(!1)}}),Z=()=>{if(r.length<M){x({title:"Incomplete Test",description:"Please answer all questions before submitting.",variant:"destructive"});return}L(!0),X.mutate()},ee=()=>j(void 0,null,function*(){if(!d||!i.moduleId){console.error("Missing courseId or moduleId for navigation"),s();return}try{const{firstLessonSlug:a}=yield de(i.moduleId);a?h(`/course/${d}/lesson/${a}`):(h(`/course/${d}/modules`),x({title:"No Lessons Found",description:"This module does not contain any lessons yet."}))}catch(a){console.error("Error navigating to next lesson:",a),s()}});return g?e.jsx(m.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.5},className:"w-full max-w-4xl mx-auto px-6",children:e.jsx("div",{className:"min-h-screen flex flex-col justify-center items-center",children:e.jsxs("div",{className:"text-center space-y-6",children:[e.jsx(m.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"mx-auto w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center",children:e.jsx(q,{className:"w-8 h-8 text-green-600 dark:text-green-400"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold text-green-800 dark:text-green-200 mb-2",children:"Assessment Completed!"}),e.jsxs("p",{className:"text-green-700 dark:text-green-300 text-sm",children:["Thank you for completing the ",i.type==="pre_test"?"pre-assessment":"post-assessment","."]})]}),e.jsx("div",{className:"bg-white dark:bg-gray-900/50 rounded-lg p-4 border border-green-200 dark:border-green-800",children:e.jsx("span",{className:"text-green-700 dark:text-green-300 text-sm font-medium",children:"Your responses have been recorded successfully"})}),t&&d&&i.type==="pre_test"?e.jsx(c,{onClick:ee,size:"default",className:"min-w-[140px]",children:"Next Lesson"}):e.jsx(c,{onClick:s,size:"default",className:"min-w-[140px]",children:"Continue to Course"})]})})}):f?e.jsx(m.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4},className:"w-full max-w-6xl mx-auto px-8",children:e.jsxs("div",{className:"h-screen flex flex-col justify-between",children:[e.jsx("div",{className:"pt-3",children:e.jsx("div",{className:"bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-3",children:e.jsxs("div",{className:"text-left",children:[e.jsx("h1",{className:"text-lg font-semibold text-foreground mb-0.5",children:i.title}),e.jsxs("div",{className:"text-sm font-medium text-muted-foreground",children:["Question ",l+1]})]})})}),e.jsx("div",{className:"flex-1 flex flex-col justify-center py-4",children:e.jsx("div",{className:"bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4",children:e.jsx(oe,{mode:"wait",children:e.jsxs(m.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.3},className:"space-y-3",children:[e.jsx("div",{className:"mb-3",children:e.jsx("p",{className:"text-base leading-normal text-foreground",children:f.question})}),e.jsx("div",{className:"space-y-3",children:e.jsx("div",{className:"space-y-3",children:le.map((a,w)=>e.jsxs(m.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:w*.1},className:v("relative flex items-center justify-between py-3 px-4 cursor-pointer transition-all duration-200 border-l-4",u()===a.value?"border-l-primary bg-primary/10 dark:bg-primary/20":"border-l-transparent hover:bg-gray-50 dark:hover:bg-gray-800/30"),onClick:()=>N(a.value.toString()),children:[e.jsx("div",{className:"flex-1 text-sm font-medium text-foreground",children:a.label}),e.jsx("div",{className:"flex-shrink-0 ml-4",children:e.jsx("div",{className:v("w-5 h-5 rounded-full border-2 transition-all duration-200 flex items-center justify-center",u()===a.value?"border-primary bg-primary":"border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700"),children:u()===a.value&&e.jsx("div",{className:"w-2 h-2 rounded-full bg-white"})})})]},a.value))})})]},l)})})}),e.jsx("div",{className:"-mt-2",children:e.jsx("div",{className:"bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-3",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(c,{variant:"outline",onClick:S,disabled:l===0,size:"default",className:"min-w-[100px] text-sm",children:"Previous"}),e.jsx(c,{onClick:E,disabled:!u()||n,size:"default",className:v("min-w-[100px] text-sm",b&&"bg-primary hover:bg-primary/90"),children:n?"Submitting...":b?"Finish":"Next"})]})})})]})}):e.jsx("div",{className:"w-full max-w-4xl mx-auto px-6",children:e.jsx("div",{className:"min-h-screen flex flex-col justify-center items-center",children:e.jsxs("div",{className:"text-center space-y-6",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"No Questions Available"}),e.jsx("p",{className:"text-muted-foreground text-sm",children:"This test does not contain any questions."}),e.jsx(c,{onClick:s,size:"default",className:"min-w-[140px]",children:"Continue"})]})})})},Ce=()=>{const{courseId:i,moduleId:s}=te(),d=J(),{user:t}=V(),{toast:y}=W(),[x,h]=T.useState(null),{data:l,isLoading:o}=k({queryKey:["module",s],queryFn:()=>j(void 0,null,function*(){if(!s)throw new Error("Module ID is required");const{data:E,error:S}=yield ne.from("modules").select("id, title, module_number").eq("id",s).single();if(S)throw S;return E}),enabled:!!s}),{data:r,isLoading:Q}=k({queryKey:["module-pre-test",s],queryFn:()=>j(void 0,null,function*(){return s?G(s,"pre_test"):null}),enabled:!!s}),{data:n,isLoading:L}=k({queryKey:["module-post-test",s],queryFn:()=>j(void 0,null,function*(){return s?G(s,"post_test"):null}),enabled:!!s}),{data:g}=k({queryKey:["pre-test-response",s,t==null?void 0:t.id],queryFn:()=>j(void 0,null,function*(){return!s||!(t!=null&&t.id)||!r?null:O(r.id,t.id)}),enabled:!!s&&!!(t!=null&&t.id)&&!!r}),{data:p}=k({queryKey:["post-test-response",s,t==null?void 0:t.id],queryFn:()=>j(void 0,null,function*(){return!s||!(t!=null&&t.id)||!n?null:O(n.id,t.id)}),enabled:!!s&&!!(t!=null&&t.id)&&!!n}),f=o||Q||L,M=()=>{y({title:"Test Completed",description:"Your responses have been recorded successfully."}),d(`/course/${i}/modules`)},b=()=>{d(`/course/${i}/modules`)};if(f)return e.jsx(P,{children:e.jsx(B,{pageType:"default",children:e.jsxs(m.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"flex flex-col items-center justify-center h-64 space-y-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(ae,{className:"w-12 h-12 animate-spin text-primary"}),e.jsx("div",{className:"absolute inset-0 w-12 h-12 border-2 border-primary/20 rounded-full"})]}),e.jsxs("div",{className:"text-center space-y-2",children:[e.jsx("h3",{className:"text-lg font-medium text-foreground",children:"Loading Module Tests"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Please wait while we prepare your assessment..."})]})]})})});if(!l)return e.jsx(P,{children:e.jsx(B,{pageType:"default",children:e.jsx(m.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.3},children:e.jsxs(_,{className:"border-destructive/20 bg-destructive/5",children:[e.jsxs(z,{className:"text-center pb-4",children:[e.jsx("div",{className:"mx-auto w-12 h-12 bg-destructive/10 rounded-full flex items-center justify-center mb-4",children:e.jsx(re,{className:"w-6 h-6 text-destructive"})}),e.jsx(A,{className:"text-destructive",children:"Module Not Found"}),e.jsx(F,{className:"text-destructive/80",children:"The requested module could not be found. This might be due to an invalid link or the module may have been removed."})]}),e.jsx(R,{className:"text-center",children:e.jsxs(c,{onClick:b,size:"lg",className:"min-w-[140px]",children:[e.jsx(K,{className:"w-4 h-4 mr-2"}),"Back to Modules"]})})]})})})});let N=null,u=null;return x?(N=x==="pre_test"?r:n,u=x):r&&!g?(N=r,u="pre_test"):n&&!p&&(N=n,u="post_test"),N&&u?e.jsx(P,{children:e.jsx(B,{pageType:"full-width",children:e.jsx(ce,{test:N,onComplete:M,courseId:i,showNextLessonButton:u==="pre_test"})})}):e.jsx(P,{children:e.jsx(B,{pageType:"default",children:e.jsxs(m.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4},children:[e.jsxs("div",{className:"mb-8",children:[e.jsxs(c,{variant:"ghost",onClick:b,className:"mb-6 hover:bg-secondary/80 transition-colors",size:"sm",children:[e.jsx(K,{className:"w-4 h-4 mr-2"}),"Back to Modules"]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("h1",{className:"text-2xl sm:text-3xl font-bold text-foreground",children:["Module ",l.module_number,": ",l.title]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(C,{variant:"outline",className:"text-sm",children:"Module Assessments"}),e.jsx("span",{className:"text-sm text-muted-foreground",children:"Complete assessments to track your progress"})]})]})]}),e.jsxs("div",{className:"grid gap-6 md:gap-8",children:[r&&e.jsx(m.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.1},children:e.jsxs(_,{className:v("transition-all duration-200 hover:shadow-md border-l-4",g?"border-l-green-500 bg-green-50/50 dark:bg-green-950/20":"border-l-yellow-500 hover:border-l-yellow-600"),children:[e.jsxs(z,{className:"pb-4",children:[e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:v("p-2 rounded-lg",g?"bg-green-100 dark:bg-green-900/30":"bg-yellow-100 dark:bg-yellow-900/30"),children:g?e.jsx(q,{className:"w-5 h-5 text-green-600 dark:text-green-400"}):e.jsx(Y,{className:"w-5 h-5 text-yellow-600 dark:text-yellow-400"})}),e.jsxs("div",{children:[e.jsx(A,{className:"text-lg font-semibold",children:r.title}),e.jsxs("div",{className:"flex items-center gap-2 mt-1",children:[e.jsx(C,{variant:"secondary",className:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",children:"Pre-Assessment"}),e.jsxs("span",{className:"text-xs text-muted-foreground",children:[r.questions.length," questions"]})]})]})]}),g&&e.jsxs(C,{variant:"default",className:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",children:[e.jsx(q,{className:"w-3 h-3 mr-1"}),"Completed"]})]}),e.jsx(F,{className:"mt-3 text-sm leading-relaxed",children:r.description||"Complete this pre-assessment before starting the module to help us understand your current knowledge level."})]}),e.jsx(R,{className:"pt-0",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(U,{className:"w-4 h-4"}),e.jsxs("span",{children:["~",Math.ceil(r.questions.length*1.5)," min"]})]})}),e.jsx("div",{className:"flex gap-2",children:g?e.jsx(c,{variant:"outline",onClick:()=>h("pre_test"),size:"sm",children:"Review Responses"}):e.jsx(c,{onClick:()=>h("pre_test"),size:"sm",className:"min-w-[120px]",children:"Start Assessment"})})]})})]})}),n&&e.jsx(m.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.2},children:e.jsxs(_,{className:v("transition-all duration-200 hover:shadow-md border-l-4",p?"border-l-green-500 bg-green-50/50 dark:bg-green-950/20":"border-l-blue-500 hover:border-l-blue-600"),children:[e.jsxs(z,{className:"pb-4",children:[e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:v("p-2 rounded-lg",p?"bg-green-100 dark:bg-green-900/30":"bg-blue-100 dark:bg-blue-900/30"),children:p?e.jsx(q,{className:"w-5 h-5 text-green-600 dark:text-green-400"}):e.jsx(Y,{className:"w-5 h-5 text-blue-600 dark:text-blue-400"})}),e.jsxs("div",{children:[e.jsx(A,{className:"text-lg font-semibold",children:n.title}),e.jsxs("div",{className:"flex items-center gap-2 mt-1",children:[e.jsx(C,{variant:"secondary",className:"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",children:"Post-Assessment"}),e.jsxs("span",{className:"text-xs text-muted-foreground",children:[n.questions.length," questions"]})]})]})]}),p&&e.jsxs(C,{variant:"default",className:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",children:[e.jsx(q,{className:"w-3 h-3 mr-1"}),"Completed"]})]}),e.jsx(F,{className:"mt-3 text-sm leading-relaxed",children:n.description||"Complete this post-assessment after finishing all module lessons to measure your learning progress."})]}),e.jsx(R,{className:"pt-0",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(U,{className:"w-4 h-4"}),e.jsxs("span",{children:["~",Math.ceil(n.questions.length*1.5)," min"]})]})}),e.jsx("div",{className:"flex gap-2",children:p?e.jsx(c,{variant:"outline",onClick:()=>h("post_test"),size:"sm",children:"Review Responses"}):e.jsx(c,{onClick:()=>h("post_test"),size:"sm",className:"min-w-[120px]",children:"Start Assessment"})})]})})]})}),!r&&!n&&e.jsx(m.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.1},children:e.jsxs(_,{className:"border-dashed border-2 border-muted-foreground/20",children:[e.jsxs(z,{className:"text-center pb-4",children:[e.jsx("div",{className:"mx-auto w-16 h-16 bg-muted/50 rounded-full flex items-center justify-center mb-4",children:e.jsx(Y,{className:"w-8 h-8 text-muted-foreground"})}),e.jsx(A,{className:"text-xl",children:"No Assessments Available"}),e.jsx(F,{className:"text-base leading-relaxed max-w-md mx-auto",children:"This module doesn't have any pre-assessments or post-assessments configured yet. You can proceed directly to the module lessons."})]}),e.jsx(R,{className:"text-center",children:e.jsxs(c,{onClick:b,size:"lg",className:"min-w-[160px]",children:[e.jsx(K,{className:"w-4 h-4 mr-2"}),"Back to Modules"]})})]})})]})]})})})};export{Ce as default};
