import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { LessonData } from '@/types';

/**
 * Legacy function for promoting users to teacher role.
 * @deprecated Use promoteUserToTeacher from roleApi.ts instead
 */
export const promoteToTeacher = async (userId: string): Promise<boolean> => {
  try {
    if (!userId) {
      toast.error('User ID is required');
      return false;
    }
    
    // Get current user to verify they are a teacher
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      toast.error('You must be logged in to perform this action');
      return false;
    }
    
    // Check if current user is a teacher
    const { data: currentUserRole, error: roleError } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', session.user.id)
      .single();
      
    if (roleError || !currentUserRole || currentUserRole.role !== 'teacher') {
      toast.error('Only teachers can promote other users to teacher role');
      return false;
    }
    
    // Promote the user to teacher
    const { error } = await supabase
      .from('user_roles')
      .upsert({ user_id: userId, role: 'teacher' }, { onConflict: 'user_id' });
    
    if (error) {
      console.error('Error promoting user to teacher:', error);
      toast.error(`Failed to promote user: ${error.message}`);
      return false;
    }
    
    // For logging role changes, log to console instead
    console.log(`Role change: User ${userId} promoted to teacher by ${session.user.id}`);
    
    toast.success('User successfully promoted to teacher role');
    return true;
  } catch (error: any) {
    console.error('Unexpected error in promoteToTeacher:', error);
    toast.error(`An unexpected error occurred: ${error.message}`);
    return false;
  }
};

/**
 * Fetch all users that can be promoted to teachers
 * @deprecated Use getPromotableUsers from roleApi.ts instead
 */
export const fetchPromotableUsers = async (): Promise<any[]> => {
  try {
    // Get all profiles with their roles
    const { data, error } = await supabase
      .from('profiles')
      .select(`
        id,
        first_name,
        last_name,
        user_roles (role)
      `);
      
    if (error) {
      console.error('Error fetching users:', error);
      return [];
    }
    
    // Filter to only include students or users without roles
    return data.filter(user => {
      // If user has no roles or is a student
      return !user.user_roles || 
        user.user_roles.length === 0 || 
        (user.user_roles[0] && 
         typeof user.user_roles[0] === 'object' && 
         user.user_roles[0] !== null && 
         (user.user_roles[0] as any)?.role === 'student');
    });
  } catch (error) {
    console.error('Error in fetchPromotableUsers:', error);
    return [];
  }
};

export const updateLesson = async (
  lessonId: string,
  data: Partial<LessonData>
): Promise<boolean> => {
  try {
    // First check if the user is a teacher
    const { data: userRole, error: roleError } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', (await supabase.auth.getUser()).data.user?.id)
      .single();

    if (roleError || !userRole || userRole.role !== 'teacher') {
      toast.error('Only teachers can update lessons');
      return false;
    }

    // Update the lesson
    const { error } = await supabase
      .from('lessons')
      .update({
        ...data,
        updated_at: new Date().toISOString()
      })
      .eq('id', lessonId);

    if (error) {
      console.error('Error updating lesson:', error);
      toast.error(`Failed to update lesson: ${error.message}`);
      return false;
    }

    // Verify the update
    const { data: updatedLesson, error: verifyError } = await supabase
      .from('lessons')
      .select('*')
      .eq('id', lessonId)
      .single();

    if (verifyError || !updatedLesson) {
      console.error('Error verifying lesson update:', verifyError);
      toast.error('Failed to verify lesson update');
      return false;
    }

    toast.success('Lesson updated successfully');
    return true;
  } catch (error: any) {
    console.error('Unexpected error updating lesson:', error);
    toast.error(`An unexpected error occurred: ${error.message}`);
    return false;
  }
};

export const createLesson = async (data: LessonData): Promise<string | null> => {
  try {
    // First check if the user is a teacher
    const { data: userRole, error: roleError } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', (await supabase.auth.getUser()).data.user?.id)
      .single();

    if (roleError || !userRole || userRole.role !== 'teacher') {
      toast.error('Only teachers can create lessons');
      return null;
    }

    // Create the lesson
    const { data: newLesson, error } = await supabase
      .from('lessons')
      .insert([{
        ...data,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }])
      .select()
      .single();

    if (error) {
      console.error('Error creating lesson:', error);
      toast.error(`Failed to create lesson: ${error.message}`);
      return null;
    }

    toast.success('Lesson created successfully');
    return newLesson.id;
  } catch (error: any) {
    console.error('Unexpected error creating lesson:', error);
    toast.error(`An unexpected error occurred: ${error.message}`);
    return null;
  }
};

export const deleteLesson = async (lessonId: string): Promise<boolean> => {
  try {
    // First check if the user is a teacher
    const { data: userRole, error: roleError } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', (await supabase.auth.getUser()).data.user?.id)
      .single();

    if (roleError || !userRole || userRole.role !== 'teacher') {
      toast.error('Only teachers can delete lessons');
      return false;
    }

    // Delete the lesson
    const { error } = await supabase
      .from('lessons')
      .delete()
      .eq('id', lessonId);

    if (error) {
      console.error('Error deleting lesson:', error);
      toast.error(`Failed to delete lesson: ${error.message}`);
      return false;
    }

    toast.success('Lesson deleted successfully');
    return true;
  } catch (error: any) {
    console.error('Unexpected error deleting lesson:', error);
    toast.error(`An unexpected error occurred: ${error.message}`);
    return false;
  }
};
