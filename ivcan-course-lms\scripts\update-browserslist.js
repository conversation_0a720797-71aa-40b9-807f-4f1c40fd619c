// This script updates the browserslist database
// Run this script with: node scripts/update-browserslist.js

const { execSync } = require('child_process');

console.log('Updating browserslist database...');

try {
  execSync('npx update-browserslist-db@latest', { stdio: 'inherit' });
  console.log('Browserslist database updated successfully!');
} catch (error) {
  console.error('Error updating browserslist database:', error.message);
  process.exit(1);
}
