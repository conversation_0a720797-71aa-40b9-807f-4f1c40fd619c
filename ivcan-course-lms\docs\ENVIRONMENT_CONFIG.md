# Environment Configuration

This document explains how to configure the application using environment variables.

## Overview

The application uses environment variables to configure various aspects, including:

- Supabase connection details
- Application settings
- Feature flags

## Setup

1. Create a `.env` file in the root directory of the project
2. Copy the contents from `.env.example` to your `.env` file
3. Update the values with your actual configuration

Example:
```
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project-url.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
VITE_SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Application Configuration
VITE_APP_NAME=IVCAN Course LMS
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=development
```

## Required Variables

| Variable | Description | Required |
|----------|-------------|----------|
| VITE_SUPABASE_URL | URL of your Supabase project | Yes |
| VITE_SUPABASE_ANON_KEY | Anonymous/public API key | Yes |
| VITE_SUPABASE_SERVICE_ROLE_KEY | Service role key (for admin operations) | For admin features |

## Using Environment Variables

### In Frontend Code

```typescript
// Access environment variables in frontend code
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
```

### In Node.js Scripts

```javascript
// Access environment variables in Node.js scripts
require('dotenv').config();
const supabaseUrl = process.env.VITE_SUPABASE_URL;
```

## Centralized Configuration

The application uses centralized configuration files to avoid hardcoded values:

- Frontend: `src/config/supabase.ts`
- Scripts: `scripts/config.js`

Always use these configuration modules instead of accessing environment variables directly.

## Security Considerations

- Never commit your `.env` file to version control
- Keep your service role key secure - it has full access to your database
- Use the anonymous key for client-side code
- Only use the service role key for admin operations that require bypassing RLS
