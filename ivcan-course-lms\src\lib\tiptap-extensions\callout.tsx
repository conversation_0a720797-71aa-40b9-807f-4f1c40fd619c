/**
 * Custom TipTap extension for callout/admonition blocks
 * Supports different callout types: info, warning, success, error, tip
 */

import { Node, mergeAttributes } from '@tiptap/core';
import { ReactNodeViewRenderer } from '@tiptap/react';
import React from 'react';
import { NodeViewWrapper, NodeViewContent } from '@tiptap/react';
import { 
  Info, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Lightbulb,
  AlertCircle 
} from 'lucide-react';

export type CalloutType = 'info' | 'warning' | 'success' | 'error' | 'tip' | 'note';

// React component for rendering the callout
const CalloutComponent = ({ node, updateAttributes }: any) => {
  const { type, title } = node.attrs;

  const getCalloutConfig = (type: CalloutType) => {
    switch (type) {
      case 'info':
        return {
          icon: <Info className="h-5 w-5" />,
          className: 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950/30',
          iconColor: 'text-blue-600 dark:text-blue-400',
          titleColor: 'text-blue-800 dark:text-blue-200',
        };
      case 'warning':
        return {
          icon: <AlertTriangle className="h-5 w-5" />,
          className: 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950/30',
          iconColor: 'text-yellow-600 dark:text-yellow-400',
          titleColor: 'text-yellow-800 dark:text-yellow-200',
        };
      case 'success':
        return {
          icon: <CheckCircle className="h-5 w-5" />,
          className: 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950/30',
          iconColor: 'text-green-600 dark:text-green-400',
          titleColor: 'text-green-800 dark:text-green-200',
        };
      case 'error':
        return {
          icon: <XCircle className="h-5 w-5" />,
          className: 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/30',
          iconColor: 'text-red-600 dark:text-red-400',
          titleColor: 'text-red-800 dark:text-red-200',
        };
      case 'tip':
        return {
          icon: <Lightbulb className="h-5 w-5" />,
          className: 'border-purple-200 bg-purple-50 dark:border-purple-800 dark:bg-purple-950/30',
          iconColor: 'text-purple-600 dark:text-purple-400',
          titleColor: 'text-purple-800 dark:text-purple-200',
        };
      case 'note':
      default:
        return {
          icon: <AlertCircle className="h-5 w-5" />,
          className: 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800/30',
          iconColor: 'text-gray-600 dark:text-gray-400',
          titleColor: 'text-gray-800 dark:text-gray-200',
        };
    }
  };

  const config = getCalloutConfig(type);

  return (
    <NodeViewWrapper className="callout-wrapper">
      <div className={`border-l-4 rounded-r-lg p-4 my-4 ${config.className}`}>
        <div className="flex items-center gap-3 mb-2">
          <span className={config.iconColor}>
            {config.icon}
          </span>
          <span
            className={`font-semibold ${config.titleColor}`}
            contentEditable
            suppressContentEditableWarning
            onBlur={(e) => {
              updateAttributes({ title: e.target.textContent });
            }}
          >
            {title || type.charAt(0).toUpperCase() + type.slice(1)}
          </span>
        </div>
        <div className="pl-8">
          <NodeViewContent />
        </div>
      </div>
    </NodeViewWrapper>
  );
};

export interface CalloutOptions {
  HTMLAttributes: Record<string, any>;
  types: CalloutType[];
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    callout: {
      /**
       * Insert a callout block
       */
      setCallout: (attributes?: { type?: CalloutType; title?: string }) => ReturnType;
      /**
       * Toggle a callout block
       */
      toggleCallout: (type?: CalloutType) => ReturnType;
      /**
       * Unset callout formatting
       */
      unsetCallout: () => ReturnType;
    };
  }
}

export const Callout = Node.create<CalloutOptions>({
  name: 'callout',

  addOptions() {
    return {
      HTMLAttributes: {},
      types: ['info', 'warning', 'success', 'error', 'tip', 'note'],
    };
  },

  group: 'block',

  content: 'block+',

  defining: true,

  addAttributes() {
    return {
      type: {
        default: 'info',
        parseHTML: (element) => {
          return element.getAttribute('data-type') || 'info';
        },
        renderHTML: (attributes) => {
          return {
            'data-type': attributes.type,
          };
        },
      },
      title: {
        default: null,
        parseHTML: (element) => {
          return element.getAttribute('data-title');
        },
        renderHTML: (attributes) => {
          if (!attributes.title) {
            return {};
          }
          return {
            'data-title': attributes.title,
          };
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-callout]',
      },
      {
        tag: 'blockquote',
        getAttrs: (element) => {
          const text = (element as HTMLElement).textContent || '';
          const match = text.match(/^\[!(INFO|WARNING|SUCCESS|ERROR|TIP|NOTE)\]/i);
          if (match) {
            return { type: match[1].toLowerCase() };
          }
          return false;
        },
      },
    ];
  },

  renderHTML({ HTMLAttributes, node }) {
    const { type, title } = node.attrs;
    
    return [
      'div',
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        'data-callout': '',
        'data-type': type,
        ...(title && { 'data-title': title }),
        class: 'callout',
      }),
      0,
    ];
  },

  addNodeView() {
    return ReactNodeViewRenderer(CalloutComponent);
  },

  addCommands() {
    return {
      setCallout:
        (attributes = {}) =>
        ({ commands }) => {
          return commands.wrapIn(this.name, attributes);
        },
      toggleCallout:
        (type = 'info') =>
        ({ commands }) => {
          return commands.toggleWrap(this.name, { type });
        },
      unsetCallout:
        () =>
        ({ commands }) => {
          return commands.lift(this.name);
        },
    };
  },

  addKeyboardShortcuts() {
    return {
      'Mod-Shift-c': () => this.editor.commands.toggleCallout(),
    };
  },
});

export default Callout;
