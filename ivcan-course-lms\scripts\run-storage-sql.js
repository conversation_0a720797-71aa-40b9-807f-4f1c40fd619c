// This script runs the SQL to create storage buckets and RLS policies
// Run this script with: node scripts/run-storage-sql.js

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Replace these with your Supabase URL and anon key
const SUPABASE_URL = "https://jibspqwieubavucdtccv.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMxMjUzNzIsImV4cCI6MjA1ODcwMTM3Mn0.H1cvGLXWvIMibv8dYsYIvY37IRoVR80HyeF1wM93IUI";

// Create Supabase client with anon key (limited permissions)
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Try to create a service role client if environment variables are available
let serviceRoleClient = null;
if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.log('Using service role client for elevated permissions');
  serviceRoleClient = createClient(SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);
}

async function runSQL(sqlFilePath) {
  try {
    console.log(`Running SQL from ${sqlFilePath}...`);
    const client = serviceRoleClient || supabase;
    
    // Read the SQL file
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    const { error } = await client.rpc('exec_sql', { sql });
    
    if (error) {
      console.error(`Error running SQL from ${sqlFilePath}:`, error);
      return false;
    }
    
    console.log(`Successfully ran SQL from ${sqlFilePath}`);
    return true;
  } catch (error) {
    console.error(`Error running SQL from ${sqlFilePath}:`, error);
    return false;
  }
}

async function runAllSQL() {
  console.log('Running all SQL scripts...');
  
  // Step 1: Run the SQL to create storage buckets
  const bucketsSqlPath = path.join(__dirname, '..', 'supabase', 'migrations', '20250501000_create_storage_buckets.sql');
  const bucketsResult = await runSQL(bucketsSqlPath);
  
  if (!bucketsResult) {
    console.log('Failed to run SQL to create storage buckets');
    console.log('SQL file path:', bucketsSqlPath);
    console.log('Please run this SQL manually in the Supabase SQL Editor');
  }
  
  // Step 2: Run the SQL to create RLS policies
  const rlsSqlPath = path.join(__dirname, '..', 'supabase', 'migrations', '20250501001_fix_storage_rls.sql');
  const rlsResult = await runSQL(rlsSqlPath);
  
  if (!rlsResult) {
    console.log('Failed to run SQL to create RLS policies');
    console.log('SQL file path:', rlsSqlPath);
    console.log('Please run this SQL manually in the Supabase SQL Editor');
  }
  
  console.log('\n--- Summary ---');
  console.log(`Storage buckets SQL: ${bucketsResult ? 'Success' : 'Failed'}`);
  console.log(`RLS policies SQL: ${rlsResult ? 'Success' : 'Failed'}`);
  
  if (!bucketsResult || !rlsResult) {
    console.log('\nManual steps required:');
    console.log('1. Log in to your Supabase dashboard');
    console.log('2. Go to the SQL Editor');
    console.log('3. Run the SQL from:');
    if (!bucketsResult) {
      console.log(`   - ${bucketsSqlPath}`);
    }
    if (!rlsResult) {
      console.log(`   - ${rlsSqlPath}`);
    }
  }
}

// Run the function
runAllSQL();
