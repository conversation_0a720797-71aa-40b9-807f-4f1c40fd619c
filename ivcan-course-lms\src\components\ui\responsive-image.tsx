import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';

interface ResponsiveImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  sizes?: string;
  fallback?: string;
  className?: string;
  containerClassName?: string;
  aspectRatio?: string;
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
  loadingStrategy?: 'lazy' | 'eager';
  placeholderColor?: string;
  overlayText?: string;
  overlayTextClassName?: string;
}

export function ResponsiveImage({
  src,
  alt,
  sizes = '100vw',
  fallback,
  className,
  containerClassName,
  aspectRatio = '16/9',
  objectFit = 'cover',
  loadingStrategy = 'lazy',
  placeholderColor,
  overlayText,
  overlayTextClassName,
  ...props
}: ResponsiveImageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(false);
  const [imageSrc, setImageSrc] = useState(src);

  // Reset loading state when src changes
  useEffect(() => {
    setIsLoading(true);
    setError(false);
    setImageSrc(src);
  }, [src]);

  // Generate srcSet for responsive images
  const generateSrcSet = () => {
    if (!src || src.startsWith('data:') || src.startsWith('blob:')) {
      return undefined;
    }

    // Extract base URL and extension
    const lastDot = src.lastIndexOf('.');
    if (lastDot === -1) return undefined;

    const baseUrl = src.substring(0, lastDot);
    const extension = src.substring(lastDot);

    // Generate srcSet with different sizes
    return [300, 600, 900, 1200, 1800]
      .map(width => `${baseUrl}-${width}w${extension} ${width}w`)
      .join(', ');
  };

  const srcSet = generateSrcSet();

  const handleLoad = () => {
    setIsLoading(false);
  };

  const handleError = () => {
    setIsLoading(false);
    setError(true);
    if (fallback) {
      setImageSrc(fallback);
    }
  };

  return (
    <div
      className={cn(
        'relative overflow-hidden w-full h-full',
        containerClassName
      )}
      style={{ aspectRatio }}
    >
      {isLoading && (
        <Skeleton
          className="absolute inset-0 z-10"
          style={{ backgroundColor: placeholderColor }}
        />
      )}

      <img
        src={imageSrc}
        alt={alt}
        sizes={sizes}
        srcSet={srcSet}
        loading={loadingStrategy}
        onLoad={handleLoad}
        onError={handleError}
        className={cn(
          'w-full h-full transition-opacity duration-300',
          objectFit === 'cover' && 'object-cover',
          objectFit === 'contain' && 'object-contain',
          objectFit === 'fill' && 'object-fill',
          objectFit === 'none' && 'object-none',
          objectFit === 'scale-down' && 'object-scale-down',
          isLoading ? 'opacity-0' : 'opacity-100',
          className
        )}
        {...props}
      />

      {overlayText && (
        <>
          {/* Gradient overlay to make text more readable */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/40 to-transparent z-10"></div>

          {/* Text overlay */}
          <div
            className={cn(
              "absolute bottom-0 left-0 right-0 p-4 sm:p-6 z-20 text-white",
              overlayTextClassName
            )}
          >
            <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-shadow-sm line-clamp-2">
              {overlayText}
            </h2>
          </div>
        </>
      )}
    </div>
  );
}
