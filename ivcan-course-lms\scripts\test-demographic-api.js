import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testDemographicAPI() {
  try {
    console.log('Testing demographic API...');

    // Test if tables exist
    const { data, error } = await supabase
      .from('demographic_questionnaires')
      .select('*')
      .limit(1);

    if (error) {
      console.log('❌ Tables do not exist yet');
      console.log('Error:', error.message);
      console.log('\nTo create the tables, run the SQL from:');
      console.log('supabase/migrations/20250115001_demographic_questionnaire.sql');
      console.log('\nIn your Supabase dashboard SQL editor.');
      return;
    }

    console.log('✅ Tables exist!');
    
    if (data && data.length > 0) {
      console.log('✅ Found existing questionnaires:', data.length);
      console.log('First questionnaire:', data[0].title);
    } else {
      console.log('⚠️  No questionnaires found. Run the create script to add default data.');
    }

  } catch (error) {
    console.error('Error testing API:', error);
  }
}

testDemographicAPI();
