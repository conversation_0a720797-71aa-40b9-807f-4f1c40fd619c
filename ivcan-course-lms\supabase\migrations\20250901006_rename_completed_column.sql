-- Rename the 'completed' column to 'is_completed' in user_lesson_progress table

-- First check if the column exists with the old name
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'user_lesson_progress' 
    AND column_name = 'completed'
  ) THEN
    -- Rename the column
    ALTER TABLE public.user_lesson_progress 
    RENAME COLUMN completed TO is_completed;
    
    RAISE NOTICE 'Column "completed" has been renamed to "is_completed"';
  ELSE
    RAISE NOTICE 'Column "completed" does not exist in user_lesson_progress table';
  END IF;
END $$; 