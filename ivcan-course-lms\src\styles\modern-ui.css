/* Modern UI Styles */

/* Typography with improved responsive sizing */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Poppins', system-ui, sans-serif;
  font-weight: 700;
  letter-spacing: -0.02em;
  line-height: 1.2;
}

h1 {
  font-size: 2rem;
  line-height: 1.2;
}

h2 {
  font-size: 1.75rem;
  line-height: 1.25;
}

h3 {
  font-size: 1.5rem;
  line-height: 1.3;
}

h4 {
  font-size: 1.25rem;
  line-height: 1.35;
}

h5 {
  font-size: 1.125rem;
  line-height: 1.4;
}

h6 {
  font-size: 1rem;
  line-height: 1.5;
}

/* Responsive typography for larger screens */
@media (min-width: 640px) {
  h1 {
    font-size: 2.25rem;
  }

  h2 {
    font-size: 1.875rem;
  }
}

@media (min-width: 768px) {
  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  h3 {
    font-size: 1.5rem;
  }
}

body {
  font-family: 'Poppins', system-ui, sans-serif;
  font-weight: 400;
  line-height: 1.6;
  letter-spacing: -0.01em;
  text-size-adjust: 100%; /* Prevent text size adjustment on orientation change */
  -webkit-text-size-adjust: 100%;
}

/* Modern Card Styles - Simplified for unified card system */
.modern-card {
  @apply rounded-xl bg-card border border-border/40 shadow-sm;
  @apply transition-all duration-200 hover:shadow-md hover:border-border/60;
  @apply min-h-[64px] sm:min-h-0 touch-manipulation active:scale-[0.98];
  @apply w-full; /* Ensure cards take full width on mobile */
}

/* Card grid for responsive layouts */
.modern-card-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6;
}

/* Modern Button Styles - Simplified for unified button system */
.modern-button {
  @apply rounded-lg font-medium transition-all duration-200;
  @apply flex items-center justify-center gap-2;
  @apply focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2;
  @apply disabled:opacity-50 disabled:pointer-events-none;
  @apply min-h-[44px]; /* Minimum height for touch targets */
  @apply px-4 py-2.5; /* Better padding for mobile */
  @apply touch-manipulation; /* Optimize for touch */
  @apply active:scale-[0.98]; /* Consistent active state */
}

/* Mobile-friendly icon button */
.modern-icon-button {
  @apply rounded-full p-2 sm:p-2.5 flex items-center justify-center;
  @apply min-h-[40px] min-w-[40px]; /* Minimum size for touch targets */
  @apply transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-red-500/20;
  @apply touch-manipulation; /* Optimize for touch */
}

/* Modern Input Styles with improved mobile usability */
.modern-input {
  @apply rounded-xl border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 px-4 py-3;
  @apply focus:outline-none focus:ring-2 focus:ring-red-500/20 focus:border-red-300 dark:focus:border-red-700;
  @apply transition-all duration-200;
  @apply w-full; /* Full width on mobile */
  @apply min-h-[44px]; /* Minimum height for touch targets */
  @apply text-base sm:text-sm; /* Larger text on mobile */
  @apply appearance-none; /* Remove browser styling */
  @apply touch-manipulation; /* Optimize for touch */
}

/* Modern form group for better spacing */
.modern-form-group {
  @apply flex flex-col gap-1.5 mb-4;
}

/* Modern form label */
.modern-form-label {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300;
  @apply mb-1;
}

/* Modern form helper text */
.modern-form-helper {
  @apply text-xs text-gray-500 dark:text-gray-400 mt-1;
}

/* Modern Badge Styles */
.modern-badge {
  @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
}

.modern-badge-primary {
  @apply bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400;
}

.modern-badge-secondary {
  @apply bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300;
}

.modern-badge-success {
  @apply bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400;
}

/* Modern Sidebar */
.modern-sidebar {
  @apply bg-red-500 dark:bg-red-700 text-white;
  @apply border-r border-red-400 dark:border-red-800;
}

.modern-sidebar-item {
  @apply flex items-center gap-3 px-4 py-3 rounded-lg font-medium transition-all duration-200;
  @apply text-white/90 hover:text-white hover:bg-white/10;
}

.modern-sidebar-item-active {
  @apply bg-white/15 text-white shadow-sm;
}

/* Modern Dashboard Stats */
.modern-stat-card {
  @apply bg-white dark:bg-gray-900 p-5 rounded-xl flex flex-col shadow-sm border border-gray-100 dark:border-gray-800;
  @apply transition-all duration-300 hover:shadow-md;
}

.modern-stat-icon {
  @apply text-red-500 dark:text-red-400 mb-2;
}

.modern-stat-value {
  @apply text-2xl font-bold;
}

.modern-stat-label {
  @apply text-gray-500 dark:text-gray-400 text-sm;
}

/* Modern Course Card */
.modern-course-card {
  @apply rounded-xl overflow-hidden bg-white dark:bg-gray-900 border border-gray-100 dark:border-gray-800 shadow-sm;
  @apply transition-all duration-300 hover:shadow-md hover:translate-y-[-4px];
}

.modern-course-image {
  @apply h-48 w-full object-cover;
}

.modern-course-content {
  @apply p-5;
}

.modern-course-title {
  @apply text-lg font-bold mb-2 line-clamp-2;
}

.modern-course-description {
  @apply text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3;
}

.modern-course-footer {
  @apply flex items-center justify-between mt-4;
}

/* Modern Progress Bar */
.modern-progress-container {
  @apply w-full h-2 bg-gray-100 dark:bg-gray-800 rounded-full overflow-hidden;
}

.modern-progress-bar {
  @apply h-full bg-red-500 dark:bg-red-600 rounded-full transition-all duration-500;
}

/* Modern Layout with improved mobile responsiveness */
.modern-layout {
  @apply bg-gray-50 dark:bg-gray-950 min-h-screen;
  @apply overflow-x-hidden; /* Prevent horizontal scrolling on mobile */
  @apply flex flex-col; /* Better layout structure */
}

.modern-container {
  @apply w-full mx-auto px-4 sm:px-6 md:px-8 py-4 sm:py-6;
  @apply max-w-full sm:max-w-xl md:max-w-2xl lg:max-w-4xl xl:max-w-6xl;
}

.modern-container-narrow {
  @apply w-full mx-auto px-4 sm:px-6 py-4 sm:py-6;
  @apply max-w-full sm:max-w-lg md:max-w-xl lg:max-w-2xl;
}

.modern-container-wide {
  @apply w-full mx-auto px-4 sm:px-6 md:px-8 py-4 sm:py-6;
  @apply max-w-full lg:max-w-6xl xl:max-w-7xl;
}

.modern-header {
  @apply sticky top-0 z-10 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-b border-gray-100 dark:border-gray-800;
  @apply transition-all duration-300;
  @apply px-4 sm:px-6 py-3 sm:py-4; /* Better padding for mobile */
  @apply flex items-center justify-between; /* Flex layout for better alignment */
}

/* Modern section with responsive spacing */
.modern-section {
  @apply py-6 sm:py-8 md:py-10;
}

/* Modern page with responsive spacing */
.modern-page {
  @apply py-4 sm:py-6 md:py-8 space-y-6 sm:space-y-8;
}

/* Modern Search with improved mobile usability */
.modern-search {
  @apply rounded-full bg-gray-100 dark:bg-gray-800 px-3 sm:px-4 py-2 flex items-center gap-2;
  @apply focus-within:ring-2 focus-within:ring-red-500/20 focus-within:bg-white dark:focus-within:bg-gray-900;
  @apply transition-all duration-200;
  @apply w-full; /* Full width on mobile */
  @apply min-h-[44px]; /* Minimum height for touch targets */
  @apply shadow-sm; /* Subtle shadow for better visibility */
}

.modern-search-input {
  @apply bg-transparent border-none focus:outline-none w-full;
  @apply text-base sm:text-sm; /* Larger text on mobile */
  @apply placeholder:text-gray-500 dark:placeholder:text-gray-500;
  @apply appearance-none; /* Remove browser styling */
  @apply touch-manipulation; /* Optimize for touch */
}

/* Modern search icon */
.modern-search-icon {
  @apply flex-shrink-0 text-gray-500 dark:text-gray-400;
  @apply w-5 h-5 sm:w-4 sm:h-4; /* Larger icon on mobile */
}

/* Modern search clear button */
.modern-search-clear {
  @apply p-1 rounded-full hover:bg-gray-200/70 dark:hover:bg-gray-700/70;
  @apply flex items-center justify-center;
  @apply min-h-[28px] min-w-[28px]; /* Minimum size for touch targets */
}

/* Modern Tabs with improved mobile usability */
.modern-tabs {
  @apply flex space-x-1 border-b border-gray-200 dark:border-gray-800;
  @apply overflow-x-auto; /* Allow horizontal scrolling on mobile */
  @apply pb-1; /* Add padding to avoid cut-off */
  @apply -mx-4 px-4 sm:mx-0 sm:px-0; /* Full width on mobile */
  scrollbar-width: none; /* Firefox */
}
.modern-tabs::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}

.modern-tab {
  @apply px-3 sm:px-4 py-2 text-sm font-medium border-b-2 border-transparent;
  @apply hover:text-red-500 dark:hover:text-red-400 transition-all duration-200;
  @apply whitespace-nowrap; /* Prevent text wrapping */
  @apply min-h-[40px]; /* Minimum height for touch targets */
  @apply flex items-center justify-center; /* Better alignment */
  @apply touch-manipulation; /* Optimize for touch */
}

.modern-tab-active {
  @apply text-red-500 dark:text-red-400 border-red-500 dark:border-red-400;
  @apply font-semibold; /* Make active tab more visible */
}

/* Modern Dropdown with improved mobile usability */
.modern-dropdown {
  @apply relative inline-block;
}

.modern-dropdown-button {
  @apply flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium;
  @apply bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800;
  @apply hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200;
  @apply min-h-[40px]; /* Minimum height for touch targets */
  @apply touch-manipulation; /* Optimize for touch */
  @apply active:bg-gray-100 dark:active:bg-gray-800; /* Better touch feedback */
}

.modern-dropdown-content {
  @apply absolute right-0 mt-2 w-56 sm:w-48 rounded-xl bg-white dark:bg-gray-900 shadow-lg border border-gray-100 dark:border-gray-800;
  @apply py-1 z-50;
  @apply max-h-[80vh] overflow-y-auto; /* Prevent overflow on small screens */
}

.modern-dropdown-item {
  @apply block px-4 py-3 sm:py-2 text-sm text-gray-700 dark:text-gray-300;
  @apply hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-200;
  @apply min-h-[44px] sm:min-h-[40px]; /* Minimum height for touch targets */
  @apply touch-manipulation; /* Optimize for touch */
  @apply active:bg-gray-100 dark:active:bg-gray-700; /* Better touch feedback */
}

/* Modern Tooltip with improved mobile usability */
.modern-tooltip {
  @apply absolute z-50 px-2 py-1 text-xs font-medium text-white bg-gray-900 dark:bg-gray-700 rounded-md shadow-sm;
  @apply opacity-0 invisible transition-all duration-200;
  @apply max-w-[90vw] sm:max-w-[300px]; /* Prevent overflow on small screens */
  @apply text-center; /* Center text for better readability */
  @apply whitespace-normal break-words; /* Allow text wrapping */
}

.modern-tooltip-visible {
  @apply opacity-100 visible;
}

/* Modern Animations optimized for performance */
.fade-in {
  animation: fadeIn 0.3s ease-out forwards;
  will-change: opacity; /* Optimize for animation */
}

.slide-up {
  animation: slideUp 0.3s ease-out forwards;
  will-change: opacity, transform; /* Optimize for animation */
}

.scale-in {
  animation: scaleIn 0.2s ease-out forwards;
  will-change: opacity, transform; /* Optimize for animation */
}

/* Reduced motion alternatives */
@media (prefers-reduced-motion: reduce) {
  .fade-in, .slide-up, .scale-in {
    animation: fadeIn 0.15s ease-out forwards !important;
  }
}

/* Mobile-optimized animations */
.mobile-fade-in {
  @apply transition-opacity duration-200 ease-out;
}

.mobile-slide-up {
  @apply transition-all duration-200 ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

/* Modern UI utilities */

/* Modern spacing that works better on mobile */
.spacing-safe {
  padding-top: max(env(safe-area-inset-top), 1rem);
  padding-bottom: max(env(safe-area-inset-bottom), 1rem);
  padding-left: max(env(safe-area-inset-left), 1rem);
  padding-right: max(env(safe-area-inset-right), 1rem);
}

/* Improved mobile text sizes */
.text-mobile-xl {
  font-size: clamp(1.25rem, 5vw, 1.5rem);
  line-height: 1.3;
}

.text-mobile-lg {
  font-size: clamp(1.125rem, 4vw, 1.25rem);
  line-height: 1.4;
}

.text-mobile-base {
  font-size: clamp(1rem, 3.5vw, 1.125rem);
  line-height: 1.5;
}

.text-mobile-sm {
  font-size: clamp(0.875rem, 3vw, 1rem);
  line-height: 1.5;
}

/* Modern grid layouts */
.grid-mobile-auto {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(100%, 300px), 1fr));
  gap: clamp(1rem, 3vw, 1.5rem);
}

/* Modern flex layouts */
.flex-mobile {
  display: flex;
  flex-direction: column;
  gap: clamp(1rem, 3vw, 1.5rem);
}

@media (min-width: 768px) {
  .flex-mobile {
    flex-direction: row;
  }
}

/* Modern card styles */
.card-modern {
  border-radius: 16px;
  background: var(--card-bg, #fff);
  border: 1px solid var(--card-border, rgba(0,0,0,0.1));
  overflow: hidden;
  transition: all 0.2s ease;
}

.card-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

/* Modern button styles */
.button-modern {
  height: 44px;
  min-width: 44px;
  padding: 0 16px;
  border-radius: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  touch-action: manipulation;
}

.button-modern:active {
  transform: scale(0.98);
}

/* Modern input styles */
.input-modern {
  height: 44px;
  padding: 0 16px;
  border-radius: 12px;
  border: 1px solid var(--input-border, rgba(0,0,0,0.1));
  background: var(--input-bg, #fff);
  font-size: 16px;
  transition: all 0.2s ease;
}

.input-modern:focus {
  border-color: var(--input-focus-border, rgba(230,57,70,0.5));
  box-shadow: 0 0 0 3px var(--input-focus-shadow, rgba(230,57,70,0.1));
}

/* Modern list styles */
.list-modern {
  border-radius: 16px;
  background: var(--list-bg, #fff);
  overflow: hidden;
}

.list-item-modern {
  padding: 16px;
  border-bottom: 1px solid var(--list-border, rgba(0,0,0,0.1));
  transition: background-color 0.2s ease;
}

.list-item-modern:last-child {
  border-bottom: none;
}

.list-item-modern:active {
  background-color: var(--list-active-bg, rgba(0,0,0,0.05));
}

/* Modern navigation styles */
.nav-modern {
  height: 60px;
  padding: 0 16px;
  background: var(--nav-bg, rgba(255,255,255,0.8));
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-bottom: 1px solid var(--nav-border, rgba(0,0,0,0.1));
}

/* Modern loader styles */
.loader-modern {
  width: 24px;
  height: 24px;
  border: 3px solid var(--loader-track, rgba(0,0,0,0.1));
  border-top-color: var(--loader-active, #E63946);
  border-radius: 50%;
  animation: loader-spin 0.8s linear infinite;
}

@keyframes loader-spin {
  to { transform: rotate(360deg); }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  :root {
    --card-bg: rgba(255,255,255,0.05);
    --card-border: rgba(255,255,255,0.1);
    --input-bg: rgba(255,255,255,0.05);
    --input-border: rgba(255,255,255,0.1);
    --list-bg: rgba(255,255,255,0.05);
    --list-border: rgba(255,255,255,0.1);
    --list-active-bg: rgba(255,255,255,0.1);
    --nav-bg: rgba(0,0,0,0.8);
    --nav-border: rgba(255,255,255,0.1);
    --loader-track: rgba(255,255,255,0.1);
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Touch-specific optimizations */
@media (hover: none) and (pointer: coarse) {
  .button-modern,
  .list-item-modern {
    cursor: default;
  }

  .card-modern:hover {
    transform: none;
  }
}

/* Enhanced focus styles */
:focus-visible {
  outline: 2px solid var(--focus-ring, rgba(230,57,70,0.5));
  outline-offset: 2px;
}
