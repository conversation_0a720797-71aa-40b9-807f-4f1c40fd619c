import React, { useState, useEffect, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Loader2, X, Trash2, AlertTriangle, Upload, Image as ImageIcon } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { updateCourseSimple, createCourseSimple } from '@/services/course/simpleCourseUpdate';

interface CourseFormValues {
  title: string;
  slug: string;
  description: string;
  instructor: string;
}

interface SimpleCourseEditorProps {
  courseId?: string;
  onClose: () => void;
  initialData?: {
    title: string;
    slug: string;
    description: string;
    instructor: string;
    image_url?: string;
  };
}

export function SimpleCourseEditor({ courseId, onClose, initialData }: SimpleCourseEditorProps) {
  const { toast } = useToast();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const isNewCourse = !courseId || courseId === 'new';

  // State for image handling
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [columnMissing, setColumnMissing] = useState(false);
  const columnErrorShown = useRef(false);

  // Form setup
  const form = useForm<CourseFormValues>({
    defaultValues: {
      title: initialData?.title || '',
      slug: initialData?.slug || '',
      description: initialData?.description || '',
      instructor: initialData?.instructor || '',
    }
  });

  // Set image preview from initial data
  useEffect(() => {
    if (initialData?.image_url) {
      setImagePreview(initialData.image_url);
    }
  }, [initialData]);

  // Handle form submission
  const onSubmit = async (values: CourseFormValues) => {
    try {
      setIsSubmitting(true);

      // Generate a slug if not provided
      const finalSlug = values.slug || values.title.toLowerCase().replace(/\\s+/g, '-');

      if (isNewCourse) {
        // Create a new course
        const newCourse = await createCourseSimple({
          title: values.title,
          slug: finalSlug,
          description: values.description,
          instructor: values.instructor,
          imageFile: imageFile
        });

        toast({
          title: "Course created",
          description: "The course has been created successfully.",
        });

        // Refresh queries
        queryClient.invalidateQueries({ queryKey: ['admin-courses'] });

        // Navigate to the new course
        navigate(`/admin?course=${newCourse.id}`);
      } else {
        // Update existing course
        await updateCourseSimple({
          id: courseId!,
          title: values.title,
          slug: finalSlug,
          description: values.description,
          instructor: values.instructor,
          imageFile: imageFile
        });

        toast({
          title: "Course updated",
          description: "The course has been updated successfully.",
        });

        // Refresh queries
        queryClient.invalidateQueries({ queryKey: ['admin-courses'] });
        queryClient.invalidateQueries({ queryKey: ['course', courseId] });

        // Close the editor
        onClose();
      }
    } catch (error: any) {
      console.error('Error saving course:', error);

      // Check if this is a column missing error
      if (error.message && error.message.includes('image_url') && !columnErrorShown.current) {
        setColumnMissing(true);
        columnErrorShown.current = true;

        toast({
          title: "Database Schema Issue",
          description: "The image_url column is missing from the database. Images will not be saved until this is fixed.",
          variant: "destructive",
          duration: 10000, // Show for longer
        });
      } else {
        toast({
          title: "Error",
          description: error.message || "Failed to save course. Please try again.",
          variant: "destructive",
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle image selection
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Check file size (max 2MB)
      if (file.size > 2 * 1024 * 1024) {
        toast({
          title: "Image too large",
          description: "Please select an image smaller than 2MB.",
          variant: "destructive",
        });
        return;
      }

      setImageFile(file);
      const previewUrl = URL.createObjectURL(file);
      setImagePreview(previewUrl);
    }
  };

  // Handle image removal
  const handleRemoveImage = () => {
    setImageFile(null);
    setImagePreview(null);
  };

  // Handle slug generation
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const title = e.target.value;
    // Only auto-generate slug if it's empty or if we're creating a new course
    if (isNewCourse || !form.getValues('slug')) {
      const slug = title.toLowerCase()
        .replace(/[^\\w\\s-]/g, '')
        .replace(/\\s+/g, '-');
      form.setValue('slug', slug);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">
          {isNewCourse ? "Create New Course" : "Edit Course"}
        </h2>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Course Image */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <FormLabel htmlFor="image">Course Image</FormLabel>
              <p className="text-xs text-muted-foreground">Max size: 2MB</p>
            </div>

            {columnMissing && (
              <div className="bg-amber-50 border border-amber-200 rounded-md p-3 mb-3 text-amber-800 text-sm">
                <p className="font-medium">Database Schema Issue</p>
                <p className="mt-1 text-xs">The image_url column is missing from the database. Images will not be saved until this is fixed.</p>
                <p className="mt-1 text-xs">Please run the migration script to add the column.</p>
              </div>
            )}
            <div className="flex flex-col space-y-4">
              {imagePreview ? (
                <div className="relative w-full max-w-md aspect-video rounded-lg overflow-hidden border border-border">
                  <img
                    src={imagePreview}
                    alt="Course preview"
                    className="w-full h-full object-cover"
                  />
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    className="absolute top-2 right-2 opacity-90"
                    onClick={handleRemoveImage}
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    Remove
                  </Button>
                </div>
              ) : (
                <div className="w-full max-w-md aspect-video rounded-lg bg-muted flex flex-col items-center justify-center border border-dashed border-border p-4">
                  <ImageIcon className="h-10 w-10 text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground">No image selected</p>
                  <p className="text-xs text-muted-foreground mt-1">Recommended size: 600x400px</p>
                </div>
              )}
              <div className="flex items-center gap-2">
                <Input
                  id="image"
                  type="file"
                  accept="image/*"
                  onChange={handleImageChange}
                  className="max-w-md"
                />
              </div>
            </div>
          </div>

          <FormField
            control={form.control}
            name="title"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Course Title</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter course title"
                    {...field}
                    onChange={(e) => {
                      field.onChange(e);
                      handleTitleChange(e);
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="slug"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Course Slug</FormLabel>
                <FormControl>
                  <Input
                    placeholder="course-slug"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter course description"
                    className="min-h-[100px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="instructor"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Instructor</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter instructor name"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                isNewCourse ? "Create Course" : "Update Course"
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
