var k=(m,g,a)=>new Promise((j,x)=>{var t=s=>{try{i(a.next(s))}catch(f){x(f)}},n=s=>{try{i(a.throw(s))}catch(f){x(f)}},i=s=>s.done?j(s.value):Promise.resolve(s.value).then(t,n);i((a=a.apply(m,g)).next())});import{r as C,j as e,bt as y,br as N,aF as H,E as h,Y as F}from"./vendor-react.BcAa1DKr.js";import{U as p}from"./unified-markdown-editor.BzxuP8op.js";import{C as r,a as l,b as o,c as u,d}from"./card.B9V6b2DK.js";import{B as c}from"./badge.C87ZuIxl.js";import{B as T}from"./index.BLDhDn0D.js";import{T as M,a as S,b,c as w}from"./tabs.B0SF6qIv.js";import{a2 as v}from"./vendor.DQpuTRuB.js";import"./markdown-preview.Bw0U-NJA.js";import"./content-converter.L-GziWIP.js";import"./tiptap-image-upload.B_U9gNrF.js";import"./vendor-supabase.sufZ44-y.js";const z=()=>{const[m,g]=C.useState(`# Unified Markdown Editor Test

Welcome to the **comprehensive test** of our unified TipTap Markdown editor! This editor now supports all GitHub Flavored Markdown features.

## Text Formatting

Here's some text with different formatting:
- **Bold text**
- *Italic text*
- ~~Strikethrough text~~
- ==Highlighted text==
- <u>Underlined text</u>
- \`inline code\`

## Headers

# H1 Header
## H2 Header  
### H3 Header
#### H4 Header
##### H5 Header
###### H6 Header

## Lists

### Bullet Lists
- First item
- Second item
  - Nested item
  - Another nested item
- Third item

### Numbered Lists
1. First numbered item
2. Second numbered item
   1. Nested numbered item
   2. Another nested item
3. Third numbered item

### Task Lists
- [x] Completed task
- [x] Another completed task
- [ ] Incomplete task
- [ ] Another incomplete task
  - [x] Nested completed task
  - [ ] Nested incomplete task

## Code Blocks

Here's a JavaScript code block:

\`\`\`javascript
function greetUser(name) {
  console.log(\`Hello, \${name}!\`);
  return \`Welcome to the unified editor!\`;
}

greetUser('Developer');
\`\`\`

And a Python example:

\`\`\`python
def calculate_fibonacci(n):
    if n <= 1:
        return n
    return calculate_fibonacci(n-1) + calculate_fibonacci(n-2)

print(calculate_fibonacci(10))
\`\`\`

## Tables

| Feature | Status | Notes |
|---------|--------|-------|
| **Bold text** | ✅ Working | Properly formatted |
| *Italic text* | ✅ Working | Looks great |
| ~~Strikethrough~~ | ✅ Working | Now supported! |
| Task lists | ✅ Working | Interactive checkboxes |
| Tables | ✅ Working | Responsive design |
| Code blocks | ✅ Working | Syntax highlighting |

## Blockquotes

> This is a blockquote with some important information.
> 
> It can span multiple lines and contains **formatted text**.

## Links and Images

Here's a [link to GitHub](https://github.com) and here's an image:

![Sample Image](https://via.placeholder.com/400x200/E63946/FFFFFF?text=Unified+Editor)

## Horizontal Rules

---

## Callouts (Custom Extension)

> [!INFO] Information Callout
> This is an informational callout with important details.

> [!WARNING] Warning Callout  
> This is a warning callout to draw attention.

> [!SUCCESS] Success Callout
> This indicates something was completed successfully.

> [!ERROR] Error Callout
> This indicates an error or problem.

## Collapsible Sections

<details>
<summary>Click to expand this section</summary>

This content is hidden by default and can be expanded by clicking the summary.

You can include any markdown content here:
- Lists
- **Formatted text**
- \`Code\`
- And more!

</details>

## Advanced Features

### Math Expressions (Future)
When LaTeX support is added, you'll be able to write:
$$E = mc^2$$

### Diagrams (Future)
Mermaid diagram support coming soon:
\`\`\`mermaid
graph TD
    A[Start] --> B{Decision}
    B -->|Yes| C[Action 1]
    B -->|No| D[Action 2]
\`\`\`

---

**Test completed!** The unified editor now supports all major GitHub Flavored Markdown features with a consistent, professional interface.`),a=()=>k(void 0,null,function*(){try{yield navigator.clipboard.writeText(m),v.success("Markdown copied to clipboard!")}catch(t){v.error("Failed to copy to clipboard")}}),j=()=>{const t=new Blob([m],{type:"text/markdown"}),n=URL.createObjectURL(t),i=document.createElement("a");i.href=n,i.download="unified-editor-test.md",document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(n),v.success("Markdown file downloaded!")},x=[{icon:e.jsx(h,{className:"h-5 w-5 text-green-600"}),title:"GitHub Flavored Markdown",description:"Full GFM support including tables, task lists, and strikethrough",status:"✅ Implemented"},{icon:e.jsx(h,{className:"h-5 w-5 text-green-600"}),title:"Live Preview",description:"Real-time preview with split-pane and full-screen modes",status:"✅ Implemented"},{icon:e.jsx(h,{className:"h-5 w-5 text-green-600"}),title:"Syntax Highlighting",description:"Code blocks with syntax highlighting for 20+ languages",status:"✅ Implemented"},{icon:e.jsx(h,{className:"h-5 w-5 text-green-600"}),title:"Image Upload",description:"Drag & drop image upload with Supabase storage integration",status:"✅ Implemented"},{icon:e.jsx(h,{className:"h-5 w-5 text-green-600"}),title:"Custom Extensions",description:"Details/summary collapsible sections and callout blocks",status:"✅ Implemented"},{icon:e.jsx(F,{className:"h-5 w-5 text-yellow-600"}),title:"Math Support",description:"LaTeX math expressions (planned for future release)",status:"🔄 Planned"}];return e.jsxs("div",{className:"container mx-auto py-8 px-4 max-w-7xl",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-4xl font-bold mb-4",children:"Unified Markdown Editor Test"}),e.jsx("p",{className:"text-lg text-muted-foreground mb-6",children:"Comprehensive test of the unified TipTap Markdown editor with all GitHub Flavored Markdown features."}),e.jsxs("div",{className:"flex flex-wrap gap-2 mb-6",children:[e.jsx(c,{variant:"default",children:"TipTap v2.12.0"}),e.jsx(c,{variant:"secondary",children:"GitHub Flavored Markdown"}),e.jsx(c,{variant:"outline",children:"Live Preview"}),e.jsx(c,{variant:"outline",children:"Image Upload"}),e.jsx(c,{variant:"outline",children:"Custom Extensions"})]})]}),e.jsxs(M,{defaultValue:"editor",className:"space-y-6",children:[e.jsxs(S,{className:"grid w-full grid-cols-3",children:[e.jsx(b,{value:"editor",children:"Editor Test"}),e.jsx(b,{value:"features",children:"Features"}),e.jsx(b,{value:"themes",children:"Themes"})]}),e.jsx(w,{value:"editor",className:"space-y-6",children:e.jsxs(r,{children:[e.jsx(l,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx(o,{children:"Unified Markdown Editor"}),e.jsx(u,{children:"Test all features including GFM support, custom extensions, and live preview."})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(T,{variant:"outline",size:"sm",onClick:a,className:"flex items-center gap-2",children:[e.jsx(y,{className:"h-4 w-4"}),"Copy Markdown"]}),e.jsxs(T,{variant:"outline",size:"sm",onClick:j,className:"flex items-center gap-2",children:[e.jsx(N,{className:"h-4 w-4"}),"Download"]})]})]})}),e.jsx(d,{children:e.jsx(p,{initialContent:m,onChange:g,placeholder:"Start testing the unified editor...",minHeight:600,autoFocus:!1,showToolbar:!0,showPreview:!0,mode:"editor",theme:"github",courseId:"test-course",moduleId:"test-module"})})]})}),e.jsx(w,{value:"features",className:"space-y-6",children:e.jsx("div",{className:"grid gap-6 md:grid-cols-2",children:x.map((t,n)=>e.jsxs(r,{children:[e.jsx(l,{children:e.jsxs("div",{className:"flex items-center gap-3",children:[t.icon,e.jsxs("div",{children:[e.jsx(o,{className:"text-lg",children:t.title}),e.jsx(u,{children:t.description})]})]})}),e.jsx(d,{children:e.jsx(c,{variant:t.status.includes("✅")?"default":"secondary",children:t.status})})]},n))})}),e.jsx(w,{value:"themes",className:"space-y-6",children:e.jsxs("div",{className:"grid gap-6 md:grid-cols-3",children:[e.jsxs(r,{children:[e.jsxs(l,{children:[e.jsx(o,{children:"GitHub Theme"}),e.jsx(u,{children:"Clean, professional GitHub-style editor"})]}),e.jsx(d,{children:e.jsx(p,{initialContent:"# GitHub Theme\\n\\nThis is the **GitHub-style** theme with clean typography and familiar styling.",minHeight:200,showToolbar:!1,showPreview:!1,theme:"github"})})]}),e.jsxs(r,{children:[e.jsxs(l,{children:[e.jsx(o,{children:"Obsidian Theme"}),e.jsx(u,{children:"Obsidian-inspired theme for note-taking"})]}),e.jsx(d,{children:e.jsx(p,{initialContent:"# Obsidian Theme\\n\\nThis is the **Obsidian-inspired** theme with enhanced readability and modern styling.",minHeight:200,showToolbar:!1,showPreview:!1,theme:"obsidian"})})]}),e.jsxs(r,{children:[e.jsxs(l,{children:[e.jsx(o,{children:"Minimal Theme"}),e.jsx(u,{children:"Clean, distraction-free minimal theme"})]}),e.jsx(d,{children:e.jsx(p,{initialContent:"# Minimal Theme\\n\\nThis is the **minimal** theme with clean, distraction-free styling perfect for focused writing.",minHeight:200,showToolbar:!1,showPreview:!1,theme:"minimal"})})]})]})})]}),e.jsxs(r,{className:"mt-8",children:[e.jsx(l,{children:e.jsxs(o,{className:"flex items-center gap-2",children:[e.jsx(H,{className:"h-5 w-5"}),"Implementation Status"]})}),e.jsx(d,{children:e.jsxs("div",{className:"grid gap-4 md:grid-cols-2",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-green-600 mb-2",children:"✅ Completed Features"}),e.jsxs("ul",{className:"space-y-1 text-sm",children:[e.jsx("li",{children:"• Strikethrough extension installed"}),e.jsx("li",{children:"• Custom Details/Summary extension"}),e.jsx("li",{children:"• Custom Callout extension"}),e.jsx("li",{children:"• Unified markdown serializer"}),e.jsx("li",{children:"• Consolidated CSS styling"}),e.jsx("li",{children:"• Image upload functionality"}),e.jsx("li",{children:"• Live preview with split mode"}),e.jsx("li",{children:"• Multiple theme support"})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-blue-600 mb-2",children:"🔄 Next Steps"}),e.jsxs("ul",{className:"space-y-1 text-sm",children:[e.jsx("li",{children:"• Replace existing editor implementations"}),e.jsx("li",{children:"• Add comprehensive test suite"}),e.jsx("li",{children:"• Implement LaTeX math support"}),e.jsx("li",{children:"• Add Mermaid diagram support"}),e.jsx("li",{children:"• Performance optimization"}),e.jsx("li",{children:"• Accessibility improvements"})]})]})]})})]})]})};export{z as default};
