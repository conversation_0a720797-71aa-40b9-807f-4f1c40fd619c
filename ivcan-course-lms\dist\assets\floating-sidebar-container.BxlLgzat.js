var b=Object.defineProperty,j=Object.defineProperties;var v=Object.getOwnPropertyDescriptors;var i=Object.getOwnPropertySymbols;var h=Object.prototype.hasOwnProperty,y=Object.prototype.propertyIsEnumerable;var f=(t,a,e)=>a in t?b(t,a,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[a]=e,m=(t,a)=>{for(var e in a||(a={}))h.call(a,e)&&f(t,e,a[e]);if(i)for(var e of i(a))y.call(a,e)&&f(t,e,a[e]);return t},o=(t,a)=>j(t,v(a));var c=(t,a)=>{var e={};for(var l in t)h.call(t,l)&&a.indexOf(l)<0&&(e[l]=t[l]);if(t!=null&&i)for(var l of i(t))a.indexOf(l)<0&&y.call(t,l)&&(e[l]=t[l]);return e};import{j as r}from"./vendor-react.BcAa1DKr.js";import{a as g,c as u}from"./index.BLDhDn0D.js";function M(C){var s=C,{children:t,className:a,maxWidth:e="4xl",padding:l="md",centerContent:p=!0,fullWidth:n=!1}=s,d=c(s,["children","className","maxWidth","padding","centerContent","fullWidth"]);const x=g(768),W={sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg",xl:"max-w-xl","2xl":"max-w-2xl","3xl":"max-w-3xl","4xl":"max-w-4xl","5xl":"max-w-5xl","6xl":"max-w-6xl","7xl":"max-w-7xl",full:"max-w-full"},w={none:"",sm:"px-3 py-3",md:"px-4 py-4",lg:"px-5 py-5",xl:"px-6 py-6"};return r.jsx("div",o(m({className:u("w-full",!n&&[W[e],p&&"mx-auto"],w[l],!x&&"relative",a)},d),{children:t}))}function E(p){var n=p,{children:t,className:a,pageType:e="default"}=n,l=c(n,["children","className","pageType"]);const d=g(768),s={default:{maxWidth:"4xl",padding:"md",centerContent:!0,fullWidth:!1},dashboard:{maxWidth:"6xl",padding:"md",centerContent:!0,fullWidth:!1},lesson:{maxWidth:"full",padding:"none",centerContent:!1,fullWidth:!0},module:{maxWidth:"4xl",padding:"md",centerContent:!0,fullWidth:!1},certificate:{maxWidth:"5xl",padding:"md",centerContent:!0,fullWidth:!1},"full-width":{maxWidth:"full",padding:"none",centerContent:!1,fullWidth:!0}}[e];return r.jsx(M,o(m({className:u(e==="lesson"&&["!px-0 !py-0",!d&&"!pl-0"],e==="dashboard"&&["space-y-4 md:space-y-5"],e==="module"&&["space-y-3 md:space-y-3"],e==="certificate"&&["space-y-4 md:space-y-5"],a),maxWidth:s.maxWidth,padding:s.padding,centerContent:s.centerContent,fullWidth:s.fullWidth},l),{children:t}))}function F(p){var n=p,{children:t,className:a,spacing:e="md"}=n,l=c(n,["children","className","spacing"]);const d={sm:"space-y-2 md:space-y-2",md:"space-y-3 md:space-y-3",lg:"space-y-4 md:space-y-4",xl:"space-y-5 md:space-y-6"};return r.jsx("div",o(m({className:u(d[e],a)},l),{children:t}))}export{F as C,E as P};
