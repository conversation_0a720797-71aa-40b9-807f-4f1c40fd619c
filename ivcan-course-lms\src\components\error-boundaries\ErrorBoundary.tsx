import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { logEvent } from '@/services/analytics/analyticsService';
import { ErrorType, getErrorType } from '@/services/error/errorService';

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  componentName?: string;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorType: ErrorType | null;
}

/**
 * Error Boundary Component
 * 
 * This component catches JavaScript errors in its child component tree,
 * logs those errors, and displays a fallback UI instead of the component
 * tree that crashed.
 */
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorType: null
    };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorType: getErrorType(error)
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to the console
    console.error('Error caught by ErrorBoundary:', error, errorInfo);
    
    // Log the error to analytics
    logEvent('error_boundary_caught', {
      errorMessage: error.message,
      errorType: this.state.errorType,
      componentName: this.props.componentName || 'UnknownComponent',
      componentStack: errorInfo.componentStack
    });
  }

  handleRetry = (): void => {
    // Reset the error state to retry rendering the component
    this.setState({
      hasError: false,
      error: null,
      errorType: null
    });
  };

  render(): ReactNode {
    if (this.state.hasError) {
      // If a custom fallback is provided, use it
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Otherwise, show a default error UI
      return (
        <div className="flex flex-col items-center justify-center min-h-[200px] p-6 border rounded-lg shadow-sm bg-red-50 dark:bg-red-950/20 text-center">
          <AlertCircle className="w-10 h-10 text-red-500 mb-4" />
          <h2 className="text-xl font-semibold mb-2 text-red-800 dark:text-red-300">Something went wrong</h2>
          <p className="text-muted-foreground mb-4 text-center max-w-md text-red-700 dark:text-red-400">
            We encountered an error while rendering this component.
          </p>
          <Button 
            onClick={this.handleRetry}
            variant="outline"
            className="flex items-center gap-2 bg-white dark:bg-red-900/20"
          >
            <RefreshCw className="w-4 h-4" />
            Try Again
          </Button>
          {this.state.error && process.env.NODE_ENV === 'development' && (
            <div className="mt-6 p-4 bg-muted rounded text-sm overflow-auto max-w-full max-h-[200px]">
              <p className="font-mono">{this.state.error.toString()}</p>
            </div>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
