var oe=Object.defineProperty,de=Object.defineProperties;var ce=Object.getOwnPropertyDescriptors;var Q=Object.getOwnPropertySymbols;var ue=Object.prototype.hasOwnProperty,me=Object.prototype.propertyIsEnumerable;var O=(t,l,i)=>l in t?oe(t,l,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[l]=i,p=(t,l)=>{for(var i in l||(l={}))ue.call(l,i)&&O(t,i,l[i]);if(Q)for(var i of Q(l))me.call(l,i)&&O(t,i,l[i]);return t},N=(t,l)=>de(t,ce(l));var x=(t,l,i)=>new Promise((r,o)=>{var j=c=>{try{h(i.next(c))}catch(v){o(v)}},f=c=>{try{h(i.throw(c))}catch(v){o(v)}},h=c=>c.done?r(c.value):Promise.resolve(c.value).then(j,f);h((i=i.apply(t,l)).next())});import{r as C,aw as he,az as V,ax as S,j as e,$ as z,av as H,aI as $,aJ as xe,aK as ge,aL as pe,aM as J,aN as W,aH as je,u as fe,at as ve}from"./vendor-react.BcAa1DKr.js";import{L as A}from"./Layout.DRjmVYQG.js";import{g as X,s as I,B as m,D as be,h as ye,i as we,j as Ne,k as Ce,I as R,l as Me}from"./index.BLDhDn0D.js";import{T as ke,a as _e,b as Y,c as T,d as De,e as E,S as Te,B as Ee,f as Fe,g as U,h as q,i as G}from"./breadcrumb.DyMjeizE.js";import{L as _}from"./label.D4YlnUPk.js";import{C as Le,a as Se,b as Ae,c as Re,d as Ue}from"./card.B9V6b2DK.js";import{u as B}from"./courseApi.BQX5-7u-.js";import{A as qe,a as Be,b as Pe,c as ze,d as Ie,e as Ke,f as Qe,g as Oe,h as He}from"./alert-dialog.BK1A3Gb_.js";import{P,C as $e}from"./floating-sidebar-container.BxlLgzat.js";import"./vendor.DQpuTRuB.js";import"./vendor-supabase.sufZ44-y.js";import"./utils.Qa9QlCj_.js";import"./enrollmentApi.CstnsQi_.js";const Je=({courseId:t})=>{const[l,i]=C.useState(!1),[r,o]=C.useState(null),[j,f]=C.useState(null),[h,c]=C.useState(null),[v,D]=C.useState(null),[F,L]=C.useState(!1),{toast:d}=X(),b=he(),{data:n,isLoading:Z}=V({queryKey:["course-modules",t],queryFn:()=>x(void 0,null,function*(){const{data:s,error:a}=yield I.from("modules").select("*").eq("course_id",t).order("module_number",{ascending:!0});if(a)throw d({title:"Error fetching modules",description:a.message,variant:"destructive"}),a;return s})}),M=S({mutationFn:s=>x(void 0,null,function*(){const a=yield B(s.id,s);if(!a)throw new Error("Failed to update module");return a}),onSuccess:()=>{d({title:"Module updated",description:"The module has been updated successfully."}),b.invalidateQueries({queryKey:["course-modules",t]}),b.invalidateQueries({queryKey:["courseModules",t]}),i(!1),o(null)},onError:s=>{d({title:"Error updating module",description:s.message,variant:"destructive"})}}),ee=S({mutationFn:s=>x(void 0,null,function*(){const{error:a}=yield I.from("modules").delete().eq("id",s);if(a)throw a;return s}),onSuccess:()=>{d({title:"Module deleted",description:"The module has been deleted successfully."}),b.invalidateQueries({queryKey:["course-modules",t]}),b.invalidateQueries({queryKey:["courseModules",t]}),f(null)},onError:s=>{d({title:"Error deleting module",description:s.message,variant:"destructive"})}}),se=S({mutationFn:g=>x(void 0,[g],function*({moduleId:s,newPosition:a}){const u=n==null?void 0:n.find(k=>k.id===s);if(!u)throw new Error("Module not found");const y=yield B(s,{module_number:a});if(!y)throw new Error("Failed to update module position");const w=n==null?void 0:n.find(k=>k.module_number===a&&k.id!==s);if(w&&!(yield B(w.id,{module_number:u.module_number})))throw new Error("Failed to swap module positions");return y}),onSuccess:()=>{b.invalidateQueries({queryKey:["course-modules",t]}),b.invalidateQueries({queryKey:["courseModules",t]})},onError:s=>{d({title:"Error reordering modules",description:s.message,variant:"destructive"})}}),K=(s,a)=>{const g=(n==null?void 0:n.findIndex(w=>w.id===s))||0;if(g===-1)return;const u=n==null?void 0:n[g];if(!u)return;const y=a==="up"?Math.max(1,u.module_number-1):u.module_number+1;se.mutate({moduleId:s,newPosition:y})},ae=(s,a)=>{M.mutate({id:s,is_locked:!a})},re=s=>x(void 0,null,function*(){var g;const a=(g=s.target.files)==null?void 0:g[0];if(a)try{if(!a.type.startsWith("image/")){d({title:"Invalid file type",description:"Please upload an image file",variant:"destructive"});return}if(a.size>5*1024*1024){d({title:"Image too large",description:"Please select an image smaller than 5MB",variant:"destructive"});return}c(a);const u=URL.createObjectURL(a);D(u),d({title:"Image selected",description:"The image will be uploaded when you save the module"})}catch(u){console.error("Error handling image:",u),d({title:"Error",description:"Failed to process image",variant:"destructive"})}}),te=()=>{c(null),D(null),r&&o(N(p({},r),{image_url:""}))},ie=s=>{o(p({},s)),D(s.image_url||null),i(!0)},le=()=>x(void 0,null,function*(){if(!r)return;let s=r.image_url||"";if(h){L(!0);try{const a=new FileReader,g=new Promise((y,w)=>{a.onload=()=>y(a.result),a.onerror=w});a.readAsDataURL(h),s=yield g,console.log("Image processed as data URL")}catch(a){console.error("Error processing image:",a),d({title:"Error",description:a.message||"Failed to process image",variant:"destructive"}),L(!1);return}L(!1)}try{yield M.mutateAsync({id:r.id,title:r.title,slug:r.slug,module_number:r.module_number,is_locked:r.is_locked,image_url:s}),i(!1),o(null),c(null),D(null),d({title:"Success",description:"Module updated successfully"})}catch(a){console.error("Error updating module:",a),d({title:"Error",description:"Failed to update module",variant:"destructive"})}}),ne=()=>{j&&ee.mutate(j)};return Z?e.jsx("div",{className:"flex justify-center p-8",children:e.jsx(z,{className:"w-8 h-8 animate-spin text-primary"})}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs(Le,{children:[e.jsxs(Se,{children:[e.jsx(Ae,{children:"Module Management"}),e.jsx(Re,{children:"Manage the modules for this course. You can edit, reorder, or delete modules."})]}),e.jsx(Ue,{children:n&&n.length>0?e.jsxs(ke,{children:[e.jsx(_e,{children:e.jsxs(Y,{children:[e.jsx(T,{children:"Order"}),e.jsx(T,{children:"Title"}),e.jsx(T,{children:"Status"}),e.jsx(T,{className:"text-right",children:"Actions"})]})}),e.jsx(De,{children:n.map(s=>e.jsxs(Y,{children:[e.jsx(E,{className:"font-medium",children:s.module_number}),e.jsx(E,{children:s.title}),e.jsx(E,{children:s.is_locked?e.jsxs("span",{className:"flex items-center text-amber-500",children:[e.jsx(H,{className:"h-4 w-4 mr-1"})," Locked"]}):e.jsxs("span",{className:"flex items-center text-green-500",children:[e.jsx($,{className:"h-4 w-4 mr-1"})," Unlocked"]})}),e.jsx(E,{className:"text-right",children:e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx(m,{variant:"outline",size:"sm",onClick:()=>K(s.id,"up"),disabled:s.module_number<=1,children:e.jsx(xe,{className:"h-4 w-4"})}),e.jsx(m,{variant:"outline",size:"sm",onClick:()=>K(s.id,"down"),disabled:s.module_number>=((n==null?void 0:n.length)||0),children:e.jsx(ge,{className:"h-4 w-4"})}),e.jsx(m,{variant:"outline",size:"sm",onClick:()=>ae(s.id,s.is_locked),children:s.is_locked?e.jsx($,{className:"h-4 w-4"}):e.jsx(H,{className:"h-4 w-4"})}),e.jsx(m,{variant:"outline",size:"sm",onClick:()=>ie(s),children:e.jsx(pe,{className:"h-4 w-4"})}),e.jsxs(qe,{children:[e.jsx(Be,{asChild:!0,children:e.jsx(m,{variant:"outline",size:"sm",onClick:()=>f(s.id),children:e.jsx(J,{className:"h-4 w-4 text-destructive"})})}),e.jsxs(Pe,{children:[e.jsxs(ze,{children:[e.jsx(Ie,{children:"Are you sure?"}),e.jsx(Ke,{children:"This action cannot be undone. This will permanently delete the module and all its lessons."})]}),e.jsxs(Qe,{children:[e.jsx(Oe,{children:"Cancel"}),e.jsx(He,{onClick:ne,children:"Delete"})]})]})]})]})})]},s.id))})]}):e.jsx("div",{className:"text-center p-8 border rounded-md bg-muted/20",children:e.jsx("p",{className:"text-muted-foreground mb-4",children:"No modules found for this course"})})})]}),e.jsx(be,{open:l,onOpenChange:i,children:e.jsxs(ye,{className:"sm:max-w-[425px]",children:[e.jsxs(we,{children:[e.jsx(Ne,{children:"Edit Module"}),e.jsx(Ce,{children:"Make changes to the module details below."})]}),r&&e.jsxs("div",{className:"space-y-4 py-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(_,{htmlFor:"image",children:"Module Image"}),e.jsxs("div",{className:"flex flex-col space-y-4",children:[v?e.jsxs("div",{className:"relative w-full max-w-md aspect-video rounded-lg overflow-hidden border border-border",children:[e.jsx("img",{src:v,alt:"Module preview",className:"w-full h-full object-cover"}),e.jsxs("div",{className:"absolute top-2 right-2 flex space-x-2",children:[e.jsxs(m,{type:"button",variant:"destructive",size:"sm",className:"opacity-90",onClick:te,children:[e.jsx(J,{className:"h-4 w-4 mr-1"}),"Remove"]}),e.jsxs(m,{type:"button",variant:"secondary",size:"sm",className:"opacity-90 bg-white",onClick:()=>{var s;(s=document.getElementById("module-image"))==null||s.click()},children:[e.jsx(W,{className:"h-4 w-4 mr-1"}),"Replace"]})]})]}):e.jsxs("div",{onClick:()=>{var s;return(s=document.getElementById("module-image"))==null?void 0:s.click()},className:"w-full max-w-md aspect-video rounded-lg border-2 border-dashed border-border hover:border-primary/50 transition-colors cursor-pointer flex flex-col items-center justify-center",children:[e.jsx(W,{className:"h-8 w-8 text-muted-foreground mb-2"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Click to upload module image"}),e.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:"Recommended size: 1280x720px (max 5MB)"})]}),e.jsx("input",{id:"module-image",type:"file",accept:"image/*",className:"hidden",onChange:re})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(_,{htmlFor:"title",children:"Title"}),e.jsx(R,{id:"title",value:r.title,onChange:s=>o(N(p({},r),{title:s.target.value}))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(_,{htmlFor:"slug",children:"URL Slug"}),e.jsx(R,{id:"slug",value:r.slug,onChange:s=>o(N(p({},r),{slug:s.target.value}))})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(_,{htmlFor:"module_number",children:"Module Order"}),e.jsx(R,{id:"module_number",type:"number",min:1,value:r.module_number,onChange:s=>o(N(p({},r),{module_number:parseInt(s.target.value)||1}))})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Te,{id:"is_locked",checked:r.is_locked,onCheckedChange:s=>o(N(p({},r),{is_locked:s}))}),e.jsx(_,{htmlFor:"is_locked",children:"Lock this module"})]})]}),e.jsxs(Me,{children:[e.jsx(m,{variant:"outline",onClick:()=>i(!1),children:"Cancel"}),e.jsxs(m,{onClick:le,disabled:F||M.isPending,children:[(F||M.isPending)&&e.jsx(z,{className:"mr-2 h-4 w-4 animate-spin"}),F?"Uploading...":M.isPending?"Saving...":"Save Changes"]})]})]})})]})},os=()=>{const{courseId:t}=je(),l=fe(),{toast:i}=X(),{data:r,isLoading:o}=V({queryKey:["course-details",t],queryFn:()=>x(void 0,null,function*(){if(!t)return null;const{data:f,error:h}=yield I.from("courses").select("*").eq("id",t).single();if(h)throw i({title:"Error fetching course",description:h.message,variant:"destructive"}),h;return f}),enabled:!!t}),j=()=>{l(`/course/${r==null?void 0:r.slug}`)};return o?e.jsx(A,{children:e.jsx(P,{pageType:"default",children:e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx(z,{className:"w-8 h-8 animate-spin text-primary"})})})}):r?e.jsx(A,{children:e.jsx(P,{pageType:"default",children:e.jsxs($e,{spacing:"md",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs(m,{variant:"ghost",size:"sm",className:"mb-4",onClick:j,children:[e.jsx(ve,{className:"mr-2 h-4 w-4"}),"Back to Course"]}),e.jsx(Ee,{className:"mb-6",children:e.jsxs(Fe,{children:[e.jsx(U,{children:e.jsx(q,{href:"/dashboard",children:"Dashboard"})}),e.jsx(G,{}),e.jsx(U,{children:e.jsx(q,{href:`/course/${r.slug}`,children:r.title})}),e.jsx(G,{}),e.jsx(U,{children:e.jsx(q,{children:"Module Management"})})]})}),e.jsx("h1",{className:"text-3xl font-bold",children:r.title}),e.jsx("p",{className:"text-muted-foreground mt-2",children:"Manage modules for this course"})]})}),t&&e.jsx(Je,{courseId:t})]})})}):e.jsx(A,{children:e.jsx(P,{pageType:"default",children:e.jsxs("div",{className:"text-center p-8",children:[e.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Course Not Found"}),e.jsx("p",{className:"mb-6",children:"The course you're looking for doesn't exist or you don't have permission to access it."}),e.jsx(m,{onClick:()=>l("/dashboard"),children:"Return to Dashboard"})]})})})};export{os as default};
