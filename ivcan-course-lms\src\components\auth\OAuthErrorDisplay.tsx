import React from 'react';
import { AlertCircle } from 'lucide-react';

interface OAuthErrorDisplayProps {
  error: Error | null;
  provider: string;
  onDismiss: () => void;
}

const OAuthErrorDisplay: React.FC<OAuthErrorDisplayProps> = ({ 
  error, 
  provider,
  onDismiss 
}) => {
  if (!error) return null;

  // Extract the error message
  const errorMessage = error.message || `Error connecting to ${provider}`;
  
  // Common OAuth errors and their user-friendly messages
  const friendlyMessages: Record<string, string> = {
    'popup_closed_by_user': 'The sign-in window was closed. Please try again.',
    'popup_blocked_by_browser': 'Your browser blocked the sign-in popup. Please allow popups for this site.',
    'access_denied': 'Access was denied. Please try again or use another sign-in method.',
    'invalid_client': 'Authentication configuration error. Please contact support.',
    'invalid_grant': 'Your session has expired. Please try signing in again.',
    'server_error': 'The authentication server encountered an error. Please try again later.',
    'temporarily_unavailable': 'The authentication service is temporarily unavailable. Please try again later.',
    'user_cancelled': 'Sign-in was cancelled. Please try again if you want to sign in.',
    'redirect_uri_mismatch': 'Authentication configuration error. Please contact support.',
  };

  // Check if the error message contains any of the known error codes
  const friendlyMessage = Object.entries(friendlyMessages).reduce((message, [code, friendly]) => {
    if (errorMessage.toLowerCase().includes(code.toLowerCase())) {
      return friendly;
    }
    return message;
  }, errorMessage);

  return (
    <div className="bg-red-50 border border-red-200 rounded-md p-4 mt-4 mb-4">
      <div className="flex">
        <div className="flex-shrink-0">
          <AlertCircle className="h-5 w-5 text-red-400" />
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-red-800">
            {provider} Sign-In Error
          </h3>
          <div className="mt-2 text-sm text-red-700">
            <p>{friendlyMessage}</p>
          </div>
          <div className="mt-4">
            <button
              type="button"
              onClick={onDismiss}
              className="text-sm font-medium text-red-600 hover:text-red-500"
            >
              Dismiss
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OAuthErrorDisplay;
