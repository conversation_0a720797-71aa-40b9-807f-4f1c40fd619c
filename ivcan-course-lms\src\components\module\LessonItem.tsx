import React from 'react';
import { Link } from 'react-router-dom';
import { Clock, CheckCircle, LockIcon, ScrollText } from 'lucide-react';
import { Lesson } from '@/services/courseService';
import { cn } from '@/lib/utils';

interface LessonItemProps {
  lesson: Lesson;
  courseId?: string;
  isTeacher?: boolean;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  isMobile?: boolean;
  isVerySmall?: boolean;
  isModuleLocked?: boolean;
}

const LessonItem: React.FC<LessonItemProps> = ({
  lesson,
  courseId,
  isTeacher,
  onEdit,
  onDelete,
  isMobile = false,
  isVerySmall = false,
  isModuleLocked = false
}) => {
  const lessonPath = `/course/${courseId}/lesson/${lesson.slug}`;

  // Check if lesson is locked (either module is locked, lesson itself is locked, or user is not a teacher)
  const isLessonLocked = isModuleLocked || lesson.is_locked;

  // Teachers can access all lessons regardless of lock status
  const canAccess = isTeacher || !isLessonLocked;

  // Determine icon based on lesson type and lock status
  const getLessonIcon = () => {
    if (isLessonLocked && !isTeacher) {
      return <LockIcon className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400 dark:text-gray-500" />;
    }
    if (lesson.type === 'quiz') {
      return <ScrollText className="w-4 h-4 sm:w-5 sm:h-5 text-primary dark:text-primary-foreground" />;
    }
    return <ScrollText className="w-4 h-4 sm:w-5 sm:h-5 text-primary dark:text-primary-foreground" />;
  };

  return (
    <Link
      to={canAccess ? lessonPath : '#'}
      className={cn(
        "flex items-center p-2 sm:p-3 rounded-lg border border-gray-200/70 dark:border-gray-700/50",
        "bg-white dark:bg-gray-800/70 hover:bg-gray-50 dark:hover:bg-gray-800",
        "transition-all duration-200 gap-2 sm:gap-3 shadow-sm",
        !canAccess && "opacity-60 cursor-not-allowed",
        lesson.completed ? "border-l-4 border-l-green-500 dark:border-l-green-400 bg-green-50/50 dark:bg-green-900/10" : ""
      )}
      onClick={(e) => {
        if (!canAccess) {
          e.preventDefault();
        }
      }}
    >
      <div className="flex items-center min-w-0 flex-1 gap-2 sm:gap-3">
        <div className="flex-shrink-0">
          {lesson.completed ? (
            <div className="w-7 h-7 rounded-full bg-green-500 flex items-center justify-center">
              <CheckCircle className="w-5 h-5 text-white" />
            </div>
          ) : (
            getLessonIcon()
          )}
        </div>

        <div className="min-w-0 flex-1">
          <span className={cn(
            "block font-medium line-clamp-2 transition-colors",
            "group-hover:text-primary dark:group-hover:text-primary-foreground",
            lesson.completed ? "text-green-700 dark:text-green-400" : "text-gray-900 dark:text-gray-100",
            isMobile
              ? isVerySmall
                ? "text-xs"
                : "text-sm"
              : "text-base"
          )}>
            {lesson.title}
            {isLessonLocked && !isTeacher && (
              <span className="inline-flex ml-2 items-center text-gray-400 dark:text-gray-500">
                <LockIcon className="w-3 h-3" />
              </span>
            )}
          </span>

          <div className="flex items-center gap-3 mt-1">
            <span className="flex items-center gap-1.5 text-gray-500 dark:text-gray-400 text-xs">
              <Clock className="w-3 h-3 text-gray-400 dark:text-gray-500" />
              {lesson.duration || "5 mins"}
            </span>
            {lesson.completed && (
              <span className="flex items-center gap-1.5 text-green-600 dark:text-green-400 text-xs font-medium">
                <CheckCircle className="w-3 h-3" />
                Completed
              </span>
            )}
          </div>
        </div>
      </div>
    </Link>
  );
};

export default LessonItem;

