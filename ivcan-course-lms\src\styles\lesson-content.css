/* Lesson Content Styling */

.lesson-content-container {
  max-width: 100%;
  margin: 0 auto;
  font-family: 'Poppins', system-ui, sans-serif;
  padding: 0;
  background-color: white;
  border-radius: 0;
  box-shadow: none;
  transition: all 0.3s ease;
  overflow-x: hidden; /* Prevent horizontal scrolling */
}

/* Full-width lesson content wrapper */
.lesson-content-wrapper-full-width {
  width: 100%;
  padding: 0;
  background-color: white;
  transition: all 0.3s ease;
}

@media (min-width: 640px) {
  .lesson-content-wrapper-full-width {
    padding: 0;
  }
}

@media (min-width: 768px) {
  .lesson-content-wrapper-full-width {
    padding: 0;
  }
}

@media (min-width: 1024px) {
  .lesson-content-wrapper-full-width {
    padding: 0;
  }
}

@media (min-width: 1280px) {
  .lesson-content-wrapper-full-width {
    padding: 0;
  }
}

@media (min-width: 1536px) {
  .lesson-content-wrapper-full-width {
    padding: 0;
  }
}

/* Full-width prose content area with optimized reading width */
.prose-content-area-full-width {
  max-width: 75ch; /* Optimal reading line length */
  margin: 0 auto;
  position: relative;
  padding: 1rem; /* Add padding for content readability */
}

/* Truly full-width variant for when maximum width is desired */
.prose-content-area-full-width.no-max-width {
  max-width: none;
  width: 100%;
  padding: 1rem; /* Add padding for content readability */
}

@media (min-width: 640px) {
  .prose-content-area-full-width {
    padding: 1.5rem;
  }

  .prose-content-area-full-width.no-max-width {
    padding: 1.5rem;
  }
}

@media (min-width: 768px) {
  .prose-content-area-full-width {
    padding: 2rem;
  }

  .prose-content-area-full-width.no-max-width {
    padding: 2rem;
  }
}

@media (min-width: 1024px) {
  .prose-content-area-full-width {
    padding: 2.5rem;
  }

  .prose-content-area-full-width.no-max-width {
    padding: 2.5rem;
  }
}

/* Full-width lesson content container - no width constraints */
.lesson-content-container-full-width {
  width: 100%;
  margin: 0;
  font-family: 'Poppins', system-ui, sans-serif;
  padding: 0;
  background-color: transparent;
  border-radius: 0;
  box-shadow: none;
  transition: all 0.3s ease;
  overflow-x: hidden; /* Prevent horizontal scrolling */
}

/* Enhanced prose container for better content presentation */
.prose-container {
  position: relative;
}

.prose-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: -8px;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, #e63946, rgba(230, 57, 70, 0.3));
  border-radius: 2px;
  opacity: 0.6;
}

@media (min-width: 768px) {
  .prose-container::before {
    left: -12px;
    width: 6px;
  }
}

/* Prose content area styling */
.prose-content-area {
  position: relative;
  z-index: 1;
}

/* Ensure proper text selection and interaction */
.lesson-content-container .prose {
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}

/* Improve text contrast for better readability */
.lesson-content-container .prose strong {
  font-weight: 600;
  color: #1a1a1a;
}

.lesson-content-container .prose em {
  font-style: italic;
  color: #2a2a2a;
}

.dark .lesson-content-container .prose strong {
  color: #ffffff;
}

.dark .lesson-content-container .prose em {
  color: #e0e0e0;
}

@media (min-width: 768px) {
  .lesson-content-container {
    padding: 0;
    border-radius: 0;
    box-shadow: none;
  }
}

@media (max-width: 768px) {
  .lesson-content-container {
    padding: 0;
  }
  
  /* Ensure images don't overflow on mobile */
  .lesson-content-container img,
  .lesson-content-container .prose img,
  .prose-content-area-full-width img,
  .prose-content-area-full-width .prose img,
  .lesson-prose img {
    max-width: calc(100vw - 2rem);
    height: auto;
    margin: 1rem auto;
    display: block;
  }

  /* Adjust table containers for mobile */
  .lesson-content-container table {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Improve code block readability on mobile */
  .lesson-content-container pre {
    margin: 12px -12px;
    padding: 12px;
    border-radius: 0;
    font-size: 13px;
    line-height: 1.4;
  }
}

.dark .lesson-content-container {
  background-color: #000;
  color: #e0e0e0;
}

/* Dark mode support for full-width layout */
.dark .lesson-content-wrapper-full-width {
  background-color: #000;
  color: #e0e0e0;
}

.dark .lesson-content-container-full-width {
  background-color: transparent;
  color: #e0e0e0;
}

/* Improved text sizing for better readability */
.lesson-content-container .prose {
  font-size: 0.9375rem; /* 15px base size for mobile - more readable */
  line-height: 1.7; /* Better line spacing for readability */
  letter-spacing: -0.011em;
  color: #333;
  max-width: 100%;
  padding: 0;
}

@media (min-width: 768px) {
  .lesson-content-container .prose {
    font-size: 1.0625rem; /* 17px for desktop - optimal reading size */
    line-height: 1.8;
  }
}

.dark .lesson-content-container .prose {
  color: #e0e0e0;
}

/* Full-width prose styling with compact typography */
.prose-content-area-full-width .prose {
  font-size: 1rem; /* 16px base size for optimal readability */
  line-height: 1.6; /* Compact line spacing for streamlined layout */
  letter-spacing: -0.011em;
  color: #333;
  max-width: 100%;
  padding: 0;
}

@media (min-width: 768px) {
  .prose-content-area-full-width .prose {
    font-size: 1.125rem; /* 18px for desktop - larger for full-width layout */
    line-height: 1.65; /* Slightly tighter for desktop */
  }
}

@media (min-width: 1024px) {
  .prose-content-area-full-width .prose {
    font-size: 1.1875rem; /* 19px for large screens */
    line-height: 1.7; /* Balanced spacing for large screens */
  }
}

.dark .prose-content-area-full-width .prose {
  color: #e0e0e0;
}

/* Full-width paragraph styling - compact spacing */
.prose-content-area-full-width .prose p {
  font-size: 1rem; /* 16px base size */
  margin: 0.75rem 0; /* Reduced spacing for compact layout */
  line-height: 1.6; /* Tighter line spacing */
  padding: 0;
}

@media (min-width: 768px) {
  .prose-content-area-full-width .prose p {
    font-size: 1.125rem; /* 18px on desktop */
    margin: 0.875rem 0; /* Compact spacing */
  }
}

@media (min-width: 1024px) {
  .prose-content-area-full-width .prose p {
    font-size: 1.1875rem; /* 19px on large screens */
    margin: 1rem 0; /* Streamlined spacing */
  }
}

/* Full-width heading styles - compact spacing */
.prose-content-area-full-width .prose h1 {
  font-size: 1.75rem; /* 28px - larger for full-width */
  margin: 1.25rem 0 0.75rem; /* Compact spacing */
  line-height: 1.2;
  letter-spacing: -0.025em;
  font-weight: 700;
  color: #1a1a1a;
}

@media (min-width: 768px) {
  .prose-content-area-full-width .prose h1 {
    font-size: 2.5rem; /* 40px */
    margin: 1.5rem 0 1rem; /* Reduced spacing */
  }
}

@media (min-width: 1024px) {
  .prose-content-area-full-width .prose h1 {
    font-size: 3rem; /* 48px */
    margin: 1.75rem 0 1.25rem; /* Streamlined spacing */
  }
}

.prose-content-area-full-width .prose h2 {
  font-size: 1.375rem; /* 22px */
  margin: 1rem 0 0.5rem; /* Compact spacing */
  line-height: 1.25;
  letter-spacing: -0.022em;
  font-weight: 600;
  color: #2a2a2a;
}

@media (min-width: 768px) {
  .prose-content-area-full-width .prose h2 {
    font-size: 2rem; /* 32px */
    margin: 1.25rem 0 0.75rem; /* Reduced spacing */
  }
}

@media (min-width: 1024px) {
  .prose-content-area-full-width .prose h2 {
    font-size: 2.25rem; /* 36px */
    margin: 1.5rem 0 1rem; /* Streamlined spacing */
  }
}

.prose-content-area-full-width .prose h3 {
  font-size: 1.1875rem; /* 19px */
  margin: 0.875rem 0 0.5rem; /* Compact spacing */
  line-height: 1.3;
  letter-spacing: -0.019em;
  font-weight: 600;
  color: #3a3a3a;
}

@media (min-width: 768px) {
  .prose-content-area-full-width .prose h3 {
    font-size: 1.5rem; /* 24px */
    margin: 1rem 0 0.625rem; /* Reduced spacing */
  }
}

@media (min-width: 1024px) {
  .prose-content-area-full-width .prose h3 {
    font-size: 1.75rem; /* 28px */
    margin: 1.25rem 0 0.75rem; /* Streamlined spacing */
  }
}

/* Dark mode for full-width headings */
.dark .prose-content-area-full-width .prose h1,
.dark .prose-content-area-full-width .prose h2,
.dark .prose-content-area-full-width .prose h3 {
  color: #fff;
}

/* Ensure prose content within full-width container uses full width */
.lesson-content-container-full-width .prose {
  max-width: 100%;
  width: 100%;
}

.lesson-content-container-full-width .prose-wrapper,
.lesson-content-container-full-width .markdown-wrapper {
  width: 100%;
  max-width: 100%;
}

/* Compact spacing for full-width lists */
.prose-content-area-full-width .prose ul,
.prose-content-area-full-width .prose ol {
  margin: 0.75rem 0; /* Reduced spacing */
  padding-left: 1.5rem;
}

.prose-content-area-full-width .prose li {
  margin: 0.25rem 0; /* Compact list item spacing */
  line-height: 1.5;
}

.prose-content-area-full-width .prose li p {
  margin: 0.25rem 0; /* Tight paragraph spacing in lists */
}

/* Compact spacing for full-width images */
.prose-content-area-full-width .prose img {
  margin: 1rem 0; /* Reduced image spacing */
  border-radius: 0.5rem;
}

/* Compact spacing for full-width code blocks */
.prose-content-area-full-width .prose pre {
  margin: 1rem 0; /* Reduced code block spacing */
  padding: 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Compact spacing for full-width tables */
.prose-content-area-full-width .prose table {
  margin: 1rem 0; /* Reduced table spacing */
  font-size: 0.875rem;
}

.prose-content-area-full-width .prose th,
.prose-content-area-full-width .prose td {
  padding: 0.5rem 0.75rem; /* Compact cell padding */
}

/* Compact spacing for full-width blockquotes */
.prose-content-area-full-width .prose blockquote {
  margin: 1rem 0; /* Reduced blockquote spacing */
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  font-size: inherit;
  line-height: 1.5;
}

/* Compact spacing for full-width horizontal rules */
.prose-content-area-full-width .prose hr {
  margin: 1.5rem 0; /* Reduced hr spacing */
}

/* General element spacing override for compact layout */
.prose-content-area-full-width .prose > * + * {
  margin-top: 0.75rem; /* Standardized compact spacing */
}

/* Specific spacing adjustments for heading-paragraph combinations */
.prose-content-area-full-width .prose h1 + p,
.prose-content-area-full-width .prose h2 + p,
.prose-content-area-full-width .prose h3 + p {
  margin-top: 0.5rem; /* Tight spacing after headings */
}

/* Responsive adjustments for compact spacing */
@media (min-width: 768px) {
  .prose-content-area-full-width .prose > * + * {
    margin-top: 0.875rem; /* Slightly more spacing on larger screens */
  }

  .prose-content-area-full-width .prose ul,
  .prose-content-area-full-width .prose ol {
    margin: 0.875rem 0;
  }

  .prose-content-area-full-width .prose img,
  .prose-content-area-full-width .prose pre,
  .prose-content-area-full-width .prose table,
  .prose-content-area-full-width .prose blockquote {
    margin: 1.25rem 0;
  }
}

@media (min-width: 1024px) {
  .prose-content-area-full-width .prose > * + * {
    margin-top: 1rem; /* Balanced spacing for large screens */
  }

  .prose-content-area-full-width .prose img,
  .prose-content-area-full-width .prose pre,
  .prose-content-area-full-width .prose table,
  .prose-content-area-full-width .prose blockquote {
    margin: 1.5rem 0;
  }
}

/* Mobile-specific compact adjustments */
@media (max-width: 640px) {
  .prose-content-area-full-width .prose p {
    margin: 0.625rem 0; /* Even tighter on mobile */
  }

  .prose-content-area-full-width .prose h1 {
    margin: 1rem 0 0.625rem;
  }

  .prose-content-area-full-width .prose h2 {
    margin: 0.875rem 0 0.5rem;
  }

  .prose-content-area-full-width .prose h3 {
    margin: 0.75rem 0 0.375rem;
  }

  .prose-content-area-full-width .prose > * + * {
    margin-top: 0.625rem; /* Very compact on mobile */
  }
}

/* Legacy paragraph styles - now handled by unified-lesson-content.css */
/* These styles are kept for backward compatibility but should be overridden */
.lesson-content-container .prose p {
  font-size: 1rem; /* 16px base size */
  margin: 1.25rem 0; /* 20px spacing for better readability */
  line-height: 1.6; /* Improved line spacing */
  padding: 0;
  text-align: justify;
  text-justify: inter-word;
}

@media (min-width: 768px) {
  .lesson-content-container .prose p {
    font-size: 1.125rem; /* 18px on desktop */
    margin: 1.5rem 0; /* 24px spacing */
  }
}

/* Legacy heading styles - now handled by unified-lesson-content.css */
.lesson-content-container .prose h1 {
  font-size: 1.875rem; /* 30px - larger for better hierarchy */
  margin: 2.5rem 0 1.5rem; /* Better spacing */
  line-height: 1.25;
  letter-spacing: -0.025em;
  font-weight: 700;
  color: #1a1a1a;
}

@media (min-width: 768px) {
  .lesson-content-container .prose h1 {
    font-size: 2.25rem; /* 36px */
    margin: 2.5rem 0 1.5rem;
  }
}

.dark .lesson-content-container .prose h1 {
  color: #fff;
  border-bottom: 2px solid rgba(230, 57, 70, 0.3);
}

.lesson-content-container .prose h2 {
  font-size: 1.5rem; /* 24px - better hierarchy */
  margin: 2rem 0 1.25rem; /* Better spacing */
  line-height: 1.3;
  letter-spacing: -0.02em;
  font-weight: 600;
  color: #2a2a2a;
}

@media (min-width: 768px) {
  .lesson-content-container .prose h2 {
    font-size: 1.875rem; /* 30px */
    margin: 2rem 0 1.25rem;
  }
}

.lesson-content-container .prose h3 {
  font-size: 1.25rem; /* 20px - better hierarchy */
  margin: 1.5rem 0 1rem; /* Better spacing */
  line-height: 1.4;
  letter-spacing: -0.015em;
  font-weight: 600;
  color: #3a3a3a;
}

@media (min-width: 768px) {
  .lesson-content-container .prose h3 {
    font-size: 1.5rem; /* 24px */
    margin: 1.5rem 0 1rem;
  }
}

.lesson-content-container .prose h4 {
  font-size: 1.25rem;
  margin-top: 0.8em; /* Reduced top margin */
  margin-bottom: 0.3em; /* Reduced bottom margin */
  font-weight: 600;
}

.lesson-content-container .prose ul,
.lesson-content-container .prose ol {
  margin: 1.25rem 0; /* Better spacing around lists */
  padding-left: 2rem; /* Better indentation */
}

@media (min-width: 768px) {
  .lesson-content-container .prose ul,
  .lesson-content-container .prose ol {
    margin: 1.5rem 0;
    padding-left: 2rem;
  }
}

/* Better list item formatting */
.lesson-content-container .prose li {
  font-size: 1rem; /* 16px to match body text */
  margin: 1rem 0; /* Better spacing between list items */
  line-height: 1.6; /* Good line height for readability */
  padding-left: 0; /* No additional indent */
}

@media (min-width: 768px) {
  .lesson-content-container .prose li {
    font-size: 1.125rem; /* 18px on desktop */
    margin: 1rem 0;
    padding-left: 0;
  }
}

.lesson-content-container .prose li p {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

/* Improved code blocks with better spacing and readability */
.lesson-content-container .prose pre {
  margin: 12px 0; /* Reduced spacing around code blocks */
  padding: 16px; /* Reduced padding */
  border-radius: 8px;
  background-color: #f8f9fa;
  overflow-x: auto;
  font-size: 0.8125rem; /* 13px - slightly larger for better readability */
  line-height: 1.6; /* Better line spacing for code */
  -webkit-overflow-scrolling: touch;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

@media (min-width: 768px) {
  .lesson-content-container .prose pre {
    font-size: 0.9375rem; /* 15px on desktop */
    margin: 16px 0; /* Reduced spacing */
    padding: 20px; /* Reduced padding */
  }
}

.dark .lesson-content-container .prose pre {
  background-color: #1a1a1a;
  border-color: rgba(255, 255, 255, 0.1);
}

/* Code block text size */
.lesson-content-container .prose pre code {
  font-size: 0.8125rem; /* 13px for technical content */
  line-height: 1.6;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

@media (min-width: 768px) {
  .lesson-content-container .prose pre code {
    font-size: 0.9375rem; /* 15px on desktop */
  }
}

/* Inline code styling */
.lesson-content-container .prose code:not(pre code) {
  font-size: 0.875rem; /* 14px for inline code */
  padding: 2px 6px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  color: #e63946;
  font-weight: 500;
}

.dark .lesson-content-container .prose code:not(pre code) {
  background-color: rgba(255, 255, 255, 0.1);
  color: #f87171;
}

/* Callouts with reduced spacing */
.lesson-content-container .prose .callout {
  margin: 1em 0; /* Reduced margin */
  padding: 1em; /* Reduced padding */
  border-radius: 0.5em;
  border-left-width: 4px;
}

/* Improved tables with better spacing and readability */
.lesson-content-container .prose table {
  margin: 12px 0; /* Reduced spacing around tables */
  width: 100%;
  font-size: 0.8125rem; /* 13px - slightly larger for better readability */
  line-height: 1.6; /* Better line spacing */
  border-collapse: collapse;
  overflow-x: auto;
  display: block;
  -webkit-overflow-scrolling: touch;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

@media (min-width: 768px) {
  .lesson-content-container .prose table {
    font-size: 0.9375rem; /* 15px on desktop */
    margin: 16px 0; /* Reduced desktop margin */
  }
}

.lesson-content-container .prose th,
.lesson-content-container .prose td {
  padding: 8px 12px; /* Reduced padding */
  border: 1px solid #eee;
  text-align: left;
}

.lesson-content-container .prose th {
  background-color: rgba(0, 0, 0, 0.03);
  font-weight: 600;
  color: #1a1a1a;
}

.dark .lesson-content-container .prose table {
  border-color: rgba(255, 255, 255, 0.1);
}

.dark .lesson-content-container .prose th {
  background-color: rgba(255, 255, 255, 0.05);
  color: #ffffff;
}

.dark .lesson-content-container .prose th,
.dark .lesson-content-container .prose td {
  border-color: rgba(255, 255, 255, 0.1);
}

/* Optimize images for lesson content */
.lesson-content-container img,
.lesson-content-container .prose img,
.prose-content-area-full-width img,
.prose-content-area-full-width .prose img,
.lesson-prose img {
  max-width: 100%;
  height: auto;
  margin: 0.8rem auto; /* Reduced image margins */
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease-in-out;
  display: block;
}

.lesson-content-container img:hover,
.lesson-content-container .prose img:hover,
.prose-content-area-full-width img:hover,
.prose-content-area-full-width .prose img:hover,
.lesson-prose img:hover {
  transform: scale(1.01);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Ensure images in markdown content are visible */
.lesson-prose .markdown-preview img,
.lesson-prose .wmde-markdown img,
.lesson-prose .w-md-editor-text img {
  max-width: 100%;
  height: auto;
  margin: 1.5rem auto;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease-in-out;
  display: block;
}

.lesson-content-container .prose .image-container {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
  width: 100%;
}

/* Responsive image sizing */
@media (max-width: 640px) {
  .lesson-content-container img,
  .lesson-content-container .prose img {
    margin: 1.5rem auto;
  }
}

/* Dark mode adjustments */
.dark .lesson-content-container img,
.dark .lesson-content-container .prose img {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .lesson-content-container img:hover,
.dark .lesson-content-container .prose img:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.15);
}

/* Improved blockquote styling for better readability */
.lesson-content-container .prose blockquote {
  font-size: 0.9375rem; /* 15px - consistent with body text */
  line-height: 1.7; /* Better line spacing */
  margin: 12px 0; /* Reduced spacing */
  padding: 12px 16px; /* Reduced padding */
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.03);
  border-left: 4px solid #e63946; /* Slightly thicker border */
  font-style: normal;
  position: relative;
}

@media (min-width: 768px) {
  .lesson-content-container .prose blockquote {
    font-size: 1.0625rem; /* 17px on desktop */
    margin: 16px 0; /* Reduced spacing */
    padding: 16px 20px; /* Reduced padding */
  }
}

.dark .lesson-content-container .prose blockquote {
  color: rgba(255, 255, 255, 0.8);
  background-color: rgba(255, 255, 255, 0.05);
  border-left-color: #f87171;
}

/* Horizontal rule with reduced spacing */
.lesson-content-container .prose hr {
  margin: 1.5em 0; /* Reduced spacing */
  border-color: rgba(0, 0, 0, 0.1);
}

.dark .lesson-content-container .prose hr {
  border-color: rgba(255, 255, 255, 0.1);
}

/* Image caption support */
.lesson-content-container figure,
.lesson-content-container .prose figure {
  margin: 1rem auto; /* Reduced figure margins */
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Caption and auxiliary text */
.lesson-content-container figcaption,
.lesson-content-container .prose figcaption {
  font-size: 0.75rem; /* 12px for captions */
  margin-top: 0.5rem;
  color: var(--muted-foreground);
  text-align: center;
  font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .lesson-content-container img,
  .lesson-content-container .prose img {
    margin: 0.6rem auto; /* Reduced mobile image margins */
  }
}

/* Better spacing between elements for readability */
.lesson-content-container .prose > * + * {
  margin-top: 1rem; /* Better spacing between elements */
}

@media (min-width: 768px) {
  .lesson-content-container .prose > * + * {
    margin-top: 1.25rem;
  }
}

/* Proper spacing after headings */
.lesson-content-container .prose h1 + p,
.lesson-content-container .prose h2 + p,
.lesson-content-container .prose h3 + p {
  margin-top: 0.75rem; /* Better space after headings */
}

@media (min-width: 768px) {
  .lesson-content-container .prose h1 + p,
  .lesson-content-container .prose h2 + p,
  .lesson-content-container .prose h3 + p {
    margin-top: 1rem;
  }
}

/* Fixed footer container styles */
.lesson-footer-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 40;
}

.dark .lesson-footer-container {
  background: rgba(0, 0, 0, 0.8);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Safe area insets for modern mobile devices */
@supports(padding: max(0px)) {
  .lesson-footer-container {
    padding-bottom: max(12px, env(safe-area-inset-bottom));
    padding-left: max(16px, env(safe-area-inset-left));
    padding-right: max(16px, env(safe-area-inset-right));
  }
}

/* Modern Accordion Styling for Lesson Content */
.modern-accordion {
  position: relative;
}

.modern-accordion-item {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  border: 1px solid rgba(230, 57, 70, 0.15);
  border-radius: 16px;
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.08),
    0 1px 3px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.modern-accordion-item:hover {
  transform: translateY(-2px);
  box-shadow:
    0 8px 30px rgba(0, 0, 0, 0.12),
    0 4px 8px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border-color: rgba(230, 57, 70, 0.25);
}

.modern-accordion-trigger {
  background: linear-gradient(135deg, rgba(230, 57, 70, 0.05) 0%, rgba(230, 57, 70, 0.02) 100%);
  font-size: 1.0625rem; /* 17px */
  font-weight: 600;
  color: #1a1a1a;
  letter-spacing: -0.01em;
  position: relative;
}

.modern-accordion-trigger:hover {
  background: linear-gradient(135deg, rgba(230, 57, 70, 0.1) 0%, rgba(230, 57, 70, 0.05) 100%);
}

.modern-accordion-trigger::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(to bottom, #e63946, rgba(230, 57, 70, 0.6));
  border-radius: 0 2px 2px 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modern-accordion-trigger:hover::before,
.modern-accordion-trigger[data-state="open"]::before {
  opacity: 1;
}

.modern-accordion-content {
  background: linear-gradient(135deg, rgba(248, 249, 250, 0.8) 0%, rgba(255, 255, 255, 0.6) 100%);
  border-top: 1px solid rgba(230, 57, 70, 0.1);
}

/* Dark mode support for modern accordion */
.dark .modern-accordion-item {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.4) 100%);
  border-color: rgba(230, 57, 70, 0.2);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.3),
    0 1px 3px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.dark .modern-accordion-item:hover {
  box-shadow:
    0 8px 30px rgba(0, 0, 0, 0.4),
    0 4px 8px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  border-color: rgba(230, 57, 70, 0.3);
}

.dark .modern-accordion-trigger {
  background: linear-gradient(135deg, rgba(230, 57, 70, 0.08) 0%, rgba(230, 57, 70, 0.03) 100%);
  color: #e0e0e0;
}

.dark .modern-accordion-trigger:hover {
  background: linear-gradient(135deg, rgba(230, 57, 70, 0.15) 0%, rgba(230, 57, 70, 0.08) 100%);
}

.dark .modern-accordion-content {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.8) 0%, rgba(0, 0, 0, 0.6) 100%);
  border-top-color: rgba(230, 57, 70, 0.15);
}

/* Enhanced animation for accordion content */
@keyframes modern-accordion-down {
  from {
    height: 0;
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    height: var(--radix-accordion-content-height);
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes modern-accordion-up {
  from {
    height: var(--radix-accordion-content-height);
    opacity: 1;
    transform: translateY(0);
  }
  to {
    height: 0;
    opacity: 0;
    transform: translateY(-10px);
  }
}

.modern-accordion [data-state=open] {
  animation: modern-accordion-down 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-accordion [data-state=closed] {
  animation: modern-accordion-up 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Mobile responsive adjustments for modern accordion */
@media (max-width: 768px) {
  .modern-accordion-item {
    border-radius: 12px;
    margin: 0 -4px; /* Slight negative margin for better mobile fit */
  }

  .modern-accordion-trigger {
    font-size: 0.9375rem; /* 15px on mobile */
    padding: 16px 20px;
  }

  .modern-accordion-content {
    padding: 16px 20px 20px;
  }

  .modern-accordion-item:hover {
    transform: none; /* Disable hover transform on mobile */
  }
}

/* Enhanced focus states for accessibility */
.modern-accordion-trigger:focus-visible {
  outline: 2px solid #e63946;
  outline-offset: 2px;
  border-radius: 8px;
}

/* Smooth icon rotation */
.modern-accordion-trigger [data-state="open"] svg {
  transform: rotate(180deg);
}

/* Clean lesson accordion styles */
.lesson-accordion {
  border: none;
  background: transparent;
}

.lesson-accordion-item {
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  border-radius: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

.lesson-accordion-trigger {
  background: transparent !important;
  border: none !important;
  padding: 1rem 0 !important;
  margin: 0 !important;
  box-shadow: none !important;
  border-radius: 0 !important;
  font-size: 1.125rem !important;
  font-weight: 600 !important;
  color: hsl(var(--foreground)) !important;
  transition: color 0.2s ease !important;
}

.lesson-accordion-trigger:hover {
  background: transparent !important;
  color: hsl(var(--primary)) !important;
}

.lesson-accordion-trigger:focus {
  background: transparent !important;
  outline: none !important;
  box-shadow: none !important;
}

.lesson-accordion-trigger[data-state="open"] {
  background: transparent !important;
  color: hsl(var(--primary)) !important;
}

.lesson-accordion-content {
  background: transparent !important;
  border: none !important;
  padding: 0 0 1rem 0 !important;
  margin: 0 !important;
  box-shadow: none !important;
}

.lesson-accordion-content > div {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
}

/* Modern plus/minus icon styling */
.lesson-accordion-trigger .h-5 {
  background: hsl(var(--muted));
  border-radius: 50%;
  padding: 2px;
  transition: all 0.2s ease;
}

.lesson-accordion-trigger:hover .h-5 {
  background: hsl(var(--primary));
  color: white;
}

.lesson-accordion-trigger[data-state="open"] .h-5 {
  background: hsl(var(--primary));
  color: white;
}

/* Content fade-in effect */
.modern-accordion-content > div {
  animation: fadeInUp 0.4s ease-out 0.1s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Modern Dropdown Styling for Lesson Content (details/summary elements) */
.lesson-content-container .prose details.dropdown {
  margin: 20px 0;
  border: none;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.06),
    0 1px 3px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(230, 57, 70, 0.12);
}

.lesson-content-container .prose details.dropdown:hover {
  transform: translateY(-2px);
  box-shadow:
    0 8px 30px rgba(0, 0, 0, 0.1),
    0 4px 8px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border-color: rgba(230, 57, 70, 0.2);
}

.lesson-content-container .prose details.dropdown summary {
  padding: 16px 20px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  color: #1a1a1a;
  background: linear-gradient(135deg, rgba(230, 57, 70, 0.04) 0%, rgba(230, 57, 70, 0.02) 100%);
  border: none;
  outline: none;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  list-style: none;
}

.lesson-content-container .prose details.dropdown summary::-webkit-details-marker {
  display: none;
}

.lesson-content-container .prose details.dropdown summary::after {
  content: '▼';
  font-size: 0.75rem;
  color: #e63946;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-left: auto;
}

.lesson-content-container .prose details.dropdown[open] summary::after {
  transform: rotate(180deg);
}

.lesson-content-container .prose details.dropdown summary:hover {
  background: linear-gradient(135deg, rgba(230, 57, 70, 0.08) 0%, rgba(230, 57, 70, 0.04) 100%);
}

.lesson-content-container .prose details.dropdown summary::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(to bottom, #e63946, rgba(230, 57, 70, 0.6));
  border-radius: 0 2px 2px 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.lesson-content-container .prose details.dropdown summary:hover::before,
.lesson-content-container .prose details.dropdown[open] summary::before {
  opacity: 1;
}

.lesson-content-container .prose details.dropdown > div {
  padding: 20px;
  background: linear-gradient(135deg, rgba(248, 249, 250, 0.8) 0%, rgba(255, 255, 255, 0.6) 100%);
  border-top: 1px solid rgba(230, 57, 70, 0.08);
  animation: fadeInUp 0.4s ease-out;
}

@media (min-width: 768px) {
  .lesson-content-container .prose details.dropdown {
    margin: 24px 0;
  }

  .lesson-content-container .prose details.dropdown summary {
    padding: 20px 24px;
    font-size: 1.0625rem;
  }

  .lesson-content-container .prose details.dropdown > div {
    padding: 24px;
  }
}

/* Dark mode support for dropdown elements */
.dark .lesson-content-container .prose details.dropdown {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.4) 100%);
  border-color: rgba(230, 57, 70, 0.15);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.3),
    0 1px 3px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.dark .lesson-content-container .prose details.dropdown:hover {
  box-shadow:
    0 8px 30px rgba(0, 0, 0, 0.4),
    0 4px 8px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  border-color: rgba(230, 57, 70, 0.25);
}

.dark .lesson-content-container .prose details.dropdown summary {
  background: linear-gradient(135deg, rgba(230, 57, 70, 0.08) 0%, rgba(230, 57, 70, 0.03) 100%);
  color: #e0e0e0;
}

.dark .lesson-content-container .prose details.dropdown summary:hover {
  background: linear-gradient(135deg, rgba(230, 57, 70, 0.15) 0%, rgba(230, 57, 70, 0.08) 100%);
}

.dark .lesson-content-container .prose details.dropdown summary::after {
  color: #f87171;
}

.dark .lesson-content-container .prose details.dropdown > div {
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.8) 0%, rgba(0, 0, 0, 0.6) 100%);
  border-top-color: rgba(230, 57, 70, 0.12);
}

/* Mobile responsive adjustments for dropdown elements */
@media (max-width: 768px) {
  .lesson-content-container .prose details.dropdown {
    border-radius: 10px;
    margin: 16px 0;
  }

  .lesson-content-container .prose details.dropdown summary {
    padding: 14px 16px;
    font-size: 0.9375rem;
  }

  .lesson-content-container .prose details.dropdown > div {
    padding: 16px;
  }

  .lesson-content-container .prose details.dropdown:hover {
    transform: none; /* Disable hover transform on mobile */
  }
}

/* Enhanced focus states for accessibility */
.lesson-content-container .prose details.dropdown summary:focus-visible {
  outline: 2px solid #e63946;
  outline-offset: 2px;
  border-radius: 8px;
}
