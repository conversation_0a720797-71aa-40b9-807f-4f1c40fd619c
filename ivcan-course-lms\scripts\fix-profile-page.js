// This script fixes issues with the profile page
// Run this script with: node scripts/fix-profile-page.js

const fs = require('fs');
const path = require('path');

console.log('Fixing profile page issues...');

// Function to read a file
function readFile(filePath) {
  return fs.readFileSync(filePath, 'utf8');
}

// Function to write a file
function writeFile(filePath, content) {
  fs.writeFileSync(filePath, content, 'utf8');
  console.log(`Updated ${filePath}`);
}

// Fix the index.html file
function fixIndexHtml() {
  const filePath = path.join(__dirname, '..', 'index.html');
  let content = readFile(filePath);
  
  // Fix the style tag issue
  content = content.replace(
    /<!-- Preload critical assets -->\s*<script src="\/preload\.js" async><\/script>\s*body \{ transition: var\(--theme-transition, none\); \}\s*:focus-visible \{ outline: 2px solid #10B981; outline-offset: 2px; \}\s*<\/style>/,
    `<!-- Preload critical assets -->
    <script src="/preload.js" async></script>
    
    <!-- Additional styles -->
    <style>
      body { transition: var(--theme-transition, none); }
      :focus-visible { outline: 2px solid #10B981; outline-offset: 2px; }
    </style>`
  );
  
  writeFile(filePath, content);
}

// Fix the ProfilePictureUpload component
function fixProfilePictureUpload() {
  const filePath = path.join(__dirname, '..', 'src', 'components', 'profile', 'ProfilePictureUpload.tsx');
  let content = readFile(filePath);
  
  // Fix the memo import if needed
  if (!content.includes('import React, { useState, useRef, memo } from \'react\';')) {
    content = content.replace(
      /import React, { useState, useRef } from 'react';/,
      'import React, { useState, useRef, memo } from \'react\';'
    );
  }
  
  // Fix the component definition
  content = content.replace(
    /const ProfilePictureUpload: React\.FC<ProfilePictureUploadProps> = memo\(function ProfilePictureUpload\({/,
    'const ProfilePictureUpload: React.FC<ProfilePictureUploadProps> = ({'
  );
  
  // Fix the export statement
  content = content.replace(
    /export default ProfilePictureUpload\);/,
    'export default memo(ProfilePictureUpload);'
  );
  
  writeFile(filePath, content);
}

// Run the fixes
try {
  fixIndexHtml();
  fixProfilePictureUpload();
  console.log('All fixes applied successfully!');
} catch (error) {
  console.error('Error applying fixes:', error);
}
