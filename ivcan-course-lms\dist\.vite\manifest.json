{"_CourseCompletionCelebration.BJZAxw59.js": {"file": "assets/CourseCompletionCelebration.BJZAxw59.js", "name": "CourseCompletionCelebration", "imports": ["_vendor-react.BcAa1DKr.js", "index.html", "_confetti.ShHySCrk.js", "_vendor.DQpuTRuB.js"]}, "_Layout.!~{00z}~.js": {"file": "assets/css/Layout.CFLJ3FLk.css", "src": "_Layout.!~{00z}~.js"}, "_Layout.DRjmVYQG.js": {"file": "assets/Layout.DRjmVYQG.js", "name": "Layout", "imports": ["_vendor-react.BcAa1DKr.js", "index.html", "_vendor.DQpuTRuB.js"], "css": ["assets/css/Layout.CFLJ3FLk.css"]}, "_LessonContent.B2xCUk6H.js": {"file": "assets/LessonContent.B2xCUk6H.js", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "imports": ["_vendor-react.BcAa1DKr.js", "_markdown-preview.Bw0U-NJA.js", "index.html", "_vendor.DQpuTRuB.js"]}, "_TestAnalyticsDashboard.B08xtLlk.js": {"file": "assets/TestAnalyticsDashboard.B08xtLlk.js", "name": "TestAnalyticsDashboard", "imports": ["_vendor-react.BcAa1DKr.js", "_card.B9V6b2DK.js", "index.html", "_badge.C87ZuIxl.js", "_module-test.BaVeK2uM.js", "_vendor.DQpuTRuB.js", "_progress.BMuwHUJX.js"]}, "_achievementService.Vkx_BHml.js": {"file": "assets/achievementService.Vkx_BHml.js", "name": "achievementService", "imports": ["index.html"]}, "_alert-dialog.BK1A3Gb_.js": {"file": "assets/alert-dialog.BK1A3Gb_.js", "name": "alert-dialog", "imports": ["_vendor-react.BcAa1DKr.js", "index.html"]}, "_alert.EBLwCiZR.js": {"file": "assets/alert.EBLwCiZR.js", "name": "alert", "imports": ["_vendor-react.BcAa1DKr.js", "_vendor.DQpuTRuB.js", "index.html"]}, "_badge.C87ZuIxl.js": {"file": "assets/badge.C87ZuIxl.js", "name": "badge", "imports": ["_vendor-react.BcAa1DKr.js", "_vendor.DQpuTRuB.js", "index.html"]}, "_breadcrumb.DyMjeizE.js": {"file": "assets/breadcrumb.DyMjeizE.js", "name": "breadcrumb", "imports": ["_vendor-react.BcAa1DKr.js", "index.html"]}, "_card.B9V6b2DK.js": {"file": "assets/card.B9V6b2DK.js", "name": "card", "imports": ["_vendor-react.BcAa1DKr.js", "_vendor.DQpuTRuB.js", "index.html"]}, "_confetti.ShHySCrk.js": {"file": "assets/confetti.ShHySCrk.js", "name": "confetti", "imports": ["_vendor-react.BcAa1DKr.js", "_vendor.DQpuTRuB.js"]}, "_content-converter.L-GziWIP.js": {"file": "assets/content-converter.L-GziWIP.js", "name": "content-converter"}, "_courseApi.BQX5-7u-.js": {"file": "assets/courseApi.BQX5-7u-.js", "name": "courseApi", "imports": ["_vendor.DQpuTRuB.js", "index.html", "_utils.Qa9QlCj_.js", "_enrollmentApi.CstnsQi_.js"], "dynamicImports": ["src/services/course/accessControlService.ts", "src/services/course/accessControlService.ts"]}, "_enhanced-markdown-preview.Du81wch8.js": {"file": "assets/enhanced-markdown-preview.Du81wch8.js", "name": "enhanced-markdown-preview", "imports": ["_vendor-react.BcAa1DKr.js", "index.html", "_content-converter.L-GziWIP.js", "_vendor.DQpuTRuB.js"]}, "_enrollmentApi.CstnsQi_.js": {"file": "assets/enrollmentApi.CstnsQi_.js", "name": "enrollmentApi", "imports": ["index.html", "_vendor.DQpuTRuB.js"]}, "_floating-sidebar-container.BxlLgzat.js": {"file": "assets/floating-sidebar-container.BxlLgzat.js", "name": "floating-sidebar-container", "imports": ["_vendor-react.BcAa1DKr.js", "index.html"]}, "_label.D4YlnUPk.js": {"file": "assets/label.D4YlnUPk.js", "name": "label", "imports": ["_vendor-react.BcAa1DKr.js", "_vendor.DQpuTRuB.js", "index.html"]}, "_loading-skeleton.DxUGnZ26.js": {"file": "assets/loading-skeleton.DxUGnZ26.js", "name": "loading-skeleton", "imports": ["_vendor-react.BcAa1DKr.js", "index.html", "_skeleton.C0Lb8Xms.js"]}, "_markdown-preview.Bw0U-NJA.js": {"file": "assets/markdown-preview.Bw0U-NJA.js", "name": "markdown-preview", "imports": ["_vendor-react.BcAa1DKr.js", "_content-converter.L-GziWIP.js", "index.html", "_vendor.DQpuTRuB.js"]}, "_module-test.BaVeK2uM.js": {"file": "assets/module-test.BaVeK2uM.js", "name": "module-test"}, "_moduleImageUtils.AJjzI8xs.js": {"file": "assets/moduleImageUtils.AJjzI8xs.js", "name": "moduleImageUtils"}, "_moduleTestService.BmAHru_J.js": {"file": "assets/moduleTestService.BmAHru_J.js", "name": "moduleTestService", "imports": ["index.html"]}, "_professional-lesson-content.!~{00X}~.js": {"file": "assets/css/professional-lesson-content.bN5oOkou.css", "src": "_professional-lesson-content.!~{00X}~.js"}, "_progress.BMuwHUJX.js": {"file": "assets/progress.BMuwHUJX.js", "name": "progress", "imports": ["_vendor-react.BcAa1DKr.js", "index.html"]}, "_responsive-image.BMcUeCRG.js": {"file": "assets/responsive-image.BMcUeCRG.js", "name": "responsive-image", "imports": ["_vendor-react.BcAa1DKr.js", "index.html", "_skeleton.C0Lb8Xms.js"]}, "_simple-markdown-editor.DQ2eiB7n.js": {"file": "assets/simple-markdown-editor.DQ2eiB7n.js", "name": "simple-markdown-editor", "imports": ["_vendor-react.BcAa1DKr.js", "_vendor.DQpuTRuB.js", "_tabs.B0SF6qIv.js", "index.html", "_label.D4YlnUPk.js", "_markdown-preview.Bw0U-NJA.js", "_content-converter.L-GziWIP.js", "_tiptap-image-upload.B_U9gNrF.js"]}, "_skeleton.C0Lb8Xms.js": {"file": "assets/skeleton.C0Lb8Xms.js", "name": "skeleton", "imports": ["_vendor-react.BcAa1DKr.js", "index.html"]}, "_tabs.B0SF6qIv.js": {"file": "assets/tabs.B0SF6qIv.js", "name": "tabs", "imports": ["_vendor-react.BcAa1DKr.js", "index.html"]}, "_tiptap-image-upload.B_U9gNrF.js": {"file": "assets/tiptap-image-upload.B_U9gNrF.js", "name": "tiptap-image-upload", "imports": ["index.html"]}, "_unified-markdown-editor.BzxuP8op.js": {"file": "assets/unified-markdown-editor.BzxuP8op.js", "name": "unified-markdown-editor", "imports": ["_vendor-react.BcAa1DKr.js", "_vendor.DQpuTRuB.js", "_tabs.B0SF6qIv.js", "index.html", "_markdown-preview.Bw0U-NJA.js", "_content-converter.L-GziWIP.js", "_tiptap-image-upload.B_U9gNrF.js"]}, "_utils.Qa9QlCj_.js": {"file": "assets/utils.Qa9QlCj_.js", "name": "utils", "imports": ["index.html", "_vendor.DQpuTRuB.js"]}, "_vendor-react.BcAa1DKr.js": {"file": "assets/vendor-react.BcAa1DKr.js", "name": "vendor-react", "imports": ["_vendor.DQpuTRuB.js"]}, "_vendor-supabase.sufZ44-y.js": {"file": "assets/vendor-supabase.sufZ44-y.js", "name": "vendor-supabase", "imports": ["_vendor.DQpuTRuB.js"], "dynamicImports": ["_vendor.DQpuTRuB.js"]}, "_vendor.DQpuTRuB.js": {"file": "assets/vendor.DQpuTRuB.js", "name": "vendor", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js"]}, "index.html": {"file": "assets/index.BLDhDn0D.js", "name": "index", "src": "index.html", "isEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "_vendor.DQpuTRuB.js", "_vendor-supabase.sufZ44-y.js"], "dynamicImports": ["src/services/auth/userRoleService.ts", "src/pages/Login.tsx", "src/pages/ResetPassword.tsx", "src/pages/Dashboard.tsx", "src/pages/ModuleContent.tsx", "src/pages/ModuleManagementPage.tsx", "src/pages/ModulesPage.tsx", "src/pages/ModuleTestPage.tsx", "src/pages/LessonContent.tsx", "src/pages/NotFound.tsx", "src/pages/Admin.tsx", "src/pages/CertificatePage.tsx", "src/pages/EditorDemo.tsx", "src/pages/MarkdownEditorDemo.tsx", "src/components/debug/MarkdownDebug.tsx", "src/components/debug/TableTest.tsx", "src/pages/MarkdownEditorTest.tsx", "src/pages/TableRenderingTest.tsx", "src/pages/MarkdownTableTest.tsx", "src/pages/TableParsingVerification.tsx", "src/pages/AchievementsPage.tsx", "src/pages/TestLessonUI.tsx", "src/pages/SpacingTestPage.tsx", "src/pages/DebugLesson.tsx", "src/components/debug/ImageDebugTest.tsx", "src/components/LessonRedirect.tsx", "src/pages/YouTubeEditorTest.tsx", "src/pages/AuthCallback.tsx", "src/pages/admin/CompletionDiagnostics.tsx", "src/pages/admin/TestAnalyticsPage.tsx", "src/pages/Login.tsx", "src/pages/Dashboard.tsx", "src/pages/ModuleContent.tsx", "src/pages/Login.tsx", "src/pages/Dashboard.tsx", "src/pages/ModuleContent.tsx"], "css": ["assets/css/index.CBAYZKm-.css"], "assets": ["assets/main.DmqMQQqE.tsx"]}, "src/components/LessonRedirect.tsx": {"file": "assets/LessonRedirect.LOlzeWdP.js", "name": "LessonRedirect", "src": "src/components/LessonRedirect.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "index.html", "_vendor.DQpuTRuB.js", "_vendor-supabase.sufZ44-y.js"]}, "src/components/debug/ImageDebugTest.tsx": {"file": "assets/ImageDebugTest.DOOC9h5o.js", "name": "ImageDebugTest", "src": "src/components/debug/ImageDebugTest.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "_markdown-preview.Bw0U-NJA.js", "_content-converter.L-GziWIP.js", "_vendor.DQpuTRuB.js", "index.html", "_vendor-supabase.sufZ44-y.js"]}, "src/components/debug/MarkdownDebug.tsx": {"file": "assets/MarkdownDebug.Dlt2-8yI.js", "name": "MarkdownDebug", "src": "src/components/debug/MarkdownDebug.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "_enhanced-markdown-preview.Du81wch8.js", "_vendor.DQpuTRuB.js", "_tabs.B0SF6qIv.js", "index.html", "_markdown-preview.Bw0U-NJA.js", "_content-converter.L-GziWIP.js", "_card.B9V6b2DK.js", "_vendor-supabase.sufZ44-y.js"]}, "src/components/debug/TableTest.tsx": {"file": "assets/TableTest.-_qIgXdX.js", "name": "TableTest", "src": "src/components/debug/TableTest.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "_markdown-preview.Bw0U-NJA.js", "_vendor.DQpuTRuB.js", "_content-converter.L-GziWIP.js", "index.html", "_vendor-supabase.sufZ44-y.js"], "css": ["assets/css/professional-lesson-content.bN5oOkou.css"]}, "src/main.tsx": {"file": "assets/main.DmqMQQqE.tsx", "src": "src/main.tsx"}, "src/pages/AchievementsPage.tsx": {"file": "assets/AchievementsPage.DYWWrx_E.js", "name": "AchievementsPage", "src": "src/pages/AchievementsPage.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "_Layout.DRjmVYQG.js", "index.html", "_card.B9V6b2DK.js", "_tabs.B0SF6qIv.js", "_badge.C87ZuIxl.js", "_achievementService.Vkx_BHml.js", "_confetti.ShHySCrk.js", "_vendor.DQpuTRuB.js", "_floating-sidebar-container.BxlLgzat.js", "_vendor-supabase.sufZ44-y.js"]}, "src/pages/Admin.tsx": {"file": "assets/Admin.CC-kr3nO.js", "name": "Admin", "src": "src/pages/Admin.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "_Layout.DRjmVYQG.js", "_tabs.B0SF6qIv.js", "index.html", "_label.D4YlnUPk.js", "_badge.C87ZuIxl.js", "_enrollmentApi.CstnsQi_.js", "_alert.EBLwCiZR.js", "_vendor-supabase.sufZ44-y.js", "_vendor.DQpuTRuB.js", "_breadcrumb.DyMjeizE.js", "_moduleImageUtils.AJjzI8xs.js", "_skeleton.C0Lb8Xms.js", "_card.B9V6b2DK.js", "_alert-dialog.BK1A3Gb_.js", "_moduleTestService.BmAHru_J.js", "_module-test.BaVeK2uM.js", "_utils.Qa9QlCj_.js", "_simple-markdown-editor.DQ2eiB7n.js", "_TestAnalyticsDashboard.B08xtLlk.js", "_markdown-preview.Bw0U-NJA.js", "_content-converter.L-GziWIP.js", "_tiptap-image-upload.B_U9gNrF.js", "_progress.BMuwHUJX.js"]}, "src/pages/AuthCallback.tsx": {"file": "assets/AuthCallback.CF4zthXy.js", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "src": "src/pages/AuthCallback.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "index.html", "_vendor.DQpuTRuB.js", "_vendor-supabase.sufZ44-y.js"]}, "src/pages/CertificatePage.tsx": {"file": "assets/CertificatePage.CeFjybHI.js", "name": "CertificatePage", "src": "src/pages/CertificatePage.tsx", "isDynamicEntry": true, "imports": ["_vendor.DQpuTRuB.js", "_vendor-react.BcAa1DKr.js", "_Layout.DRjmVYQG.js", "index.html", "_courseApi.BQX5-7u-.js", "_confetti.ShHySCrk.js", "_floating-sidebar-container.BxlLgzat.js", "_vendor-supabase.sufZ44-y.js", "_utils.Qa9QlCj_.js", "_enrollmentApi.CstnsQi_.js"], "dynamicImports": ["src/services/course/completionService.ts"]}, "src/pages/Dashboard.tsx": {"file": "assets/Dashboard.Cc5az03q.js", "name": "Dashboard", "src": "src/pages/Dashboard.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "_Layout.DRjmVYQG.js", "_responsive-image.BMcUeCRG.js", "index.html", "_enrollmentApi.CstnsQi_.js", "_vendor.DQpuTRuB.js", "_loading-skeleton.DxUGnZ26.js", "_floating-sidebar-container.BxlLgzat.js", "_alert.EBLwCiZR.js", "_skeleton.C0Lb8Xms.js", "_vendor-supabase.sufZ44-y.js"]}, "src/pages/DebugLesson.tsx": {"file": "assets/DebugLesson.BBeHXqq7.js", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "src": "src/pages/DebugLesson.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "index.html", "_courseApi.BQX5-7u-.js", "_vendor.DQpuTRuB.js", "_vendor-supabase.sufZ44-y.js", "_utils.Qa9QlCj_.js", "_enrollmentApi.CstnsQi_.js"]}, "src/pages/EditorDemo.tsx": {"file": "assets/EditorDemo.BB8Qx_Xq.js", "name": "EditorDemo", "src": "src/pages/EditorDemo.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "_simple-markdown-editor.DQ2eiB7n.js", "_card.B9V6b2DK.js", "_badge.C87ZuIxl.js", "index.html", "_vendor.DQpuTRuB.js", "_tabs.B0SF6qIv.js", "_label.D4YlnUPk.js", "_markdown-preview.Bw0U-NJA.js", "_content-converter.L-GziWIP.js", "_tiptap-image-upload.B_U9gNrF.js", "_vendor-supabase.sufZ44-y.js"]}, "src/pages/LessonContent.tsx": {"file": "assets/LessonContent.CfhFGPRt.js", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "src": "src/pages/LessonContent.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "_Layout.DRjmVYQG.js", "index.html", "_courseApi.BQX5-7u-.js", "_utils.Qa9QlCj_.js", "_loading-skeleton.DxUGnZ26.js", "_floating-sidebar-container.BxlLgzat.js", "_LessonContent.B2xCUk6H.js", "_vendor.DQpuTRuB.js", "_card.B9V6b2DK.js", "_label.D4YlnUPk.js", "_achievementService.Vkx_BHml.js", "_alert-dialog.BK1A3Gb_.js", "src/services/course/completionService.ts", "_enrollmentApi.CstnsQi_.js", "_vendor-supabase.sufZ44-y.js", "_skeleton.C0Lb8Xms.js", "_markdown-preview.Bw0U-NJA.js", "_content-converter.L-GziWIP.js"], "css": ["assets/css/LessonContent.BwOXDaPG.css", "assets/css/professional-lesson-content.bN5oOkou.css"]}, "src/pages/Login.tsx": {"file": "assets/Login.D9BFbxWH.js", "name": "<PERSON><PERSON>", "src": "src/pages/Login.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "index.html", "_vendor.DQpuTRuB.js", "_vendor-supabase.sufZ44-y.js"]}, "src/pages/MarkdownEditorDemo.tsx": {"file": "assets/MarkdownEditorDemo.BPMRzgsL.js", "name": "MarkdownEditorDemo", "src": "src/pages/MarkdownEditorDemo.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "_vendor.DQpuTRuB.js", "index.html", "_tabs.B0SF6qIv.js", "_label.D4YlnUPk.js", "_enhanced-markdown-preview.Du81wch8.js", "_tiptap-image-upload.B_U9gNrF.js", "_card.B9V6b2DK.js", "_badge.C87ZuIxl.js", "_vendor-supabase.sufZ44-y.js", "_content-converter.L-GziWIP.js"]}, "src/pages/MarkdownEditorTest.tsx": {"file": "assets/MarkdownEditorTest.aGsVYXMm.js", "name": "MarkdownEditorTest", "src": "src/pages/MarkdownEditorTest.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "_unified-markdown-editor.BzxuP8op.js", "_card.B9V6b2DK.js", "_badge.C87ZuIxl.js", "index.html", "_tabs.B0SF6qIv.js", "_vendor.DQpuTRuB.js", "_markdown-preview.Bw0U-NJA.js", "_content-converter.L-GziWIP.js", "_tiptap-image-upload.B_U9gNrF.js", "_vendor-supabase.sufZ44-y.js"]}, "src/pages/MarkdownTableTest.tsx": {"file": "assets/MarkdownTableTest.BWvqDVg7.js", "name": "MarkdownTableTest", "src": "src/pages/MarkdownTableTest.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "_markdown-preview.Bw0U-NJA.js", "_vendor.DQpuTRuB.js", "_content-converter.L-GziWIP.js", "index.html", "_vendor-supabase.sufZ44-y.js"]}, "src/pages/ModuleContent.tsx": {"file": "assets/ModuleContent.gv_-rfab.js", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "src": "src/pages/ModuleContent.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "_Layout.DRjmVYQG.js", "index.html", "_responsive-image.BMcUeCRG.js", "_progress.BMuwHUJX.js", "_markdown-preview.Bw0U-NJA.js", "_vendor.DQpuTRuB.js", "_CourseCompletionCelebration.BJZAxw59.js", "src/services/course/completionService.ts", "_courseApi.BQX5-7u-.js", "_loading-skeleton.DxUGnZ26.js", "_floating-sidebar-container.BxlLgzat.js", "_enrollmentApi.CstnsQi_.js", "_vendor-supabase.sufZ44-y.js", "_skeleton.C0Lb8Xms.js", "_content-converter.L-GziWIP.js", "_confetti.ShHySCrk.js", "_achievementService.Vkx_BHml.js", "_utils.Qa9QlCj_.js"], "css": ["assets/css/ModuleContent.DoOZiXEi.css"]}, "src/pages/ModuleManagementPage.tsx": {"file": "assets/ModuleManagementPage.DXOiTvi6.js", "name": "ModuleManagementPage", "src": "src/pages/ModuleManagementPage.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "_Layout.DRjmVYQG.js", "index.html", "_breadcrumb.DyMjeizE.js", "_label.D4YlnUPk.js", "_card.B9V6b2DK.js", "_courseApi.BQX5-7u-.js", "_alert-dialog.BK1A3Gb_.js", "_floating-sidebar-container.BxlLgzat.js", "_vendor.DQpuTRuB.js", "_vendor-supabase.sufZ44-y.js", "_utils.Qa9QlCj_.js", "_enrollmentApi.CstnsQi_.js"]}, "src/pages/ModuleTestPage.tsx": {"file": "assets/ModuleTestPage.BPl7sTnK.js", "name": "ModuleTestPage", "src": "src/pages/ModuleTestPage.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "_Layout.DRjmVYQG.js", "index.html", "_moduleTestService.BmAHru_J.js", "_module-test.BaVeK2uM.js", "_courseApi.BQX5-7u-.js", "_vendor.DQpuTRuB.js", "_card.B9V6b2DK.js", "_badge.C87ZuIxl.js", "_floating-sidebar-container.BxlLgzat.js", "_vendor-supabase.sufZ44-y.js", "_utils.Qa9QlCj_.js", "_enrollmentApi.CstnsQi_.js"]}, "src/pages/ModulesPage.tsx": {"file": "assets/ModulesPage.C6n5RnQl.js", "name": "ModulesPage", "src": "src/pages/ModulesPage.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "_Layout.DRjmVYQG.js", "_progress.BMuwHUJX.js", "index.html", "_moduleImageUtils.AJjzI8xs.js", "_moduleTestService.BmAHru_J.js", "_vendor.DQpuTRuB.js", "src/services/course/completionService.ts", "_CourseCompletionCelebration.BJZAxw59.js", "_courseApi.BQX5-7u-.js", "_floating-sidebar-container.BxlLgzat.js", "_vendor-supabase.sufZ44-y.js", "_achievementService.Vkx_BHml.js", "_confetti.ShHySCrk.js", "_utils.Qa9QlCj_.js", "_enrollmentApi.CstnsQi_.js"]}, "src/pages/NotFound.tsx": {"file": "assets/NotFound.D7YCCh6G.js", "name": "NotFound", "src": "src/pages/NotFound.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "_vendor.DQpuTRuB.js"]}, "src/pages/ResetPassword.tsx": {"file": "assets/ResetPassword.Cs9USIZW.js", "name": "ResetPassword", "src": "src/pages/ResetPassword.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "_vendor.DQpuTRuB.js", "index.html", "_vendor-supabase.sufZ44-y.js"]}, "src/pages/SpacingTestPage.tsx": {"file": "assets/SpacingTestPage.B3CFvju1.js", "name": "SpacingTestPage", "src": "src/pages/SpacingTestPage.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "_Layout.DRjmVYQG.js", "_LessonContent.B2xCUk6H.js", "_floating-sidebar-container.BxlLgzat.js", "_vendor.DQpuTRuB.js", "index.html", "_vendor-supabase.sufZ44-y.js", "_markdown-preview.Bw0U-NJA.js", "_content-converter.L-GziWIP.js"]}, "src/pages/TableParsingVerification.tsx": {"file": "assets/TableParsingVerification.Cs7ZuQbt.js", "name": "TableParsingVerification", "src": "src/pages/TableParsingVerification.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "_markdown-preview.Bw0U-NJA.js", "_content-converter.L-GziWIP.js", "_vendor.DQpuTRuB.js", "index.html", "_vendor-supabase.sufZ44-y.js"]}, "src/pages/TableRenderingTest.tsx": {"file": "assets/TableRenderingTest.CUlbKRDO.js", "name": "TableRenderingTest", "src": "src/pages/TableRenderingTest.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "_Layout.DRjmVYQG.js", "_unified-markdown-editor.BzxuP8op.js", "_markdown-preview.Bw0U-NJA.js", "_LessonContent.B2xCUk6H.js", "_card.B9V6b2DK.js", "_tabs.B0SF6qIv.js", "_badge.C87ZuIxl.js", "index.html", "_vendor.DQpuTRuB.js", "_content-converter.L-GziWIP.js", "_tiptap-image-upload.B_U9gNrF.js", "_vendor-supabase.sufZ44-y.js"]}, "src/pages/TestLessonUI.tsx": {"file": "assets/TestLessonUI.BR2dGiiK.js", "name": "TestLessonUI", "src": "src/pages/TestLessonUI.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "_Layout.DRjmVYQG.js", "_LessonContent.B2xCUk6H.js", "_floating-sidebar-container.BxlLgzat.js", "_vendor.DQpuTRuB.js", "index.html", "_vendor-supabase.sufZ44-y.js", "_markdown-preview.Bw0U-NJA.js", "_content-converter.L-GziWIP.js"]}, "src/pages/YouTubeEditorTest.tsx": {"file": "assets/YouTubeEditorTest.DkqlUuny.js", "name": "YouTubeEditorTest", "src": "src/pages/YouTubeEditorTest.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "_simple-markdown-editor.DQ2eiB7n.js", "_card.B9V6b2DK.js", "_badge.C87ZuIxl.js", "index.html", "_vendor.DQpuTRuB.js", "_tabs.B0SF6qIv.js", "_label.D4YlnUPk.js", "_markdown-preview.Bw0U-NJA.js", "_content-converter.L-GziWIP.js", "_tiptap-image-upload.B_U9gNrF.js", "_vendor-supabase.sufZ44-y.js"]}, "src/pages/admin/CompletionDiagnostics.tsx": {"file": "assets/CompletionDiagnostics.Dd73kFC9.js", "name": "CompletionDiagnostics", "src": "src/pages/admin/CompletionDiagnostics.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "index.html", "_Layout.DRjmVYQG.js", "_card.B9V6b2DK.js", "_tabs.B0SF6qIv.js", "_vendor.DQpuTRuB.js", "src/services/course/completionService.ts", "_vendor-supabase.sufZ44-y.js", "_achievementService.Vkx_BHml.js"]}, "src/pages/admin/TestAnalyticsPage.tsx": {"file": "assets/TestAnalyticsPage.9U_HeWPy.js", "name": "TestAnalyticsPage", "src": "src/pages/admin/TestAnalyticsPage.tsx", "isDynamicEntry": true, "imports": ["_vendor-react.BcAa1DKr.js", "_Layout.DRjmVYQG.js", "_TestAnalyticsDashboard.B08xtLlk.js", "_vendor.DQpuTRuB.js", "index.html", "_vendor-supabase.sufZ44-y.js", "_card.B9V6b2DK.js", "_badge.C87ZuIxl.js", "_module-test.BaVeK2uM.js", "_progress.BMuwHUJX.js"]}, "src/services/auth/userRoleService.ts": {"file": "assets/userRoleService.Crm_K_HM.js", "name": "userRoleService", "src": "src/services/auth/userRoleService.ts", "isDynamicEntry": true, "imports": ["index.html", "_vendor.DQpuTRuB.js", "_vendor-react.BcAa1DKr.js", "_vendor-supabase.sufZ44-y.js"]}, "src/services/course/accessControlService.ts": {"file": "assets/accessControlService.DuaMmltt.js", "name": "accessControlService", "src": "src/services/course/accessControlService.ts", "isDynamicEntry": true, "imports": ["index.html", "src/services/auth/userRoleService.ts", "_vendor-react.BcAa1DKr.js", "_vendor.DQpuTRuB.js", "_vendor-supabase.sufZ44-y.js"]}, "src/services/course/completionService.ts": {"file": "assets/completionService.BUb78ZaE.js", "name": "completionService", "src": "src/services/course/completionService.ts", "isDynamicEntry": true, "imports": ["_vendor.DQpuTRuB.js", "index.html", "_achievementService.Vkx_BHml.js", "_vendor-react.BcAa1DKr.js", "_vendor-supabase.sufZ44-y.js"], "dynamicImports": ["src/services/course/accessControlService.ts"]}}