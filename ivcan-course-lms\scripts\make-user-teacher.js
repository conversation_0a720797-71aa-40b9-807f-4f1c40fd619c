/**
 * <PERSON><PERSON><PERSON> to make a user a teacher
 *
 * This script adds the "teacher" role to a specified user.
 *
 * Usage:
 * node scripts/make-user-teacher.js <user_id>
 */

const { createClient } = require('@supabase/supabase-js');
const { SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY } = require('./config');

// Get the user ID from command line arguments
const userId = process.argv[2];

if (!userId) {
  console.error('Error: User ID is required');
  console.log('Usage: node scripts/make-user-teacher.js <user_id>');
  process.exit(1);
}

// Initialize Supabase client
if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('Error: Supabase URL and service role key are required');
  console.log('Please check your config.js file');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function makeUserTeacher(userId) {
  console.log(`Making user ${userId} a teacher...`);

  try {
    // First, check if the user exists
    const { data: user, error: userError } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error('Error checking if user exists:', userError);
      return false;
    }

    if (!user) {
      console.error(`User with ID ${userId} not found`);
      return false;
    }

    // Check if the user already has a role
    const { data: existingRole, error: roleError } = await supabase
      .from('user_roles')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (roleError && !roleError.message.includes('No rows found')) {
      console.error('Error checking existing role:', roleError);
      return false;
    }

    if (existingRole) {
      if (existingRole.role === 'teacher') {
        console.log(`User ${userId} is already a teacher`);
        return true;
      }

      // Update the existing role to teacher
      const { error: updateError } = await supabase
        .from('user_roles')
        .update({ role: 'teacher' })
        .eq('user_id', userId);

      if (updateError) {
        console.error('Error updating user role:', updateError);
        return false;
      }

      console.log(`Updated user ${userId} role to teacher`);
      return true;
    }

    // Insert a new role
    const { error: insertError } = await supabase
      .from('user_roles')
      .insert([
        {
          user_id: userId,
          role: 'teacher'
        }
      ]);

    if (insertError) {
      console.error('Error inserting user role:', insertError);
      return false;
    }

    console.log(`Successfully made user ${userId} a teacher`);
    return true;
  } catch (error) {
    console.error('Unexpected error:', error);
    return false;
  }
}

// Run the function
makeUserTeacher(userId)
  .then(success => {
    if (success) {
      console.log('Operation completed successfully');
      process.exit(0);
    } else {
      console.error('Operation failed');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
