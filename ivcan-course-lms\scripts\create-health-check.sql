-- Create a function to set up the health check table
CREATE OR REPLACE FUNCTION create_health_check_table()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the table exists
  IF NOT EXISTS (
    SELECT FROM pg_tables 
    WHERE schemaname = 'public' 
    AND tablename = 'health_check'
  ) THEN
    -- Create the table
    CREATE TABLE public.health_check (
      id SERIAL PRIMARY KEY,
      status TEXT NOT NULL DEFAULT 'ok',
      created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
    );
    
    -- Insert a record
    INSERT INTO public.health_check (status) VALUES ('ok');
    
    -- Set up RLS
    ALTER TABLE public.health_check ENABLE ROW LEVEL SECURITY;
    
    -- Create policy to allow anonymous reads
    CREATE POLICY "Allow anonymous read access" 
      ON public.health_check
      FOR SELECT 
      TO anon
      USING (true);
  END IF;
END;
$$;
