-- Consolidated Fixes Migration
-- This migration combines several previous migrations to ensure a consistent database state
-- It is idempotent and can be run multiple times without error

-- =============================================
-- USER COURSE ENROLLMENT FIXES
-- =============================================

-- Make sure the completed_at column exists in user_course_enrollment
ALTER TABLE IF EXISTS public.user_course_enrollment 
ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP WITH TIME ZONE;

-- Update existing completed enrollments that don't have a completed_at date
UPDATE public.user_course_enrollment
SET completed_at = updated_at
WHERE status = 'completed' AND completed_at IS NULL;

-- Make sure the status column has the correct type
ALTER TABLE IF EXISTS public.user_course_enrollment 
ALTER COLUMN status TYPE TEXT;

-- =============================================
-- USER MODULE PROGRESS TABLE
-- =============================================

-- Create user_module_progress table to track module completion per user
CREATE TABLE IF NOT EXISTS public.user_module_progress (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  module_id UUID NOT NULL REFERENCES public.modules(id) ON DELETE CASCADE,
  is_completed BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, module_id)
);

-- Add RLS policies for user_module_progress
ALTER TABLE public.user_module_progress ENABLE ROW LEVEL SECURITY;

-- Allow users to view their own progress
CREATE POLICY IF NOT EXISTS "Users can view their own module progress"
  ON public.user_module_progress
  FOR SELECT
  USING (auth.uid() = user_id);

-- Allow users to update their own progress
CREATE POLICY IF NOT EXISTS "Users can update their own module progress"
  ON public.user_module_progress
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Allow users to insert their own progress
CREATE POLICY IF NOT EXISTS "Users can insert their own module progress"
  ON public.user_module_progress
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Allow service role to bypass RLS
CREATE POLICY IF NOT EXISTS "Service role can do anything with module progress"
  ON public.user_module_progress
  USING (auth.jwt() ->> 'role' = 'service_role');

-- =============================================
-- MODULES TABLE FIXES
-- =============================================

-- Make sure the is_completed column exists in modules
ALTER TABLE IF EXISTS public.modules 
ADD COLUMN IF NOT EXISTS is_completed BOOLEAN DEFAULT false;

-- Fix RLS policies for modules table
DROP POLICY IF EXISTS "Users can update module completion" ON public.modules;

-- Create new policy to allow users to update module completion
CREATE POLICY IF NOT EXISTS "Users can update module completion" 
ON public.modules FOR UPDATE 
USING (true)
WITH CHECK (true);

-- =============================================
-- USER PREFERENCES TABLE
-- =============================================

-- Create user_preferences table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.user_preferences (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  auto_complete_courses BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(user_id)
);

-- Add RLS policies for user_preferences
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;

-- Allow users to view their own preferences
CREATE POLICY IF NOT EXISTS "Users can view their own preferences"
  ON public.user_preferences
  FOR SELECT
  USING (auth.uid() = user_id);

-- Allow users to update their own preferences
CREATE POLICY IF NOT EXISTS "Users can update their own preferences"
  ON public.user_preferences
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Allow users to insert their own preferences
CREATE POLICY IF NOT EXISTS "Users can insert their own preferences"
  ON public.user_preferences
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Allow service role to bypass RLS
CREATE POLICY IF NOT EXISTS "Service role can do anything with user preferences"
  ON public.user_preferences
  USING (auth.jwt() ->> 'role' = 'service_role');

-- =============================================
-- STORAGE BUCKETS AND RLS
-- =============================================

-- Create storage buckets if they don't exist
SELECT CASE 
  WHEN NOT EXISTS (
    SELECT 1 FROM storage.buckets WHERE name = 'course-images'
  ) 
  THEN storage.create_bucket('course-images', {'public': true})
END;

SELECT CASE 
  WHEN NOT EXISTS (
    SELECT 1 FROM storage.buckets WHERE name = 'avatars'
  ) 
  THEN storage.create_bucket('avatars', {'public': true})
END;

SELECT CASE 
  WHEN NOT EXISTS (
    SELECT 1 FROM storage.buckets WHERE name = 'app-uploads'
  ) 
  THEN storage.create_bucket('app-uploads', {'public': true})
END;

-- Enable RLS for storage.objects
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Allow public access to course-images" ON storage.objects;
DROP POLICY IF EXISTS "Allow public access to avatars" ON storage.objects;
DROP POLICY IF EXISTS "Allow public access to app-uploads" ON storage.objects;

DROP POLICY IF EXISTS "Allow authenticated uploads to course-images" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated uploads to avatars" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated uploads to app-uploads" ON storage.objects;

-- Create policies for public read access to all buckets
CREATE POLICY "Allow public access to course-images"
ON storage.objects FOR SELECT
USING (bucket_id = 'course-images');

CREATE POLICY "Allow public access to avatars"
ON storage.objects FOR SELECT
USING (bucket_id = 'avatars');

CREATE POLICY "Allow public access to app-uploads"
ON storage.objects FOR SELECT
USING (bucket_id = 'app-uploads');

-- Create policies for authenticated uploads
CREATE POLICY "Allow authenticated uploads to course-images"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'course-images' 
  AND auth.role() = 'authenticated'
);

CREATE POLICY "Allow authenticated uploads to avatars"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'avatars' 
  AND auth.role() = 'authenticated'
);

CREATE POLICY "Allow authenticated uploads to app-uploads"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'app-uploads' 
  AND auth.role() = 'authenticated'
);

-- Create policies for authenticated updates and deletes
CREATE POLICY "Allow authenticated updates to course-images"
ON storage.objects FOR UPDATE
USING (
  bucket_id = 'course-images' 
  AND auth.role() = 'authenticated'
);

CREATE POLICY "Allow authenticated updates to avatars"
ON storage.objects FOR UPDATE
USING (
  bucket_id = 'avatars' 
  AND auth.role() = 'authenticated'
);

CREATE POLICY "Allow authenticated updates to app-uploads"
ON storage.objects FOR UPDATE
USING (
  bucket_id = 'app-uploads' 
  AND auth.role() = 'authenticated'
);

CREATE POLICY "Allow authenticated deletes to course-images"
ON storage.objects FOR DELETE
USING (
  bucket_id = 'course-images' 
  AND auth.role() = 'authenticated'
);

CREATE POLICY "Allow authenticated deletes to avatars"
ON storage.objects FOR DELETE
USING (
  bucket_id = 'avatars' 
  AND auth.role() = 'authenticated'
);

CREATE POLICY "Allow authenticated deletes to app-uploads"
ON storage.objects FOR DELETE
USING (
  bucket_id = 'app-uploads' 
  AND auth.role() = 'authenticated'
);

-- =============================================
-- ANALYTICS EVENTS TABLE
-- =============================================

-- Create analytics_events table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.analytics_events (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  event_name TEXT NOT NULL,
  event_data JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- Add RLS policies for analytics_events
ALTER TABLE public.analytics_events ENABLE ROW LEVEL SECURITY;

-- Only allow service role to select analytics events
CREATE POLICY IF NOT EXISTS "Service role can select analytics events"
  ON public.analytics_events
  FOR SELECT
  USING (auth.jwt() ->> 'role' = 'service_role');

-- Allow authenticated users to insert analytics events
CREATE POLICY IF NOT EXISTS "Authenticated users can insert analytics events"
  ON public.analytics_events
  FOR INSERT
  WITH CHECK (auth.role() = 'authenticated');

-- =============================================
-- HELPER FUNCTIONS
-- =============================================

-- Function to complete a course for a user
CREATE OR REPLACE FUNCTION complete_course(p_user_id UUID, p_course_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  v_enrollment_exists BOOLEAN;
BEGIN
  -- Check if enrollment exists
  SELECT EXISTS(
    SELECT 1 FROM public.user_course_enrollment
    WHERE user_id = p_user_id AND course_id = p_course_id
  ) INTO v_enrollment_exists;
  
  -- If enrollment doesn't exist, create it
  IF NOT v_enrollment_exists THEN
    INSERT INTO public.user_course_enrollment (
      user_id, course_id, status, enrolled_at, completed_at, updated_at
    ) VALUES (
      p_user_id, p_course_id, 'completed', NOW(), NOW(), NOW()
    );
  ELSE
    -- Update existing enrollment
    UPDATE public.user_course_enrollment
    SET 
      status = 'completed',
      completed_at = COALESCE(completed_at, NOW()),
      updated_at = NOW()
    WHERE 
      user_id = p_user_id AND course_id = p_course_id;
  END IF;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in complete_course: %', SQLERRM;
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to complete all courses for a user
CREATE OR REPLACE FUNCTION complete_all_courses(p_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  v_course RECORD;
  v_success BOOLEAN := TRUE;
BEGIN
  -- Loop through all courses
  FOR v_course IN SELECT id FROM public.courses LOOP
    -- Complete each course
    IF NOT complete_course(p_user_id, v_course.id) THEN
      v_success := FALSE;
    END IF;
  END LOOP;
  
  RETURN v_success;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in complete_all_courses: %', SQLERRM;
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to reset user progress
CREATE OR REPLACE FUNCTION reset_module_completion(p_user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  -- Delete user module progress
  DELETE FROM public.user_module_progress
  WHERE user_id = p_user_id;
  
  -- Delete user lesson progress
  DELETE FROM public.user_lesson_progress
  WHERE user_id = p_user_id;
  
  -- Update user course enrollments
  UPDATE public.user_course_enrollment
  SET 
    status = 'in_progress',
    completed_at = NULL,
    updated_at = NOW()
  WHERE 
    user_id = p_user_id;
  
  -- Disable auto-completion
  INSERT INTO public.user_preferences (user_id, auto_complete_courses, updated_at)
  VALUES (p_user_id, FALSE, NOW())
  ON CONFLICT (user_id)
  DO UPDATE SET 
    auto_complete_courses = FALSE,
    updated_at = NOW();
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in reset_module_completion: %', SQLERRM;
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
