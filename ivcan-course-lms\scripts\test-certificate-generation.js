/**
 * Certificate Generation Test Script
 *
 * This script tests the complete certificate generation workflow:
 * 1. Tests course completion functionality
 * 2. Verifies certificate data is properly stored
 * 3. Tests certificate retrieval for achievements page
 * 4. Validates certificate display functionality
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.VITE_SUPABASE_URL || 'https://jibspqwieubavucdtccv.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_KEY;

if (!SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY is required');
  console.error('Please set VITE_SUPABASE_SERVICE_ROLE_KEY in your .env file');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

/**
 * Test course completion functionality
 */
async function testCourseCompletion() {
  console.log(`${colors.blue}🧪 Testing Course Completion Functionality...${colors.reset}`);

  try {
    // Get a test course
    const { data: courses, error: courseError } = await supabase
      .from('courses')
      .select('id, title')
      .limit(1);

    if (courseError || !courses || courses.length === 0) {
      console.error(`${colors.red}❌ No courses found for testing: ${courseError?.message || 'No courses available'}${colors.reset}`);
      return false;
    }

    const testCourse = courses[0];
    console.log(`${colors.cyan}📚 Using test course: ${testCourse.title} (${testCourse.id})${colors.reset}`);

    // Get a test user (first user in the system)
    const { data: users, error: userError } = await supabase.auth.admin.listUsers();

    if (userError || !users || users.users.length === 0) {
      console.error(`${colors.red}❌ No users found for testing: ${userError?.message || 'No users available'}${colors.reset}`);
      return false;
    }

    const testUser = users.users[0];
    console.log(`${colors.cyan}👤 Using test user: ${testUser.email} (${testUser.id})${colors.reset}`);

    // Test the complete_course function
    const { data: completionResult, error: completionError } = await supabase.rpc('complete_course', {
      p_user_id: testUser.id,
      p_course_id: testCourse.id
    });

    if (completionError) {
      console.error(`${colors.red}❌ Course completion failed: ${completionError.message}${colors.reset}`);
      return false;
    }

    console.log(`${colors.green}✅ Course completion successful: ${completionResult}${colors.reset}`);

    // Verify the enrollment was created/updated
    const { data: enrollment, error: enrollmentError } = await supabase
      .from('user_course_enrollment')
      .select('*')
      .eq('user_id', testUser.id)
      .eq('course_id', testCourse.id)
      .single();

    if (enrollmentError) {
      console.error(`${colors.red}❌ Failed to verify enrollment: ${enrollmentError.message}${colors.reset}`);
      return false;
    }

    if (enrollment.status !== 'completed' || !enrollment.completed_at) {
      console.error(`${colors.red}❌ Enrollment not properly completed: status=${enrollment.status}, completed_at=${enrollment.completed_at}${colors.reset}`);
      return false;
    }

    console.log(`${colors.green}✅ Enrollment verified: status=${enrollment.status}, completed_at=${enrollment.completed_at}${colors.reset}`);

    return true;

  } catch (err) {
    console.error(`${colors.red}❌ Course completion test error: ${err.message}${colors.reset}`);
    return false;
  }
}

/**
 * Test certificate retrieval functionality
 */
async function testCertificateRetrieval() {
  console.log(`${colors.blue}🧪 Testing Certificate Retrieval...${colors.reset}`);

  try {
    // Test the certificate query used by AchievementsPage
    const { data: certificates, error: certError } = await supabase
      .from('user_course_enrollment')
      .select(`
        id,
        course_id,
        user_id,
        completed_at,
        enrolled_at,
        updated_at,
        status,
        course:courses(id, title, description, image_url)
      `)
      .eq('status', 'completed')
      .limit(5);

    if (certError) {
      console.error(`${colors.red}❌ Certificate retrieval failed: ${certError.message}${colors.reset}`);
      return false;
    }

    console.log(`${colors.green}✅ Certificate retrieval successful: found ${certificates.length} certificates${colors.reset}`);

    // Validate certificate data structure
    for (const cert of certificates) {
      if (!cert.completed_at) {
        console.error(`${colors.red}❌ Certificate ${cert.id} missing completed_at date${colors.reset}`);
        return false;
      }

      if (!cert.course || !cert.course.title) {
        console.error(`${colors.red}❌ Certificate ${cert.id} missing course data${colors.reset}`);
        return false;
      }

      console.log(`${colors.cyan}📜 Certificate: ${cert.course.title} - completed ${cert.completed_at}${colors.reset}`);
    }

    return true;

  } catch (err) {
    console.error(`${colors.red}❌ Certificate retrieval test error: ${err.message}${colors.reset}`);
    return false;
  }
}

/**
 * Test new certificate helper functions
 */
async function testCertificateHelperFunctions() {
  console.log(`${colors.blue}🧪 Testing Certificate Helper Functions...${colors.reset}`);

  try {
    // Get a test user
    const { data: users, error: userError } = await supabase.auth.admin.listUsers();

    if (userError || !users || users.users.length === 0) {
      console.error(`${colors.red}❌ No users found for testing: ${userError?.message || 'No users available'}${colors.reset}`);
      return false;
    }

    const testUser = users.users[0];

    // Test get_user_certificates function
    const { data: userCertificates, error: userCertError } = await supabase.rpc('get_user_certificates', {
      p_user_id: testUser.id
    });

    if (userCertError) {
      console.error(`${colors.red}❌ get_user_certificates failed: ${userCertError.message}${colors.reset}`);
      return false;
    }

    console.log(`${colors.green}✅ get_user_certificates successful: found ${userCertificates.length} certificates${colors.reset}`);

    // Test is_course_completed function if we have certificates
    if (userCertificates.length > 0) {
      const testCert = userCertificates[0];

      const { data: isCompleted, error: completedError } = await supabase.rpc('is_course_completed', {
        p_user_id: testUser.id,
        p_course_id: testCert.course_id
      });

      if (completedError) {
        console.error(`${colors.red}❌ is_course_completed failed: ${completedError.message}${colors.reset}`);
        return false;
      }

      if (!isCompleted) {
        console.error(`${colors.red}❌ is_course_completed returned false for completed course${colors.reset}`);
        return false;
      }

      console.log(`${colors.green}✅ is_course_completed successful: ${isCompleted}${colors.reset}`);
    }

    return true;

  } catch (err) {
    console.error(`${colors.red}❌ Certificate helper functions test error: ${err.message}${colors.reset}`);
    return false;
  }
}

/**
 * Test database schema and constraints
 */
async function testDatabaseSchema() {
  console.log(`${colors.blue}🧪 Testing Database Schema...${colors.reset}`);

  try {
    // Test that required columns exist
    const { data: schemaTest, error: schemaError } = await supabase
      .from('user_course_enrollment')
      .select('id, user_id, course_id, status, completed_at, enrolled_at, updated_at')
      .limit(1);

    if (schemaError) {
      console.error(`${colors.red}❌ Schema test failed: ${schemaError.message}${colors.reset}`);
      return false;
    }

    console.log(`${colors.green}✅ Database schema test passed${colors.reset}`);

    // Test courses table has image_url
    const { data: courseSchemaTest, error: courseSchemaError } = await supabase
      .from('courses')
      .select('id, title, description, image_url')
      .limit(1);

    if (courseSchemaError) {
      console.error(`${colors.red}❌ Courses schema test failed: ${courseSchemaError.message}${colors.reset}`);
      return false;
    }

    console.log(`${colors.green}✅ Courses schema test passed${colors.reset}`);

    return true;

  } catch (err) {
    console.error(`${colors.red}❌ Database schema test error: ${err.message}${colors.reset}`);
    return false;
  }
}

/**
 * Main test function
 */
async function runCertificateTests() {
  console.log(`${colors.magenta}🎓 Certificate Generation Test Suite${colors.reset}`);
  console.log(`${colors.cyan}📍 Supabase URL: ${SUPABASE_URL}${colors.reset}`);
  console.log('='.repeat(60));

  let allTestsPassed = true;

  // Test 1: Database Schema
  const schemaOk = await testDatabaseSchema();
  if (!schemaOk) {
    allTestsPassed = false;
  }

  // Test 2: Course Completion
  const completionOk = await testCourseCompletion();
  if (!completionOk) {
    allTestsPassed = false;
  }

  // Test 3: Certificate Retrieval
  const retrievalOk = await testCertificateRetrieval();
  if (!retrievalOk) {
    allTestsPassed = false;
  }

  // Test 4: Helper Functions
  const helpersOk = await testCertificateHelperFunctions();
  if (!helpersOk) {
    allTestsPassed = false;
  }

  // Final results
  console.log('\n' + '='.repeat(60));
  if (allTestsPassed) {
    console.log(`${colors.green}🎉 All Certificate Tests Passed!${colors.reset}`);
    console.log(`${colors.green}✅ Certificate generation is working correctly${colors.reset}`);
    console.log(`${colors.green}✅ Certificates should appear in Achievements page${colors.reset}`);
    console.log(`${colors.green}✅ Certificate download functionality should work${colors.reset}`);
  } else {
    console.log(`${colors.yellow}⚠️  Some Certificate Tests Failed${colors.reset}`);
    console.log(`${colors.yellow}⚠️  Certificate functionality may have issues${colors.reset}`);
    console.log(`${colors.yellow}⚠️  Please review the errors above and apply fixes${colors.reset}`);
  }
  console.log('='.repeat(60));

  return allTestsPassed;
}

// Run the tests
runCertificateTests().catch(console.error);
