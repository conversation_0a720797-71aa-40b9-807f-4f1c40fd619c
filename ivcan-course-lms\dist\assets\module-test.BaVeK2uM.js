const a=(e,t)=>({moduleId:e,type:t,title:t==="pre_test"?"Pre-Intervention Questionnaire":"Post-Intervention Questionnaire",questions:[]}),n=e=>({id:crypto.randomUUID(),type:"rating",question:"",questionNumber:e,minRating:1,maxRating:4,minLabel:"Strongly disagree",maxLabel:"Strongly agree"}),r=[{value:1,label:"Strongly disagree"},{value:2,label:"Disagree"},{value:3,label:"Agree"},{value:4,label:"Strongly agree"}];export{a,n as c,r};
