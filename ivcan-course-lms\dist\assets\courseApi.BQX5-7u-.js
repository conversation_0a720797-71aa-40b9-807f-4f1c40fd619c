const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/accessControlService.DuaMmltt.js","assets/index.BLDhDn0D.js","assets/vendor-react.BcAa1DKr.js","assets/vendor.DQpuTRuB.js","assets/vendor-supabase.sufZ44-y.js","assets/css/index.CBAYZKm-.css","assets/userRoleService.Crm_K_HM.js"])))=>i.map(i=>d[i]);
var y=Object.defineProperty,C=Object.defineProperties;var R=Object.getOwnPropertyDescriptors;var D=Object.getOwnPropertySymbols;var x=Object.prototype.hasOwnProperty,T=Object.prototype.propertyIsEnumerable;var U=(o,e,r)=>e in o?y(o,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):o[e]=r,S=(o,e)=>{for(var r in e||(e={}))x.call(e,r)&&U(o,r,e[r]);if(D)for(var r of D(e))T.call(e,r)&&U(o,r,e[r]);return o},N=(o,e)=>C(o,R(e));var m=(o,e,r)=>new Promise((s,t)=>{var l=u=>{try{c(r.next(u))}catch(a){t(a)}},n=u=>{try{c(r.throw(u))}catch(a){t(a)}},c=u=>u.done?s(u.value):Promise.resolve(u.value).then(l,n);c((r=r.apply(o,e)).next())});import{W as F,a2 as L}from"./vendor.DQpuTRuB.js";import{s as i}from"./index.BLDhDn0D.js";import{c as $}from"./utils.Qa9QlCj_.js";import{m as P}from"./enrollmentApi.CstnsQi_.js";const g={MODULES:"modules",LESSONS:"lessons",COURSES:"courses",MODULE_PROGRESS:"user_module_progress",LESSON_PROGRESS:"user_lesson_progress",COURSE_ENROLLMENT:"user_course_enrollment"},B=o=>m(void 0,null,function*(){if(console.log("Fetching course by slug:",o),!o)return console.error("No slug provided to fetchCourseBySlug"),null;try{const{data:e,error:r}=yield i.from(g.COURSES).select("*").eq("slug",o).single();if(r){if(console.error("Error fetching course by slug:",r),r.code==="PGRST116"){console.log("Course not found by slug, trying as ID");const{data:s,error:t}=yield i.from(g.COURSES).select("*").eq("id",o).single();return t?(console.error("Error fetching course by ID:",t),null):(console.log("Course found by ID:",s),s)}return null}return console.log("Course found by slug:",e),e}catch(e){return console.error("Unexpected error in fetchCourseBySlug:",e),null}}),W=o=>m(void 0,null,function*(){if(!o)return null;const{data:e,error:r}=yield i.from(g.COURSES).select("*").eq("id",o).single();return r?(console.error("Error fetching course by ID:",r),null):e}),V=(o,e)=>m(void 0,null,function*(){if(console.log(`Fetching modules for course ${o}, user: ${e||"none"}`),!o)return console.error("No courseId provided to fetchCourseModules"),[];let r=[];try{console.log(`Making Supabase query: SELECT * FROM modules WHERE course_id = '${o}' ORDER BY module_number ASC`);const{data:s,error:t}=yield i.from(g.MODULES).select("*").eq("course_id",o).order("module_number",{ascending:!0});if(t)return console.error("Error fetching modules:",t),[];r=s||[];const l=yield Promise.all(r.map(n=>m(void 0,null,function*(){const{data:c,error:u}=yield i.from(g.LESSONS).select("*").eq("module_id",n.id).order("created_at",{ascending:!0});if(u)return console.error("Error fetching lessons:",u),N(S({},n),{lessons:[]});let a=c?c.map($):[],E=!1;if(e){const{data:d,error:_}=yield i.from(g.MODULE_PROGRESS).select("is_completed").eq("user_id",e).eq("module_id",n.id).maybeSingle();_?console.error("Error fetching module progress:",_):d&&(E=d.is_completed);const{data:p}=yield i.from(g.LESSON_PROGRESS).select("*").eq("user_id",e).in("lesson_id",a.map(I=>I.id));p&&p.length>0?(a=a.map(h=>{const O=p.find(w=>w.lesson_id===h.id);return N(S({},h),{completed:O?O.is_completed:!1})}),a.length>0&&a.every(h=>h.completed)&&!E&&(yield k(n.id,e,!0),E=!0)):a=a.map(I=>N(S({},I),{completed:!1}))}else a=a.map(d=>N(S({},d),{completed:!1}));let f=n.is_locked;if(e)try{console.log(`[COURSE API] Checking access for module ${n.id} (${n.title}) for user ${e}`);const{checkModuleAccess:d}=yield F(()=>m(void 0,null,function*(){const{checkModuleAccess:p}=yield import("./accessControlService.DuaMmltt.js");return{checkModuleAccess:p}}),__vite__mapDeps([0,1,2,3,4,5,6])),_=yield d(e,n.id);f=!_.hasAccess,console.log(`[COURSE API] Module ${n.id} access result: ${_.hasAccess}, locked: ${f}`)}catch(d){console.error("[COURSE API] Error checking module access, defaulting to unlocked:",d),f=n.is_locked}else f=n.module_number!==1,console.log(`[COURSE API] Non-authenticated user, module ${n.module_number} locked: ${f}`);return{id:n.id,course_id:n.course_id,slug:n.slug,module_number:n.module_number,title:n.title,is_locked:f,is_completed:E,lessons:a}})));if(e){const n=l.filter(u=>u.is_completed).length,c=l.length;yield A(o,n,c)}return l}catch(s){return console.error("Unexpected error in fetchCourseModules:",s),[]}}),H=(o,e)=>m(void 0,null,function*(){var r;if(console.log(`[COURSE API] Fetching lesson by slug: ${o}, userId: ${e}`),!o)throw new Error("No lesson slug provided");try{if(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(o)){console.log("[COURSE API] Slug appears to be a UUID, trying direct ID fetch");const{data:c,error:u}=yield i.from("lessons").select(`
          id,
          title,
          slug,
          content,
          type,
          module_id,
          modules:module_id (title),
          order,
          created_at,
          updated_at,
          image_url,
          video_url,
          external_url,
          duration_minutes,
          description
        `).eq("id",o).single();if(!u&&c)return console.log(`[COURSE API] Lesson found by ID: ${c.id}`),yield b(c,e);console.log("[COURSE API] Lesson not found by ID, trying by slug")}const{data:t,error:l}=yield i.from("lessons").select(`
        id,
        title,
        slug,
        content,
        type,
        module_id,
        modules:module_id (title),
        order,
        created_at,
        updated_at,
        image_url,
        video_url,
        external_url,
        duration_minutes,
        description
      `).eq("slug",o).single();if(l)throw console.error("[COURSE API] Error fetching lesson by slug:",l),console.error("[COURSE API] Error details:",{code:l.code,message:l.message,details:l.details,hint:l.hint}),new Error(`Failed to fetch lesson: ${l.message} (Code: ${l.code})`);if(!t)throw console.error("[COURSE API] No lesson data returned"),new Error("Lesson not found");return console.log(`[COURSE API] Lesson found by slug: ${t.id} - ${t.title}`),yield b(t,e)}catch(s){throw console.error("[COURSE API] Unexpected error in fetchLessonBySlug:",s),s.name==="AbortError"?new Error("Request was cancelled - this might be due to a network timeout or connection issue"):(r=s.message)!=null&&r.includes("fetch")?new Error("Network error - please check your internet connection and try again"):new Error(`Error fetching lesson: ${s.message}`)}});function b(o,e){return m(this,null,function*(){var s;const r=N(S({},o),{module_title:(s=o.modules)==null?void 0:s.title,completed:!1,previous_lesson_slug:null,next_lesson_slug:null});if(e)try{const{data:t}=yield i.from("user_lesson_progress").select("is_completed").eq("user_id",e).eq("lesson_id",o.id).single();r.completed=(t==null?void 0:t.is_completed)||!1;const{checkLessonAccess:l}=yield F(()=>m(this,null,function*(){const{checkLessonAccess:c}=yield import("./accessControlService.DuaMmltt.js");return{checkLessonAccess:c}}),__vite__mapDeps([0,1,2,3,4,5,6])),n=yield l(e,o.id);r.is_locked=!n.hasAccess}catch(t){console.error("[COURSE API] Error checking lesson completion:",t)}else try{const{data:t}=yield i.from("modules").select("module_number").eq("id",o.module_id).single(),{data:l}=yield i.from("lessons").select("lesson_number").eq("id",o.id).single();r.is_locked=!((t==null?void 0:t.module_number)===1&&(l==null?void 0:l.lesson_number)===1)}catch(t){console.error("[COURSE API] Error checking lesson access for non-authenticated user:",t),r.is_locked=!0}try{const{data:t}=yield i.rpc("get_lesson_navigation",{p_current_lesson_id:o.id,p_module_id:o.module_id});t&&(r.previous_lesson_slug=t.previous_slug,r.next_lesson_slug=t.next_slug)}catch(t){console.error("[COURSE API] Error fetching lesson navigation:",t)}return r})}const k=(o,e,r)=>m(void 0,null,function*(){try{if(console.log(`Updating module completion for module ${o} and user ${e} to ${r}`),!o||!e)return console.error("Missing moduleId or userId in updateModuleCompletion"),!1;const s=new Date().toISOString(),{error:t}=yield i.from(g.MODULE_PROGRESS).upsert({user_id:e,module_id:o,is_completed:r,completed_at:r?s:null,updated_at:s},{onConflict:"user_id,module_id",ignoreDuplicates:!1});if(t)return console.error("Error updating module completion:",t),!1;const{data:l,error:n}=yield i.from(g.MODULES).select("course_id").eq("id",o).single();if(n||!l)return console.error("Error getting course ID for module:",n),!1;const{data:c,error:u}=yield i.from(g.MODULE_PROGRESS).select("is_completed").eq("user_id",e).eq("course_id",l.course_id);return!u&&c&&c.every(E=>E.is_completed)&&(yield i.from(g.COURSE_ENROLLMENT).upsert({user_id:e,course_id:l.course_id,status:"completed",completed_at:s,updated_at:s},{onConflict:"user_id,course_id",ignoreDuplicates:!1})),!0}catch(s){return console.error("Unexpected error in updateModuleCompletion:",s),!1}}),A=(o,e,r)=>m(void 0,null,function*(){try{if(console.log(`Updating course progress for course ${o}: ${e}/${r} modules completed`),!o)return console.error("Missing courseId in updateCourseProgress"),!1;const{error:s}=yield i.from(g.COURSES).update({completed_modules:e,total_modules:r,updated_at:new Date().toISOString()}).eq("id",o);if(s)return console.error("Error updating course progress:",s),L.error("Failed to update course progress"),!1;if(console.log(`Successfully updated course ${o} progress: ${e}/${r}`),e>0&&e===r){console.log(`All modules completed for course ${o}, marking course as completed`);const{data:{user:t}}=yield i.auth.getUser();t&&(yield P(o,t.id))}return!0}catch(s){return console.error("Unexpected error in updateCourseProgress:",s),L.error("An unexpected error occurred while updating course progress"),!1}}),J=(o,e)=>m(void 0,null,function*(){try{const{data:r,error:s}=yield i.from(g.MODULES).update(N(S({},e),{updated_at:new Date().toISOString()})).eq("id",o).select();return s?(console.error("Error updating module:",s),L.error(`Failed to update module: ${s.message}`),null):(L.success("Module updated successfully"),r[0])}catch(r){return console.error("Unexpected error updating module:",r),L.error(`Failed to update module: ${r.message}`),null}}),Y=o=>m(void 0,null,function*(){try{console.log("[GET FIRST LESSON] Getting first lesson for module:",o);const{data:e,error:r}=yield i.from("lessons").select("slug").eq("module_id",o).order("created_at",{ascending:!0}).limit(1).single();return r||!e?(console.error("[GET FIRST LESSON] No lessons found for module:",o,r),{firstLessonSlug:null}):(console.log("[GET FIRST LESSON] First lesson found:",e.slug),{firstLessonSlug:e.slug})}catch(e){return console.error("[GET FIRST LESSON] Error getting first lesson:",e),{firstLessonSlug:null}}}),j=o=>m(void 0,null,function*(){try{console.log("[FIND NEXT LESSON UNIFIED] Starting search for lesson:",o);const{data:e,error:r}=yield i.from("lessons").select("id, slug, module_id, lesson_number, created_at").eq("slug",o).single();if(r||!e)throw console.error("[FIND NEXT LESSON UNIFIED] Failed to find current lesson:",r),new Error(`Could not find current lesson: ${(r==null?void 0:r.message)||"Unknown error"}`);console.log("[FIND NEXT LESSON UNIFIED] Current lesson found:",{id:e.id,slug:e.slug,module_id:e.module_id,lesson_number:e.lesson_number,created_at:e.created_at});let s=null;if(e.lesson_number!==null&&e.lesson_number!==void 0){console.log("[FIND NEXT LESSON UNIFIED] Using lesson_number ordering, current lesson_number:",e.lesson_number);const{data:d,error:_}=yield i.from("lessons").select("slug, lesson_number, title").eq("module_id",e.module_id).gt("lesson_number",e.lesson_number).order("lesson_number",{ascending:!0}).limit(1);_?console.error("[FIND NEXT LESSON UNIFIED] Error finding next lesson by lesson_number:",_):d&&d.length>0?(s=d[0],console.log("[FIND NEXT LESSON UNIFIED] Found next lesson by lesson_number:",{slug:s.slug,lesson_number:s.lesson_number,title:s.title})):console.log("[FIND NEXT LESSON UNIFIED] No next lesson found by lesson_number in current module")}else console.warn("[FIND NEXT LESSON UNIFIED] lesson_number is null/undefined for current lesson, this should not happen!");if(!s&&(e.lesson_number===null||e.lesson_number===void 0)){console.log("[FIND NEXT LESSON UNIFIED] Falling back to created_at ordering");const{data:d,error:_}=yield i.from("lessons").select("slug, created_at, title").eq("module_id",e.module_id).gt("created_at",e.created_at).order("created_at",{ascending:!0}).limit(1);_?console.error("[FIND NEXT LESSON UNIFIED] Error finding next lesson by created_at:",_):d&&d.length>0&&(s=d[0],console.log("[FIND NEXT LESSON UNIFIED] Found next lesson by created_at:",{slug:s.slug,created_at:s.created_at,title:s.title}))}if(s)return console.log("[FIND NEXT LESSON UNIFIED] Returning next lesson in same module:",s.slug),{nextLessonSlug:s.slug,isLastLesson:!1};console.log("[FIND NEXT LESSON UNIFIED] No more lessons in current module, looking for next module");const{data:t,error:l}=yield i.from("modules").select("course_id, module_number, title").eq("id",e.module_id).single();if(l||!t)throw console.error("[FIND NEXT LESSON UNIFIED] Failed to find current module:",l),new Error(`Could not find current module: ${(l==null?void 0:l.message)||"Unknown error"}`);console.log("[FIND NEXT LESSON UNIFIED] Current module:",{course_id:t.course_id,module_number:t.module_number,title:t.title});const{data:n,error:c}=yield i.from("modules").select("id, module_number, title").eq("course_id",t.course_id).gt("module_number",t.module_number).order("module_number",{ascending:!0}).limit(1);if(c)return console.error("[FIND NEXT LESSON UNIFIED] Error finding next module:",c),{nextLessonSlug:null,isLastLesson:!0};if(!n||n.length===0)return console.log("[FIND NEXT LESSON UNIFIED] No next module found - this is the last lesson"),{nextLessonSlug:null,isLastLesson:!0};const u=n[0];console.log("[FIND NEXT LESSON UNIFIED] Found next module:",{id:u.id,module_number:u.module_number,title:u.title}),console.log("[FIND NEXT LESSON UNIFIED] Looking for first lesson in next module");const{data:a,error:E}=yield i.from("lessons").select("slug, lesson_number, title").eq("module_id",u.id).order("lesson_number",{ascending:!0}).limit(1);if(E)return console.error("[FIND NEXT LESSON UNIFIED] Error finding first lesson in next module:",E),{nextLessonSlug:null,isLastLesson:!0};if(!a||a.length===0)return console.log("[FIND NEXT LESSON UNIFIED] No lessons found in next module"),{nextLessonSlug:null,isLastLesson:!0};const f=a[0];return console.log("[FIND NEXT LESSON UNIFIED] Found first lesson in next module:",{slug:f.slug,lesson_number:f.lesson_number,title:f.title}),{nextLessonSlug:f.slug,isLastLesson:!1}}catch(e){return console.error("[FIND NEXT LESSON UNIFIED] Unexpected error:",e),console.error("[FIND NEXT LESSON UNIFIED] Error stack:",e.stack),{nextLessonSlug:null,isLastLesson:!0}}});export{B as a,k as b,j as c,H as d,W as e,V as f,Y as g,J as u};
