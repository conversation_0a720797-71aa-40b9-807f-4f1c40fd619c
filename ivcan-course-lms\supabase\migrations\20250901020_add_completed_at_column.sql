-- Add completed_at column to user_course_enrollment table
ALTER TABLE public.user_course_enrollment
ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP WITH TIME ZONE;

-- Update existing completed enrollments that don't have a completed_at date
UPDATE public.user_course_enrollment
SET completed_at = updated_at
WHERE status = 'completed' AND completed_at IS NULL;

-- Add index for faster queries
CREATE INDEX IF NOT EXISTS idx_user_course_enrollment_completed_at
ON public.user_course_enrollment(completed_at);

-- Add trigger to automatically set completed_at when status changes to completed
CREATE OR REPLACE FUNCTION public.set_completed_at()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.status = 'completed' AND NEW.completed_at IS NULL THEN
    NEW.completed_at = CURRENT_TIMESTAMP;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS tr_set_completed_at ON public.user_course_enrollment;
CREATE TRIGGER tr_set_completed_at
BEFORE INSERT OR UPDATE ON public.user_course_enrollment
FOR EACH ROW
EXECUTE FUNCTION public.set_completed_at();

-- Add function to complete a course
CREATE OR REPLACE FUNCTION public.complete_course(p_user_id UUID, p_course_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  v_total_modules INTEGER;
  v_completed_modules INTEGER;
BEGIN
  -- Get total number of modules for the course
  SELECT COUNT(*) INTO v_total_modules
  FROM public.modules
  WHERE course_id = p_course_id;

  -- Get number of completed modules for the user
  SELECT COUNT(*) INTO v_completed_modules
  FROM public.user_module_progress ump
  JOIN public.modules m ON m.id = ump.module_id
  WHERE m.course_id = p_course_id
  AND ump.user_id = p_user_id
  AND ump.is_completed = true;

  -- Only mark as completed if all modules are completed
  IF v_completed_modules = v_total_modules THEN
    -- Update enrollment status
    UPDATE public.user_course_enrollment
    SET status = 'completed',
        completed_at = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP
    WHERE user_id = p_user_id
    AND course_id = p_course_id;

    -- Update course progress
    UPDATE public.user_course_progress
    SET hours_spent = COALESCE(hours_spent, 0),
        last_accessed_at = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP
    WHERE user_id = p_user_id
    AND course_id = p_course_id;

    RETURN true;
  END IF;

  RETURN false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 