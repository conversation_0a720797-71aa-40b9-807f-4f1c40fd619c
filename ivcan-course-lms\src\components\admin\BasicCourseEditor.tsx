import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, X, Image as ImageIcon, Users } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { createCourse, updateCourse } from '@/services/course/basicCourseService';
import { getCourseEnrollmentCount } from '@/services/course/enrollmentApi';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface CourseFormValues {
  title: string;
  slug: string;
  description: string;
  instructor: string;
}

interface BasicCourseEditorProps {
  courseId?: string;
  onClose: () => void;
  initialData?: {
    title: string;
    slug: string;
    description: string;
    instructor: string;
    image_url?: string;
  };
}

export function BasicCourseEditor({ courseId, onClose, initialData }: BasicCourseEditorProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const isNewCourse = !courseId;

  // State for image handling
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  // Fetch enrollment count if this is an existing course
  const { data: enrollmentCount = 0, isLoading: isLoadingEnrollment } = useQuery({
    queryKey: ['course-enrollment-count', courseId],
    queryFn: () => getCourseEnrollmentCount(courseId || ''),
    enabled: !!courseId && courseId !== 'new',
  });

  // Form setup
  const form = useForm<CourseFormValues>({
    defaultValues: {
      title: initialData?.title || '',
      slug: initialData?.slug || '',
      description: initialData?.description || '',
      instructor: initialData?.instructor || '',
    }
  });

  // Set image preview from initial data
  useEffect(() => {
    if (initialData?.image_url) {
      setImagePreview(initialData.image_url);
    }
  }, [initialData]);

  // Handle form submission
  const onSubmit = async (values: CourseFormValues) => {
    try {
      setIsSubmitting(true);

      // Generate a slug if not provided
      const finalSlug = values.slug || values.title.toLowerCase().replace(/\\s+/g, '-');

      if (isNewCourse) {
        // Create a new course
        await createCourse({
          title: values.title,
          slug: finalSlug,
          description: values.description,
          instructor: values.instructor,
          image: imageFile
        });

        toast({
          title: "Course created",
          description: "The course has been created successfully.",
        });

        // Show success message
        setShowSuccess(true);
      } else {
        // Update existing course
        await updateCourse(courseId, {
          title: values.title,
          slug: finalSlug,
          description: values.description,
          instructor: values.instructor,
          image: imageFile
        });

        toast({
          title: "Course updated",
          description: "The course has been updated successfully.",
        });

        // Show success message
        setShowSuccess(true);
      }

      // Refresh queries
      queryClient.invalidateQueries({ queryKey: ['admin-courses'] });
      queryClient.invalidateQueries({ queryKey: ['courses'] });

      if (courseId) {
        queryClient.invalidateQueries({ queryKey: ['course', courseId] });
      }

      // Wait a moment to show the success message
      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (error: any) {
      console.error('Error saving course:', error);

      toast({
        title: "Error",
        description: error.message || "Failed to save course. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle image selection
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: "Image too large",
          description: "Please select an image smaller than 5MB.",
          variant: "destructive",
        });
        return;
      }

      setImageFile(file);
      const previewUrl = URL.createObjectURL(file);
      setImagePreview(previewUrl);

      toast({
        title: "Image selected",
        description: "The image has been selected and will be uploaded when you save the course.",
      });
    }
  };

  // Handle image removal
  const handleRemoveImage = () => {
    setImageFile(null);
    setImagePreview(null);
  };

  // Handle slug generation
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const title = e.target.value;
    // Only auto-generate slug if it's empty or if we're creating a new course
    if (isNewCourse || !form.getValues('slug')) {
      const slug = title.toLowerCase()
        .replace(/[^\\w\\s-]/g, '')
        .replace(/\\s+/g, '-');
      form.setValue('slug', slug);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-3">
          <h2 className="text-xl font-semibold">
            {isNewCourse ? "Create New Course" : "Edit Course"}
          </h2>
          {!isNewCourse && !isLoadingEnrollment && (
            <Badge variant="outline" className="flex items-center gap-1 bg-blue-50 text-blue-700">
              <Users className="h-3 w-3" />
              {enrollmentCount} {enrollmentCount === 1 ? 'student' : 'students'} enrolled
            </Badge>
          )}
        </div>
      </div>

      {showSuccess && (
        <Alert className="bg-red-50 border-red-200">
          <AlertTitle className="text-red-800">Success!</AlertTitle>
          <AlertDescription className="text-red-700">
            {isNewCourse ? "Course created successfully." : "Course updated successfully."}
            {imageFile && " The image has been uploaded and will appear on the course card."}
          </AlertDescription>
        </Alert>
      )}

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Course Image */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <FormLabel htmlFor="image">Course Image</FormLabel>
              <p className="text-xs text-muted-foreground">Max size: 5MB</p>
            </div>
            <div className="flex flex-col space-y-4">
              {imagePreview ? (
                <div className="relative w-full max-w-md aspect-video rounded-lg overflow-hidden border border-border">
                  <img
                    src={imagePreview}
                    alt="Course preview"
                    className="w-full h-full object-cover"
                  />
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    className="absolute top-2 right-2 opacity-90"
                    onClick={handleRemoveImage}
                  >
                    <X className="h-4 w-4 mr-1" />
                    Remove
                  </Button>
                </div>
              ) : (
                <div className="w-full max-w-md aspect-video rounded-lg bg-muted flex flex-col items-center justify-center border border-dashed border-border p-4">
                  <ImageIcon className="h-10 w-10 text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground">No image selected</p>
                  <p className="text-xs text-muted-foreground mt-1">Recommended size: 600x400px</p>
                </div>
              )}
              <div className="flex items-center gap-2">
                <Input
                  id="image"
                  type="file"
                  accept="image/*"
                  onChange={handleImageChange}
                  className="max-w-md"
                />
              </div>
            </div>
          </div>

          <FormField
            control={form.control}
            name="title"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Course Title</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter course title"
                    {...field}
                    onChange={(e) => {
                      field.onChange(e);
                      handleTitleChange(e);
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="slug"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Course Slug</FormLabel>
                <FormControl>
                  <Input
                    placeholder="course-slug"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter course description"
                    className="min-h-[100px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="instructor"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Instructor</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter instructor name"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {!isNewCourse && (
            <div className="pt-4 pb-2">
              <h3 className="text-sm font-medium mb-2 flex items-center gap-2">
                <Users className="h-4 w-4 text-blue-500" />
                Student Enrollment
              </h3>
              <div className="bg-gray-50 p-3 rounded-md border text-sm">
                {isLoadingEnrollment ? (
                  <div className="flex items-center justify-center py-2">
                    <Loader2 className="h-4 w-4 animate-spin text-gray-400 mr-2" />
                    Loading enrollment data...
                  </div>
                ) : enrollmentCount > 0 ? (
                  <div>
                    <p className="text-gray-700">
                      This course has <span className="font-medium">{enrollmentCount}</span> {enrollmentCount === 1 ? 'student' : 'students'} enrolled.
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      Changes to this course will affect all enrolled students.
                    </p>
                  </div>
                ) : (
                  <p className="text-gray-500">No students are currently enrolled in this course.</p>
                )}
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                isNewCourse ? "Create Course" : "Update Course"
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
