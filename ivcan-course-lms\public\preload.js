// Simplified preload script that won't block rendering
(function() {
  // Use requestIdleCallback to run preloading during browser idle time
  const runWhenIdle = (callback) => {
    if ('requestIdleCallback' in window) {
      window.requestIdleCallback(callback, { timeout: 2000 });
    } else {
      // Fallback for browsers without requestIdleCallback
      setTimeout(callback, 1000);
    }
  };

  // Run preloading after a delay to ensure it doesn't block initial rendering
  setTimeout(() => {
    runWhenIdle(() => {
      try {
        // Check if the browser supports the preload API
        if (!('preload' in document.createElement('link'))) {
          return;
        }

        // Function to preload an asset with low priority
        function preloadAsset(url, type) {
          try {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = url;
            link.as = type;
            // Add low priority hint
            link.setAttribute('importance', 'low');
            document.head.appendChild(link);
          } catch (e) {
            // Silently fail - preloading is just an optimization
          }
        }

        // Only preload the most critical assets for the current page
        const path = window.location.pathname;

        // Minimal preloading based on current path
        if (path === '/' || path === '/login') {
          // Home or login page - preload dashboard
          preloadAsset('/assets/Dashboard.js', 'script');
        } else if (path.includes('/dashboard')) {
          // Dashboard - preload course page
          preloadAsset('/assets/ModuleContent.js', 'script');
        }
      } catch (error) {
        // Silently fail - preloading is just an optimization
      }
    });
  }, 2000); // Delay preloading by 2 seconds
})();
