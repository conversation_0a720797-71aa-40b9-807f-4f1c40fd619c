
import { useQuery } from '@tanstack/react-query';
import { fetchCourseBySlug, fetchCourseModules } from './courseApi';
import { fetchLessonBySlug } from './courseApi';

// React Query hooks
export const useCourse = (slug: string) => {
  return useQuery({
    queryKey: ['course', slug],
    queryFn: () => fetchCourseBySlug(slug),
    enabled: !!slug,
  });
};

export const useCourseModules = (courseId: string, userId?: string) => {
  return useQuery({
    queryKey: ['courseModules', courseId, userId],
    queryFn: () => fetchCourseModules(courseId, userId),
    enabled: !!courseId,
  });
};

export const useLesson = (slug: string, userId?: string) => {
  return useQuery({
    queryKey: ['lesson', slug, userId],
    queryFn: () => fetchLessonBySlug(slug, userId),
    enabled: !!slug,
  });
};
