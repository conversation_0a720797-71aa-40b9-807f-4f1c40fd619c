import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';
import { fetchLessonBySlug } from '@/services/course/courseApi';

const DebugLesson = () => {
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const addResult = (test: string, result: any, error?: any) => {
    setResults(prev => [...prev, {
      test,
      result,
      error,
      timestamp: new Date().toISOString()
    }]);
  };

  const testBasicConnection = async () => {
    setLoading(true);
    try {
      console.log('Testing basic Supabase connection...');
      const { data, error } = await supabase
        .from('lessons')
        .select('count')
        .limit(1);
      
      addResult('Basic Connection', data, error);
    } catch (err) {
      addResult('Basic Connection', null, err);
    }
    setLoading(false);
  };

  const testLessonsTable = async () => {
    setLoading(true);
    try {
      console.log('Testing lessons table access...');
      const { data, error } = await supabase
        .from('lessons')
        .select('id, title, slug')
        .limit(10);
      
      addResult('Lessons Table', data, error);
    } catch (err) {
      addResult('Lessons Table', null, err);
    }
    setLoading(false);
  };

  const testSpecificLesson = async () => {
    setLoading(true);
    try {
      console.log('Testing specific lesson slug...');
      const { data, error } = await supabase
        .from('lessons')
        .select('*')
        .eq('slug', 'introduction-to-intravenous-cannulation')
        .single();
      
      addResult('Specific Lesson (Direct)', data, error);
    } catch (err) {
      addResult('Specific Lesson (Direct)', null, err);
    }
    setLoading(false);
  };

  const testFetchLessonBySlug = async () => {
    setLoading(true);
    try {
      console.log('Testing fetchLessonBySlug function...');
      const result = await fetchLessonBySlug('introduction-to-intravenous-cannulation');
      addResult('fetchLessonBySlug', result, null);
    } catch (err) {
      addResult('fetchLessonBySlug', null, err);
    }
    setLoading(false);
  };

  const clearResults = () => {
    setResults([]);
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Debug Lesson Loading</h1>
      
      <div className="space-y-4 mb-6">
        <Button onClick={testBasicConnection} disabled={loading}>
          Test Basic Connection
        </Button>
        <Button onClick={testLessonsTable} disabled={loading}>
          Test Lessons Table
        </Button>
        <Button onClick={testSpecificLesson} disabled={loading}>
          Test Specific Lesson (Direct)
        </Button>
        <Button onClick={testFetchLessonBySlug} disabled={loading}>
          Test fetchLessonBySlug
        </Button>
        <Button onClick={clearResults} variant="outline">
          Clear Results
        </Button>
      </div>

      {loading && <div className="text-blue-600">Running test...</div>}

      <div className="space-y-4">
        {results.map((result, index) => (
          <div key={index} className="border p-4 rounded-lg">
            <h3 className="font-semibold text-lg">{result.test}</h3>
            <p className="text-sm text-gray-500 mb-2">{result.timestamp}</p>
            
            {result.error ? (
              <div className="bg-red-50 p-3 rounded">
                <h4 className="font-medium text-red-800">Error:</h4>
                <pre className="text-sm text-red-700 mt-1 overflow-auto">
                  {JSON.stringify(result.error, null, 2)}
                </pre>
              </div>
            ) : (
              <div className="bg-green-50 p-3 rounded">
                <h4 className="font-medium text-green-800">Success:</h4>
                <pre className="text-sm text-green-700 mt-1 overflow-auto max-h-40">
                  {JSON.stringify(result.result, null, 2)}
                </pre>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default DebugLesson;
