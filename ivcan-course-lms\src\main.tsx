import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import './styles/unified-lesson-content.css' // Unified lesson content styles - single source of truth
import './styles/red-theme.css'
import './styles/ios-theme.css'
import './styles/red-sidebar.css'
import './styles/modern-ui.css'
import './styles/mobile-utils.css'
import './styles/dark-mode-fix.css'
import { toast } from 'sonner'
import { startPeriodicHealthChecks } from './lib/health-check'
import { CleanupTasks } from './utils/cleanup-tasks'
import { checkAndNotifyMissingImages } from './utils/checkStaticImages'

// Import debug functions for development
if (import.meta.env.DEV) {
  import('./debug/test-connection');
}

// Performance optimization: Start measuring performance
const startTime = performance.now();

// Start health checks for Supabase connectivity
startPeriodicHealthChecks();

// Initialize cleanup tasks in production only
if (import.meta.env.PROD) {
  // Run cleanup tasks once per 24 hours
  CleanupTasks.startScheduledCleanup();
  console.log('Image cleanup tasks scheduled');
}

// Check if all static module images exist
window.addEventListener('load', () => {
  setTimeout(() => {
    checkAndNotifyMissingImages(toast);
  }, 2000); // Wait 2 seconds after load to check images
});

// Error handler to catch and log initialization errors
window.addEventListener('error', (event) => {
  console.error('Runtime error caught:', event.error);

  // Remove the loading spinner if an error occurs during initialization
  const loader = document.getElementById('initial-loader');
  if (loader && loader.parentNode) {
    loader.parentNode.removeChild(loader);
    console.log('Loader removed due to initialization error');
  }

  // Show a minimal error message
  const rootElement = document.getElementById('root');
  if (rootElement) {
    rootElement.innerHTML = `
      <div style={{
        fontFamily: "'Poppins', system-ui, sans-serif",
        padding: '2rem',
        textAlign: 'center'
      }}>
        <h2>Application Error</h2>
        <p>The application failed to initialize. Please try refreshing the page.</p>
        <button onclick="window.location.reload()" style="padding: 0.5rem 1rem; background: #E63946; color: white; border: none; border-radius: 4px; cursor: pointer;">
          Refresh Page
        </button>
      </div>
    `;
  }
});

// Use a more optimized approach to rendering
const rootElement = document.getElementById("root");

// Make sure the root element exists
if (!rootElement) {
  console.error("Root element not found! Make sure there is a div with id 'root' in your HTML.");
  // Try to create it
  const newRoot = document.createElement('div');
  newRoot.id = 'root';
  document.body.appendChild(newRoot);
  console.log('Created missing root element');
}

// Try/catch to handle any errors during render
try {
  // Create the root
  const root = createRoot(rootElement || document.body);

  // Function to render the app with priority handling
  const renderApp = () => {
    // Render immediately for better performance
    root.render(<App />);

    // Mark when the app was rendered
    if (import.meta.env.DEV) {
      console.log(`App rendered in ${(performance.now() - startTime).toFixed(2)}ms`);
    }
  };

  // Start rendering
  renderApp();
} catch (error) {
  console.error('Failed to initialize React:', error);

  // Add a fallback UI in case of render failure
  if (rootElement) {
    rootElement.innerHTML = `
      <div style={{
        fontFamily: "'Poppins', system-ui, sans-serif",
        padding: '2rem',
        textAlign: 'center'
      }}>
        <h2>Application Error</h2>
        <p>The application failed to initialize. Please try refreshing the page.</p>
        <button onclick="window.location.reload()" style="padding: 0.5rem 1rem; background: #E63946; color: white; border: none; border-radius: 4px; cursor: pointer;">
          Refresh Page
        </button>
      </div>
    `;
  }

  // Remove loader
  const loader = document.getElementById('initial-loader');
  if (loader && loader.parentNode) {
    loader.parentNode.removeChild(loader);
  }
}

// Register service worker for offline capabilities
// Temporarily disable service worker registration until we fix the MIME type issue
/*
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/service-worker.js')
      .then(registration => {
        console.log('Service Worker registered with scope:', registration.scope);

        // Check for updates to the Service Worker
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;
          if (newWorker) {
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                // New content is available, show refresh toast
                toast.info(
                  'New version available',
                  {
                    description: 'Refresh to update the application',
                    action: {
                      label: 'Refresh',
                      onClick: () => window.location.reload()
                    },
                    duration: 10000 // 10 seconds
                  }
                );
              }
            });
          }
        });
      })
      .catch(error => {
        console.error('Service Worker registration failed:', error);
      });

    // Handle service worker updates
    let refreshing = false;
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      if (!refreshing) {
        refreshing = true;
        window.location.reload();
      }
    });
  });
}
*/

// Set up network status monitoring
window.addEventListener('online', () => {
  toast.success('You are back online');
});

window.addEventListener('offline', () => {
  toast.error('You are offline. Some features may be limited.');
});

// Report web vitals for performance monitoring
if (import.meta.env.MODE === 'production') {
  // This is a lightweight way to report performance metrics
  // without adding a heavy dependency
  const reportWebVitals = () => {
    if ('performance' in window && 'getEntriesByType' in performance) {
      const navEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navEntry) {
        // Log key metrics
        console.log(`Time to Interactive: ${navEntry.domInteractive}ms`);
        console.log(`DOM Content Loaded: ${navEntry.domContentLoadedEventEnd}ms`);
        console.log(`Load Complete: ${navEntry.loadEventEnd}ms`);

        // Report to analytics if available
        const w = window as any;
        if ('gtag' in w && typeof w.gtag === 'function') {
          w.gtag('event', 'performance', {
            'time_to_interactive': navEntry.domInteractive,
            'dom_content_loaded': navEntry.domContentLoadedEventEnd,
            'load_complete': navEntry.loadEventEnd
          });
        }
      }
    }
  };

  // Report after the page has fully loaded
  window.addEventListener('load', () => setTimeout(reportWebVitals, 100));
}
