// This script preloads critical assets for better performance
(() => {
  try {
    // Only run in production
    if (window.location.hostname === 'localhost') return;

    // Function to preload JavaScript
    const preloadScript = (src) => {
      const link = document.createElement('link');
      link.rel = 'modulepreload';
      link.href = src;
      document.head.appendChild(link);
    };

    // Function to preload CSS
    const preloadStyle = (href) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'style';
      link.href = href;
      document.head.appendChild(link);
    };

    // Function to preload images
    const preloadImage = (src) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = src;
      document.head.appendChild(link);
    };

    // Preload main chunks based on route
    const path = window.location.pathname;
    
    // Preload common assets for all routes
    if (path === '/') {
      // Home page
      preloadScript('/assets/index.js');
      preloadStyle('/assets/index.css');
    } else if (path.includes('/dashboard')) {
      // Dashboard page
      preloadScript('/assets/Dashboard.js');
    } else if (path.includes('/profile')) {
      // Profile page
      preloadScript('/assets/Profile.js');
    }
    
    // Preload vendor chunks for all routes
    preloadScript('/assets/vendor.js');
    
    // Preload common images
    preloadImage('/placeholder-avatar.svg');
  } catch (error) {
    console.warn('Error in preload-critical script:', error);
  }
})();
