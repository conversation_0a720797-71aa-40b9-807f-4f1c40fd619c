var Ne=Object.defineProperty,je=Object.defineProperties;var be=Object.getOwnPropertyDescriptors;var Q=Object.getOwnPropertySymbols;var ae=Object.prototype.hasOwnProperty,ie=Object.prototype.propertyIsEnumerable;var le=Math.pow,ne=(t,s,r)=>s in t?Ne(t,s,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[s]=r,$=(t,s)=>{for(var r in s||(s={}))ae.call(s,r)&&ne(t,r,s[r]);if(Q)for(var r of Q(s))ie.call(s,r)&&ne(t,r,s[r]);return t},U=(t,s)=>je(t,be(s));var H=(t,s)=>{var r={};for(var o in t)ae.call(t,o)&&s.indexOf(o)<0&&(r[o]=t[o]);if(t!=null&&Q)for(var o of Q(t))s.indexOf(o)<0&&ie.call(t,o)&&(r[o]=t[o]);return r};var w=(t,s,r)=>new Promise((o,c)=>{var p=i=>{try{f(r.next(i))}catch(N){c(N)}},a=i=>{try{f(r.throw(i))}catch(N){c(N)}},f=i=>i.done?o(i.value):Promise.resolve(i.value).then(p,a);f((r=r.apply(t,s)).next())});import{r as u,u as de,j as e,E as ue,$ as ee,aa as Ee,aQ as me,aR as pe,aS as ve,aT as Se,aw as se,ax as fe,aU as Ce,aV as ke,aW as Te,aH as Le,az as _e,aX as Re,F as Ae}from"./vendor-react.BcAa1DKr.js";import{L as W}from"./Layout.DRjmVYQG.js";import{s as j,u as re,B as v,c as ge,g as te}from"./index.BLDhDn0D.js";import{b as Oe,c as he,d as qe}from"./courseApi.BQX5-7u-.js";import{i as Pe}from"./utils.Qa9QlCj_.js";/* empty css                                    */import{L as Fe}from"./loading-skeleton.DxUGnZ26.js";import{P as I}from"./floating-sidebar-container.BxlLgzat.js";import{L as Me}from"./LessonContent.B2xCUk6H.js";import{a4 as xe}from"./vendor.DQpuTRuB.js";import{C as A,a as D,b as z,d as O,c as J,e as Z}from"./card.B9V6b2DK.js";import{L as Qe}from"./label.D4YlnUPk.js";import{a as ce}from"./achievementService.Vkx_BHml.js";import{A as $e,b as Ue,c as De,d as ze,e as Ge,f as Ke,g as Ve,h as Ye}from"./alert-dialog.BK1A3Gb_.js";import{markLessonAsCompleted as Xe}from"./completionService.BUb78ZaE.js";import{e as Be}from"./enrollmentApi.CstnsQi_.js";import"./vendor-supabase.sufZ44-y.js";import"./skeleton.C0Lb8Xms.js";import"./markdown-preview.Bw0U-NJA.js";import"./content-converter.L-GziWIP.js";const He=(t,s)=>w(void 0,null,function*(){try{console.log(`Starting completeLessonProgress for lesson ${t} and user ${s}`);const{data:r,error:o}=yield j.from("lessons").select("module_id").eq("id",t).single();if(o||!r)return console.error("Error getting lesson:",o),!1;const{data:c,error:p}=yield j.from("user_lesson_progress").select("id, is_completed").eq("user_id",s).eq("lesson_id",t).maybeSingle(),a=new Date().toISOString(),f={};if(c){console.log("Found existing progress record"),f.is_completed=!0,f.completed_at=a;const{error:i}=yield j.from("user_lesson_progress").update(U($({},f),{updated_at:a})).eq("id",c.id);if(i)return console.error("Error updating lesson progress:",i),!1}else{console.log("Creating new progress record");const{error:i}=yield j.from("user_lesson_progress").insert({user_id:s,lesson_id:t,is_completed:!0,completed_at:a,progress_percent:100,created_at:a,updated_at:a});if(i)return console.error("Error creating lesson progress:",i),!1}return yield We(r.module_id,s),!0}catch(r){return console.error("Unexpected error in completeLessonProgress:",r),!1}}),We=(t,s)=>w(void 0,null,function*(){try{if(yield Ie(t,s)){console.log(`Module ${t} is now completed by user ${s}`),yield Oe(t,s,!0);try{const{data:o}=yield j.from("modules").select("module_number, course_id").eq("id",t).single();if(o){const c=(o.module_number||1)-1;yield ce(s,t,c),console.log(`Module ${t} completed by user ${s}. Badge awarded.`)}}catch(o){console.error("Error getting module data for badge award:",o),yield ce(s,t,0)}}}catch(r){console.error("Error in checkAndUpdateModuleCompletion:",r)}}),Ie=(t,s)=>w(void 0,null,function*(){try{console.log(`Checking module completion for module ${t} and user ${s}`);const{data:r,error:o}=yield j.from("lessons").select("id").eq("module_id",t);if(o||!r)return console.error("Error fetching lessons:",o),!1;if(r.length===0)return console.log("No lessons found for module"),!1;const{data:c,error:p}=yield j.from("user_lesson_progress").select("lesson_id").eq("user_id",s).eq("is_completed",!0).in("lesson_id",r.map(f=>f.id));if(p)return console.error("Error fetching completed lessons:",p),!1;const a=c&&c.length===r.length;return console.log(`Module has ${(c==null?void 0:c.length)||0} completed lessons out of ${r.length}`),a}catch(r){return console.error("Error in checkModuleCompletion:",r),!1}}),Je=({isCompleted:t,onComplete:s,onNext:r,completionMutation:o,hasNextLesson:c,progress:p,courseId:a,currentLessonSlug:f})=>{const[i,N]=u.useState(!1),[x,g]=u.useState(!1),{user:y}=re(),b=de(),R=()=>{i||o.isPending||(N(!0),s(),setTimeout(()=>N(!1),2e3))},k=()=>w(void 0,null,function*(){if(!(x||o.isPending||!y)){g(!0);try{console.log("[LESSON FOOTER] Starting next lesson navigation from:",f),t||(console.log("[LESSON FOOTER] Completing lesson before navigation"),yield o.mutateAsync()),console.log("[LESSON FOOTER] Finding next lesson using unified navigation");const{nextLessonSlug:h,isLastLesson:S}=yield he(f);console.log("[LESSON FOOTER] Navigation result:",{nextLessonSlug:h,isLastLesson:S}),h?(console.log("[LESSON FOOTER] Navigating to next lesson:",h),b(`/course/${a}/lesson/${h}`)):S?(console.log("[LESSON FOOTER] Last lesson reached, going to modules page"),b(`/course/${a}/modules`)):(console.log("[LESSON FOOTER] Using fallback onNext behavior"),r())}catch(h){console.error("[LESSON FOOTER] Error during navigation:",h),console.error("[LESSON FOOTER] Error details:",{currentLessonSlug:f,courseId:a,isCompleted:t,error:h instanceof Error?h.message:String(h)}),console.log("[LESSON FOOTER] Using emergency fallback - going to modules page"),b(`/course/${a}/modules`)}finally{g(!1)}}}),C=i||o.isPending;return e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-center gap-4 pt-6 border-t",children:[e.jsx("div",{className:"flex items-center gap-2",children:t?e.jsxs("div",{className:"flex items-center gap-2 text-green-600 bg-green-50 px-4 py-2 rounded-md dark:bg-green-900/20 dark:text-green-400",children:[e.jsx(ue,{className:"h-5 w-5"}),e.jsx("span",{children:"Completed"})]}):e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-24 bg-muted rounded-full h-2 overflow-hidden",children:e.jsx(xe.div,{className:"h-full bg-primary",style:{width:`${p}%`},initial:{width:0},animate:{width:`${p}%`},transition:{duration:.5}})}),e.jsxs("span",{className:"text-sm text-muted-foreground",children:[p,"%"]}),e.jsx(v,{onClick:R,disabled:C,variant:"outline",className:"ml-2 min-w-[140px]",children:C?e.jsxs("span",{className:"flex items-center gap-2",children:[e.jsx(ee,{className:"h-4 w-4 animate-spin"}),"Marking..."]}):"Mark as Complete"})]})}),e.jsx(v,{onClick:k,disabled:x||o.isPending,variant:"default",size:"lg",className:"flex items-center gap-2",children:x?e.jsxs(e.Fragment,{children:[e.jsx(ee,{className:"h-4 w-4 animate-spin"}),t?"Loading...":"Completing..."]}):e.jsxs(e.Fragment,{children:["Next",e.jsx(Ee,{className:"h-4 w-4"})]})})]})},ye=u.forwardRef((o,r)=>{var c=o,{className:t}=c,s=H(c,["className"]);return e.jsx(me,U($({className:ge("grid gap-2",t)},s),{ref:r}))});ye.displayName=me.displayName;const we=u.forwardRef((o,r)=>{var c=o,{className:t}=c,s=H(c,["className"]);return e.jsx(pe,U($({ref:r,className:ge("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t)},s),{children:e.jsx(ve,{className:"flex items-center justify-center",children:e.jsx(Se,{className:"h-2.5 w-2.5 fill-current text-current"})})}))});we.displayName=pe.displayName;const Ze=({lessonId:t,content:s,onComplete:r})=>{const{user:o}=re(),{toast:c}=te(),p=se(),[a,f]=u.useState(null),[i,N]=u.useState(0),[x,g]=u.useState([]),[y,b]=u.useState(!1),[R,k]=u.useState(!1),[C,h]=u.useState(0),[S,l]=u.useState(!1),[q,T]=u.useState(null),[E,P]=u.useState(!1);u.useEffect(()=>{try{const d=JSON.parse(s),n=d.quizType==="questionnaire";P(n),f(d),d.questions&&Array.isArray(d.questions)?g(new Array(d.questions.length).fill(-1)):T("Invalid quiz data: questions array is missing or not an array")}catch(d){console.error("Failed to parse quiz content:",d),T("Failed to parse quiz content"),f(null)}},[s]);const G=fe({mutationFn:()=>w(void 0,null,function*(){if(!o)throw new Error("User not authenticated");return yield He(t,o.id)}),onSuccess:()=>{p.invalidateQueries({queryKey:["lesson",t]});const d=window.location.pathname.split("/"),n=d.indexOf("course")+1,m=n>0&&n<d.length?d[n]:null;m&&p.invalidateQueries({queryKey:["courseModules",m]}),p.invalidateQueries({queryKey:["dashboard-courses",o==null?void 0:o.id]}),p.invalidateQueries({queryKey:["courses"]}),c({title:E?"Questionnaire completed!":"Quiz completed!",description:E?"Thank you for your feedback.":`You scored ${C} out of ${a==null?void 0:a.questions.length} questions.`})},onError:d=>{console.error("Error saving quiz progress:",d),c({title:"Error",description:"Failed to save your progress.",variant:"destructive"})}}),K=d=>{const n=[...x];n[i]=d,g(n)},V=()=>{if(x[i]===-1){c({title:"Please select an answer",description:"You need to select an answer before proceeding.",variant:"destructive"});return}if(i<((a==null?void 0:a.questions.length)||0)-1)N(i+1);else{if(E)h((a==null?void 0:a.questions.length)||0);else{let d=0;a==null||a.questions.forEach((n,m)=>{x[m]===n.correctAnswer&&d++}),h(d)}k(!0),l(!0),o&&G.mutate()}},Y=()=>{i>0&&N(i-1)};return q?e.jsxs(A,{className:"my-6",children:[e.jsx(D,{children:e.jsx(z,{className:"text-lg",children:"Error Loading Content"})}),e.jsx(O,{className:"flex items-center justify-center py-6",children:e.jsxs("div",{className:"text-center",children:[e.jsx(Ce,{className:"h-12 w-12 text-red-500 mx-auto mb-3"}),e.jsx("p",{className:"text-muted-foreground",children:q}),e.jsx(v,{variant:"outline",className:"mt-4",onClick:()=>window.location.reload(),children:"Refresh Page"})]})})]}):!a||!a.questions||a.questions.length===0?e.jsx(A,{className:"my-6",children:e.jsx(O,{className:"flex items-center justify-center py-6",children:e.jsxs("div",{className:"text-center",children:[e.jsx(ee,{className:"h-12 w-12 text-muted-foreground mx-auto mb-3 animate-spin"}),e.jsx("p",{className:"text-muted-foreground",children:"Loading content..."})]})})}):(a.questions[i],(i+1)/a.questions.length*100,S?E?e.jsxs(A,{className:"my-6",children:[e.jsxs(D,{children:[e.jsx(z,{children:"Questionnaire Complete"}),e.jsx(J,{children:"Thank you for completing this questionnaire"})]}),e.jsx(O,{className:"space-y-6",children:e.jsxs("div",{className:"text-center py-6",children:[e.jsx(ke,{className:"h-16 w-16 text-red-500 mx-auto mb-4"}),e.jsx("p",{className:"text-lg font-medium",children:"Your responses have been recorded"}),e.jsx("p",{className:"text-muted-foreground mt-2",children:"Thank you for providing your feedback."})]})}),e.jsx(Z,{className:"flex justify-center",children:e.jsx(v,{variant:"default",onClick:r,children:"Continue"})})]}):e.jsxs(A,{className:"my-6",children:[e.jsxs(D,{children:[e.jsx(z,{children:"Quiz Results"}),e.jsxs(J,{children:["You scored ",C," out of ",a.questions.length," questions"]})]}),e.jsx(O,{className:"space-y-6",children:a.questions.map((d,n)=>e.jsx("div",{className:"border rounded-lg p-4",children:e.jsxs("div",{className:"flex items-start mb-3",children:[e.jsx("div",{className:"flex-shrink-0 mr-3",children:x[n]===d.correctAnswer?e.jsx(ue,{className:"h-5 w-5 text-red-500"}):e.jsx(Te,{className:"h-5 w-5 text-red-500"})}),e.jsxs("div",{children:[e.jsxs("p",{className:"font-medium mb-2",children:["Question ",n+1,": ",d.question]}),e.jsx("div",{className:"space-y-2",children:d.options.map((m,L)=>e.jsx("div",{className:`px-3 py-2 text-sm rounded ${L===d.correctAnswer?"bg-red-100 border border-red-200":x[n]===L?"bg-red-100/50 border border-red-200":"bg-gray-50 border border-gray-200"}`,children:typeof m=="string"?m:m.text},L))})]})]})},n))}),e.jsxs(Z,{className:"flex justify-between",children:[e.jsx(v,{variant:"outline",onClick:()=>{N(0),g(new Array(a.questions.length).fill(-1)),k(!1),l(!1)},children:"Retry Quiz"}),e.jsx(v,{variant:"default",onClick:r,children:"Complete"})]})]}):e.jsxs(A,{className:"mt-6 mb-24",children:[" ",e.jsxs(D,{className:"space-y-1",children:[e.jsxs(z,{className:"text-lg sm:text-xl",children:["Question ",i+1," of ",a.questions.length]}),e.jsx(J,{className:"text-sm sm:text-base",children:a.questions[i].question})]}),e.jsx(O,{children:e.jsx("div",{className:"space-y-3",children:e.jsx(ye,{value:x[i]||"",onValueChange:d=>K(d),className:"space-y-2",children:a.questions[i].options.map((d,n)=>e.jsxs("div",{className:"flex items-center",children:[e.jsx(we,{value:d,id:`option-${n}`,className:"w-5 h-5 border-2"}),e.jsx(Qe,{htmlFor:`option-${n}`,className:"flex-1 pl-3 py-3 text-sm sm:text-base",children:d})]},n))})})}),e.jsx(Z,{className:"fixed bottom-0 left-0 right-0 p-4 bg-white/80 dark:bg-black/80 backdrop-blur-md border-t border-gray-100 dark:border-gray-800 z-10",children:e.jsxs("div",{className:"max-w-[650px] mx-auto w-full flex justify-between gap-3",children:[e.jsx(v,{variant:"outline",onClick:Y,disabled:i===0,size:"sm",className:"min-w-[100px] text-sm py-2 h-9",children:"Previous"}),e.jsx(v,{onClick:V,disabled:y,size:"sm",className:"min-w-[100px] text-sm py-2 h-9",children:i===a.questions.length-1?"Finish":"Next"})]})})]}))};class _{static repairLessonProgressSchema(){return w(this,null,function*(){try{const{data:s,error:r}=yield j.from("user_lesson_progress").select("id").limit(1);if(r){console.error("Error checking lesson progress schema:",r),yield j.rpc("repair_completion_tables"),yield new Promise(c=>setTimeout(c,500));const{error:o}=yield j.from("user_lesson_progress").select("id").limit(1);if(o)return console.error("Schema repair failed:",o),!1}return!0}catch(s){return console.error("Unexpected error in repairLessonProgressSchema:",s),!1}})}static fixRlsPolicies(){return w(this,null,function*(){try{const{error:s}=yield j.rpc("ensure_lesson_completion_rls");return s?(console.error("Error fixing RLS policies:",s),!1):!0}catch(s){return console.error("Unexpected error in fixRlsPolicies:",s),!1}})}static fixModuleTrackingRLS(){return w(this,null,function*(){try{const{error:s}=yield j.rpc("ensure_module_tracking_rls");return s?(console.error("Error fixing module tracking RLS:",s),!1):!0}catch(s){return console.error("Unexpected error in fixModuleTrackingRLS:",s),!1}})}static addPerformanceIndexes(){return w(this,null,function*(){try{const{error:s}=yield j.rpc("add_completion_indexes");return s?(console.error("Error adding performance indexes:",s),!1):!0}catch(s){return console.error("Unexpected error in addPerformanceIndexes:",s),!1}})}static updateModuleProgressDirectly(s,r,o){return w(this,null,function*(){try{const{error:c}=yield j.rpc("update_module_progress_directly",{p_user_id:s,p_module_id:r,p_is_completed:o});return c?(console.error("Error directly updating module progress:",c),!1):!0}catch(c){return console.error("Unexpected error in updateModuleProgressDirectly:",c),!1}})}static completeSystemRepair(){return w(this,null,function*(){try{const{error:s}=yield j.rpc("repair_completion_system");return s?(console.error("Error performing complete system repair:",s),!1):!0}catch(s){return console.error("Unexpected error in completeSystemRepair:",s),!1}})}}function es({lessonId:t,lessonSlug:s,userId:r,courseId:o,onSchemaError:c,enabled:p=!0}){const[a,f]=u.useState(!1),i=se(),{toast:N}=te();return{completeMutation:fe({mutationFn:()=>w(this,null,function*(){if(!p)throw console.log("Lesson completion mutation not enabled yet, waiting for lesson data to load"),new Error("Please wait for lesson data to load completely before marking as completed");if(!r)throw console.error("Completion error: User not logged in"),new Error("You must be logged in to mark a lesson as completed");if(!t)throw console.error("Completion error: No valid lesson ID available",{providedId:t,slug:s}),new Error("Lesson data not found or not loaded yet");const g=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(t);if(!g)throw console.error("Completion error: Invalid lesson ID format - not a UUID",{providedId:t,slug:s}),new Error("Cannot mark lesson as complete - lesson data not fully loaded");console.log("Starting lesson completion process",{lessonId:t,userId:r,isValidUuid:g}),f(!0);try{console.log("Updating course enrollment status"),yield Be(o,r,"in_progress")}catch(b){console.error("Error enrolling in course, but continuing with lesson completion:",b)}const y=yield Xe(t,r);if(console.log("Mark lesson completed result:",y),!y.success)throw new Error(y.error||"Failed to mark lesson as completed");return y.error&&c&&(y.error.includes("is_completed")||y.error.includes("schema cache")||y.error.includes("column")||y.error.includes("not found"))&&c(),!0}),onSuccess:()=>{console.log("Invalidating related queries"),i.invalidateQueries({queryKey:["lesson",s||t]}),i.invalidateQueries({queryKey:["courseModules",o]}),i.invalidateQueries({queryKey:["dashboard-courses",r]}),i.invalidateQueries({queryKey:["courses"]}),i.invalidateQueries({queryKey:["moduleProgress",o,r]}),i.invalidateQueries({queryKey:["modules",o]}),i.invalidateQueries({queryKey:["lesson-completions",r]}),N({title:"Success",description:"Lesson marked as completed!"}),f(!1)},onError:g=>{var b;console.error("Error marking lesson as completed:",g),f(!1);let y="Failed to mark lesson as completed. Please try again.";g!=null&&g.message&&(y=g.message),(b=g==null?void 0:g.cause)!=null&&b.message&&(y+=`: ${g.cause.message}`),N({title:"Error",description:y,variant:"destructive"})}}),isCompleting:a}}const bs=()=>{var d;const{courseId:t="",lessonId:s=""}=Le(),{user:r}=re(),o=de(),c=se(),{toast:p}=te(),[a,f]=u.useState(0),[i,N]=u.useState(!1),[x,g]=u.useState(null),[y,b]=u.useState(!1),[R,k]=u.useState(!1),C=u.useRef(0),h=u.useRef(null),S=u.useRef(!0),{data:l,isLoading:q,error:T}=_e({queryKey:["lesson",s],queryFn:()=>w(void 0,null,function*(){console.log("[LESSON PAGE] Fetching lesson with slug:",s,"userId:",r==null?void 0:r.id);try{const n=yield qe(s,r==null?void 0:r.id);return console.log("[LESSON PAGE] Successfully fetched lesson:",n),n}catch(n){throw console.error("[LESSON PAGE] Error fetching lesson:",n),n}}),enabled:!!s,retry:3,retryDelay:n=>Math.min(1e3*le(2,n),3e4)}),{completeMutation:E}=es({lessonId:(l==null?void 0:l.id)||"",lessonSlug:s,userId:(r==null?void 0:r.id)||"",courseId:t,onSchemaError:()=>b(!0),enabled:!!(l!=null&&l.id)});u.useEffect(()=>{if(l!=null&&l.content)try{const n=JSON.parse(l.content);n&&n.externalRedirectUrl&&g(n.externalRedirectUrl)}catch(n){g(null)}},[l]);const P=u.useCallback(()=>w(void 0,null,function*(){if(console.log("[NAVIGATE TO NEXT] Starting navigation from lesson:",s),i){console.log("[NAVIGATE TO NEXT] Already navigating, skipping");return}N(!0);try{console.log("[NAVIGATE TO NEXT] Calling findNextLessonUnified...");const{nextLessonSlug:n,isLastLesson:m}=yield he(s);console.log("[NAVIGATE TO NEXT] Result from findNextLessonUnified:",{nextLessonSlug:n,isLastLesson:m,currentLessonId:s,courseId:t}),n?(console.log("[NAVIGATE TO NEXT] Navigating to next lesson:",n),o(`/course/${t}/lesson/${n}`)):m?(console.log("[NAVIGATE TO NEXT] This is the last lesson, going to modules page"),o(`/course/${t}/modules`),p({title:"Congratulations!",description:"You've completed all lessons in this course!"})):(console.log("[NAVIGATE TO NEXT] No next lesson found and not marked as last lesson, going to modules page"),o(`/course/${t}/modules`))}catch(n){console.error("[NAVIGATE TO NEXT] Error navigating to next lesson:",n),o(`/course/${t}/modules`)}finally{N(!1)}}),[s,t,o,p,i]);u.useEffect(()=>(S.current=!0,()=>{S.current=!1,h.current&&(window.clearTimeout(h.current),h.current=null)}),[]);const G=()=>w(void 0,null,function*(){k(!0);try{if(!(yield _.completeSystemRepair()))if(console.log("Complete system repair failed, trying individual repairs..."),(yield _.repairLessonProgressSchema())||console.log("Schema repair failed, trying RLS policy fixes..."),(yield _.fixRlsPolicies())||console.log("RLS policy fixes failed, trying module tracking fixes..."),(yield _.fixModuleTrackingRLS())||console.log("Module RLS fixes failed, trying performance optimizations..."),(yield _.addPerformanceIndexes())||console.log("Performance index creation failed, trying direct module update..."),l&&r){if(!(yield _.updateModuleProgressDirectly(r.id,l.module_id,!1)))throw new Error("All repair attempts failed")}else throw new Error("Could not repair without lesson or user data");S.current&&(c.invalidateQueries({queryKey:["lesson",s]}),p({title:"Repair Complete",description:"Database access issues have been fixed. Please try again."}))}catch(n){console.error("Error repairing database access:",n),S.current&&p({title:"Error",description:"Could not repair database access. You can continue using the app, but progress may only be saved locally.",variant:"destructive"})}finally{S.current&&(k(!1),b(!1))}});u.useEffect(()=>{if(!l||l.type==="quiz"||l.completed)return;if(x){const m=setTimeout(()=>{f(100),l.completed||E.mutate()},2e3);return()=>clearTimeout(m)}let n=!1;if(l.content&&l.content.startsWith("{"))try{const m=JSON.parse(l.content);n=!!(m.videoUrl||m.imageUrl)}catch(m){}if(n){const m=setTimeout(()=>{f(100),l.completed||E.mutate()},3e4);return()=>clearTimeout(m)}else{let m=!1,L=0,X=0;const F=()=>{L=window.scrollY,!m&&(m=!0,window.requestAnimationFrame(()=>{const B=Date.now();if(B-X<300){m=!1;return}const oe=document.documentElement.scrollHeight-window.innerHeight;if(oe<=0){m=!1;return}const M=Math.min(Math.floor(L/oe*100),100);Math.abs(M-C.current)>5&&(C.current=M,X=B,f(M),M>90&&!l.completed&&!h.current&&(h.current=window.setTimeout(()=>{E.mutate(),h.current=null},2e3))),m=!1}))};return window.addEventListener("scroll",F,{passive:!0}),setTimeout(()=>F(),500),()=>{window.removeEventListener("scroll",F),h.current&&(window.clearTimeout(h.current),h.current=null)}}},[l,E,x]);const K=u.useCallback(()=>w(void 0,null,function*(){if(!r){p({title:"Error",description:"You must be logged in to mark a lesson as completed.",variant:"destructive"});return}if(!l){p({title:"Error",description:"Lesson data not found.",variant:"destructive"});return}if(l.completed){console.log("Lesson already completed, no need to mark again");return}try{console.log("Attempting to mark lesson as completed...",{lessonId:l.id,slug:s}),yield E.mutateAsync(),l&&(l.completed=!0),console.log("Lesson completion mutation succeeded")}catch(n){console.error("Error in handleCompleteLesson:",n)}}),[r,l,E,p,s]),V=u.useCallback(()=>{x&&window.open(x,"_blank")},[x]);if(q)return e.jsx(W,{children:e.jsx(I,{pageType:"default",children:e.jsx(Fe,{})})});if(T||!l)return e.jsx(W,{children:e.jsx(I,{pageType:"default",children:e.jsxs("div",{className:"flex flex-col items-center justify-center p-8 bg-destructive/10 rounded-lg",children:[e.jsx("h1",{className:"text-xl font-bold mb-4",children:"Error loading lesson"}),e.jsx("p",{className:"text-muted-foreground text-center mb-6",children:(T==null?void 0:T.message)||"Could not load lesson content. Please try again later."}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(v,{variant:"outline",onClick:()=>o(`/course/${t}/modules`),children:"Back to Modules"}),e.jsx(v,{onClick:()=>window.location.reload(),children:"Try Again"})]})]})})});const Y=Pe(l.content)?"quiz":"content";return e.jsxs(W,{children:[e.jsx(I,{pageType:"default",children:Y==="quiz"?e.jsx(Ze,{content:l.content,onComplete:P,lessonId:l.id}):e.jsxs(e.Fragment,{children:[e.jsx(Me,{content:l.content}),e.jsxs("div",{className:"mt-8",children:[e.jsx(Je,{isCompleted:l.completed,onComplete:K,onNext:P,completionMutation:E,hasNextLesson:!!l.next_lesson_slug,progress:a,courseId:t,currentLessonSlug:s}),x&&e.jsxs(xe.div,{className:"mt-6 mb-4 flex flex-col items-center justify-center p-6 bg-primary/5 rounded-xl border border-primary/20",initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.5},children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"External Form"}),e.jsx("p",{className:"text-center mb-6",children:"You are being redirected to an external form. If you are not redirected automatically, please click the button below."}),e.jsxs(v,{size:"lg",onClick:V,className:"flex items-center",children:[e.jsx(Re,{className:"mr-2 h-5 w-5"}),"Open External Form"]})]})]})]})}),e.jsx($e,{open:y,onOpenChange:b,children:e.jsxs(Ue,{children:[e.jsxs(De,{children:[e.jsx(ze,{children:"Database Access Issue Detected"}),e.jsxs(Ge,{children:["There appears to be an issue with the progress tracking system. This can happen due to database schema changes, caching issues, or permission restrictions.",e.jsx("div",{className:"mt-4 p-4 bg-muted rounded-md",children:e.jsxs("div",{className:"flex items-start",children:[e.jsx(Ae,{className:"h-5 w-5 text-amber-500 mr-2 mt-0.5"}),e.jsxs("p",{className:"text-sm",children:["Error: ",((d=E.error)==null?void 0:d.message)||"Could not update your progress. This might be due to database permission restrictions."]})]})}),e.jsx("p",{className:"mt-4",children:"The system will continue to function but your progress may only be saved locally. Would you like to attempt to repair this issue now?"})]})]}),e.jsxs(Ke,{children:[e.jsx(Ve,{children:"Continue Without Fixing"}),e.jsx(Ye,{onClick:G,disabled:R,children:R?"Repairing...":"Repair Database Access"})]})]})})]})};export{bs as default};
