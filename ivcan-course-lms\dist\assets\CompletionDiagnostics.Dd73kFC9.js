var _=(n,f,r)=>new Promise((b,w)=>{var S=t=>{try{y(r.next(t))}catch(N){w(N)}},a=t=>{try{y(r.throw(t))}catch(N){w(N)}},y=t=>t.done?b(t.value):Promise.resolve(t.value).then(S,a);y((r=r.apply(n,f)).next())});import{u as U,r as d,j as e,$ as c,c8 as V,aO as A,F as O}from"./vendor-react.BcAa1DKr.js";import{u as $,B,s as h}from"./index.BLDhDn0D.js";import{L as H}from"./Layout.DRjmVYQG.js";import{C as p,a as u,b as j,d as g,c as k}from"./card.B9V6b2DK.js";import{T as M,a as Y,b as T,c as C}from"./tabs.B0SF6qIv.js";import{a2 as E}from"./vendor.DQpuTRuB.js";import{getCompletionSystemStatus as z}from"./completionService.BUb78ZaE.js";import"./vendor-supabase.sufZ44-y.js";import"./achievementService.Vkx_BHml.js";const ae=()=>{var F;const{user:n}=$(),f=U(),[r,b]=d.useState(!0),[w,S]=d.useState(!1),[a,y]=d.useState(null),[t,N]=d.useState(null),[D,J]=d.useState([]),[I,q]=d.useState(!1),[l,L]=d.useState(null);d.useEffect(()=>{_(void 0,null,function*(){if(!n){f("/login");return}try{const{data:i}=yield h.from("user_roles").select("role").eq("user_id",n.id).eq("role","admin").maybeSingle();if(!i){E.error("You need admin privileges to access this page"),f("/");return}S(!0),R()}catch(i){console.error("Error checking admin status:",i),E.error("Error checking permissions"),f("/")}})},[n,f]);const R=()=>_(void 0,null,function*(){b(!0);try{const s=yield z();s.success?y(s.data):E.error(`Error fetching system status: ${s.error}`);const{data:i,error:o}=yield h.rpc("count_completion_records");o?console.error("Error fetching completion stats:",o):N(i);const{data:m,error:x}=yield h.from("completion_audit_log").select("*").order("completed_at",{ascending:!1}).limit(20);x?console.error("Error fetching audit logs:",x):J(m||[])}catch(s){console.error("Error loading diagnostic data:",s),E.error("Failed to load diagnostic data")}finally{b(!1)}}),P=()=>_(void 0,null,function*(){if(n){q(!0),L(null);try{const{data:s,error:i}=yield h.from("lessons").select("id").limit(1);if(i||!s||s.length===0)throw new Error("No lessons available for testing");const o=s[0].id;yield h.from("user_lesson_progress").delete().eq("user_id",n.id).eq("lesson_id",o);const{error:m}=yield h.from("user_lesson_progress").insert({user_id:n.id,lesson_id:o,is_completed:!0,completed_at:new Date().toISOString()}),{data:x,error:v}=yield h.from("user_lesson_progress").select("*").eq("user_id",n.id).eq("lesson_id",o).single();L({success:!m&&!v&&(x==null?void 0:x.is_completed)===!0,insertError:m==null?void 0:m.message,verifyError:v==null?void 0:v.message,verification:x}),R()}catch(s){L({success:!1,error:s.message})}finally{q(!1)}}});return w?e.jsx(H,{children:e.jsxs("div",{className:"container py-10",children:[e.jsxs("div",{className:"mb-6 flex flex-col md:flex-row md:items-center justify-between gap-4",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:"Completion System Diagnostics"}),e.jsx("p",{className:"text-muted-foreground",children:"Troubleshoot lesson completion issues"})]}),e.jsxs(B,{onClick:R,variant:"outline",className:"flex items-center gap-2",disabled:r,children:[r?e.jsx(c,{className:"h-4 w-4 animate-spin"}):e.jsx(V,{className:"h-4 w-4"}),"Refresh"]})]}),e.jsxs(M,{defaultValue:"overview",children:[e.jsxs(Y,{className:"mb-4",children:[e.jsx(T,{value:"overview",children:"Overview"}),e.jsx(T,{value:"schema",children:"Database Schema"}),e.jsx(T,{value:"audit",children:"Audit Logs"}),e.jsx(T,{value:"test",children:"Run Tests"})]}),e.jsxs(C,{value:"overview",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-8",children:[e.jsxs(p,{children:[e.jsx(u,{className:"pb-2",children:e.jsx(j,{children:"Total Progress Records"})}),e.jsx(g,{children:e.jsx("div",{className:"text-3xl font-bold",children:r?e.jsx(c,{className:"h-5 w-5 animate-spin"}):(t==null?void 0:t.total)||0})})]}),e.jsxs(p,{children:[e.jsx(u,{className:"pb-2",children:e.jsx(j,{children:"Completed Lessons"})}),e.jsx(g,{children:e.jsx("div",{className:"text-3xl font-bold",children:r?e.jsx(c,{className:"h-5 w-5 animate-spin"}):(t==null?void 0:t.completed)||0})})]}),e.jsxs(p,{children:[e.jsx(u,{className:"pb-2",children:e.jsx(j,{children:"Users with Progress"})}),e.jsx(g,{children:e.jsx("div",{className:"text-3xl font-bold",children:r?e.jsx(c,{className:"h-5 w-5 animate-spin"}):(t==null?void 0:t.users)||0})})]})]}),e.jsxs(p,{children:[e.jsxs(u,{children:[e.jsx(j,{children:"System Status"}),e.jsx(k,{children:"Details about the completion system's current state"})]}),e.jsx(g,{children:r?e.jsx("div",{className:"flex justify-center p-4",children:e.jsx(c,{className:"h-8 w-8 animate-spin text-primary"})}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-2",children:"Recent Activity"}),e.jsxs("p",{children:["Last 24 hours: ",D.filter(s=>{const i=new Date(s.completed_at);return new Date().getTime()-i.getTime()<24*60*60*1e3}).length," completion attempts"]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-2",children:"Database Table Health"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(A,{className:"h-5 w-5 text-green-500 mr-2"}),e.jsx("span",{children:"user_lesson_progress table exists"})]}),e.jsxs("div",{className:"flex items-center",children:[a!=null&&a.tableInfo&&a.tableInfo.some(s=>s.column_name==="is_completed")?e.jsx(A,{className:"h-5 w-5 text-green-500 mr-2"}):e.jsx(O,{className:"h-5 w-5 text-red-500 mr-2"}),e.jsxs("span",{children:["is_completed column ",a!=null&&a.tableInfo&&a.tableInfo.some(s=>s.column_name==="is_completed")?"exists":"missing"]})]})]})]})]})})]})]}),e.jsx(C,{value:"schema",children:e.jsxs(p,{children:[e.jsxs(u,{children:[e.jsx(j,{children:"Database Schema"}),e.jsx(k,{children:"Detailed information about the completion tables"})]}),e.jsx(g,{children:r?e.jsx("div",{className:"flex justify-center p-4",children:e.jsx(c,{className:"h-8 w-8 animate-spin text-primary"})}):e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-2",children:"user_lesson_progress Table"}),e.jsx("div",{className:"border rounded-lg overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50 dark:bg-gray-800",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Column Name"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Data Type"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Nullable"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Default"})]})}),e.jsx("tbody",{className:"bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700",children:(F=a==null?void 0:a.tableInfo)==null?void 0:F.map((s,i)=>e.jsxs("tr",{className:s.column_name==="is_completed"?"bg-green-50 dark:bg-green-900/20":"",children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:s.column_name}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:s.data_type}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:s.is_nullable}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:s.column_default||"-"})]},i))})]})})]})})})]})}),e.jsx(C,{value:"audit",children:e.jsxs(p,{children:[e.jsxs(u,{children:[e.jsx(j,{children:"Completion Audit Logs"}),e.jsx(k,{children:"Recent lesson completion attempts"})]}),e.jsx(g,{children:r?e.jsx("div",{className:"flex justify-center p-4",children:e.jsx(c,{className:"h-8 w-8 animate-spin text-primary"})}):D.length>0?e.jsx("div",{className:"border rounded-lg overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50 dark:bg-gray-800",children:e.jsxs("tr",{children:[e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Timestamp"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"User"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Lesson"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Status"}),e.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Message"})]})}),e.jsx("tbody",{className:"bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700",children:D.map((s,i)=>e.jsxs("tr",{children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:new Date(s.completed_at).toLocaleString()}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:[s.user_id.substring(0,8),"..."]}),e.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:[s.lesson_id.substring(0,8),"..."]}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:s.success?e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",children:"Success"}):e.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",children:"Failed"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:s.error_message||"-"})]},i))})]})}):e.jsx("div",{className:"text-center py-6 text-muted-foreground",children:"No audit logs found"})})]})}),e.jsx(C,{value:"test",children:e.jsxs(p,{children:[e.jsxs(u,{children:[e.jsx(j,{children:"Test Completion System"}),e.jsx(k,{children:"Run tests against the completion system"})]}),e.jsx(g,{children:e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"p-4 border rounded-lg",children:[e.jsx("h3",{className:"font-semibold mb-2",children:"Direct Database Test"}),e.jsx("p",{className:"text-sm text-muted-foreground mb-4",children:"Test direct insertion of a lesson completion record in the database."}),e.jsx(B,{onClick:P,disabled:I||r,variant:"outline",children:I?e.jsxs(e.Fragment,{children:[e.jsx(c,{className:"mr-2 h-4 w-4 animate-spin"}),"Running Test..."]}):"Run Database Test"}),l&&e.jsxs("div",{className:"mt-4 p-4 border rounded-lg bg-gray-50 dark:bg-gray-800/50",children:[e.jsxs("div",{className:"flex items-center mb-2",children:[l.success?e.jsx(A,{className:"h-5 w-5 text-green-500 mr-2"}):e.jsx(O,{className:"h-5 w-5 text-red-500 mr-2"}),e.jsx("span",{className:"font-medium",children:l.success?"Test passed":"Test failed"})]}),l.insertError&&e.jsxs("div",{className:"text-sm text-red-500 mt-2",children:["Insert Error: ",l.insertError]}),l.verifyError&&e.jsxs("div",{className:"text-sm text-red-500 mt-2",children:["Verify Error: ",l.verifyError]}),l.error&&e.jsxs("div",{className:"text-sm text-red-500 mt-2",children:["Error: ",l.error]}),l.verification&&e.jsx("div",{className:"mt-2 text-sm",children:e.jsx("pre",{className:"p-2 bg-gray-100 dark:bg-gray-900 rounded overflow-x-auto",children:JSON.stringify(l.verification,null,2)})})]})]})})})]})})]})]})}):e.jsx(H,{children:e.jsx("div",{className:"flex items-center justify-center min-h-[70vh]",children:e.jsx(c,{className:"w-8 h-8 animate-spin text-primary"})})})};export{ae as default};
