-- Create a notifications table for system notifications
CREATE TABLE IF NOT EXISTS public.notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users NOT NULL,
  type TEXT NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  data JSONB,
  read B<PERSON><PERSON><PERSON><PERSON> DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  read_at TIMESTAMP WITH TIME ZONE
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS notifications_user_id_idx ON public.notifications (user_id);
CREATE INDEX IF NOT EXISTS notifications_read_idx ON public.notifications (read);
CREATE INDEX IF NOT EXISTS notifications_type_idx ON public.notifications (type);

-- Enable Row Level Security
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Create policies for notifications table
CREATE POLICY "Users can view their own notifications" 
ON public.notifications FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" 
ON public.notifications FOR UPDATE 
USING (auth.uid() = user_id);

-- Create a function to mark a notification as read
CREATE OR REPLACE FUNCTION public.mark_notification_read(
  _notification_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the notification exists and belongs to the current user
  IF NOT EXISTS (
    SELECT 1 FROM public.notifications
    WHERE id = _notification_id AND user_id = auth.uid()
  ) THEN
    RAISE EXCEPTION 'Notification not found or does not belong to the current user';
  END IF;
  
  -- Update the notification
  UPDATE public.notifications
  SET 
    read = true,
    read_at = now()
  WHERE id = _notification_id;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE;
END;
$$;

-- Create a function to mark all notifications as read for a user
CREATE OR REPLACE FUNCTION public.mark_all_notifications_read()
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Update all unread notifications for the current user
  UPDATE public.notifications
  SET 
    read = true,
    read_at = now()
  WHERE user_id = auth.uid() AND read = false;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE;
END;
$$;

-- Grant permissions to use these functions
GRANT EXECUTE ON FUNCTION public.mark_notification_read TO authenticated;
GRANT EXECUTE ON FUNCTION public.mark_all_notifications_read TO authenticated;

-- Add the notifications table to the realtime publication
ALTER PUBLICATION supabase_realtime ADD TABLE public.notifications;

-- Set up replica identity for realtime
ALTER TABLE public.notifications REPLICA IDENTITY FULL;

-- Create a trigger function to notify teachers when a role request is created
CREATE OR REPLACE FUNCTION public.notify_teachers_on_role_request()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  _user_record RECORD;
  _teacher_record RECORD;
  _user_name TEXT;
  _user_email TEXT;
BEGIN
  -- Only proceed for new teacher role requests
  IF NEW.requested_role = 'teacher' AND NEW.status = 'pending' THEN
    -- Get user information
    SELECT * INTO _user_record FROM auth.users WHERE id = NEW.user_id;
    
    -- Extract user details
    _user_name := _user_record.raw_user_meta_data->>'full_name';
    IF _user_name IS NULL THEN
      _user_name := _user_record.raw_user_meta_data->>'first_name';
      IF _user_name IS NULL THEN
        _user_name := 'A new user';
      END IF;
    END IF;
    _user_email := _user_record.email;
    
    -- Create notifications for all teachers
    FOR _teacher_record IN 
      SELECT user_id FROM public.user_roles WHERE role = 'teacher'
    LOOP
      INSERT INTO public.notifications (
        user_id,
        type,
        title,
        message,
        data,
        read,
        created_at
      ) VALUES (
        _teacher_record.user_id,
        'role_request',
        'New Teacher Role Request',
        _user_name || ' (' || _user_email || ') has requested teacher privileges.',
        jsonb_build_object(
          'request_id', NEW.id,
          'user_id', NEW.user_id,
          'user_name', _user_name,
          'user_email', _user_email
        ),
        false,
        now()
      );
    END LOOP;
  END IF;
  
  RETURN NEW;
END;
$$;

-- Create a trigger on the role_requests table
DROP TRIGGER IF EXISTS role_request_notification_trigger ON public.role_requests;
CREATE TRIGGER role_request_notification_trigger
AFTER INSERT ON public.role_requests
FOR EACH ROW
EXECUTE FUNCTION public.notify_teachers_on_role_request();
