import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Plus, Edit, Trash2, FileText } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { deleteModuleTest, getModuleTestsByModuleId } from '@/services/module-test/moduleTestService';
import ModuleTestEditor from './ModuleTestEditor';
import { ModuleTest } from '@/types/module-test';

interface ModuleTestsListProps {
  moduleId: string;
  moduleName?: string;
}

const ModuleTestsList: React.FC<ModuleTestsListProps> = ({ moduleId, moduleName }) => {
  const [isAddingTest, setIsAddingTest] = useState(false);
  const [isEditingTest, setIsEditingTest] = useState(false);
  const [activeTestType, setActiveTestType] = useState<'pre_test' | 'post_test'>('pre_test');
  const [selectedTestId, setSelectedTestId] = useState<string | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch tests for this module
  const { data: tests, isLoading: isLoadingTests } = useQuery({
    queryKey: ['module-tests', moduleId],
    queryFn: async () => {
      try {
        const moduleTests = await getModuleTestsByModuleId(moduleId);
        return moduleTests;
      } catch (error) {
        console.error('Error fetching module tests:', error);
        toast({
          title: "Error",
          description: "Failed to load module tests",
          variant: "destructive"
        });
        return [];
      }
    }
  });

  // Delete a test
  const deleteTestMutation = useMutation({
    mutationFn: async (testId: string) => {
      await deleteModuleTest(testId);
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Test deleted successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['module-tests', moduleId] });
    },
    onError: (error: any) => {
      console.error('Error deleting test:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to delete test",
        variant: "destructive"
      });
    }
  });

  const handleAddTest = (type: 'pre_test' | 'post_test') => {
    setActiveTestType(type);
    setSelectedTestId(null);
    setIsAddingTest(true);
    setIsEditingTest(false);
  };

  const handleEditTest = (test: ModuleTest) => {
    setActiveTestType(test.type);
    setSelectedTestId(test.id);
    setIsEditingTest(true);
    setIsAddingTest(false);
  };

  const handleDeleteTest = (testId: string) => {
    deleteTestMutation.mutate(testId);
  };

  const handleCloseEditor = () => {
    setIsAddingTest(false);
    setIsEditingTest(false);
    setSelectedTestId(null);
  };

  // Filter tests by type
  const preTests = tests?.filter(test => test.type === 'pre_test') || [];
  const postTests = tests?.filter(test => test.type === 'post_test') || [];

  if (isLoadingTests) {
    return (
      <div className="flex justify-center py-8">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  if (isAddingTest || isEditingTest) {
    return (
      <div className="space-y-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCloseEditor}
          className="mb-4"
        >
          ← Back to Tests List
        </Button>
        <ModuleTestEditor
          moduleId={moduleId}
          testId={selectedTestId || undefined}
          testType={activeTestType}
          onClose={handleCloseEditor}
        />
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl">
          Module Tests
          {moduleName && <span className="text-muted-foreground ml-2 text-base">({moduleName})</span>}
        </CardTitle>
        <CardDescription>
          Manage pre-tests and post-tests for this module
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="pre-tests">
          <TabsList className="mb-4">
            <TabsTrigger value="pre-tests">Pre-Tests</TabsTrigger>
            <TabsTrigger value="post-tests">Post-Tests</TabsTrigger>
          </TabsList>

          <TabsContent value="pre-tests">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Pre-Intervention Tests</h3>
                {preTests.length === 0 && (
                  <Button
                    onClick={() => handleAddTest('pre_test')}
                    size="sm"
                  >
                    <Plus className="w-4 h-4 mr-2" /> Add Pre-Test
                  </Button>
                )}
              </div>
              
              {preTests.length === 0 ? (
                <div className="text-center py-8 border border-dashed rounded-lg">
                  <p className="text-muted-foreground mb-2">No pre-tests have been created for this module</p>
                  <Button
                    onClick={() => handleAddTest('pre_test')}
                    variant="outline"
                    size="sm"
                  >
                    <Plus className="w-4 h-4 mr-2" /> Add Pre-Test
                  </Button>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Title</TableHead>
                      <TableHead>Questions</TableHead>
                      <TableHead>Last Updated</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {preTests.map((test) => (
                      <TableRow key={test.id}>
                        <TableCell className="font-medium">{test.title}</TableCell>
                        <TableCell>{test.questions.length} questions</TableCell>
                        <TableCell>{new Date(test.updatedAt).toLocaleDateString()}</TableCell>
                        <TableCell className="text-right space-x-2">
                          <Button
                            onClick={() => handleEditTest(test)}
                            variant="ghost"
                            size="icon"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                <AlertDialogDescription>
                                  This will permanently delete this test and all student responses to it.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction onClick={() => handleDeleteTest(test.id)}>
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </div>
          </TabsContent>

          <TabsContent value="post-tests">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Post-Intervention Tests</h3>
                {postTests.length === 0 && (
                  <Button
                    onClick={() => handleAddTest('post_test')}
                    size="sm"
                  >
                    <Plus className="w-4 h-4 mr-2" /> Add Post-Test
                  </Button>
                )}
              </div>
              
              {postTests.length === 0 ? (
                <div className="text-center py-8 border border-dashed rounded-lg">
                  <p className="text-muted-foreground mb-2">No post-tests have been created for this module</p>
                  <Button
                    onClick={() => handleAddTest('post_test')}
                    variant="outline"
                    size="sm"
                  >
                    <Plus className="w-4 h-4 mr-2" /> Add Post-Test
                  </Button>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Title</TableHead>
                      <TableHead>Questions</TableHead>
                      <TableHead>Last Updated</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {postTests.map((test) => (
                      <TableRow key={test.id}>
                        <TableCell className="font-medium">{test.title}</TableCell>
                        <TableCell>{test.questions.length} questions</TableCell>
                        <TableCell>{new Date(test.updatedAt).toLocaleDateString()}</TableCell>
                        <TableCell className="text-right space-x-2">
                          <Button
                            onClick={() => handleEditTest(test)}
                            variant="ghost"
                            size="icon"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                <AlertDialogDescription>
                                  This will permanently delete this test and all student responses to it.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction onClick={() => handleDeleteTest(test.id)}>
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default ModuleTestsList; 