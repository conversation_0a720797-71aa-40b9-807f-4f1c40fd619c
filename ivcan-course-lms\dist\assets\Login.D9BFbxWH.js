var P=(r,a,t)=>new Promise((n,o)=>{var u=d=>{try{i(t.next(d))}catch(m){o(m)}},l=d=>{try{i(t.throw(d))}catch(m){o(m)}},i=d=>d.done?n(d.value):Promise.resolve(d.value).then(u,l);i((t=t.apply(r,a)).next())});import{r as c,j as e,ap as L,aq as R,ar as _,as as O,x as z,F as U,ac as E,Z as D,at as B,_ as G,$ as F,u as q,a8 as $,a3 as W,au as H}from"./vendor-react.BcAa1DKr.js";import{u as A,I as V,B as J,s as T}from"./index.BLDhDn0D.js";import{a4 as s,a2 as g}from"./vendor.DQpuTRuB.js";import"./vendor-supabase.sufZ44-y.js";const K=({email:r,setEmail:a,password:t,setPassword:n,isSubmitting:o,onForgotPassword:u})=>{const[l,i]=c.useState(!1),{signIn:d}=A(),m=x=>P(void 0,null,function*(){if(x.preventDefault(),!r.trim()||!t.trim()){g.error("Please enter both email and password");return}try{const h=yield d(r,t);h!=null&&h.error&&g.error(h.error.message||"Failed to sign in")}catch(h){g.error(h.message||"An error occurred during sign in")}});return e.jsxs("form",{className:"space-y-5",onSubmit:m,children:[e.jsxs(s.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.1},className:"space-y-1.5",children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-foreground",children:"Email Address"}),e.jsx("div",{className:"relative",children:e.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:r,onChange:x=>a(x.target.value),className:"block w-full px-4 py-3 text-foreground border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-card/50 transition-all duration-200 placeholder:text-muted-foreground/60",placeholder:"<EMAIL>"})})]}),e.jsxs(s.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.2},className:"space-y-1.5",children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-foreground",children:"Password"}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{id:"password",name:"password",type:l?"text":"password",autoComplete:"current-password",required:!0,value:t,onChange:x=>n(x.target.value),className:"block w-full px-4 py-3 text-foreground border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent pr-10 bg-card/50 transition-all duration-200",placeholder:"••••••••"}),e.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center text-muted-foreground hover:text-foreground transition-colors",onClick:()=>i(!l),children:l?e.jsx(L,{className:"h-5 w-5"}):e.jsx(R,{className:"h-5 w-5"})})]})]}),e.jsx(s.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},className:"pt-2",children:e.jsxs("button",{type:"submit",disabled:o,className:"relative w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed overflow-hidden group",children:[e.jsx("span",{className:"absolute inset-0 w-full h-full bg-gradient-to-r from-primary to-primary transition-all duration-300 transform group-hover:scale-102 opacity-0 group-hover:opacity-100"}),e.jsx("span",{className:"flex items-center relative z-10",children:o?"Signing in...":e.jsxs(e.Fragment,{children:[e.jsx(_,{className:"w-4 h-4 mr-2"}),"Sign In"]})})]})}),e.jsxs(s.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.4},className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-primary focus:ring-primary border-border rounded transition-colors"}),e.jsx("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-muted-foreground",children:"Remember me"})]}),e.jsx("div",{className:"text-sm relative z-10",children:e.jsx("button",{type:"button",onClick:u,className:"relative px-2 py-1 bg-primary text-white border border-primary rounded-md font-regular hover:bg-primary/90 transition-colors",children:"Forgot Password?"})})]})]})};var w=(r=>(r.WEAK="weak",r.MEDIUM="medium",r.STRONG="strong",r))(w||{});function M(r){return r.length>=8}function Z(r){if(!r||r.length<8)return"weak";let a=0;return r.length>=12?a+=2:r.length>=8&&(a+=1),/[A-Z]/.test(r)&&(a+=1),/[a-z]/.test(r)&&(a+=1),/[0-9]/.test(r)&&(a+=1),/[^A-Za-z0-9]/.test(r)&&(a+=1),a>=4?"strong":a>=2?"medium":"weak"}function Y(r){switch(r){case"strong":return"#E63946";case"medium":return"orange";case"weak":return"#9D0208";default:return"gray"}}const Q=({password:r,className:a=""})=>{if(!r)return null;const t=Z(r),n=Y(t),o=()=>{switch(t){case w.STRONG:return"100%";case w.MEDIUM:return"66%";case w.WEAK:return"33%";default:return"0%"}},u=()=>{switch(t){case w.STRONG:return"Strong password";case w.MEDIUM:return"Medium strength - add special characters";case w.WEAK:return"Weak password - use at least 8 characters";default:return""}};return e.jsxs("div",{className:`mt-1 ${a}`,children:[e.jsx("div",{className:"h-1 w-full bg-gray-200 rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full rounded-full transition-all duration-300",style:{width:o(),backgroundColor:n}})}),e.jsx("p",{className:"text-xs mt-1",style:{color:n},children:u()})]})},X=({email:r,setEmail:a,password:t,setPassword:n,name:o,setName:u,isSubmitting:l})=>{const[i,d]=c.useState(!1),[m,x]=c.useState(""),[h,k]=c.useState(!0),[b,f]=c.useState(!1),{signUp:v}=A();c.useEffect(()=>{const p=r.trim()!==""&&M(t)&&t===m&&o.trim()!=="";f(p),t&&m&&k(t===m)},[r,t,m,o]);const j=p=>P(void 0,null,function*(){if(p.preventDefault(),!b){if(!M(t)){g.error("Password must be at least 8 characters long");return}if(t!==m){g.error("Passwords do not match");return}if(!r.trim()){g.error("Please enter your email");return}if(!o.trim()){g.error("Please enter your name");return}return}try{const y=o.split(" "),C=y[0]||"",N=y.slice(1).join(" ")||"",S={first_name:C,last_name:N,full_name:o.trim(),requested_role:"student"};yield v(r,t,S)}catch(y){}});return e.jsxs("form",{className:"space-y-5",onSubmit:j,children:[e.jsxs(s.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.1},className:"space-y-1.5",children:[e.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-foreground",children:"Full Name"}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{id:"name",name:"name",type:"text",value:o,onChange:p=>u(p.target.value),required:!0,className:"block w-full px-4 py-3 text-foreground border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-card/50 transition-all duration-200 placeholder:text-muted-foreground/60",placeholder:"John Doe"}),e.jsx(O,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground/60"})]})]}),e.jsxs(s.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.2},className:"space-y-1.5",children:[e.jsx("label",{htmlFor:"signup-email",className:"block text-sm font-medium text-foreground",children:"Email Address"}),e.jsx("input",{id:"signup-email",name:"email",type:"email",autoComplete:"email",required:!0,value:r,onChange:p=>a(p.target.value),className:"block w-full px-4 py-3 text-foreground border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-card/50 transition-all duration-200 placeholder:text-muted-foreground/60",placeholder:"<EMAIL>"})]}),e.jsxs(s.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},className:"space-y-1.5",children:[e.jsx("label",{htmlFor:"signup-password",className:"block text-sm font-medium text-foreground",children:"Password"}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{id:"signup-password",name:"password",type:i?"text":"password",autoComplete:"new-password",required:!0,value:t,onChange:p=>n(p.target.value),className:"block w-full px-4 py-3 text-foreground border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent pr-10 bg-card/50 transition-all duration-200",placeholder:"••••••••"}),e.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center text-muted-foreground hover:text-foreground transition-colors",onClick:()=>d(!i),children:i?e.jsx(L,{className:"h-4 w-4"}):e.jsx(R,{className:"h-4 w-4"})})]}),t&&e.jsx(Q,{password:t,className:"mt-1"})]}),e.jsxs(s.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.4},className:"space-y-1.5",children:[e.jsx("label",{htmlFor:"confirm-password",className:"block text-sm font-medium text-foreground",children:"Confirm Password"}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{id:"confirm-password",name:"confirmPassword",type:i?"text":"password",autoComplete:"new-password",required:!0,value:m,onChange:p=>x(p.target.value),className:`block w-full px-4 py-3 text-foreground border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent pr-10 bg-card/50 transition-all duration-200 ${m&&!h?"border-red-500":"border-border"}`,placeholder:"••••••••"}),m&&h&&e.jsx(z,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500"})]}),m&&!h&&e.jsxs("p",{className:"text-xs text-red-500 mt-1 flex items-center",children:[e.jsx(U,{className:"h-3 w-3 mr-1"}),"Passwords do not match"]})]}),e.jsxs(s.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.5},className:"flex items-center",children:[e.jsx("input",{id:"terms",name:"terms",type:"checkbox",required:!0,className:"h-4 w-4 text-primary focus:ring-primary border-border rounded"}),e.jsxs("label",{htmlFor:"terms",className:"ml-2 block text-sm text-foreground",children:["I agree to the"," ",e.jsx("a",{href:"#",className:"inline-flex items-center px-2 py-0.5 bg-primary/10 border border-primary/30 rounded text-primary font-medium hover:bg-primary hover:text-white transition-colors",children:"Terms"})," ","and"," ",e.jsx("a",{href:"#",className:"inline-flex items-center px-2 py-0.5 bg-primary/10 border border-primary/30 rounded text-primary font-medium hover:bg-primary hover:text-white transition-colors",children:"Privacy Policy"})]})]}),e.jsx(s.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.6},className:"pt-2",children:e.jsxs("button",{type:"submit",disabled:l||!b,className:"relative w-full flex justify-center items-center px-4 py-3 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed overflow-hidden group",children:[e.jsx("span",{className:"absolute inset-0 w-full h-full bg-gradient-to-r from-primary to-primary transition-all duration-300 transform group-hover:scale-102 opacity-0 group-hover:opacity-100"}),e.jsx("span",{className:"flex items-center relative z-10",children:l?"Processing...":e.jsxs(e.Fragment,{children:[e.jsx(_,{className:"w-4 h-4 mr-2"}),"Create Account"]})})]})})]})},ee=({onBack:r})=>{const[a,t]=c.useState(""),[n,o]=c.useState(!1),u=l=>P(void 0,null,function*(){if(l.preventDefault(),!a){g.error("Please enter your email address");return}o(!0);try{const{error:i}=yield T.auth.resetPasswordForEmail(a,{redirectTo:`${window.location.origin}/reset-password`});if(i)throw i;g.success("Password reset email sent! Please check your inbox.")}catch(i){console.error("Error sending reset password email:",i),g.error(i.message||"Failed to send reset email. Please try again.")}finally{o(!1)}});return e.jsx(s.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.4},children:e.jsxs("form",{className:"space-y-6",onSubmit:u,children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx(s.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.5},className:"flex justify-center mb-4",children:e.jsx("div",{className:"bg-primary/10 p-3 rounded-full",children:e.jsx(E,{className:"h-8 w-8 text-primary"})})}),e.jsx("h2",{className:"text-2xl font-bold text-foreground",children:"Reset Password"}),e.jsx("p",{className:"text-sm text-muted-foreground mt-2",children:"Enter your email and we'll send you instructions to reset your password"})]}),e.jsxs(s.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.2},className:"space-y-1.5",children:[e.jsx("label",{htmlFor:"reset-email",className:"block text-sm font-medium text-foreground",children:"Email Address"}),e.jsxs("div",{className:"relative",children:[e.jsx(V,{id:"reset-email",type:"email",value:a,onChange:l=>t(l.target.value),placeholder:"Enter your email",disabled:n,className:"pl-10 py-6 bg-card/50 transition-all duration-200",required:!0}),e.jsx(E,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground"})]})]}),e.jsx(s.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},children:e.jsxs(J,{type:"submit",className:"w-full relative overflow-hidden group h-11",disabled:n,children:[e.jsx("span",{className:"absolute inset-0 w-full h-full bg-gradient-to-r from-primary/80 to-primary transition-all duration-300 transform group-hover:scale-102 opacity-0 group-hover:opacity-100"}),e.jsx("span",{className:"relative z-10 flex items-center justify-center",children:n?e.jsxs(e.Fragment,{children:[e.jsx(D,{className:"h-4 w-4 mr-2 animate-spin"}),"Sending..."]}):"Send Reset Instructions"})]})}),e.jsx(s.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},className:"text-center",children:e.jsxs("button",{type:"button",onClick:r,className:"flex items-center justify-center mx-auto px-4 py-2 border border-border bg-card hover:bg-primary hover:text-white hover:border-primary rounded-md text-sm font-medium text-foreground transition-colors group mt-4",children:[e.jsx(B,{className:"h-4 w-4 mr-2 group-hover:-translate-x-1 transition-transform"}),"Back to login"]})})]})})};function re(){return window.location.hostname==="localhost"||window.location.hostname==="127.0.0.1"?`${window.location.protocol}//${window.location.host}`:window.location.origin}function te(){return`${re()}/auth/callback`}function ae(){return{access_type:"offline",prompt:"select_account",scope:"profile email"}}const I=({error:r,provider:a,onDismiss:t})=>{if(!r)return null;const n=r.message||`Error connecting to ${a}`,u=Object.entries({popup_closed_by_user:"The sign-in window was closed. Please try again.",popup_blocked_by_browser:"Your browser blocked the sign-in popup. Please allow popups for this site.",access_denied:"Access was denied. Please try again or use another sign-in method.",invalid_client:"Authentication configuration error. Please contact support.",invalid_grant:"Your session has expired. Please try signing in again.",server_error:"The authentication server encountered an error. Please try again later.",temporarily_unavailable:"The authentication service is temporarily unavailable. Please try again later.",user_cancelled:"Sign-in was cancelled. Please try again if you want to sign in.",redirect_uri_mismatch:"Authentication configuration error. Please contact support."}).reduce((l,[i,d])=>n.toLowerCase().includes(i.toLowerCase())?d:l,n);return e.jsx("div",{className:"bg-red-50 border border-red-200 rounded-md p-4 mt-4 mb-4",children:e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx(U,{className:"h-5 w-5 text-red-400"})}),e.jsxs("div",{className:"ml-3",children:[e.jsxs("h3",{className:"text-sm font-medium text-red-800",children:[a," Sign-In Error"]}),e.jsx("div",{className:"mt-2 text-sm text-red-700",children:e.jsx("p",{children:u})}),e.jsx("div",{className:"mt-4",children:e.jsx("button",{type:"button",onClick:t,className:"text-sm font-medium text-red-600 hover:text-red-500",children:"Dismiss"})})]})]})})},se=()=>{const[r,a]=c.useState(!1),[t,n]=c.useState(!1),[o,u]=c.useState(null),[l,i]=c.useState(null),d=G(),m=()=>new URLSearchParams(d.search).get("returnTo")||"/dashboard",x=()=>P(void 0,null,function*(){u(null);try{a(!0),console.log("Starting Google OAuth sign-in process");const f=te();console.log("Callback URL:",f);const v=m();console.log("Return URL after auth:",v);const j=ae();console.log("OAuth query params:",j),console.log("Environment:","production"),console.log("Origin:",window.location.origin),console.log("Hostname:",window.location.hostname);const{data:p,error:y}=yield T.auth.signInWithOAuth({provider:"google",options:{redirectTo:f,queryParams:j,skipBrowserRedirect:!1}});if(y)throw console.error("Google OAuth error:",y),y;console.log("Google OAuth response:",p),g.loading("Redirecting to Google authentication...")}catch(f){console.error("Google sign-in error details:",f),u(f),g.error(f.message||"Error signing in with Google"),a(!1)}}),h=()=>{u(null)},k=()=>{i(null),n(!0);try{g.error("Apple sign in is not available yet")}finally{n(!1)}},b=()=>{i(null)};return e.jsxs("div",{className:"space-y-3",children:[o&&e.jsx(I,{error:o,provider:"Google",onDismiss:h}),l&&e.jsx(I,{error:l,provider:"Apple",onDismiss:b}),e.jsxs(s.button,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},type:"button",onClick:x,disabled:r,className:"flex justify-center items-center w-full px-4 py-3 border border-border rounded-lg text-sm font-medium text-foreground bg-card hover:bg-accent transition-colors disabled:opacity-70 disabled:cursor-not-allowed",children:[r?e.jsx(F,{className:"w-5 h-5 mr-3 animate-spin"}):e.jsxs("svg",{className:"w-5 h-5 mr-3",viewBox:"0 0 24 24",children:[e.jsx("path",{d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z",fill:"#4285F4"}),e.jsx("path",{d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z",fill:"#34A853"}),e.jsx("path",{d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z",fill:"#FBBC05"}),e.jsx("path",{d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z",fill:"#EA4335"})]}),r?"Signing in...":"Log in with Google"]}),e.jsxs(s.button,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.1},type:"button",onClick:k,disabled:t,className:"flex justify-center items-center w-full px-4 py-3 border border-border rounded-lg text-sm font-medium text-foreground bg-card hover:bg-accent transition-colors disabled:opacity-70 disabled:cursor-not-allowed",children:[t?e.jsx(F,{className:"w-5 h-5 mr-3 animate-spin"}):e.jsx("svg",{className:"w-5 h-5 mr-3",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M17.05 20.28c-.98.95-2.05.88-3.08.4-1.09-.5-2.08-.52-3.2 0-.83.38-1.54.35-2.31-.04A9.64 9.64 0 014.85 16C2.46 11.18 4.24 4.71 8.6 4.23c1.61-.19 2.97.83 3.96.91 1.22-.3 2.37-1.01 3.82-.85 1.79.2 3.11.94 3.93 2.16-3.36 2.11-2.66 6.27.54 7.61-.54 1.61-1.24 3.18-2.8 5.22zm-3.15-17.4c-.1 1.33-.56 2.44-1.34 3.38-.82.94-1.98 1.59-3.22 1.45C9.04 5.86 10.15 3.36 12 3c.07 0 .13-.04.19-.06.01-.04.02-.08.02-.12l-.31.06z"})}),t?"Signing in...":"Log in with Apple"]})]})},de=()=>{var C;const r=G(),[a,t]=c.useState(((C=r.state)==null?void 0:C.showSignup)!==!0),[n,o]=c.useState(!1),[u,l]=c.useState(""),[i,d]=c.useState(""),[m,x]=c.useState(""),[h,k]=c.useState(!1),[b,f]=c.useState(!1),v=q(),{user:j,loading:p}=A(),y=()=>{const S=new URLSearchParams(r.search).get("returnTo");return S?decodeURIComponent(S):"/dashboard"};return c.useEffect(()=>{if(j&&!b&&!p){console.log("User detected in Login page, redirecting to dashboard or return URL"),f(!0);const N=y();console.log("Redirecting to:",N),setTimeout(()=>{window.location.href=N},100),g.success("Welcome back!")}},[j,v,b,p,r]),e.jsxs("div",{className:"min-h-screen flex flex-col lg:flex-row",children:[e.jsxs("div",{className:"hidden lg:flex lg:w-1/2 bg-primary/10 flex-col items-center justify-center p-12 relative overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/5 to-primary/30 z-0"}),e.jsx("div",{className:"absolute top-0 left-0 w-full h-full",children:Array.from({length:5}).map((N,S)=>e.jsx(s.div,{className:"absolute bg-primary/10 rounded-full",style:{width:Math.random()*300+50,height:Math.random()*300+50,top:`${Math.random()*100}%`,left:`${Math.random()*100}%`,filter:"blur(60px)"},animate:{y:[0,Math.random()*40-20,0],x:[0,Math.random()*40-20,0],opacity:[.3,.6,.3]},transition:{duration:8+Math.random()*5,repeat:1/0,ease:"easeInOut"}},S))}),e.jsxs("div",{className:"relative z-10 text-center space-y-8",children:[e.jsx(s.div,{initial:{scale:0},animate:{scale:1},transition:{duration:.6,type:"spring"},className:"flex justify-center",children:e.jsx("div",{className:"p-4 rounded-full bg-primary/20 backdrop-blur-md",children:e.jsx($,{className:"w-24 h-24 text-primary"})})}),e.jsxs(s.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3,duration:.8},children:[e.jsx("h1",{className:"text-4xl font-bold text-foreground mb-4",children:"e4mi"}),e.jsx("p",{className:"text-xl text-muted-foreground",children:"eLearning for Medical Imaging Workforce"})]}),e.jsx(s.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.6,duration:.8},className:"max-w-md mx-auto mt-8",children:e.jsx("p",{className:"text-muted-foreground text-md",children:"Join our platform to enhance your medical imaging skills with evidence-based learning resources."})})]})]}),e.jsx("div",{className:"w-full lg:w-1/2 flex items-center justify-center p-4 sm:p-6 lg:p-8",children:e.jsx(s.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"w-full max-w-md",children:e.jsxs("div",{className:"bg-card p-8 rounded-2xl shadow-sm border border-border space-y-6",children:[e.jsxs(W,{to:"/",className:"inline-flex items-center px-3 py-1.5 bg-card border border-border rounded-md text-sm font-medium text-foreground hover:bg-primary hover:text-white hover:border-primary transition-colors",children:[e.jsx(H,{className:"w-4 h-4 mr-1.5"}),"Back to Home"]}),!n&&e.jsxs("div",{children:[e.jsx(s.h2,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"text-center text-3xl font-bold tracking-tight text-foreground mb-2",children:a?"Welcome Back":"Create Account"}),e.jsx(s.p,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{delay:.1},className:"text-center text-sm text-muted-foreground",children:a?"Sign in to continue your learning journey":"Join our medical imaging e-learning platform"})]}),e.jsx("div",{className:"space-y-6",children:n?e.jsx(ee,{onBack:()=>o(!1)}):e.jsxs(e.Fragment,{children:[e.jsx(se,{}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 flex items-center",children:e.jsx("div",{className:"w-full border-t border-border"})}),e.jsx("div",{className:"relative flex justify-center text-sm",children:e.jsx("span",{className:"px-4 bg-card text-muted-foreground",children:"OR"})})]}),e.jsx(s.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},children:a?e.jsx(K,{email:u,setEmail:l,password:i,setPassword:d,isSubmitting:h,onForgotPassword:()=>o(!0)}):e.jsx(X,{email:u,setEmail:l,password:i,setPassword:d,name:m,setName:x,isSubmitting:h})}),e.jsx(s.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},className:"text-center mt-6 pt-4 border-t border-border",children:e.jsxs("p",{className:"text-sm text-foreground flex flex-wrap justify-center items-center gap-2",children:[a?"Don't have an account yet? ":"Already have an account? ",e.jsx("button",{type:"button",className:"px-3 py-1.5 bg-primary border border-primary rounded-md font-regular text-white hover:bg-primary/90 transition-colors",onClick:()=>{t(!a),l(""),d(""),x("")},children:a?"Sign Up":"Sign In"})]})})]})})]})})})]})};export{de as default};
