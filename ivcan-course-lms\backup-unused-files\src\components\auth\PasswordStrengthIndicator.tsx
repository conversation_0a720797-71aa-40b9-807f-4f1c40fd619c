import React from 'react';
import { getPasswordStrength, PasswordStrength, getPasswordStrengthColor } from '@/utils/passwordUtils';

interface PasswordStrengthIndicatorProps {
  password: string;
  className?: string;
}

/**
 * A component that displays a password strength indicator
 */
const PasswordStrengthIndicator: React.FC<PasswordStrengthIndicatorProps> = ({ 
  password,
  className = ''
}) => {
  if (!password) {
    return null;
  }
  
  const strength = getPasswordStrength(password);
  const color = getPasswordStrengthColor(strength);
  
  // Calculate the width of the strength bar
  const getBarWidth = () => {
    switch (strength) {
      case PasswordStrength.STRONG:
        return '100%';
      case PasswordStrength.MEDIUM:
        return '66%';
      case PasswordStrength.WEAK:
        return '33%';
      default:
        return '0%';
    }
  };
  
  // Get a message based on the strength
  const getMessage = () => {
    switch (strength) {
      case PasswordStrength.STRONG:
        return 'Strong password';
      case PasswordStrength.MEDIUM:
        return 'Medium strength - add special characters';
      case PasswordStrength.WEAK:
        return 'Weak password - use at least 8 characters';
      default:
        return '';
    }
  };
  
  return (
    <div className={`mt-1 ${className}`}>
      <div className="h-1 w-full bg-gray-200 rounded-full overflow-hidden">
        <div 
          className="h-full rounded-full transition-all duration-300"
          style={{ 
            width: getBarWidth(),
            backgroundColor: color
          }}
        />
      </div>
      <p className="text-xs mt-1" style={{ color }}>
        {getMessage()}
      </p>
    </div>
  );
};

export default PasswordStrengthIndicator;
