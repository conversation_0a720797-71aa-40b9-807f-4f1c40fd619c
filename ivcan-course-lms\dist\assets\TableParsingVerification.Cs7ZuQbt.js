import{j as e}from"./vendor-react.BcAa1DKr.js";import{M as r}from"./markdown-preview.Bw0U-NJA.js";import{m as l}from"./content-converter.L-GziWIP.js";import"./vendor.DQpuTRuB.js";import"./index.BLDhDn0D.js";import"./vendor-supabase.sufZ44-y.js";const a=`#### Types of IV cannula

| SIZES | COLOR          | FLOW RATE     | INDICATIONS                     |
|-------|----------------|---------------|---------------------------------|
| 14 G  | Orange         | Faster flow   | Trauma, surgical procedures     |
| 16 G  | Grey           | Faster flow   | Trauma, surgical procedures     |
| 18 G  | Green          | Medium flow   | Trauma, quick blood transfusion |
| 20 G  | Pink           | Medium flow   | Normal IV or blood transfusion  |
| 22 G  | Blue           | Slower flow   | Children, older adults          |
| 24 G  | Yellow         | Slower flow rate | Neonates, children, old elderly |
| 26 G  | Purple/violet  | Slower flow rate | Neonates                        |

This text should appear after the table.`;function m(){const s=l(a);return e.jsx("div",{className:"container mx-auto p-8 max-w-6xl",children:e.jsxs("div",{className:"bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6",children:[e.jsx("h1",{className:"text-3xl font-bold mb-6",children:"Table Parsing Verification"}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Raw Markdown (from Database)"}),e.jsx("pre",{className:"bg-gray-100 dark:bg-gray-800 p-4 rounded-md text-sm overflow-x-auto whitespace-pre-wrap",children:a})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Rendered Output"}),e.jsx("div",{className:"border border-gray-200 dark:border-gray-700 rounded-md p-4",children:e.jsx(r,{content:a,className:"professional-prose"})})]})]}),e.jsxs("div",{className:"mt-8",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Direct HTML Output (Debug)"}),e.jsx("div",{className:"bg-gray-100 dark:bg-gray-800 p-4 rounded-md",children:e.jsx("pre",{className:"text-sm overflow-x-auto whitespace-pre-wrap",children:s})})]}),e.jsxs("div",{className:"mt-8",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Verification Checklist"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:s.includes("<table>")?"text-green-600":"text-red-600",children:s.includes("<table>")?"✅":"❌"}),e.jsx("span",{children:"Table HTML tags are generated"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:s.includes("<thead>")?"text-green-600":"text-red-600",children:s.includes("<thead>")?"✅":"❌"}),e.jsx("span",{children:"Table header is properly structured"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:s.includes("<tbody>")?"text-green-600":"text-red-600",children:s.includes("<tbody>")?"✅":"❌"}),e.jsx("span",{children:"Table body is properly structured"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:s.includes("| SIZES |")?"text-red-600":"text-green-600",children:s.includes("| SIZES |")?"❌":"✅"}),e.jsx("span",{children:"Raw markdown pipes are converted (not visible in output)"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:s.includes("Orange")&&s.includes("<td>")?"text-green-600":"text-red-600",children:s.includes("Orange")&&s.includes("<td>")?"✅":"❌"}),e.jsx("span",{children:"Table data is properly wrapped in TD tags"})]})]})]})]})})}export{m as default};
