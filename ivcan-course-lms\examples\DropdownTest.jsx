import React from 'react';
import { markdownToHtml } from '../src/lib/content-converter';
import fs from 'fs';
import path from 'path';

const DropdownTest = () => {
  // In a real component, you'd fetch this from an API or props
  const markdownContent = fs.readFileSync(
    path.join(process.cwd(), 'examples', 'dropdown-example.md'),
    'utf-8'
  );
  
  const htmlContent = markdownToHtml(markdownContent);
  
  return (
    <div className="prose max-w-3xl mx-auto p-6">
      <div dangerouslySetInnerHTML={{ __html: htmlContent }} />
    </div>
  );
};

export default DropdownTest; 