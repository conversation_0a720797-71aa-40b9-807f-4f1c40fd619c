
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

/**
 * Delete a course and all associated content
 */
export const deleteCourse = async (courseId: string): Promise<boolean> => {
  try {
    // First, get all modules for this course
    const { data: modules, error: modulesError } = await supabase
      .from('modules')
      .select('id')
      .eq('course_id', courseId);

    if (modulesError) {
      console.error('Error fetching modules for deletion:', modulesError);
      toast.error('Failed to delete course: Could not fetch modules');
      return false;
    }

    // For each module, delete all lessons
    if (modules && modules.length > 0) {
      const moduleIds = modules.map(module => module.id);
      
      const { error: lessonsError } = await supabase
        .from('lessons')
        .delete()
        .in('module_id', moduleIds);

      if (lessonsError) {
        console.error('Error deleting lessons:', lessonsError);
        toast.error('Failed to delete course: Could not delete lessons');
        return false;
      }
      
      // Delete the modules
      const { error: modulesDeleteError } = await supabase
        .from('modules')
        .delete()
        .in('id', moduleIds);

      if (modulesDeleteError) {
        console.error('Error deleting modules:', modulesDeleteError);
        toast.error('Failed to delete course: Could not delete modules');
        return false;
      }
    }

    // Delete user course progress related to this course
    const { error: progressError } = await supabase
      .from('user_course_progress')
      .delete()
      .eq('course_id', courseId);

    if (progressError) {
      console.error('Error deleting course progress:', progressError);
      // Continue anyway, not critical
    }

    // Finally, delete the course itself
    const { error: courseError } = await supabase
      .from('courses')
      .delete()
      .eq('id', courseId);

    if (courseError) {
      console.error('Error deleting course:', courseError);
      toast.error('Failed to delete course: Could not delete the course entry');
      return false;
    }

    toast.success('Course and all its content successfully deleted');
    return true;
  } catch (error: any) {
    console.error('Unexpected error in deleteCourse:', error);
    toast.error(`Failed to delete course: ${error.message}`);
    return false;
  }
};

/**
 * Override course data - for administrative fixes
 */
export const overrideCourse = async (courseId: string, courseData: any): Promise<boolean> => {
  try {
    if (!courseId) {
      toast.error('Course ID is required for override operation');
      return false;
    }
    
    const { error } = await supabase
      .from('courses')
      .update({
        ...courseData,
        updated_at: new Date().toISOString()
      })
      .eq('id', courseId);
    
    if (error) {
      console.error('Error in course override operation:', error);
      toast.error(`Failed to override course data: ${error.message}`);
      return false;
    }
    
    toast.success('Course data successfully overridden');
    console.log('Course override performed:', { courseId, data: courseData });
    return true;
  } catch (error: any) {
    console.error('Unexpected error in overrideCourse:', error);
    toast.error(`Failed to override course: ${error.message}`);
    return false;
  }
};
