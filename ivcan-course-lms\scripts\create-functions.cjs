/**
 * Create SQL Functions for Health Check and RLS
 * This script creates the missing SQL functions using simpler syntax
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

// Create Supabase client with service role key for admin operations
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function createHealthCheckFunction() {
  console.log('\n🔧 Creating health check function...');
  
  try {
    // Use a simple approach without dollar quoting
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: `CREATE OR REPLACE FUNCTION public.health_check()
RETURNS jsonb
LANGUAGE SQL
SECURITY DEFINER
AS 'SELECT jsonb_build_object(''status'', ''ok'', ''timestamp'', now(), ''version'', current_setting(''server_version''));'`
    });
    
    if (error) {
      console.error(`❌ Error: ${error.message}`);
      return false;
    }
    
    // Grant permissions
    const { error: grantError } = await supabase.rpc('exec_sql', {
      sql: 'GRANT EXECUTE ON FUNCTION public.health_check() TO anon'
    });
    
    if (grantError) {
      console.error(`❌ Grant Error: ${grantError.message}`);
      return false;
    }
    
    console.log('✅ Health check function created successfully');
    return true;
  } catch (err) {
    console.error(`❌ Error: ${err.message}`);
    return false;
  }
}

async function createRLSHelperFunction() {
  console.log('\n🔧 Creating RLS helper function...');
  
  try {
    // Use a simple approach without dollar quoting
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: `CREATE OR REPLACE FUNCTION public.get_tables_with_rls()
RETURNS TABLE (table_schema text, table_name text, rls_enabled boolean)
LANGUAGE SQL
SECURITY DEFINER
AS 'SELECT n.nspname AS table_schema, c.relname AS table_name, c.relrowsecurity AS rls_enabled FROM pg_class c JOIN pg_namespace n ON n.oid = c.relnamespace WHERE c.relkind = ''r'' AND n.nspname = ''public'' ORDER BY table_schema, table_name;'`
    });
    
    if (error) {
      console.error(`❌ Error: ${error.message}`);
      return false;
    }
    
    // Grant permissions
    const { error: grantError } = await supabase.rpc('exec_sql', {
      sql: 'GRANT EXECUTE ON FUNCTION public.get_tables_with_rls() TO service_role'
    });
    
    if (grantError) {
      console.error(`❌ Grant Error: ${grantError.message}`);
      return false;
    }
    
    console.log('✅ RLS helper function created successfully');
    return true;
  } catch (err) {
    console.error(`❌ Error: ${err.message}`);
    return false;
  }
}

async function testFunctions() {
  console.log('\n🔧 Testing functions...');
  
  try {
    // Test health check function
    const { data: healthData, error: healthError } = await supabase.rpc('health_check');
    if (healthError) {
      console.error(`❌ Health check test failed: ${healthError.message}`);
    } else {
      console.log('✅ Health check function working:', healthData);
    }
    
    // Test RLS helper function
    const { data: rlsData, error: rlsError } = await supabase.rpc('get_tables_with_rls');
    if (rlsError) {
      console.error(`❌ RLS helper test failed: ${rlsError.message}`);
    } else {
      console.log('✅ RLS helper function working, found', rlsData?.length || 0, 'tables');
    }
    
  } catch (err) {
    console.error(`❌ Test Error: ${err.message}`);
  }
}

async function main() {
  console.log('🚀 Creating SQL functions...');
  console.log(`📍 Supabase URL: ${SUPABASE_URL}`);
  
  let allSuccess = true;

  // Step 1: Create health check function
  if (!await createHealthCheckFunction()) {
    allSuccess = false;
  }

  // Step 2: Create RLS helper function
  if (!await createRLSHelperFunction()) {
    allSuccess = false;
  }

  // Step 3: Test the functions
  await testFunctions();

  console.log('\n' + '='.repeat(50));
  if (allSuccess) {
    console.log('🎉 All SQL functions created successfully!');
    console.log('✅ Database schema fixes are now complete');
  } else {
    console.log('⚠️  Some functions failed to create');
  }
  console.log('='.repeat(50));
}

main().catch(console.error);
