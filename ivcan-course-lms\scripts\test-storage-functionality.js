// Test script for storage functionality
// This script tests image upload, download, and display functionality

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Supabase configuration
const SUPABASE_URL = "https://jibspqwieubavucdtccv.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMxMjUzNzIsImV4cCI6MjA1ODcwMTM3Mn0.H1cvGLXWvIMibv8dYsYIvY37IRoVR80HyeF1wM93IUI";

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Expected buckets
const EXPECTED_BUCKETS = [
  'course-images',
  'avatars',
  'app-uploads',
  'uploads',
  'default-bucket',
  'module-images'
];

async function testStorageBuckets() {
  console.log('=== Testing Storage Buckets ===');

  try {
    const { data: buckets, error } = await supabase.storage.listBuckets();

    if (error) {
      console.error('❌ Error listing buckets:', error);
      return false;
    }

    console.log('✅ Successfully connected to storage');
    console.log(`Found ${buckets.length} buckets:`);

    buckets.forEach(bucket => {
      console.log(`  - ${bucket.name} (public: ${bucket.public})`);
    });

    // Check if all expected buckets exist
    const missingBuckets = EXPECTED_BUCKETS.filter(
      expectedBucket => !buckets.some(bucket => bucket.name === expectedBucket)
    );

    if (missingBuckets.length > 0) {
      console.log('⚠️ Missing buckets:', missingBuckets);
      return false;
    }

    console.log('✅ All expected buckets are present');
    return true;
  } catch (error) {
    console.error('❌ Unexpected error:', error);
    return false;
  }
}

async function testImageUpload() {
  console.log('\n=== Testing Image Upload ===');

  try {
    // Create a simple test image (1x1 pixel PNG)
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00,
      0xFF, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, 0xE2, 0x21, 0xBC, 0x33,
      0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);

    const fileName = `test-image-${Date.now()}.png`;
    const filePath = `test/${fileName}`;

    console.log(`Uploading test image: ${filePath}`);

    // Test upload to multiple buckets
    const bucketsToTest = ['course-images', 'app-uploads', 'module-images'];

    for (const bucketName of bucketsToTest) {
      console.log(`\n--- Testing ${bucketName} bucket ---`);

      const { data, error } = await supabase.storage
        .from(bucketName)
        .upload(filePath, testImageBuffer, {
          contentType: 'image/png',
          cacheControl: '3600',
          upsert: true
        });

      if (error) {
        console.error(`❌ Upload to ${bucketName} failed:`, error);
        continue;
      }

      console.log(`✅ Upload to ${bucketName} successful:`, data.path);

      // Test getting public URL
      const { data: { publicUrl } } = supabase.storage
        .from(bucketName)
        .getPublicUrl(filePath);

      console.log(`✅ Public URL for ${bucketName}:`, publicUrl);

      // Test download
      const { data: downloadData, error: downloadError } = await supabase.storage
        .from(bucketName)
        .download(filePath);

      if (downloadError) {
        console.error(`❌ Download from ${bucketName} failed:`, downloadError);
      } else {
        console.log(`✅ Download from ${bucketName} successful, size:`, downloadData.size, 'bytes');
      }

      // Clean up test file
      const { error: deleteError } = await supabase.storage
        .from(bucketName)
        .remove([filePath]);

      if (deleteError) {
        console.warn(`⚠️ Failed to clean up test file from ${bucketName}:`, deleteError);
      } else {
        console.log(`✅ Test file cleaned up from ${bucketName}`);
      }
    }

    return true;
  } catch (error) {
    console.error('❌ Unexpected error during upload test:', error);
    return false;
  }
}

async function testImageDisplay() {
  console.log('\n=== Testing Image Display URLs ===');

  try {
    // Test different URL formats
    const testPaths = [
      'course-images/test/sample.jpg',
      'avatars/user/profile.png',
      'app-uploads/documents/file.pdf'
    ];

    testPaths.forEach(testPath => {
      const { data: { publicUrl } } = supabase.storage
        .from(testPath.split('/')[0])
        .getPublicUrl(testPath.substring(testPath.indexOf('/') + 1));

      console.log(`✅ URL for ${testPath}: ${publicUrl}`);
    });

    return true;
  } catch (error) {
    console.error('❌ Error generating URLs:', error);
    return false;
  }
}

async function runStorageTests() {
  console.log('🧪 Starting Storage Functionality Tests\n');

  const results = {
    buckets: await testStorageBuckets(),
    upload: await testImageUpload(),
    display: await testImageDisplay()
  };

  console.log('\n=== Test Results Summary ===');
  console.log(`Storage Buckets: ${results.buckets ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Image Upload: ${results.upload ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Image Display: ${results.display ? '✅ PASS' : '❌ FAIL'}`);

  const allPassed = Object.values(results).every(result => result === true);

  if (allPassed) {
    console.log('\n🎉 All storage tests passed! Image handling is working correctly.');
  } else {
    console.log('\n⚠️ Some storage tests failed. Check the errors above.');
  }

  return allPassed;
}

// Run the tests
runStorageTests().catch(console.error);
