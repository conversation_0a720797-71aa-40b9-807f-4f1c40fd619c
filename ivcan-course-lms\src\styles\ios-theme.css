/* iOS Theme Styles with Red Color Scheme */

/* iOS-specific variables */
:root {
  /* iOS color palette */
  --ios-blue: 212 100% 50%;      /* #007AFF */
  --ios-red: 0 70% 50%;          /* #E63946 - Primary red */
  --ios-indigo: 231 100% 60%;    /* #5856D6 */
  --ios-orange: 14 100% 52%;     /* #FF9500 */
  --ios-pink: 349 100% 58%;      /* #FF2D55 */
  --ios-purple: 292 100% 50%;    /* #AF52DE */
  /* Legacy green color removed */
  --ios-teal: 180 100% 39%;      /* #5AC8FA */
  --ios-yellow: 45 100% 50%;     /* #FFCC00 */

  /* iOS UI properties */
  --ios-radius-sm: 8px;
  --ios-radius-md: 12px;
  --ios-radius-lg: 16px;
  --ios-radius-xl: 22px;
  --ios-radius-full: 9999px;

  /* iOS shadows */
  --ios-shadow-sm: 0 2px 6px rgba(0, 0, 0, 0.1);
  --ios-shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
  --ios-shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.06);

  /* Modern fonts */
  --ios-font-family: 'Poppins', system-ui, sans-serif;

  /* Override existing variables with iOS values */
  --radius: 12px;
  --primary: var(--ios-red);

  /* iOS-specific transitions */
  --ios-transition-fast: 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ios-transition-medium: 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ios-transition-slow: 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  /* iOS-style UI Variables */
  --ios-background: #F2F2F7;
  --ios-text: #000000;
  --ios-secondary-text: #6C6C70;
  --ios-border: #C5C5C7;
  --ios-primary: #007AFF;
  --ios-secondary: #5856D6;
  --ios-success: #34C759;
  --ios-warning: #FF9500;
  --ios-danger: #FF3B30;
  --ios-radius: 10px;
}

/* iOS-specific dark mode overrides */
.dark {
  --ios-shadow-sm: 0 2px 6px rgba(0, 0, 0, 0.2);
  --ios-shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --ios-shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.12);
}

/* iOS-specific theme */
.ios {
  /* iOS-style tap highlights */
  -webkit-tap-highlight-color: transparent;

  /* iOS-style momentum scrolling */
  -webkit-overflow-scrolling: touch;

  /* iOS-style buttons */
  --ios-button-height: 44px;
  --ios-button-padding: 16px;
  --ios-button-radius: 10px;
  --ios-button-active-scale: 0.97;

  /* iOS-style inputs */
  --ios-input-height: 44px;
  --ios-input-radius: 10px;

  /* iOS-style safe areas */
  --safe-area-inset-top: env(safe-area-inset-top);
  --safe-area-inset-bottom: env(safe-area-inset-bottom);
}

/* iOS Typography */
body {
  font-family: var(--ios-font-family);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--ios-font-family);
  font-weight: 600;
}

/* iOS-style buttons - Simplified for unified button system */
.ios-button {
  border-radius: var(--ios-button-radius);
  font-weight: 500;
  transition: transform 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  touch-action: manipulation;
  user-select: none;
  -webkit-user-select: none;
  min-height: var(--ios-button-height);
  padding: 0 var(--ios-button-padding);
  box-shadow: none !important;
}

.ios-button:active {
  transform: scale(var(--ios-button-active-scale));
}

/* iOS-style cards */
.ios-card {
  border-radius: var(--ios-radius-lg);
  border: none;
  box-shadow: var(--ios-shadow-sm);
  overflow: hidden;
  transition: transform var(--ios-transition-medium), box-shadow var(--ios-transition-medium);
  border-radius: var(--ios-button-radius);
  overflow: hidden;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.ios-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--ios-shadow-md);
}

/* iOS-style inputs */
.ios-input {
  border-radius: var(--ios-radius-md);
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: all var(--ios-transition-fast);
  min-height: var(--ios-input-height);
  border-radius: var(--ios-input-radius);
  padding: 12px 16px;
  font-size: 16px;
  -webkit-appearance: none;
  appearance: none;
}

.ios-input:focus {
  border-color: hsl(var(--ios-red));
  box-shadow: 0 0 0 2px rgba(230, 57, 70, 0.2);
}

/* iOS-style switches */
.ios-switch {
  position: relative;
  width: 52px;
  height: 32px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  transition: background-color var(--ios-transition-fast);
}

.ios-switch::after {
  content: "";
  position: absolute;
  top: 2px;
  left: 2px;
  width: 28px;
  height: 28px;
  background-color: white;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform var(--ios-transition-fast);
}

.ios-switch[data-state="checked"] {
  background-color: hsl(var(--ios-red));
}

.ios-switch[data-state="checked"]::after {
  transform: translateX(20px);
}

/* iOS-style navigation */
.ios-nav {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  height: calc(44px + var(--safe-area-inset-top));
  padding-top: var(--safe-area-inset-top);
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  padding-left: 16px;
  padding-right: 16px;
}

.dark .ios-nav {
  background-color: rgba(30, 30, 30, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* iOS-style bottom tab bar */
.ios-tab-bar {
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding: 0.75rem 0;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 50;
  height: calc(49px + var(--safe-area-inset-bottom));
  padding-bottom: var(--safe-area-inset-bottom);
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-top: 0.5px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.dark .ios-tab-bar {
  background-color: rgba(30, 30, 30, 0.8);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.ios-tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.75rem;
  transition: color var(--ios-transition-fast);
}

.ios-tab-item.active {
  color: hsl(var(--ios-red));
}

.dark .ios-tab-item {
  color: rgba(255, 255, 255, 0.6);
}

.dark .ios-tab-item.active {
  color: hsl(var(--ios-red));
}

.ios-tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4px 0;
  color: rgba(0, 0, 0, 0.6);
  font-size: 10px;
  touch-action: manipulation;
}

.ios-tab[data-state="active"] {
  color: var(--primary);
}

/* iOS-style badges */
.ios-badge {
  background-color: hsl(var(--ios-red));
  color: white;
  border-radius: var(--ios-radius-full);
  padding: 0.125rem 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
}

/* iOS-style lists */
.ios-list {
  border-radius: var(--ios-radius-lg);
  overflow: hidden;
  background-color: white;
  margin: 0;
  padding: 0;
  list-style: none;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: var(--ios-button-radius);
}

.dark .ios-list {
  background-color: rgba(30, 30, 30, 0.8);
}

.ios-list-item {
  padding: 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: var(--ios-button-height);
  padding: 12px 16px;
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  touch-action: manipulation;
}

.dark .ios-list-item {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.ios-list-item:last-child {
  border-bottom: none;
}

.ios-list-item:active {
  background: rgba(0, 0, 0, 0.05);
}

/* iOS-style segmented control */
.ios-segmented-control {
  display: flex;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: var(--ios-radius-full);
  padding: 0.25rem;
}

.ios-segment {
  padding: 0.5rem 1rem;
  border-radius: var(--ios-radius-full);
  font-size: 0.875rem;
  font-weight: 500;
  transition: all var(--ios-transition-fast);
}

.ios-segment.active {
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.ios-segments {
  display: flex;
  background: rgba(0, 0, 0, 0.05);
  padding: 2px;
  border-radius: 8px;
  margin: 8px 0;
}

.ios-segment {
  flex: 1;
  text-align: center;
  padding: 6px 12px;
  font-size: 14px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.ios-segment[data-state="active"] {
  background: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* iOS-style alerts and modals */
.ios-alert {
  border-radius: var(--ios-radius-lg);
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: var(--ios-shadow-lg);
}

.dark .ios-alert {
  background-color: rgba(30, 30, 30, 0.8);
}

.ios-alert-title {
  text-align: center;
  font-weight: 600;
  font-size: 1.125rem;
  padding: 1.25rem 1.25rem 0.75rem;
}

.ios-alert-message {
  text-align: center;
  padding: 0 1.25rem 1.25rem;
  color: rgba(0, 0, 0, 0.6);
}

.dark .ios-alert-message {
  color: rgba(255, 255, 255, 0.6);
}

.ios-alert-actions {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
}

.dark .ios-alert-actions {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.ios-alert-button {
  flex: 1;
  padding: 0.875rem;
  text-align: center;
  font-weight: 500;
  transition: background-color var(--ios-transition-fast);
}

.ios-alert-button:not(:last-child) {
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.dark .ios-alert-button:not(:last-child) {
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.ios-alert-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.dark .ios-alert-button:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.ios-alert-button.primary {
  color: hsl(var(--ios-red));
  font-weight: 600;
}

.ios-alert-button.destructive {
  color: hsl(var(--ios-red));
}

/* iOS-style toast notifications */
.ios-toast {
  border-radius: var(--ios-radius-lg);
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: var(--ios-shadow-md);
  padding: 1rem;
  max-width: 24rem;
  margin: 0 auto;
}

.dark .ios-toast {
  background-color: rgba(30, 30, 30, 0.8);
}

/* iOS-style loading indicators */
.ios-spinner {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-top-color: hsl(var(--ios-red));
  animation: ios-spin 0.8s linear infinite;
}

@keyframes ios-spin {
  to {
    transform: rotate(360deg);
  }
}

/* iOS-style pull-to-refresh */
.ios-ptr {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
  margin-top: -60px;
  transition: margin-top var(--ios-transition-medium);
}

.ios-ptr.active {
  margin-top: 0;
}

/* iOS-style scrollbars */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

.dark ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* iOS-style bottom sheet */
.ios-sheet {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 12px 12px 0 0;
  padding-bottom: var(--safe-area-inset-bottom);
}

.ios-sheet-handle {
  width: 36px;
  height: 5px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2.5px;
  margin: 8px auto;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .ios-card,
  .ios-list,
  .ios-sheet,
  .ios-nav,
  .ios-tab-bar {
    background: rgba(0, 0, 0, 0.7);
  }

  .ios-list-item {
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .ios-list-item:active {
    background: rgba(255, 255, 255, 0.05);
  }

  .ios-segments {
    background: rgba(255, 255, 255, 0.1);
  }

  .ios-segment[data-state="active"] {
    background: rgba(0, 0, 0, 0.5);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  .ios-nav,
  .ios-tab-bar {
    border-color: rgba(255, 255, 255, 0.1);
  }

  .ios-tab {
    color: rgba(255, 255, 255, 0.6);
  }
}
