import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from './ui/accordion';
import { Clock, FileText, LockIcon, CheckIcon, ClipboardCheck } from 'lucide-react';
import { Lesson } from '../services/courseService';
import { useUserRole } from '@/hooks/useUserRole';
import LessonItem from './module/LessonItem';
import DeleteLessonDialog from './module/DeleteLessonDialog';
import { useToast } from '@/hooks/use-toast';
import { useQueryClient } from '@tanstack/react-query';
import { useIsMobile } from '@/hooks/use-mobile';
import { useScreenSize } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import { Button } from './ui/button';
import { useQuery } from '@tanstack/react-query';
import { getModuleTestByType, getUserTestResponse } from '@/services/module-test/moduleTestService';
import { useAuth } from '@/context/AuthContext';
import ModuleTest from './module/ModuleTest';

interface ModuleSectionProps {
  moduleNumber: number;
  title: string;
  lessons: Lesson[];
  isLocked: boolean;
  isCompleted: boolean;
  isActive?: boolean;
  courseId?: string;
  moduleId?: string;
  isPreview?: boolean;
}

// Function to get module color based on module number
const getModuleColor = (moduleNumber: number) => {
  const colors = [
    'bg-blue-100 text-blue-700 border-blue-200',
    'bg-green-100 text-green-700 border-green-200',
    'bg-amber-100 text-amber-700 border-amber-200',
    'bg-purple-100 text-purple-700 border-purple-200',
    'bg-pink-100 text-pink-700 border-pink-200',
    'bg-cyan-100 text-cyan-700 border-cyan-200',
  ];
  return colors[(moduleNumber - 1) % colors.length];
};

const ModuleSection: React.FC<ModuleSectionProps> = ({
  moduleNumber,
  title,
  lessons,
  isLocked,
  isCompleted,
  isActive = false,
  courseId = 'design-strategy',
  moduleId,
  isPreview = false
}) => {
  const { isTeacher } = useUserRole();
  const { user } = useAuth();
  const isMobile = useIsMobile();
  const screenSize = useScreenSize();
  const isVerySmall = screenSize === 'xs';
  const isLargeScreen = screenSize === 'lg' || screenSize === 'xl' || screenSize === '2xl';
  const [editingLessonId, setEditingLessonId] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [lessonToDelete, setLessonToDelete] = useState<string | null>(null);
  const [showPreTest, setShowPreTest] = useState(false);
  const [showPostTest, setShowPostTest] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const moduleColor = getModuleColor(moduleNumber);

  const handleDeleteLesson = (id: string) => {
    setLessonToDelete(id);
    setDeleteDialogOpen(true);
  };

  // Calculate module duration
  const getTotalDuration = () => {
    return lessons.reduce((acc, lesson) => {
      // Extract minutes from duration strings like "15:00 mins" or "25:00 mins"
      const minutes = parseInt(lesson.duration?.split(':')[0] || '0');
      return acc + (isNaN(minutes) ? 0 : minutes);
    }, 0);
  };

  // Get preview lessons
  const previewLessons = isPreview ? lessons.slice(0, 3) : lessons;

  // Fetch pre-test if moduleId is available
  const { data: preTest } = useQuery({
    queryKey: ['module-pre-test', moduleId],
    queryFn: async () => {
      if (!moduleId) return null;
      return getModuleTestByType(moduleId, 'pre_test');
    },
    enabled: !!moduleId && !isPreview
  });

  // Fetch post-test if moduleId is available
  const { data: postTest } = useQuery({
    queryKey: ['module-post-test', moduleId],
    queryFn: async () => {
      if (!moduleId) return null;
      return getModuleTestByType(moduleId, 'post_test');
    },
    enabled: !!moduleId && !isPreview
  });

  // Check if user has completed the pre-test
  const { data: preTestResponse } = useQuery({
    queryKey: ['pre-test-response', moduleId, user?.id],
    queryFn: async () => {
      if (!moduleId || !user?.id || !preTest) return null;
      return getUserTestResponse(preTest.id, user.id);
    },
    enabled: !!moduleId && !!user?.id && !!preTest && !isPreview
  });

  // Check if user has completed the post-test
  const { data: postTestResponse } = useQuery({
    queryKey: ['post-test-response', moduleId, user?.id],
    queryFn: async () => {
      if (!moduleId || !user?.id || !postTest) return null;
      return getUserTestResponse(postTest.id, user.id);
    },
    enabled: !!moduleId && !!user?.id && !!postTest && !isPreview
  });

  // Handle pre-test completion
  const handlePreTestComplete = () => {
    setShowPreTest(false);
    // Refresh the pre-test response
    queryClient.invalidateQueries({ queryKey: ['pre-test-response', moduleId, user?.id] });
    toast({
      title: 'Pre-Test Completed',
      description: 'You can now proceed with the lessons in this module.',
    });
  };

  // Handle post-test completion
  const handlePostTestComplete = () => {
    setShowPostTest(false);
    // Refresh the post-test response
    queryClient.invalidateQueries({ queryKey: ['post-test-response', moduleId, user?.id] });
    toast({
      title: 'Post-Test Completed',
      description: 'Thank you for completing this module.',
    });
  };

  // Determine if pre/post tests should be shown
  useEffect(() => {
    if (!isPreview && !isLocked) {
      // Show pre-test if it exists and user hasn't completed it
      if (preTest && !preTestResponse) {
        setShowPreTest(true);
      } else {
        setShowPreTest(false);
      }

      // Show post-test if it exists, user hasn't completed it, and all lessons in module are completed
      const allLessonsCompleted = lessons.every(lesson => lesson.completed);
      if (postTest && !postTestResponse && allLessonsCompleted) {
        setShowPostTest(true);
      } else {
        setShowPostTest(false);
      }
    }
  }, [preTest, preTestResponse, postTest, postTestResponse, lessons, isLocked, isPreview]);

  // If pre-test needs to be shown in full-screen mode, display it instead of lessons
  if (showPreTest && preTest && !isTeacher) {
    return (
      <div className="py-4">
        <div className="mb-4">
          <h3 className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${moduleColor}`}>
            Module {moduleNumber}: {title}
          </h3>
        </div>
        <ModuleTest
          test={preTest}
          onComplete={handlePreTestComplete}
          courseId={courseId}
          showNextLessonButton={true}
        />
      </div>
    );
  }

  // If post-test needs to be shown in full-screen mode, display it after lessons
  if (showPostTest && postTest && !isTeacher) {
    return (
      <div className="py-4">
        <div className="mb-4">
          <h3 className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${moduleColor}`}>
            Module {moduleNumber}: {title}
          </h3>
        </div>
        <ModuleTest
          test={postTest}
          onComplete={handlePostTestComplete}
          courseId={courseId}
          showNextLessonButton={false}
        />
      </div>
    );
  }

  return (
    <div>
      <Accordion type="single" collapsible className="mb-6">
        <AccordionItem value={`module-${moduleNumber}`} className={cn("border rounded-md", isActive && "ring-2 ring-primary")}>
          <AccordionTrigger className={`px-4 py-3 ${isActive ? 'bg-primary/5' : ''}`}>
            <div className="flex flex-col md:flex-row md:items-center w-full">
              <div className="flex-1 flex items-center">
                <div className={`mr-3 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${moduleColor}`}>
                  Module {moduleNumber}
                </div>
                <h3 className="text-base font-medium">{title}</h3>
                {isLocked && !isTeacher && (
                  <LockIcon className="h-4 w-4 ml-2 text-muted-foreground" />
                )}
                {isCompleted && (
                  <CheckIcon className="h-4 w-4 ml-2 text-green-600" />
                )}
              </div>
              
              <div className="flex items-center mt-2 md:mt-0">
                <div className="mr-4 flex items-center text-sm text-muted-foreground">
                  <Clock className="h-4 w-4 mr-1" />
                  <span>{getTotalDuration()} mins</span>
                </div>
                <div className="flex items-center text-sm text-muted-foreground">
                  <FileText className="h-4 w-4 mr-1" />
                  <span>{lessons.length} lessons</span>
                </div>
                {(preTest || postTest) && (
                  <div className="ml-4 flex items-center text-sm text-muted-foreground">
                    <ClipboardCheck className="h-4 w-4 mr-1" />
                    <span>Tests Available</span>
                  </div>
                )}
              </div>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4">
            <div className="space-y-2 pt-2">
              {/* Pre-test section */}
              {preTest && (
                <div className="mb-4">
                  {preTestResponse ? (
                    <div className="flex items-center px-3 py-2 bg-secondary/50 rounded-md mb-2 text-sm">
                      <ClipboardCheck className="h-4 w-4 mr-2 text-primary" />
                      <span>Pre-test completed</span>
                    </div>
                  ) : (
                    <Button 
                      variant="outline" 
                      className="w-full mb-2" 
                      onClick={() => setShowPreTest(true)}
                    >
                      <ClipboardCheck className="h-4 w-4 mr-2" />
                      Take Pre-Test Before Starting Module
                    </Button>
                  )}
                </div>
              )}
              
              {/* List of lessons */}
              {(preTestResponse || !preTest || isTeacher) && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-muted-foreground mb-2">Module Lessons</h4>
                  {previewLessons.map((lesson) => (
                    <LessonItem
                      key={lesson.id}
                      lesson={lesson}
                      courseId={courseId}
                      isModuleLocked={(isLocked || (!preTestResponse && preTest && !isTeacher)) && !isTeacher}
                      onDelete={isTeacher ? () => handleDeleteLesson(lesson.id) : undefined}
                      isMobile={isMobile}
                      isVerySmall={isVerySmall}
                    />
                  ))}
                </div>
              )}
              
              {/* Show "View all lessons" button in preview mode */}
              {isPreview && lessons.length > 3 && (
                <div className="text-center pt-2">
                  <Button variant="ghost" size="sm">View all {lessons.length} lessons</Button>
                </div>
              )}
              
              {/* Post-test section */}
              {postTest && (
                <div className="mt-4">
                  {postTestResponse ? (
                    <div className="flex items-center px-3 py-2 bg-secondary/50 rounded-md mt-2 text-sm">
                      <ClipboardCheck className="h-4 w-4 mr-2 text-primary" />
                      <span>Post-test completed</span>
                    </div>
                  ) : (
                    <Button 
                      variant="outline" 
                      className="w-full mt-2" 
                      onClick={() => setShowPostTest(true)}
                      disabled={!lessons.every(lesson => lesson.completed) && !isTeacher}
                    >
                      <ClipboardCheck className="h-4 w-4 mr-2" />
                      Take Post-Test After Completing Module
                    </Button>
                  )}
                </div>
              )}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      {/* Delete Lesson Dialog */}
      <DeleteLessonDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        lessonId={lessonToDelete}
      />
    </div>
  );
};

export default ModuleSection;
