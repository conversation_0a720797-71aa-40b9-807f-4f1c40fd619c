import{j as s}from"./vendor-react.BcAa1DKr.js";import{c as l}from"./index.BLDhDn0D.js";import{S as e}from"./skeleton.C0Lb8Xms.js";const r=({className:a})=>s.jsxs("div",{className:l("flex flex-col sm:flex-row gap-4 p-4 sm:p-5 bg-white dark:bg-gray-800/50 border border-border/40 rounded-xl",a),children:[s.jsx("div",{className:"w-full sm:w-[300px] aspect-video sm:aspect-square bg-muted animate-pulse rounded-xl"}),s.jsxs("div",{className:"flex-1 space-y-4",children:[s.jsx(e,{className:"h-4 w-3/4"}),s.jsxs("div",{className:"space-y-2",children:[s.jsx(e,{className:"h-4 w-full"}),s.jsx(e,{className:"h-4 w-5/6"})]}),s.jsx("div",{className:"pt-4 border-t border-border/40",children:s.jsx(e,{className:"h-4 w-1/2"})})]})]}),o=({className:a})=>s.jsxs("div",{className:l("space-y-4 md:space-y-6",a),children:[s.jsxs("div",{className:"space-y-2 mb-4 md:mb-8",children:[s.jsx(e,{className:"h-6 md:h-8 w-3/4"}),s.jsx(e,{className:"h-3 md:h-4 w-full"})]}),s.jsx(e,{className:"h-[300px] md:h-[400px] w-full rounded-xl"})]}),x=({className:a})=>s.jsxs("div",{className:l("flex flex-col gap-8",a),children:[s.jsx(e,{className:"h-8 w-2/3"}),s.jsx(e,{className:"h-64 w-full rounded-lg"}),s.jsx(e,{className:"h-8 w-1/2"})]});export{r as C,x as L,o as M};
