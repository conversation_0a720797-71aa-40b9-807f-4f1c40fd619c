import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { verifyTeacher<PERSON>ole } from '@/services/auth/roleService';
import { logEvent } from '@/services/analytics/analyticsService';

/**
 * Auto-completion Service
 *
 * This service provides functions for automatically marking courses, modules, and lessons as completed.
 * It includes strict security checks to ensure only authorized users can trigger auto-completion.
 *
 * IMPORTANT: Auto-completion is an opt-in feature for teachers only and should never be enabled by default.
 */

// Security token verification
function verifySecurityToken(userId: string, token: string): boolean {
  // Implementation of token verification logic
  return token === `auto-complete-${userId}-${new Date().getDate()}`;
}

/**
 * Marks all courses as completed for a user
 * @param userId The ID of the user
 * @param securityToken Security token to verify the request
 * @param isManualTrigger Whether this was manually triggered by the user (vs. automatic)
 * @returns A promise that resolves to true if successful, false otherwise
 */
export async function markAllCoursesAsCompleted(
  userId: string,
  securityToken?: string,
  isManualTrigger: boolean = false
): Promise<boolean> {
  console.log('=== AUTO COMPLETION SERVICE: MARKING ALL COURSES AS COMPLETED ===');
  console.log('User ID:', userId);
  console.log('Manual trigger:', isManualTrigger);

  if (!userId) {
    console.error('Invalid user ID');
    return false;
  }

  // Security check: verify token if provided
  if (securityToken) {
    if (!verifySecurityToken(userId, securityToken)) {
      console.error('Invalid security token - auto-completion aborted');
      logEvent('security_violation', {
        action: 'auto_completion_attempt',
        userId,
        reason: 'invalid_token'
      });
      return false;
    }
  }

  // SAFETY CHECK: Only allow teachers to auto-complete courses using server-side verification
  const isUserTeacher = await verifyTeacherRole(userId);
  if (!isUserTeacher) {
    console.log('Auto-completion blocked: User is not a teacher');
    logEvent('security_violation', {
      action: 'auto_completion_attempt',
      userId,
      reason: 'not_teacher'
    });
    return false;
  }

  // Log the auto-completion attempt
  logEvent('auto_completion', {
    userId,
    action: 'mark_all_courses',
    isManualTrigger
  });

  try {
    // Try using the RPC function first - this is the most efficient approach
    try {
      console.log('Using RPC function to complete all courses...');
      const { data, error } = await supabase.rpc('complete_all_courses', {
        p_user_id: userId
      });

      if (!error) {
        console.log('Successfully marked all courses as completed using RPC function');
        if (isManualTrigger) {
          toast.success('Successfully marked all courses as completed', {
            description: `All courses have been marked as completed for the user.`,
            duration: 5000
          });
        }
        return true;
      }

      console.error('RPC function failed:', error);
    } catch (rpcError) {
      console.error('Error calling RPC function:', rpcError);
    }

    // Fallback: Get all courses and mark them as completed
    console.log('Falling back to manual course completion...');

    // Step 1: Get all courses (limit to 10 for performance)
    const { data: courses, error: coursesError } = await supabase
      .from('courses')
      .select('id, title')
      .limit(10); // Limit to 10 courses for better performance

    if (coursesError) {
      console.error('Error fetching courses:', coursesError);
      return false;
    }

    if (!courses || courses.length === 0) {
      console.log('No courses found to complete');
      return true; // No courses to complete is still a success
    }

    console.log(`Found ${courses.length} courses to mark as completed`);

    // Step 2: Mark each course as completed one by one (more reliable)
    let successCount = 0;

    for (const course of courses) {
      try {
        // Try using the RPC function for each course
        const { error: rpcError } = await supabase.rpc('complete_course', {
          p_user_id: userId,
          p_course_id: course.id
        });

        if (!rpcError) {
          successCount++;
          console.log(`Successfully marked course ${course.title} as completed`);
        } else {
          console.error(`Error marking course ${course.title} as completed:`, rpcError);
        }
      } catch (courseError) {
        console.error(`Error processing course ${course.title}:`, courseError);
      }
    }

    console.log(`Successfully marked ${successCount} out of ${courses.length} courses as completed`);
    if (isManualTrigger) {
      toast.success('Successfully marked all courses as completed', {
        description: `All courses have been marked as completed for the user.`,
        duration: 5000
      });
    }
    return successCount > 0;
  } catch (error) {
    console.error('Error in markAllCoursesAsCompleted:', error);
    return false;
  }
}

/**
 * Marks all modules and lessons as completed for a user
 * This is a more thorough approach that ensures all modules and lessons are marked as completed
 * @param userId The ID of the user
 * @param securityToken Security token to verify the request
 * @param isManualTrigger Whether this was manually triggered by the user (vs. automatic)
 * @returns A promise that resolves to true if successful, false otherwise
 */
export async function markAllModulesAndLessonsAsCompleted(
  userId: string,
  securityToken?: string,
  isManualTrigger: boolean = false
): Promise<boolean> {
  console.log('=== AUTO COMPLETION SERVICE: MARKING ALL MODULES AND LESSONS AS COMPLETED ===');
  console.log('User ID:', userId);
  console.log('Manual trigger:', isManualTrigger);

  if (!userId) {
    console.error('Invalid user ID');
    return false;
  }

  // Security check: verify token if provided
  if (securityToken) {
    if (!verifySecurityToken(userId, securityToken)) {
      console.error('Invalid security token - auto-completion aborted');
      logEvent('security_violation', {
        action: 'auto_completion_attempt',
        userId,
        reason: 'invalid_token'
      });
      return false;
    }
  }

  // SAFETY CHECK: Only allow teachers to auto-complete modules and lessons
  const isUserTeacher = await verifyTeacherRole(userId);
  if (!isUserTeacher) {
    console.log('Auto-completion blocked: User is not a teacher');
    logEvent('security_violation', {
      action: 'auto_completion_attempt',
      userId,
      reason: 'not_teacher'
    });
    return false;
  }

  // Log the auto-completion attempt
  logEvent('auto_completion', {
    userId,
    action: 'mark_all_modules_and_lessons',
    isManualTrigger
  });

  try {
    // Step 1: Get a limited number of courses for better performance
    const { data: courses, error: coursesError } = await supabase
      .from('courses')
      .select('id')
      .limit(5); // Limit to 5 courses for better performance

    if (coursesError || !courses) {
      console.error('Error fetching courses:', coursesError);
      return false;
    }

    // Process courses in parallel for better performance
    await Promise.all(courses.map(async (course) => {
      try {
        // Get modules for this course
        const { data: modules, error: modulesError } = await supabase
          .from('modules')
          .select('id')
          .eq('course_id', course.id)
          .limit(10); // Limit to 10 modules per course

        if (modulesError || !modules || modules.length === 0) {
          console.log(`No modules found for course ${course.id} or error:`, modulesError);
          return;
        }

        const now = new Date().toISOString();

        // Process modules in parallel
        await Promise.all(modules.map(async (module) => {
          try {
            // Mark the module as completed for this user
            await supabase
              .from('user_module_progress')
              .upsert({
                user_id: userId,
                module_id: module.id,
                is_completed: true,
                updated_at: now
              }, {
                onConflict: 'user_id,module_id',
                ignoreDuplicates: false
              });

            // Get lessons for this module
            const { data: lessons, error: lessonsError } = await supabase
              .from('lessons')
              .select('id')
              .eq('module_id', module.id)
              .limit(20); // Limit to 20 lessons per module

            if (lessonsError || !lessons || lessons.length === 0) {
              console.log(`No lessons found for module ${module.id} or error:`, lessonsError);
              return;
            }

            // Mark all lessons as completed for this user
            const lessonProgress = lessons.map(lesson => ({
              user_id: userId,
              lesson_id: lesson.id,
              is_completed: true,
              updated_at: now
            }));

            // Use smaller batches for better performance
            const batchSize = 10;
            for (let i = 0; i < lessonProgress.length; i += batchSize) {
              const batch = lessonProgress.slice(i, i + batchSize);
              await supabase
                .from('user_lesson_progress')
                .upsert(batch, {
                  onConflict: 'user_id,lesson_id',
                  ignoreDuplicates: false
                });
            }
          } catch (moduleError) {
            console.error(`Error processing module ${module.id}:`, moduleError);
          }
        }));
      } catch (courseError) {
        console.error(`Error processing course ${course.id}:`, courseError);
      }
    }));

    console.log('Successfully marked all modules and lessons as completed');
    if (isManualTrigger) {
      toast.success('Successfully marked all modules and lessons as completed', {
        description: `All modules and lessons have been marked as completed for the user.`,
        duration: 5000
      });
    }
    return true;
  } catch (error) {
    console.error('Error in markAllModulesAndLessonsAsCompleted:', error);
    return false;
  }
}
