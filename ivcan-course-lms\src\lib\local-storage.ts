/**
 * Local storage utility for handling file uploads when Supabase storage is not available
 * This provides a fallback mechanism that stores files as base64 data URLs in the database
 */

/**
 * Convert a file to a data URL (base64 encoded string)
 * @param file The file to convert
 * @returns A Promise that resolves to the data URL
 */
export function fileToDataUrl(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result);
      } else {
        reject(new Error('Failed to convert file to data URL'));
      }
    };

    reader.onerror = () => {
      reject(reader.error || new Error('Unknown error occurred while reading file'));
    };

    reader.readAsDataURL(file);
  });
}

/**
 * Compress an image file to reduce its size
 * @param file The image file to compress
 * @param options Compression options
 * @returns A Promise that resolves to the compressed file
 */
export function compressImage(
  file: File,
  options: {
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
    maxSizeKB?: number;
  } = {}
): Promise<File> {
  const {
    maxWidth = 800,
    maxHeight = 600,
    quality = 0.7,
    maxSizeKB = 500 // Default max size of 500KB
  } = options;

  return new Promise((resolve, reject) => {
    // Create an image element to load the file
    const img = new Image();
    img.src = URL.createObjectURL(file);

    img.onload = () => {
      // Create a canvas to resize the image
      const canvas = document.createElement('canvas');
      let width = img.width;
      let height = img.height;

      // Calculate new dimensions while maintaining aspect ratio
      if (width > maxWidth) {
        height = Math.round(height * (maxWidth / width));
        width = maxWidth;
      }

      if (height > maxHeight) {
        width = Math.round(width * (maxHeight / height));
        height = maxHeight;
      }

      // Set canvas dimensions and draw the resized image
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        reject(new Error('Failed to get canvas context'));
        return;
      }

      ctx.drawImage(img, 0, 0, width, height);

      // Convert canvas to blob
      canvas.toBlob(
        (blob) => {
          if (!blob) {
            reject(new Error('Failed to create blob from canvas'));
            return;
          }

          // Create a new file from the blob
          const compressedFile = new File(
            [blob],
            file.name,
            {
              type: file.type,
              lastModified: file.lastModified
            }
          );

          resolve(compressedFile);
        },
        file.type,
        quality
      );
    };

    img.onerror = () => {
      reject(new Error('Failed to load image for compression'));
    };
  });
}

/**
 * Process an image file for storage
 * This compresses the image and converts it to a data URL
 *
 * @param file The image file to process
 * @returns A Promise that resolves to the processed data URL
 */
/**
 * Maximum size for data URLs in KB
 * This is to ensure the data URL can be stored in the database
 * PostgreSQL TEXT columns can typically handle up to 1GB, but we want to be conservative
 */
const MAX_DATA_URL_SIZE_KB = 800; // 800KB max size for data URLs

/**
 * Process an image file for storage with size limits
 * This function will compress the image until it's below the size limit
 *
 * @param file The image file to process
 * @returns A Promise that resolves to the processed data URL
 */
export async function processImageForStorage(file: File): Promise<string> {
  try {
    // Check if the file is an image
    if (!file.type.startsWith('image/')) {
      throw new Error('File is not an image');
    }

    // Initial compression settings based on file size
    let compressionSettings = {
      maxWidth: 800,
      maxHeight: 600,
      quality: 0.7
    };

    // For very large files, start with more aggressive compression
    if (file.size > 2 * 1024 * 1024) { // If larger than 2MB
      console.log(`Very large image detected (${(file.size / (1024 * 1024)).toFixed(2)}MB), applying aggressive compression`);
      compressionSettings = {
        maxWidth: 600,
        maxHeight: 400,
        quality: 0.5
      };
    } else if (file.size > 1024 * 1024) { // If larger than 1MB
      console.log(`Large image detected (${(file.size / (1024 * 1024)).toFixed(2)}MB), applying stronger compression`);
      compressionSettings = {
        maxWidth: 800,
        maxHeight: 600,
        quality: 0.6
      };
    }

    // Try to compress the image with initial settings
    let compressedFile = await compressImage(file, compressionSettings);
    let dataUrl = await fileToDataUrl(compressedFile);
    let dataUrlSizeKB = Math.round(dataUrl.length / 1024);

    console.log(`Initial compression result: ${dataUrlSizeKB}KB`);

    // If the data URL is still too large, try more aggressive compression
    let attempts = 0;
    const maxAttempts = 3;

    while (dataUrlSizeKB > MAX_DATA_URL_SIZE_KB && attempts < maxAttempts) {
      attempts++;

      // Reduce quality and dimensions with each attempt
      compressionSettings.quality = Math.max(0.3, compressionSettings.quality - 0.1);
      compressionSettings.maxWidth = Math.floor(compressionSettings.maxWidth * 0.8);
      compressionSettings.maxHeight = Math.floor(compressionSettings.maxHeight * 0.8);

      console.log(`Attempt ${attempts}: Compressing further to ${compressionSettings.maxWidth}x${compressionSettings.maxHeight} at quality ${compressionSettings.quality}`);

      compressedFile = await compressImage(file, compressionSettings);
      dataUrl = await fileToDataUrl(compressedFile);
      dataUrlSizeKB = Math.round(dataUrl.length / 1024);

      console.log(`Compressed to ${dataUrlSizeKB}KB`);
    }

    // If still too large after all attempts, throw an error
    if (dataUrlSizeKB > MAX_DATA_URL_SIZE_KB) {
      throw new Error(`Image is too large (${dataUrlSizeKB}KB). Maximum allowed size is ${MAX_DATA_URL_SIZE_KB}KB. Please use a smaller image.`);
    }

    console.log(`Final image size: ${dataUrlSizeKB}KB`);
    return dataUrl;
  } catch (error) {
    console.error('Error processing image:', error);
    throw error; // Propagate the error to be handled by the caller
  }
}
