const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/completionService.BUb78ZaE.js","assets/vendor.DQpuTRuB.js","assets/vendor-react.BcAa1DKr.js","assets/index.BLDhDn0D.js","assets/vendor-supabase.sufZ44-y.js","assets/css/index.CBAYZKm-.css","assets/achievementService.Vkx_BHml.js"])))=>i.map(i=>d[i]);
var C=(s,m,n)=>new Promise((x,u)=>{var t=l=>{try{r(n.next(l))}catch(f){u(f)}},h=l=>{try{r(n.throw(l))}catch(f){u(f)}},r=l=>l.done?x(l.value):Promise.resolve(l.value).then(t,h);r((n=n.apply(s,m)).next())});import{a4 as b,ag as P,ah as B,a2 as p,W as O}from"./vendor.DQpuTRuB.js";import{r as c,j as e,bq as Y,ah as A,$ as T,br as H,x as U,bs as Q,aH as z,u as G,az as V,at as W}from"./vendor-react.BcAa1DKr.js";import{L as D}from"./Layout.DRjmVYQG.js";import{u as M,g as Z,a as $,c as a,B as S,s as I}from"./index.BLDhDn0D.js";import{e as K}from"./courseApi.BQX5-7u-.js";import{C as R}from"./confetti.ShHySCrk.js";import{P as J,C as X}from"./floating-sidebar-container.BxlLgzat.js";import"./vendor-supabase.sufZ44-y.js";import"./utils.Qa9QlCj_.js";import"./enrollmentApi.CstnsQi_.js";const ee=({courseName:s,completionDate:m,courseId:n})=>{var E;const{user:x}=M(),{toast:u}=Z(),t=$(),h=c.useRef(null),[r,l]=c.useState(!1),[f,w]=c.useState(!1),[j,v]=c.useState(!1),y=()=>C(void 0,null,function*(){if(h.current){l(!0);try{const i=yield P(h.current,{scale:t?1.5:2,logging:!1,useCORS:!0,backgroundColor:"#1e40af"}),d=i.toDataURL("image/png");if(t){const o=document.createElement("a");o.href=d,o.download=`${s.replace(/\s+/g,"_")}_Certificate.png`,document.body.appendChild(o),o.click(),document.body.removeChild(o),u({title:"Certificate Downloaded",description:"Your certificate has been saved as a PNG image."})}else{const o=new B({orientation:"landscape",unit:"mm",format:"a4"}),g=297,_=i.height*g/i.width;o.addImage(d,"PNG",0,0,g,_),o.save(`${s.replace(/\s+/g,"_")}_Certificate.pdf`),u({title:"Certificate Downloaded",description:"Your certificate has been saved as a PDF."})}}catch(i){console.error("Error generating certificate:",i),u({title:"Download Failed",description:"There was an error creating your certificate. Please try again.",variant:"destructive"})}finally{l(!1)}}}),N=()=>C(void 0,null,function*(){if(h.current){w(!0);try{const d=(yield P(h.current,{scale:1.5,logging:!1,useCORS:!0,backgroundColor:"#1e40af"})).toDataURL("image/png"),o=yield(yield fetch(d)).blob();navigator.share?(yield navigator.share({title:`${s} Certificate`,text:`I've completed the ${s} course!`,files:[new File([o],`${s.replace(/\s+/g,"_")}_Certificate.png`,{type:"image/png"})]}),v(!0),setTimeout(()=>v(!1),3e3)):(yield navigator.share({title:`${s} Certificate`,text:`I've completed the ${s} course!`}),v(!0),setTimeout(()=>v(!1),3e3))}catch(i){console.error("Error sharing certificate:",i),i.name!=="AbortError"&&u({title:"Sharing Failed",description:"There was an error sharing your certificate. Please try again.",variant:"destructive"})}finally{w(!1)}}});return e.jsxs("div",{className:"flex flex-col items-center",children:[t&&e.jsx("div",{className:"w-full mb-6 p-4 bg-muted/50 rounded-lg border border-border",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(Y,{className:"h-5 w-5 text-primary mt-0.5"}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium mb-1",children:"Mobile Certificate Tips"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"For the best experience, rotate your device to landscape mode when viewing or downloading your certificate."})]})]})}),e.jsxs("div",{ref:h,className:a("w-full relative overflow-hidden",t?"max-w-full":"max-w-4xl"),style:{aspectRatio:"1.414/1"},children:[e.jsxs("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800",children:[e.jsx("div",{className:"absolute top-0 right-0 w-1/2 h-full",children:e.jsxs("svg",{className:"w-full h-full",viewBox:"0 0 400 600",preserveAspectRatio:"xMidYMid slice",children:[e.jsx("path",{d:"M0,0 Q200,100 400,0 L400,200 Q200,300 0,200 Z",fill:"rgba(255,255,255,0.1)"}),e.jsx("path",{d:"M0,400 Q200,500 400,400 L400,600 L0,600 Z",fill:"rgba(255,255,255,0.05)"})]})}),e.jsx("div",{className:"absolute bottom-0 left-0 w-full h-1/3",children:e.jsx("svg",{className:"w-full h-full",viewBox:"0 0 800 200",preserveAspectRatio:"none",children:e.jsx("path",{d:"M0,200 Q400,50 800,200 L800,200 L0,200 Z",fill:"rgba(255,255,255,0.08)"})})})]}),e.jsx("div",{className:a("absolute bg-white rounded-lg shadow-2xl",t?"inset-4 sm:inset-6":"inset-8"),children:e.jsxs("div",{className:a("h-full flex flex-col justify-between text-center relative",t?"p-4 sm:p-6":"p-8 sm:p-12"),children:[e.jsxs("div",{className:"flex-shrink-0",children:[e.jsx("h1",{className:a("font-bold text-blue-900 mb-2",t?"text-2xl sm:text-3xl":"text-4xl lg:text-5xl"),children:"I-can-IV"}),e.jsx("p",{className:a("text-blue-700 tracking-wider",t?"text-sm sm:text-base":"text-lg lg:text-xl"),children:"eLearning Modules"})]}),e.jsxs("div",{className:"flex-grow flex flex-col justify-center",children:[e.jsx("p",{className:a("text-gray-700 mb-4 sm:mb-6 tracking-wide",t?"text-xs sm:text-sm":"text-base"),children:"THIS CERTIFICATE IS AWARDED TO"}),e.jsxs("div",{className:"mb-6 sm:mb-8",children:[e.jsx("div",{className:a("border-b-2 border-gray-400 mx-auto mb-2",t?"w-48 sm:w-64":"w-80")}),e.jsx("h2",{className:a("font-bold text-gray-800",t?"text-lg sm:text-xl":"text-2xl lg:text-3xl"),children:((E=x==null?void 0:x.user_metadata)==null?void 0:E.full_name)||(x==null?void 0:x.email)})]}),e.jsxs("div",{className:a("mb-6 sm:mb-8 leading-relaxed",t?"px-2":"px-4"),children:[e.jsxs("p",{className:a("text-gray-700 mb-2",t?"text-sm sm:text-base":"text-lg"),children:["for successfully completing the ",s," eLearning modules,"]}),e.jsx("p",{className:a("text-gray-700",t?"text-sm sm:text-base":"text-lg"),children:"demonstrating dedication to professional development in medical imaging."})]}),e.jsx("p",{className:a("text-gray-600 font-medium mb-6 sm:mb-8",t?"text-xs sm:text-sm":"text-sm lg:text-base"),children:"e4mi - providing an evidence-based eLearning platform to train the medical imaging workforce"})]}),e.jsx("div",{className:"flex-shrink-0",children:e.jsxs("div",{className:a("flex items-end justify-between",t?"gap-4":"gap-8"),children:[e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsxs("div",{className:"relative mb-2",children:[e.jsx("div",{className:"w-12 h-12 sm:w-16 sm:h-16 bg-blue-600 rounded-full flex items-center justify-center",children:e.jsx(A,{className:"w-6 h-6 sm:w-8 sm:h-8 text-white"})}),e.jsx("div",{className:"absolute -bottom-1 -right-1 w-6 h-6 sm:w-8 sm:h-8 bg-blue-800 rounded-full flex items-center justify-center",children:e.jsx("div",{className:"w-2 h-2 sm:w-3 sm:h-3 bg-white rounded-full"})})]}),e.jsx("p",{className:a("text-gray-600 font-medium",t?"text-xs":"text-sm"),children:"(40-45 mins)"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"mb-2",children:[e.jsx("div",{className:a("font-signature text-gray-700 mb-1",t?"text-lg":"text-xl lg:text-2xl"),children:"Dr. Andrew Donkor"}),e.jsx("div",{className:a("h-px bg-gray-400",t?"w-24 sm:w-32":"w-40")})]}),e.jsxs("div",{className:a("text-gray-600 leading-tight",t?"text-xs":"text-sm"),children:[e.jsx("p",{className:"font-medium",children:"Dr. Andrew Donkor"}),e.jsx("p",{children:"Project Lead, e4mi Initiative"}),e.jsx("p",{children:"Lecturer, Department of Medical"}),e.jsx("p",{children:"Imaging, KNUST"})]})]}),e.jsx("div",{className:"flex flex-col items-center",children:e.jsxs("div",{className:a("bg-red-600 text-white font-bold rounded",t?"px-2 py-1 text-xs":"px-3 py-2 text-sm"),children:[e.jsx("div",{children:"AD EDUCATION"}),e.jsx("div",{children:"& RESEARCH"}),e.jsx("div",{children:"GROUP"})]})})]})})]})})]}),e.jsxs("div",{className:"mt-6 flex flex-col sm:flex-row gap-3 w-full sm:w-auto sm:justify-center",children:[e.jsx(b.div,{whileHover:{scale:1.03},whileTap:{scale:.97},className:"w-full sm:w-auto",children:e.jsx(S,{onClick:y,className:"bg-primary hover:bg-primary/90 text-primary-foreground px-6 py-2 rounded-full w-full sm:w-auto",disabled:r,children:r?e.jsxs(e.Fragment,{children:[e.jsx(T,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):e.jsxs(e.Fragment,{children:[e.jsx(H,{className:"mr-2 h-4 w-4"}),"Download Certificate"]})})}),(t||typeof navigator!="undefined"&&"share"in navigator)&&e.jsx(b.div,{whileHover:{scale:1.03},whileTap:{scale:.97},className:"w-full sm:w-auto",children:e.jsx(S,{onClick:N,variant:"outline",className:"border-primary/30 text-primary hover:bg-primary/5 px-6 py-2 rounded-full w-full sm:w-auto",disabled:f||j,children:f?e.jsxs(e.Fragment,{children:[e.jsx(T,{className:"mr-2 h-4 w-4 animate-spin"}),"Preparing..."]}):j?e.jsxs(e.Fragment,{children:[e.jsx(U,{className:"mr-2 h-4 w-4"}),"Shared!"]}):e.jsxs(e.Fragment,{children:[e.jsx(Q,{className:"mr-2 h-4 w-4"}),"Share Achievement"]})})})]})]})},ue=()=>{const{courseId:s}=z(),{user:m}=M(),n=G();$();const[x,u]=c.useState(""),[t,h]=c.useState(!1),[r,l]=c.useState(!1),[f,w]=c.useState(!1),[j,v]=c.useState(!1),{data:y,isLoading:N,error:E}=V({queryKey:["course",s],queryFn:()=>K(s||""),enabled:!!s});return c.useEffect(()=>{if(!s||!m)return;C(void 0,null,function*(){try{const{data:d,error:o}=yield I.from("user_course_enrollment").select("status, completed_at").eq("user_id",m.id).eq("course_id",s).single();if(o){console.error("Error checking enrollment:",o),p.error("Failed to verify course completion status");return}if(d&&(h(!0),d.status==="completed"&&d.completed_at)){l(!0),u(d.completed_at);return}const{data:g,error:_}=yield I.from("modules").select("id").eq("course_id",s);if(_){console.error("Error fetching modules:",_),p.error("Failed to verify module completion status");return}if(g&&g.length>0){const{data:L,error:F}=yield I.from("user_module_progress").select("module_id").eq("user_id",m.id).eq("is_completed",!0).in("module_id",g.map(k=>k.id));if(F){console.error("Error checking module progress:",F),p.error("Failed to verify module completion status");return}if(L&&L.length===g.length){const{completeCourse:k}=yield O(()=>C(void 0,null,function*(){const{completeCourse:q}=yield import("./completionService.BUb78ZaE.js");return{completeCourse:q}}),__vite__mapDeps([0,1,2,3,4,5,6]));(yield k(s,m.id))?(l(!0),u(new Date().toISOString()),p.success("Course completion verified successfully")):p.error("Failed to mark course as completed")}else p.error("You need to complete all modules before accessing the certificate")}else p.error("No modules found for this course")}catch(d){console.error("Error checking completion status:",d),p.error("Failed to verify course completion status")}})},[s,m]),c.useEffect(()=>{},[]),c.useEffect(()=>{if(!N&&!r&&!j&&m){const i=setTimeout(()=>{!r&&!j&&(p.error("You need to complete this course to view the certificate"),n(`/course/${s}`))},2e3);return()=>clearTimeout(i)}},[r,j,N,m,s,n]),N?e.jsx(D,{children:e.jsx("div",{className:"flex items-center justify-center min-h-[60vh]",children:e.jsx(T,{className:"h-8 w-8 animate-spin text-primary"})})}):E||!y?e.jsx(D,{children:e.jsxs("div",{className:"text-center py-12",children:[e.jsx("h1",{className:"text-2xl font-bold mb-4",children:"Course not found"}),e.jsx(S,{onClick:()=>n("/dashboard"),children:"Back to Dashboard"})]})}):(c.useEffect(()=>{if(r&&!f){w(!0);const i=setTimeout(()=>w(!1),5e3);return()=>clearTimeout(i)}},[r]),e.jsx(D,{children:e.jsx(J,{pageType:"certificate",children:e.jsxs(X,{spacing:"lg",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6",children:[e.jsxs(S,{variant:"ghost",onClick:()=>n(`/course/${s}`),className:"w-fit",children:[e.jsx(W,{className:"mr-2 h-4 w-4"}),"Back to Course"]}),e.jsxs(S,{variant:"outline",onClick:()=>n("/achievements"),className:"w-fit border-primary/20 text-primary hover:bg-primary/5",children:[e.jsx(A,{className:"mr-2 h-4 w-4"}),"View All Achievements"]})]}),f&&e.jsxs(b.div,{className:"fixed inset-0 pointer-events-none z-50 flex items-center justify-center",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:[e.jsx("div",{className:"absolute inset-0 bg-primary/10 backdrop-blur-sm"}),e.jsx(R,{count:200,duration:5,active:!0}),e.jsxs(b.div,{initial:{scale:.5,opacity:0},animate:{scale:1,opacity:1},transition:{type:"spring",damping:12},className:"bg-card p-6 rounded-xl shadow-lg border border-primary/20 text-center z-10",children:[e.jsx(A,{className:"h-16 w-16 text-primary mx-auto mb-4"}),e.jsx("h2",{className:"text-2xl font-bold mb-2",children:"Achievement Unlocked!"}),e.jsx("p",{className:"text-muted-foreground mb-2",children:"You've earned your certificate!"}),e.jsx("p",{className:"text-xs text-primary font-medium",children:"Course completed successfully"})]})]}),r&&e.jsx(R,{count:50,duration:8,active:!0}),e.jsxs("div",{className:"mb-6 sm:mb-8 text-center",children:[e.jsxs(b.h1,{className:"text-2xl sm:text-3xl font-bold mb-2",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{delay:.2},children:[y.title," - Certificate"]}),e.jsx(b.p,{className:"text-muted-foreground text-sm sm:text-base",initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},children:"Congratulations on completing this course! You can download or share your certificate below."})]}),r&&e.jsx(b.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:e.jsx(ee,{courseName:y.title,completionDate:x,courseId:s||""})})]})})}))};export{ue as default};
