import{r as o,j as e}from"./vendor-react.BcAa1DKr.js";import{c as n,M as c}from"./markdown-preview.Bw0U-NJA.js";import{m}from"./content-converter.L-GziWIP.js";import"./vendor.DQpuTRuB.js";import"./index.BLDhDn0D.js";import"./vendor-supabase.sufZ44-y.js";const f=()=>{const[r,i]=o.useState(""),[d,l]=o.useState(""),s=`
# Image Test

This is a test to verify image display functionality.

![Test Image](https://jibspqwieubavucdtccv.supabase.co/storage/v1/object/public/course-images/editor-images/general/1749397228641.jpg)

The image above should be visible if the content security fix is working properly.

## Additional Test

Here's another test with a different image format:

![Another Test](https://via.placeholder.com/300x200/0066cc/ffffff?text=Test+Image)

Both images should display correctly.
  `;return o.useEffect(()=>{const t=m(s);i(t);const a=n.sanitizeHtml(t);l(a),console.log("Original markdown:",s),console.log("Processed HTML:",t),console.log("Sanitized HTML:",a)},[]),e.jsxs("div",{className:"p-6 max-w-4xl mx-auto",children:[e.jsx("h1",{className:"text-2xl font-bold mb-4",children:"Image Display Debug Test"}),e.jsxs("div",{className:"border rounded-lg p-4 bg-background mb-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-2",children:"Rendered Content:"}),e.jsx(c,{content:s,allowHtml:!0,securityLevel:"extended"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[e.jsxs("div",{className:"p-4 bg-muted rounded-lg",children:[e.jsx("h3",{className:"font-semibold mb-2",children:"Processed HTML:"}),e.jsx("pre",{className:"text-xs overflow-auto max-h-40 bg-background p-2 rounded",children:r})]}),e.jsxs("div",{className:"p-4 bg-muted rounded-lg",children:[e.jsx("h3",{className:"font-semibold mb-2",children:"Sanitized HTML:"}),e.jsx("pre",{className:"text-xs overflow-auto max-h-40 bg-background p-2 rounded",children:d})]})]}),e.jsxs("div",{className:"p-4 bg-muted rounded-lg",children:[e.jsx("h2",{className:"font-semibold mb-2",children:"Debug Info:"}),e.jsx("p",{className:"text-sm text-muted-foreground mb-2",children:"This test verifies that images from Supabase storage are properly displayed after fixing the content security configuration."}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Check the browser console for detailed processing logs."})]})]})};export{f as default};
