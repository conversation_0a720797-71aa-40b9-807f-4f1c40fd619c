import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/context/AuthContext';

/**
 * Auth Callback Page
 *
 * This page handles callbacks from Supabase auth operations like OAuth and email verification.
 * It processes the auth callback and redirects the user to the appropriate page.
 */
const AuthCallback: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, setUser } = useAuth();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processing your request...');
  const [redirectAttempted, setRedirectAttempted] = useState(false);
  const [debugInfo, setDebugInfo] = useState<any>(null);

  // Effect to check if user is already authenticated
  useEffect(() => {
    // If we already have a user and haven't attempted a redirect yet
    if (user && !redirectAttempted) {
      console.log('User already authenticated in AuthCallback, redirecting to dashboard');
      setRedirectAttempted(true);

      // Small delay to ensure state is updated
      setTimeout(() => {
        navigate('/dashboard');
      }, 100);
    }
  }, [user, navigate, redirectAttempted]);

  useEffect(() => {
    // Skip if we've already attempted a redirect
    if (redirectAttempted) {
      return;
    }
    const handleAuthCallback = async () => {
      try {
        console.log('Auth callback triggered');
        console.log('URL:', window.location.href);
        console.log('Location:', location);
        
        // Get the URL hash and search params
        const hash = window.location.hash;
        const searchParams = new URLSearchParams(window.location.search);
        
        // Save debug info
        setDebugInfo({
          url: window.location.href,
          hash,
          params: Object.fromEntries(searchParams.entries()),
          host: window.location.host,
          origin: window.location.origin
        });

        console.log('Hash:', hash);
        console.log('Search params:', Object.fromEntries(searchParams.entries()));

        // Check for error in the URL
        const errorParam = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');

        if (errorParam) {
          console.error('Auth error from URL:', errorParam, errorDescription);
          setStatus('error');
          setMessage(errorDescription || `Authentication error: ${errorParam}`);
          return;
        }

        // Handle the OAuth callback directly
        let sessionData = null;

        // First, check if there's an error in the URL that we missed
        if (window.location.href.includes('error=')) {
          console.error('OAuth error detected in URL:', window.location.href);
          setStatus('error');
          setMessage('Authentication failed. Please try again.');
          return;
        }

        try {
          // Simple approach: just get the current session
          console.log('Attempting to get current session');
          const { data, error } = await supabase.auth.getSession();

          if (error) {
            throw error;
          }

          sessionData = data;
          console.log('Successfully retrieved session:', sessionData);
        } catch (sessionError: any) {
          console.error('Error getting session:', sessionError);

          // Try a different approach - exchange the code if present
          if (searchParams.has('code')) {
            try {
              console.log('Attempting to exchange code for session');
              
              // The code exchange happens automatically when we call getSession()
              // Let's try one more time with a small delay
              await new Promise(resolve => setTimeout(resolve, 1000)); // Increased delay
              
              // Force the code exchange by calling exchangeCodeForSession
              try {
                console.log('Manually exchanging code for session');
                await supabase.auth.exchangeCodeForSession(searchParams.get('code') || '');
              } catch (exchangeError) {
                console.error('Error in manual code exchange:', exchangeError);
                // Continue anyway as this might still work
              }
              
              const { data, error } = await supabase.auth.getSession();

              if (error) {
                throw error;
              }

              sessionData = data;
              console.log('Successfully retrieved session after code exchange:', sessionData);
            } catch (codeError: any) {
              console.error('Error exchanging code for session:', codeError);
              setStatus('error');
              setMessage('Failed to complete authentication. Please try again.');
              return;
            }
          } else {
            setStatus('error');
            setMessage(sessionError.message || 'Failed to get authentication session.');
            return;
          }
        }

        console.log('Session data:', sessionData);

        // Try to parse the state parameter to get the redirect destination
        let redirectTo = '/dashboard';
        try {
          // The state parameter might be in the URL hash or search params
          const stateParam = searchParams.get('state') || new URLSearchParams(hash.substring(1)).get('state');
          if (stateParam) {
            try {
              const stateObj = JSON.parse(decodeURIComponent(stateParam));
              if (stateObj.redirectTo) {
                redirectTo = stateObj.redirectTo;
              }
            } catch (parseError) {
              console.warn('Error parsing state JSON:', parseError);
              // If it's not JSON, try to use it directly if it looks like a path
              if (stateParam.startsWith('/')) {
                redirectTo = stateParam;
              }
            }
          }
        } catch (e) {
          console.warn('Could not parse state parameter:', e);
        }

        if (sessionData?.session) {
          // Session exists, authentication was successful
          setStatus('success');
          setMessage('Authentication successful! Redirecting...');

          // Log the user info
          console.log('User authenticated:', sessionData.session.user);

          // Update the user in the auth context
          if (setUser) {
            console.log('Updating user in auth context');
            setUser(sessionData.session.user);

            // Force a refresh of the auth state
            try {
              // Store session in localStorage to ensure it persists
              localStorage.setItem('supabase.auth.token', JSON.stringify({
                currentSession: sessionData.session,
                expiresAt: Math.floor(Date.now() / 1000) + 3600
              }));
            } catch (e) {
              console.warn('Failed to store session in localStorage:', e);
            }
          } else {
            console.warn('setUser function not available in auth context');
          }

          // Show success toast
          toast.success('Successfully signed in!');

          // Mark that we've attempted a redirect
          setRedirectAttempted(true);

          // Use absolute URL to ensure we stay on the current origin
          const currentOrigin = window.location.origin;
          const redirectPath = redirectTo.startsWith('/') ? redirectTo : `/${redirectTo}`;
          const absoluteRedirectUrl = `${currentOrigin}${redirectPath}`;
          
          console.log('Redirecting to:', absoluteRedirectUrl);
          
          // Use navigate in React Router instead of window.location for better behavior
          navigate(redirectTo);
        } else {
          // No session, but no error either - might be email confirmation
          setStatus('success');
          setMessage('Email confirmed! You can now sign in with your credentials.');

          // Mark that we've attempted a redirect
          setRedirectAttempted(true);

          // Redirect to login after a short delay
          setTimeout(() => {
            navigate('/login');
          }, 1000);
        }
      } catch (error: any) {
        console.error('Error handling auth callback:', error);
        setStatus('error');
        setMessage(error.message || 'An unexpected error occurred. Please try again.');
      }
    };

    handleAuthCallback();
  }, [navigate, location, setUser, redirectAttempted]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="max-w-md w-full p-8 bg-card rounded-xl shadow-sm border border-border">
        <div className="flex flex-col items-center justify-center text-center">
          {status === 'loading' && (
            <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
          )}

          {status === 'success' && (
            <CheckCircle className="h-12 w-12 text-red-500 mb-4" />
          )}

          {status === 'error' && (
            <XCircle className="h-12 w-12 text-red-500 mb-4" />
          )}

          <h1 className="text-2xl font-bold mb-2">
            {status === 'loading' && 'Processing'}
            {status === 'success' && 'Success'}
            {status === 'error' && 'Error'}
          </h1>

          <p className="text-muted-foreground mb-6">{message}</p>

          {status === 'error' && (
            <div className="flex flex-col gap-4">
              <div className="flex gap-4">
                <button
                  onClick={() => navigate('/login')}
                  className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
                >
                  Go to Login
                </button>
                <button
                  onClick={() => navigate('/')}
                  className="px-4 py-2 bg-muted text-foreground rounded-lg hover:bg-muted/90 transition-colors"
                >
                  Go to Home
                </button>
              </div>
              
              {/* Show debug info in dev mode */}
              {import.meta.env.DEV && debugInfo && (
                <div className="mt-4 p-4 bg-muted/50 rounded-md text-left text-xs overflow-auto">
                  <details>
                    <summary className="cursor-pointer font-medium">Debug Information</summary>
                    <pre className="mt-2">{JSON.stringify(debugInfo, null, 2)}</pre>
                  </details>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AuthCallback;
