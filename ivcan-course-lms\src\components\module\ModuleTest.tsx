import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { CheckCircle } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { useToast } from '@/hooks/use-toast';
import type { ModuleTest as ModuleTestType, TestResponse } from '@/types/module-test';
import { ratingDescriptions } from '@/types/module-test';
import { useMutation } from '@tanstack/react-query';
import { submitTestResponse } from '@/services/module-test/moduleTestService';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { useNavigate } from 'react-router-dom';
import { getFirstLessonOfModule } from '@/services/course/courseApi';

interface ModuleTestProps {
  test: ModuleTestType;
  onComplete: () => void;
  courseId?: string;
  showNextLessonButton?: boolean;
}

const ModuleTest: React.FC<ModuleTestProps> = ({ test, onComplete, courseId, showNextLessonButton = false }) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [responses, setResponses] = useState<TestResponse[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [testCompleted, setTestCompleted] = useState(false);

  const currentQuestion = test.questions[currentQuestionIndex];
  const totalQuestions = test.questions.length;
  const isLastQuestion = currentQuestionIndex === totalQuestions - 1;

  // Handle rating selection
  const handleRatingSelect = (value: string) => {
    const rating = parseInt(value);

    // Create a new response for this question
    const response: TestResponse = {
      questionId: currentQuestion.id,
      rating
    };

    // Update responses
    setResponses(prev => {
      // Check if we already have a response for this question
      const existingIndex = prev.findIndex(r => r.questionId === currentQuestion.id);
      if (existingIndex >= 0) {
        // Replace existing response
        const updated = [...prev];
        updated[existingIndex] = response;
        return updated;
      } else {
        // Add new response
        return [...prev, response];
      }
    });
  };

  // Check if we have a response for the current question
  const getCurrentResponse = () => {
    return responses.find(r => r.questionId === currentQuestion.id)?.rating;
  };

  // Move to the next question
  const handleNextQuestion = () => {
    if (isLastQuestion) {
      handleSubmit();
    } else {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  // Move to the previous question
  const handlePreviousQuestion = () => {
    setCurrentQuestionIndex(prev => Math.max(0, prev - 1));
  };

  // Submit the test responses
  const submitMutation = useMutation({
    mutationFn: async () => {
      if (!user) throw new Error('User not authenticated');
      return submitTestResponse(test.id, user.id, responses);
    },
    onSuccess: () => {
      setTestCompleted(true);
      toast({
        title: 'Test Completed',
        description: 'Your responses have been submitted successfully.',
      });
    },
    onError: (error: any) => {
      console.error('Error submitting test:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to submit test responses',
        variant: 'destructive'
      });
      setIsSubmitting(false);
    }
  });

  const handleSubmit = () => {
    if (responses.length < totalQuestions) {
      toast({
        title: 'Incomplete Test',
        description: 'Please answer all questions before submitting.',
        variant: 'destructive'
      });
      return;
    }

    setIsSubmitting(true);
    submitMutation.mutate();
  };

  // Handle next lesson navigation
  const handleNextLesson = async () => {
    if (!courseId || !test.moduleId) {
      console.error('Missing courseId or moduleId for navigation');
      onComplete();
      return;
    }

    try {
      const { firstLessonSlug } = await getFirstLessonOfModule(test.moduleId);

      if (firstLessonSlug) {
        navigate(`/course/${courseId}/lesson/${firstLessonSlug}`);
      } else {
        // If no lessons found, go back to modules page
        navigate(`/course/${courseId}/modules`);
        toast({
          title: 'No Lessons Found',
          description: 'This module does not contain any lessons yet.',
        });
      }
    } catch (error) {
      console.error('Error navigating to next lesson:', error);
      // Fallback to original onComplete behavior
      onComplete();
    }
  };

  if (testCompleted) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-4xl mx-auto px-6"
      >
        <div className="min-h-screen flex flex-col justify-center items-center">
          <div className="text-center space-y-6">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center"
            >
              <CheckCircle className="w-8 h-8 text-green-600 dark:text-green-400" />
            </motion.div>

            <div>
              <h2 className="text-xl font-semibold text-green-800 dark:text-green-200 mb-2">
                Assessment Completed!
              </h2>
              <p className="text-green-700 dark:text-green-300 text-sm">
                Thank you for completing the {test.type === 'pre_test' ? 'pre-assessment' : 'post-assessment'}.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-900/50 rounded-lg p-4 border border-green-200 dark:border-green-800">
              <span className="text-green-700 dark:text-green-300 text-sm font-medium">
                Your responses have been recorded successfully
              </span>
            </div>

            {showNextLessonButton && courseId && test.type === 'pre_test' ? (
              <Button onClick={handleNextLesson} size="default" className="min-w-[140px]">
                Next Lesson
              </Button>
            ) : (
              <Button onClick={onComplete} size="default" className="min-w-[140px]">
                Continue to Course
              </Button>
            )}
          </div>
        </div>
      </motion.div>
    );
  }

  if (!currentQuestion) {
    return (
      <div className="w-full max-w-4xl mx-auto px-6">
        <div className="min-h-screen flex flex-col justify-center items-center">
          <div className="text-center space-y-6">
            <h2 className="text-xl font-semibold">No Questions Available</h2>
            <p className="text-muted-foreground text-sm">This test does not contain any questions.</p>
            <Button onClick={onComplete} size="default" className="min-w-[140px]">
              Continue
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      className="w-full max-w-6xl mx-auto px-8"
    >
      <div className="h-screen flex flex-col justify-between">
        {/* Header Section */}
        <div className="pt-3">
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-3">
            <div className="text-left">
              <h1 className="text-lg font-semibold text-foreground mb-0.5">{test.title}</h1>
              <div className="text-sm font-medium text-muted-foreground">
                Question {currentQuestionIndex + 1}
              </div>
            </div>
          </div>
        </div>

        {/* Content Section */}
        <div className="flex-1 flex flex-col justify-center py-4">
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentQuestionIndex}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="space-y-3"
              >
                {/* Question */}
                <div className="mb-3">
                  <p className="text-base leading-normal text-foreground">
                    {currentQuestion.question}
                  </p>
                </div>

                {/* Rating Options */}
                <div className="space-y-3">
                  <div className="space-y-3">
                    {ratingDescriptions.map((option, index) => (
                      <motion.div
                        key={option.value}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className={cn(
                          "relative flex items-center justify-between py-3 px-4 cursor-pointer transition-all duration-200 border-l-4",
                          getCurrentResponse() === option.value
                            ? "border-l-primary bg-primary/10 dark:bg-primary/20"
                            : "border-l-transparent hover:bg-gray-50 dark:hover:bg-gray-800/30"
                        )}
                        onClick={() => handleRatingSelect(option.value.toString())}
                      >
                        <div className="flex-1 text-sm font-medium text-foreground">
                          {option.label}
                        </div>
                        <div className="flex-shrink-0 ml-4">
                          <div className={cn(
                            "w-5 h-5 rounded-full border-2 transition-all duration-200 flex items-center justify-center",
                            getCurrentResponse() === option.value
                              ? "border-primary bg-primary"
                              : "border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700"
                          )}>
                            {getCurrentResponse() === option.value && (
                              <div className="w-2 h-2 rounded-full bg-white" />
                            )}
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>
              </motion.div>
            </AnimatePresence>
          </div>
        </div>

        {/* Footer Section */}
        <div className="-mt-2">
          <div className="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-3">
            <div className="flex justify-between items-center">
              <Button
                variant="outline"
                onClick={handlePreviousQuestion}
                disabled={currentQuestionIndex === 0}
                size="default"
                className="min-w-[100px] text-sm"
              >
                Previous
              </Button>

              <Button
                onClick={handleNextQuestion}
                disabled={!getCurrentResponse() || isSubmitting}
                size="default"
                className={cn(
                  "min-w-[100px] text-sm",
                  isLastQuestion && "bg-primary hover:bg-primary/90"
                )}
              >
                {isSubmitting ? (
                  "Submitting..."
                ) : isLastQuestion ? (
                  "Finish"
                ) : (
                  "Next"
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default ModuleTest;