# Netlify Deployment Troubleshooting Checklist

## MIME Type Issues Fix

If you're experiencing MIME type errors in the browser console when deploying to Netlify, follow these steps:

### 1. Deploy the Updated Build

1. Run the production build:
   ```
   npm run build:prod
   ```

2. Verify that the `_headers` file in your `dist` directory contains the correct MIME type settings:
   ```
   /*
     X-Frame-Options: DENY
     X-XSS-Protection: 1; mode=block
     X-Content-Type-Options: nosniff
     Referrer-Policy: strict-origin-when-cross-origin

   /assets/*.js
     Content-Type: application/javascript; charset=utf-8

   /assets/*.css
     Content-Type: text/css; charset=utf-8

   /*.js
     Content-Type: application/javascript; charset=utf-8

   /*.css
     Content-Type: text/css; charset=utf-8

   /*.svg
     Content-Type: image/svg+xml
   ```

3. Deploy the `dist` directory to Netlify using one of these methods:
   - Manual drag-and-drop of the `dist` folder in the Netlify dashboard
   - Using the Netlify CLI: `netlify deploy --prod`
   - Through Git integration with your repository

### 2. Check Netlify Site Settings

1. In your Netlify dashboard, go to **Site settings** > **Build & deploy** > **Post processing**
2. Disable "Asset optimization" as it can sometimes override your custom content type headers
3. Check that "Bundle CSS" and "Minify CSS" are disabled as well

### 3. After Deployment

1. Clear your browser cache completely:
   - Chrome: Settings > Privacy and security > Clear browsing data
   - Firefox: Settings > Privacy & Security > Cookies and Site Data > Clear Data
   - Edge: Settings > Privacy, search, and services > Clear browsing data

2. Open your deployed site in an incognito/private window to test without cache interference

3. Check the browser's Network tab in Developer Tools to verify:
   - JS files are served with `Content-Type: application/javascript`
   - CSS files are served with `Content-Type: text/css`

### 4. If Issues Persist

1. Try adding the MIME type configuration through the Netlify dashboard:
   - Go to Site settings > Build & deploy > Post processing > Headers
   - Add the following headers:
     ```
     /assets/*.js
       Content-Type: application/javascript; charset=utf-8
     /assets/*.css
       Content-Type: text/css; charset=utf-8
     /*.js
       Content-Type: application/javascript; charset=utf-8
     /*.css
       Content-Type: text/css; charset=utf-8
     ```

2. Check if your hosting plan allows custom headers

3. Consider using Netlify's Edge Functions or serverless functions to manipulate response headers

## Additional Troubleshooting

### Service Worker Issues

If you're using a service worker:

1. Make sure it's not caching old versions of your assets incorrectly
2. Consider temporarily disabling it to test if it's causing the issue

### CDN Caching

1. Netlify uses a CDN that might cache assets. Add a cache purge to your deployment:
   - Via Netlify dashboard: Deploy > Trigger deploy > Clear cache and deploy site
   - Via API: Use the site cache clear endpoint

### Network Debug

Check if the MIME type errors are consistently reproducible:
1. Try accessing your site from different networks
2. Test on different browsers
3. Try mobile vs. desktop devices

## Contact Netlify Support

If all else fails, contact Netlify support with:
1. Your site ID
2. Screenshots of the console errors
3. Details of steps you've already attempted
4. A HAR file export from your browser developer tools 