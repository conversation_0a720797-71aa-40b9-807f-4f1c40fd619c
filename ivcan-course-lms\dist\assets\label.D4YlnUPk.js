var n=Object.defineProperty;var r=Object.getOwnPropertySymbols;var i=Object.prototype.hasOwnProperty,l=Object.prototype.propertyIsEnumerable;var m=(a,e,s)=>e in a?n(a,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):a[e]=s,p=(a,e)=>{for(var s in e||(e={}))i.call(e,s)&&m(a,s,e[s]);if(r)for(var s of r(e))l.call(e,s)&&m(a,s,e[s]);return a};var c=(a,e)=>{var s={};for(var o in a)i.call(a,o)&&e.indexOf(o)<0&&(s[o]=a[o]);if(a!=null&&r)for(var o of r(a))e.indexOf(o)<0&&l.call(a,o)&&(s[o]=a[o]);return s};import{r as f,j as x,c7 as d}from"./vendor-react.BcAa1DKr.js";import{a3 as b}from"./vendor.DQpuTRuB.js";import{c as j}from"./index.BLDhDn0D.js";const u=b("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),y=f.forwardRef((o,s)=>{var t=o,{className:a}=t,e=c(t,["className"]);return x.jsx(d,p({ref:s,className:j(u(),a)},e))});y.displayName=d.displayName;export{y as L};
