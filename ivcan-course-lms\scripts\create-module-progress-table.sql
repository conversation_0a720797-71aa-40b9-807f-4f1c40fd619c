-- Create user_module_progress table to track module completion per user
CREATE TABLE IF NOT EXISTS public.user_module_progress (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  module_id UUID NOT NULL REFERENCES public.modules(id) ON DELETE CASCADE,
  is_completed BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, module_id)
);

-- Add RLS policies
ALTER TABLE public.user_module_progress ENABLE ROW LEVEL SECURITY;

-- Allow users to view their own progress
CREATE POLICY "Users can view their own module progress"
  ON public.user_module_progress
  FOR SELECT
  USING (auth.uid() = user_id);

-- Allow users to update their own progress
CREATE POLICY "Users can update their own module progress"
  ON public.user_module_progress
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Allow users to insert their own progress
CREATE POLICY "Users can insert their own module progress"
  ON public.user_module_progress
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_module_progress_user_id ON public.user_module_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_user_module_progress_module_id ON public.user_module_progress(module_id);
CREATE INDEX IF NOT EXISTS idx_user_module_progress_completion ON public.user_module_progress(is_completed);

-- Add comments
COMMENT ON TABLE public.user_module_progress IS 'Tracks module completion status per user';
COMMENT ON COLUMN public.user_module_progress.user_id IS 'The user who completed the module';
COMMENT ON COLUMN public.user_module_progress.module_id IS 'The module that was completed';
COMMENT ON COLUMN public.user_module_progress.is_completed IS 'Whether the module is completed by the user';
