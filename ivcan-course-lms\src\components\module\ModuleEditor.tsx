import React, { useState } from 'react';
import { useMutation, useQueryClient } from 'react-query';
import { toast } from 'react-hot-toast';
import { supabase } from '../../lib/supabase';

const ModuleEditor: React.FC<{ courseId: string; onClose: () => void }> = ({ courseId, onClose }) => {
  const queryClient = useQueryClient();
  const [isUploading, setIsUploading] = useState(false);
  const [imageFile, setImageFile] = useState<File | null>(null);

  // Save or update module
  const mutation = useMutation({
    mutationFn: async (values: ModuleFormValues) => {
      // Validate courseId
      if (!courseId) {
        throw new Error('Course ID is required');
      }

      // Generate a unique slug if needed
      const finalSlug = values.slug || values.title.toLowerCase().replace(/\s+/g, '-');

      // Handle image upload if there's a new image
      let finalImageUrl = values.image_url || '';

      if (imageFile) {
        setIsUploading(true);
        try {
          // Upload to Supabase storage
          const fileName = `module-images/new-${Date.now()}.${imageFile.name.split('.').pop()}`;
          const { data: uploadData, error: uploadError } = await supabase.storage
            .from('module-images')
            .upload(fileName, imageFile, {
              cacheControl: '3600',
              upsert: true
            });

          if (uploadError) {
            console.error('Error uploading image:', uploadError);
            throw new Error('Failed to upload image');
          }

          // Get the public URL
          const { data: { publicUrl } } = supabase.storage
            .from('module-images')
            .getPublicUrl(fileName);

          finalImageUrl = publicUrl;
          console.log('Image uploaded successfully:', publicUrl);
        } catch (error: any) {
          console.error('Error processing image:', error);
          toast({
            title: "Error",
            description: error.message || "Failed to process image",
            variant: "destructive"
          });
          throw error;
        } finally {
          setIsUploading(false);
        }
      }

      // Create the module
      const { data, error } = await supabase
        .from('modules')
        .insert([
          {
            title: values.title,
            slug: finalSlug,
            module_number: values.module_number,
            course_id: courseId,
            image_url: finalImageUrl
          }
        ])
        .select()
        .single();

      if (error) throw error;

      return data;
    },
    onSuccess: () => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ['admin-modules', courseId] });
      
      // Show success message
      toast({
        title: "Success",
        description: "Module created successfully",
      });

      // Close the editor
      onClose();
    },
    onError: (error: any) => {
      console.error('Error creating module:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to create module",
        variant: "destructive"
      });
    }
  });

  return (
    <div>
      {/* Render your form and other components here */}
    </div>
  );
};

export default ModuleEditor; 