export type QuestionType = 'multiple-choice' | 'true-false' | 'short-answer';

export interface BaseQuizQuestion {
  id: string;
  question: string;
  type: QuestionType;
  points: number;
}

export interface MultipleChoiceQuestion extends BaseQuizQuestion {
  type: 'multiple-choice';
  options: string[];
  correctAnswer: number;
}

export interface TrueFalseQuestion extends BaseQuizQuestion {
  type: 'true-false';
  correctAnswer: boolean;
}

export interface ShortAnswerQuestion extends BaseQuizQuestion {
  type: 'short-answer';
  correctAnswer: string;
}

export type QuizQuestion = MultipleChoiceQuestion | TrueFalseQuestion | ShortAnswerQuestion;

export interface QuizContent {
  questions: QuizQuestion[];
}

export function isQuizContent(content: string): boolean {
  try {
    const parsed = JSON.parse(content);
    return Array.isArray(parsed.questions);
  } catch (e) {
    return false;
  }
}

export function createDefaultQuestion(type: QuestionType): QuizQuestion {
  const baseQuestion = {
    id: crypto.randomUUID(),
    question: '',
    type,
    points: 1
  };

  switch (type) {
    case 'multiple-choice':
      return {
        ...baseQuestion,
        type: 'multiple-choice',
        options: ['', '', '', ''],
        correctAnswer: 0
      };
    case 'true-false':
      return {
        ...baseQuestion,
        type: 'true-false',
        correctAnswer: true
      };
    case 'short-answer':
      return {
        ...baseQuestion,
        type: 'short-answer',
        correctAnswer: ''
      };
  }
}
