/**
 * Enhanced content security service for markdown and HTML processing
 * Provides XSS protection, content validation, and secure rendering
 */

import DOMPurify from 'dompurify';

// Content Security Policy configuration
export const CSP_CONFIG = {
  'default-src': ["'self'"],
  'script-src': ["'self'", "'unsafe-inline'"],
  'style-src': ["'self'", "'unsafe-inline'"],
  'img-src': ["'self'", 'data:', 'https:', 'blob:', '*.supabase.co'],
  'media-src': ["'self'", 'https:', '*.supabase.co'],
  'frame-src': ["'self'", 'https://www.youtube.com', 'https://youtube.com'],
  'connect-src': ["'self'", 'https:', '*.supabase.co'],
  'font-src': ["'self'", 'data:', 'https:'],
  'object-src': ["'none'"],
  'base-uri': ["'self'"],
  'form-action': ["'self'"],
  'frame-ancestors': ["'none'"],
  'upgrade-insecure-requests': []
};

// Allowed HTML tags for different content types
export const ALLOWED_TAGS = {
  basic: [
    'p', 'br', 'strong', 'em', 'u', 'del', 'mark', 'code', 'pre',
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'ul', 'ol', 'li',
    'blockquote',
    'a', 'img',
    'hr'
  ],
  extended: [
    'p', 'br', 'strong', 'em', 'u', 'del', 'mark', 'code', 'pre',
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'ul', 'ol', 'li',
    'blockquote',
    'a', 'img',
    'hr',
    'table', 'thead', 'tbody', 'tr', 'th', 'td',
    'div', 'span',
    'details', 'summary',
    'input' // For task lists
  ],
  full: [
    'p', 'br', 'strong', 'em', 'u', 'del', 'mark', 'code', 'pre',
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'ul', 'ol', 'li',
    'blockquote',
    'a', 'img',
    'hr',
    'table', 'thead', 'tbody', 'tr', 'th', 'td',
    'div', 'span',
    'details', 'summary',
    'input',
    'iframe', // Carefully controlled
    'figure', 'figcaption',
    'video', 'audio', 'source'
  ]
};

// Allowed attributes for different security levels
export const ALLOWED_ATTRIBUTES = {
  basic: [
    'href', 'src', 'alt', 'title', 'class', 'id',
    'width', 'height', 'style'
  ],
  extended: [
    'href', 'src', 'alt', 'title', 'class', 'id',
    'width', 'height', 'style',
    'type', 'checked', 'disabled',
    'colspan', 'rowspan',
    'data-*'
  ],
  full: [
    'href', 'src', 'alt', 'title', 'class', 'id',
    'width', 'height', 'style',
    'type', 'checked', 'disabled',
    'colspan', 'rowspan',
    'data-*',
    'frameborder', 'allowfullscreen', 'allow',
    'controls', 'autoplay', 'muted', 'loop',
    'loading'
  ]
};

// URL validation patterns
const SAFE_URL_PATTERNS = {
  http: /^https?:\/\/[^\s<>"']+$/i,
  data: /^data:image\/[a-z]+;base64,[a-z0-9+/]+=*$/i,
  blob: /^blob:[a-z0-9-]+$/i,
  youtube: /^https:\/\/(www\.)?(youtube\.com\/embed\/|youtu\.be\/)[a-z0-9_-]+(\?[a-z0-9&=_-]*)?$/i
};

export interface SecurityConfig {
  level: 'basic' | 'extended' | 'full';
  allowIframes?: boolean;
  allowedDomains?: string[];
  maxContentLength?: number;
  validateUrls?: boolean;
}

export class ContentSecurityService {
  private config: SecurityConfig;

  constructor(config: SecurityConfig = { level: 'extended' }) {
    this.config = {
      allowIframes: false,
      allowedDomains: [],
      maxContentLength: 1048576, // 1MB
      validateUrls: true,
      ...config
    };
  }

  /**
   * Sanitize HTML content with security controls
   */
  sanitizeHtml(html: string): string {
    if (!html) return '';

    // Check content length
    if (this.config.maxContentLength && html.length > this.config.maxContentLength) {
      throw new Error(`Content exceeds maximum length of ${this.config.maxContentLength} characters`);
    }

    const allowedTags = ALLOWED_TAGS[this.config.level];
    const allowedAttributes = ALLOWED_ATTRIBUTES[this.config.level];

    // Configure DOMPurify
    const purifyConfig: any = {
      ADD_TAGS: allowedTags,
      ADD_ATTR: allowedAttributes,
      ALLOW_DATA_ATTR: this.config.level !== 'basic',
      FORBID_SCRIPT: true,
      FORBID_TAGS: ['script', 'object', 'embed', 'applet'],
      FORBID_ATTR: ['onerror', 'onload', 'onclick', 'onmouseover'],
    };

    // Handle iframes specially
    if (this.config.allowIframes && this.config.level === 'full') {
      purifyConfig.ADD_TAGS.push('iframe');
      purifyConfig.ADD_ATTR.push('frameborder', 'allowfullscreen', 'allow');
    }

    // Custom URL validator
    if (this.config.validateUrls) {
      DOMPurify.addHook('afterSanitizeAttributes', (node) => {
        // Validate src attributes
        if (node.hasAttribute('src')) {
          const src = node.getAttribute('src');
          if (src && !this.isUrlSafe(src)) {
            node.removeAttribute('src');
          }
        }

        // Validate href attributes
        if (node.hasAttribute('href')) {
          const href = node.getAttribute('href');
          if (href && !this.isUrlSafe(href)) {
            node.removeAttribute('href');
          }
        }

        // Special handling for iframes
        if (node.tagName === 'IFRAME') {
          const src = node.getAttribute('src');
          if (!src || !this.isIframeSafe(src)) {
            node.parentNode?.removeChild(node);
          }
        }
      });
    }

    const sanitized = DOMPurify.sanitize(html, purifyConfig);

    // Clean up DOMPurify hooks
    DOMPurify.removeAllHooks();

    return sanitized;
  }

  /**
   * Validate if a URL is safe to use
   */
  private isUrlSafe(url: string): boolean {
    if (!url) return false;

    // Check against safe patterns
    const isSafePattern = Object.values(SAFE_URL_PATTERNS).some(pattern => pattern.test(url));
    if (!isSafePattern) return false;

    // Check allowed domains if specified
    if (this.config.allowedDomains && this.config.allowedDomains.length > 0) {
      try {
        const urlObj = new URL(url);
        const domain = urlObj.hostname.toLowerCase();
        return this.config.allowedDomains.some(allowedDomain => 
          domain === allowedDomain || domain.endsWith('.' + allowedDomain)
        );
      } catch {
        return false;
      }
    }

    return true;
  }

  /**
   * Validate if an iframe source is safe
   */
  private isIframeSafe(src: string): boolean {
    if (!this.config.allowIframes) return false;

    // Only allow YouTube embeds for now
    return SAFE_URL_PATTERNS.youtube.test(src);
  }

  /**
   * Validate content structure and encoding
   */
  validateContent(content: string): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check content length
    if (this.config.maxContentLength && content.length > this.config.maxContentLength) {
      errors.push(`Content exceeds maximum length of ${this.config.maxContentLength} characters`);
    }

    // Check for potentially dangerous patterns
    const dangerousPatterns = [
      /<script[^>]*>/i,
      /javascript:/i,
      /vbscript:/i,
      /data:text\/html/i,
      /on\w+\s*=/i, // Event handlers
    ];

    dangerousPatterns.forEach(pattern => {
      if (pattern.test(content)) {
        errors.push(`Content contains potentially dangerous pattern: ${pattern.source}`);
      }
    });

    // Validate UTF-8 encoding
    try {
      const encoded = new TextEncoder().encode(content);
      const decoded = new TextDecoder('utf-8', { fatal: true }).decode(encoded);
      if (decoded !== content) {
        errors.push('Content contains invalid UTF-8 encoding');
      }
    } catch {
      errors.push('Content contains invalid UTF-8 encoding');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Generate Content Security Policy header value
   */
  generateCSPHeader(): string {
    const directives = Object.entries(CSP_CONFIG)
      .map(([directive, values]) => {
        if (values.length === 0) return directive;
        return `${directive} ${values.join(' ')}`;
      })
      .join('; ');

    return directives;
  }

  /**
   * Escape HTML entities in text content
   */
  escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * Unescape HTML entities
   */
  unescapeHtml(html: string): string {
    const div = document.createElement('div');
    div.innerHTML = html;
    return div.textContent || div.innerText || '';
  }
}

// Default security service instance
export const contentSecurity = new ContentSecurityService({
  level: 'extended',
  allowIframes: true,
  allowedDomains: [
    'youtube.com',
    'www.youtube.com',
    'youtu.be',
    'supabase.co',
    'jibspqwieubavucdtccv.supabase.co' // Specific Supabase project domain
  ],
  maxContentLength: 1048576, // 1MB
  validateUrls: true
});

// Convenience functions
export function sanitizeContent(html: string, level: 'basic' | 'extended' | 'full' = 'extended'): string {
  const service = new ContentSecurityService({ level });
  return service.sanitizeHtml(html);
}

export function validateContentSecurity(content: string): { valid: boolean; errors: string[] } {
  return contentSecurity.validateContent(content);
}

export function generateCSP(): string {
  return contentSecurity.generateCSPHeader();
}
