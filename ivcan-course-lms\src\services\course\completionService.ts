import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { getEnrollmentStatus } from './enrollmentApi';
import { awardCourseBadge } from '@/services/achievements/achievementService';

/**
 * A service dedicated to handling course completion
 * This uses multiple approaches to ensure the course is marked as completed
 */

/**
 * Enhanced service for managing course completion functionality with robust error handling
 * This service handles both module completion and final course completion (certificate generation)
 */

interface CompletionResult {
  success: boolean;
  error?: string;
  data?: any;
}

// Add this type definition at the top of the file to tell TypeScript about our custom RPC functions
type SupabaseRpcFunction =
  | "complete_course"
  | "assign_role"
  | "has_role"
  | "get_column_info"
  | "count_completion_records"
  | "get_trigger_info"
  | "mark_lesson_completed"
  | "check_module_completion";

/**
 * Ensures course completion is stored in localStorage for client-side tracking
 * @param courseId The ID of the course to mark as completed
 * @param userId The ID of the user completing the course
 */
export function saveCompletionToLocalStorage(courseId: string, userId: string): boolean {
  try {
    // Get existing completed courses or initialize empty array
    const completedCoursesStr = localStorage.getItem('completedCourses') || '[]';
    const completedCourses = JSON.parse(completedCoursesStr);

    // Add this course if not already included
    if (!completedCourses.includes(courseId)) {
      completedCourses.push(courseId);
      localStorage.setItem('completedCourses', JSON.stringify(completedCourses));
    }

    // Store additional metadata
    const now = new Date().toISOString();
    localStorage.setItem(`course_${courseId}_completed_at`, now);
    localStorage.setItem(`course_${courseId}_completed_by`, userId);

    // Add lastCompletedCourse for easy access
    localStorage.setItem('lastCompletedCourse', courseId);

    console.log('Course completion successfully saved to localStorage:', courseId);
    return true;
  } catch (storageError) {
    console.error('Error storing completion in localStorage:', storageError);
    return false;
  }
}

/**
 * Check if a course is completed for a user either in database or localStorage
 * @param courseId ID of the course to check
 * @param userId ID of the user to check
 * @returns Promise resolving to true if the course is completed
 */
export async function isCourseCompleted(courseId: string, userId: string): Promise<boolean> {
  // First check localStorage
  try {
    const completedCoursesStr = localStorage.getItem('completedCourses');
    if (completedCoursesStr) {
      const completedCourses = JSON.parse(completedCoursesStr);
      if (completedCourses.includes(courseId)) {
        console.log('Course completion found in localStorage');
        return true;
      }
    }
  } catch (e) {
    console.error('Error checking localStorage:', e);
  }

  // Then check the database
  try {
    const enrollment = await getEnrollmentStatus(courseId, userId);
    if (enrollment?.status === 'completed') {
      console.log('Course completion found in database');

      // Update localStorage for consistency
      saveCompletionToLocalStorage(courseId, userId);

      return true;
    }
  } catch (e) {
    console.error('Error checking enrollment status:', e);
  }

  return false;
}

/**
 * Marks a course as completed in both database and localStorage
 */
export async function completeCourse(courseId: string, userId: string): Promise<boolean> {
  if (!courseId || !userId) {
    console.error('Missing required parameters');
    return false;
  }

  try {
    const now = new Date().toISOString();

    // 1. Update enrollment status using RPC function
    const { error: rpcError } = await supabase.rpc('complete_course' as any, {
      p_user_id: userId,
      p_course_id: courseId
    });

    if (rpcError) {
      console.error('Failed to complete course using RPC:', rpcError);

      // Fallback: Try direct database update
      const { error: enrollmentError } = await supabase
        .from('user_course_enrollment')
        .upsert({
          user_id: userId,
          course_id: courseId,
          status: 'completed',
          enrolled_at: now,
          completed_at: now,
          updated_at: now
        }, {
          onConflict: 'user_id,course_id'
        });

      if (enrollmentError) {
        console.error('Failed to update enrollment:', enrollmentError);
        return false;
      }
    }

    // 2. Update progress
    const { error: progressError } = await supabase
      .from('user_course_progress')
      .upsert({
        user_id: userId,
        course_id: courseId,
        completed_modules: 100,
        updated_at: now
      }, {
        onConflict: 'user_id,course_id'
      });

    if (progressError) {
      console.error('Failed to update progress:', progressError);
      // Continue anyway as this is not critical
    }

    // 3. Store in localStorage
    try {
      // Update completed courses list
      const completedCoursesStr = localStorage.getItem('completedCourses') || '[]';
      const completedCourses = JSON.parse(completedCoursesStr);
      if (!completedCourses.includes(courseId)) {
        completedCourses.push(courseId);
        localStorage.setItem('completedCourses', JSON.stringify(completedCourses));
      }

      // Store completion metadata
      localStorage.setItem(`course_${courseId}_completed_at`, now);
      localStorage.setItem(`course_${courseId}_completed_by`, userId);
      localStorage.setItem('lastCompletedCourse', courseId);
    } catch (e) {
      console.error('LocalStorage update failed:', e);
      // Continue anyway as this is not critical
    }

    // Verify the completion was saved
    const { data: verifyData, error: verifyError } = await supabase
      .from('user_course_enrollment')
      .select('status, completed_at')
      .eq('user_id', userId)
      .eq('course_id', courseId)
      .single();

    if (verifyError) {
      console.error('Failed to verify completion:', verifyError);
      return false;
    }

    return verifyData?.status === 'completed' && !!verifyData?.completed_at;
  } catch (error) {
    console.error('Course completion failed:', error);
    return false;
  }
}

/**
 * Robust function to mark a lesson as completed, using database functions and proper error handling
 */
export const markLessonAsCompleted = async (
  lessonId: string,
  userId: string,
  clientInfo?: string
): Promise<CompletionResult> => {
  console.log(`[COMPLETION SERVICE] Starting lesson completion for lesson ${lessonId} and user ${userId}`);

  if (!lessonId || !userId) {
    console.error('[COMPLETION SERVICE] Missing required parameters', { lessonId, userId });
    return {
      success: false,
      error: 'Missing required parameters'
    };
  }

  // Validate UUID format to prevent obvious errors
  const isValidUuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(lessonId);
  if (!isValidUuid) {
    console.error('[COMPLETION SERVICE] Invalid lesson ID format (not a UUID)', { lessonId });
    return {
      success: false,
      error: `Invalid lesson ID format: "${lessonId}" is not a valid UUID`
    };
  }

  try {
    // 1. First check if the lesson is already completed to avoid unnecessary operations
    const { data: existingProgress, error: progressError } = await supabase
      .from('user_lesson_progress')
      .select('*')
      .eq('user_id', userId)
      .eq('lesson_id', lessonId)
      .maybeSingle();

    if (!progressError && existingProgress?.is_completed) {
      console.log('[COMPLETION SERVICE] Lesson already marked as completed');
      return {
        success: true,
        data: existingProgress
      };
    }

    // 2. Use the database function to mark the lesson as completed
    const { data, error } = await supabase.rpc('mark_lesson_completed' as any, {
      p_user_id: userId,
      p_lesson_id: lessonId
    });

    if (error) {
      console.error('[COMPLETION SERVICE] Error using mark_lesson_completed function:', error);

      // Fallback: direct table update if RPC function fails
      const now = new Date().toISOString();
      const { data: fallbackData, error: fallbackError } = await supabase
        .from('user_lesson_progress')
        .upsert({
          user_id: userId,
          lesson_id: lessonId,
          is_completed: true,
          completed_at: now,
          updated_at: now
        }, {
          onConflict: 'user_id,lesson_id'
        })
        .select('*')
        .maybeSingle();

      if (fallbackError) {
        console.error('[COMPLETION SERVICE] Fallback update also failed:', fallbackError);

        // Last resort - save to localStorage
        try {
          localStorage.setItem(`lesson_${lessonId}_completed`, 'true');
          localStorage.setItem(`lesson_${lessonId}_completed_at`, now);

          return {
            success: true,
            data: {
              is_completed: true,
              warning: 'Completion saved locally only due to database errors'
            }
          };
        } catch (storageError) {
          console.error('[COMPLETION SERVICE] Even localStorage fallback failed:', storageError);

          return {
            success: false,
            error: `Failed to mark lesson as completed: ${fallbackError.message}`
          };
        }
      }

      console.log('[COMPLETION SERVICE] Lesson marked as completed using fallback method');

      // Trigger module completion check
      setTimeout(() => {
        try {
          // Get the module ID for this lesson
          supabase
            .from('lessons')
            .select('module_id')
            .eq('id', lessonId)
            .single()
            .then(({ data: lesson }) => {
              if (lesson?.module_id) {
                // Use the check_module_completion function
                supabase.rpc('check_module_completion' as any, {
                  p_user_id: userId,
                  p_module_id: lesson.module_id
                }).then(({ error: moduleError }) => {
                  if (moduleError) {
                    console.error('[COMPLETION SERVICE] Error checking module completion:', moduleError);
                  }
                });
              }
            });
        } catch (moduleCheckError) {
          console.error('[COMPLETION SERVICE] Error in module completion check:', moduleCheckError);
        }
      }, 100);

      return {
        success: true,
        data: fallbackData || { is_completed: true }
      };
    }

    console.log('[COMPLETION SERVICE] Lesson successfully marked as completed using RPC function');

    // Update user access after lesson completion
    try {
      const { updateUserAccess } = await import('./accessControlService');
      await updateUserAccess(userId);
      console.log('[COMPLETION SERVICE] User access updated after lesson completion');
    } catch (accessError) {
      console.error('[COMPLETION SERVICE] Error updating user access:', accessError);
      // Don't fail the completion if access update fails
    }

    return {
      success: true,
      data: { is_completed: true }
    };
  } catch (error: any) {
    console.error('[COMPLETION SERVICE] Unexpected error in markLessonAsCompleted:', error);
    return {
      success: false,
      error: `Unexpected error: ${error.message || 'Unknown error'}`
    };
  }
};

/**
 * Explicitly check and update module completion status for a specific user
 */
export const checkAndUpdateModuleCompletion = async (
  moduleId: string,
  userId: string
): Promise<CompletionResult> => {
  console.log(`[COMPLETION SERVICE] Checking module completion for module ${moduleId} and user ${userId}`);

  try {
    // Use the database function to check module completion
    const { data, error } = await supabase.rpc('check_module_completion' as any, {
      p_user_id: userId,
      p_module_id: moduleId
    });

    if (error) {
      console.error('[COMPLETION SERVICE] Error using check_module_completion function:', error);

      // Fall back to the old implementation if needed
      // Get all lessons for this module
      const { data: lessons, error: lessonsError } = await supabase
        .from('lessons')
        .select('id')
        .eq('module_id', moduleId);

      if (lessonsError) {
        console.error('[COMPLETION SERVICE] Error fetching lessons:', lessonsError);
        return {
          success: false,
          error: `Error fetching lessons: ${lessonsError.message}`
        };
      }

      if (!lessons || lessons.length === 0) {
        console.log('[COMPLETION SERVICE] No lessons found for module');
        return {
          success: false,
          error: 'No lessons found for module'
        };
      }

      // Get completed lessons for this module
      const { data: completedLessons, error: progressError } = await supabase
        .from('user_lesson_progress')
        .select('lesson_id')
        .eq('user_id', userId)
        .eq('is_completed', true)
        .in('lesson_id', lessons.map(l => l.id));

      if (progressError) {
        console.error('[COMPLETION SERVICE] Error fetching completed lessons:', progressError);
        return {
          success: false,
          error: `Error fetching completed lessons: ${progressError.message}`
        };
      }

      const completedCount = completedLessons?.length || 0;
      const totalLessons = lessons.length;
      const allLessonsCompleted = completedCount === totalLessons;

      console.log(`[COMPLETION SERVICE] Module ${moduleId} has ${completedCount}/${totalLessons} lessons completed`);

      // Update module progress
      const now = new Date().toISOString();
      const { error: moduleUpdateError } = await supabase
        .from('user_module_progress')
        .upsert({
          user_id: userId,
          module_id: moduleId,
          is_completed: allLessonsCompleted,
          completed_at: allLessonsCompleted ? now : null,
          updated_at: now
        }, {
          onConflict: 'user_id,module_id'
        });

      if (moduleUpdateError) {
        console.error('[COMPLETION SERVICE] Error updating module progress:', moduleUpdateError);
        return {
          success: false,
          error: `Error updating module progress: ${moduleUpdateError.message}`
        };
      }

      return {
        success: true,
        data: {
          moduleId,
          userId,
          isCompleted: allLessonsCompleted,
          completedLessons: completedCount,
          totalLessons
        }
      };
    }

    // data from the RPC function will be true if all lessons are completed
    const isCompleted = !!data;

    console.log(`[COMPLETION SERVICE] Module progress updated successfully, is_completed=${isCompleted}`);

    return {
      success: true,
      data: {
        moduleId,
        userId,
        isCompleted
      }
    };
  } catch (error: any) {
    console.error('[COMPLETION SERVICE] Unexpected error in checkAndUpdateModuleCompletion:', error);
    return {
      success: false,
      error: `Unexpected error: ${error.message || 'Unknown error'}`
    };
  }
};

/**
 * Get detailed lesson completion status for a specific lesson and user
 */
export const getLessonCompletionStatus = async (
  lessonId: string,
  userId: string
): Promise<CompletionResult> => {
  try {
    const { data, error } = await supabase
      .from('user_lesson_progress')
      .select('*')
      .eq('user_id', userId)
      .eq('lesson_id', lessonId)
      .maybeSingle();

    if (error) {
      console.error('[COMPLETION SERVICE] Error fetching lesson completion status:', error);
      return {
        success: false,
        error: `Error fetching lesson completion status: ${error.message}`
      };
    }

    const isCompleted = data?.is_completed === true;

    return {
      success: true,
      data: {
        isCompleted,
        completionRecord: data || null
      }
    };
  } catch (error: any) {
    console.error('[COMPLETION SERVICE] Unexpected error in getLessonCompletionStatus:', error);
    return {
      success: false,
      error: `Unexpected error: ${error.message || 'Unknown error'}`
    };
  }
};

/**
 * Get debug information about database schema related to lesson completion
 */
export const getCompletionSystemStatus = async (): Promise<CompletionResult> => {
  try {
    // Check user_lesson_progress table columns
    const { data: columns, error: columnsError } = await supabase
      .rpc('get_column_info' as any, {
        table_name: 'user_lesson_progress'
      });

    if (columnsError) {
      return {
        success: false,
        error: `Could not fetch column info: ${columnsError.message}`
      };
    }

    // Check for recent completion attempts
    const { data: recentAttempts, error: attemptsError } = await supabase
      .from('completion_audit_log' as any)
      .select('*')
      .order('completed_at', { ascending: false })
      .limit(10);

    if (attemptsError) {
      console.error('[COMPLETION SERVICE] Error fetching recent completion attempts:', attemptsError);
    }

    return {
      success: true,
      data: {
        tableInfo: columns,
        recentAttempts: recentAttempts || []
      }
    };
  } catch (error: any) {
    return {
      success: false,
      error: `Unexpected error: ${error.message || 'Unknown error'}`
    };
  }
};

/**
 * Explicitly finish a course - this is called when user clicks "Finish Course" button
 * This function awards the course badge and generates the certificate
 */
export const finishCourse = async (
  courseId: string,
  userId: string
): Promise<CompletionResult> => {
  console.log(`[COMPLETION SERVICE] Starting explicit course finish for course ${courseId} and user ${userId}`);

  if (!courseId || !userId) {
    console.error('[COMPLETION SERVICE] Missing required parameters', { courseId, userId });
    return {
      success: false,
      error: 'Missing required parameters'
    };
  }

  try {
    // First, verify that all modules in the course are actually completed
    const { data: modules, error: modulesError } = await supabase
      .from('modules')
      .select('id')
      .eq('course_id', courseId)
      .order('module_number');

    if (modulesError || !modules || modules.length === 0) {
      console.error('[COMPLETION SERVICE] Error fetching modules or no modules found:', modulesError);
      return {
        success: false,
        error: 'Could not verify course modules'
      };
    }

    // Check if all modules are completed by this user
    const { data: completedModules, error: progressError } = await supabase
      .from('user_module_progress')
      .select('module_id')
      .eq('user_id', userId)
      .eq('is_completed', true)
      .in('module_id', modules.map(m => m.id));

    if (progressError) {
      console.error('[COMPLETION SERVICE] Error checking module progress:', progressError);
      return {
        success: false,
        error: 'Could not verify module completion status'
      };
    }

    const completedCount = completedModules?.length || 0;
    const totalModules = modules.length;

    if (completedCount !== totalModules) {
      console.error(`[COMPLETION SERVICE] Not all modules completed: ${completedCount}/${totalModules}`);
      return {
        success: false,
        error: `Only ${completedCount} of ${totalModules} modules completed. Please complete all modules first.`
      };
    }

    console.log(`[COMPLETION SERVICE] All ${totalModules} modules verified as completed`);

    // Now complete the course and award certificate
    const now = new Date().toISOString();

    // 1. Update enrollment status to 'completed' (this generates the certificate)
    // Use direct database update instead of RPC to avoid potential hanging issues
    console.log('[COMPLETION SERVICE] Updating enrollment status to completed');

    const { error: enrollmentError } = await supabase
      .from('user_course_enrollment')
      .upsert({
        user_id: userId,
        course_id: courseId,
        status: 'completed',
        enrolled_at: now,
        completed_at: now,
        updated_at: now
      }, {
        onConflict: 'user_id,course_id'
      });

    if (enrollmentError) {
      console.error('[COMPLETION SERVICE] Failed to update enrollment:', enrollmentError);
      return {
        success: false,
        error: 'Failed to complete course enrollment'
      };
    }

    console.log('[COMPLETION SERVICE] Successfully updated enrollment status to completed');

    // 2. Award the course completion badge with timeout
    try {
      console.log(`[COMPLETION SERVICE] Awarding course badge for course ${courseId}`);

      // Add timeout to prevent hanging
      const badgePromise = awardCourseBadge(userId, courseId);
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Badge awarding timeout')), 10000)
      );

      await Promise.race([badgePromise, timeoutPromise]);
      console.log('[COMPLETION SERVICE] Course badge awarded successfully');
    } catch (badgeError) {
      console.error('[COMPLETION SERVICE] Error awarding course badge:', badgeError);
      // Continue anyway - badge awarding should not block course completion
    }

    console.log(`[COMPLETION SERVICE] Successfully finished course ${courseId} for user ${userId}`);

    return {
      success: true,
      data: {
        courseId,
        userId,
        completedAt: now
      }
    };

  } catch (error) {
    console.error('[COMPLETION SERVICE] Unexpected error in finishCourse:', error);
    return {
      success: false,
      error: 'An unexpected error occurred while finishing the course'
    };
  }
};
