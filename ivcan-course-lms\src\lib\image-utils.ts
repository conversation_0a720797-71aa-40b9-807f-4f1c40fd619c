import { supabase } from '@/lib/supabase';

/**
 * Validates an image file for type and size constraints
 * @param file The file to validate
 * @returns Validation result with status and optional error message
 */
export function validateImage(file: File): { valid: boolean; message?: string } {
  // Check if file exists
  if (!file) {
    return { valid: false, message: 'No file provided' };
  }
  
  // Validate file type (only accept images)
  if (!file.type.startsWith('image/')) {
    return { valid: false, message: 'Please upload an image file' };
  }
  
  // Validate specific image formats
  const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
  if (!validTypes.includes(file.type)) {
    return { valid: false, message: 'Please upload a JPEG, PNG, or WebP image' };
  }
  
  // Check file size (max 5MB)
  const MAX_SIZE = 5 * 1024 * 1024; // 5MB in bytes
  if (file.size > MAX_SIZE) {
    return { 
      valid: false, 
      message: `Image is too large. Maximum size is ${MAX_SIZE / (1024 * 1024)}MB` 
    };
  }
  
  return { valid: true };
}

/**
 * Uploads a module image to Supabase storage
 * @param file The image file to upload
 * @param courseId Course ID for organizing images
 * @param moduleId Optional module ID to include in the file path
 * @returns The storage path of the uploaded image or null if upload fails
 */
export async function uploadModuleImage(
  file: File, 
  courseId: string,
  moduleId?: string
): Promise<string | null> {
  try {
    // Validate the image
    const validation = validateImage(file);
    if (!validation.valid) {
      console.error('Image validation failed:', validation.message);
      throw new Error(validation.message || 'Invalid image');
    }

    // Create a structured path for better organization
    const fileName = `courses/${courseId}/modules/${moduleId || 'new'}/${Date.now()}.${file.name.split('.').pop()}`;
    
    // Upload with exponential backoff retry
    return await uploadWithRetry(file, fileName);
  } catch (error) {
    console.error('Error in uploadModuleImage:', error);
    return null;
  }
}

/**
 * Uploads a file with retry logic for better reliability
 * @param file The file to upload
 * @param path The storage path
 * @returns The public URL of the uploaded file or null if upload fails
 */
export async function uploadWithRetry(
  file: File, 
  path: string, 
  maxRetries = 3
): Promise<string | null> {
  let retries = 0;
  
  while (retries < maxRetries) {
    try {
      const { error: uploadError } = await supabase.storage
        .from('module-images')
        .upload(path, file, {
          cacheControl: '3600',
          upsert: true
        });

      if (uploadError) {
        console.error(`Upload attempt ${retries + 1} failed:`, uploadError);
        retries++;
        
        if (retries >= maxRetries) {
          throw new Error('Maximum upload retries exceeded');
        }
        
        // Wait before retrying (with exponential backoff)
        await new Promise(resolve => 
          setTimeout(resolve, 1000 * Math.pow(2, retries))
        );
        continue;
      }

      // Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from('module-images')
        .getPublicUrl(path);

      return publicUrl;
    } catch (error) {
      console.error(`Upload attempt ${retries + 1} failed with exception:`, error);
      retries++;
      
      if (retries >= maxRetries) {
        throw error;
      }
      
      // Wait before retrying
      await new Promise(resolve => 
        setTimeout(resolve, 1000 * Math.pow(2, retries))
      );
    }
  }
  
  return null;
}

/**
 * Transforms an image URL to get different sizes/formats
 * This is a utility function that works with Supabase Storage URLs 
 * @param url Original image URL
 * @param options Transformation options
 * @returns Transformed URL
 */
export function transformImageUrl(
  url: string,
  options?: {
    width?: number;
    height?: number;
    format?: 'webp' | 'avif' | 'jpg' | 'png';
    quality?: number;
  }
): string {
  if (!url) return '';
  
  // If no options, return original URL
  if (!options) return url;
  
  // Only proceed if the URL is from our storage
  if (!url.includes('storage.googleapis.com') && !url.includes('supabase.co')) {
    return url;
  }
  
  try {
    // Extract base URL and path
    const urlObj = new URL(url);
    let transformParams = '';
    
    // Add transformation parameters
    if (options.width) transformParams += `width=${options.width}&`;
    if (options.height) transformParams += `height=${options.height}&`;
    if (options.format) transformParams += `format=${options.format}&`;
    if (options.quality) transformParams += `quality=${options.quality}&`;
    
    // Remove trailing ampersand
    if (transformParams.endsWith('&')) {
      transformParams = transformParams.slice(0, -1);
    }
    
    // Add transformation parameters to URL
    if (transformParams) {
      // Check if URL already has query parameters
      if (urlObj.search) {
        return `${url}&${transformParams}`;
      } else {
        return `${url}?${transformParams}`;
      }
    }
    
    return url;
  } catch (error) {
    console.error('Error transforming image URL:', error);
    return url;
  }
}

/**
 * Cleanup orphaned module images that are no longer referenced in the database
 * This should be run periodically as a maintenance task
 */
export async function cleanupOrphanedModuleImages(): Promise<void> {
  try {
    // Get all module image URLs from the database
    const { data: modules, error: dbError } = await supabase
      .from('modules')
      .select('image_url')
      .not('image_url', 'is', null);
    
    if (dbError) throw dbError;
    
    // Create a set of used image URLs
    const usedImageUrls = new Set(modules?.map(m => m.image_url) || []);
    
    // List all images in the storage bucket (this may need pagination for large numbers)
    const { data: storageFiles, error: storageError } = await supabase.storage
      .from('module-images')
      .list();
    
    if (storageError) throw storageError;
    if (!storageFiles) return;
    
    // Find and delete orphaned files
    const orphanedFiles = storageFiles.filter(file => {
      const fileUrl = supabase.storage
        .from('module-images')
        .getPublicUrl(file.name).data.publicUrl;
      
      return !usedImageUrls.has(fileUrl);
    });
    
    // Delete orphaned files
    for (const file of orphanedFiles) {
      await supabase.storage
        .from('module-images')
        .remove([file.name]);
      
      console.log(`Deleted orphaned file: ${file.name}`);
    }
  } catch (error) {
    console.error('Error cleaning up orphaned module images:', error);
  }
} 