-- Fix RLS policies for user_lesson_progress table

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own lesson progress" ON public.user_lesson_progress;
DROP POLICY IF EXISTS "Users can update their own lesson progress" ON public.user_lesson_progress;
DROP POLICY IF EXISTS "Users can insert their own lesson progress" ON public.user_lesson_progress;

-- Create new policies with more permissive rules
CREATE POLICY "Users can view their own lesson progress" 
ON public.user_lesson_progress FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own lesson progress" 
ON public.user_lesson_progress FOR UPDATE 
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can insert their own lesson progress" 
ON public.user_lesson_progress FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Make sure RLS is enabled
ALTER TABLE public.user_lesson_progress ENABLE ROW LEVEL SECURITY;

-- Add a trigger to update module completion when lesson completion changes
CREATE OR REPLACE FUNCTION update_module_completion()
R<PERSON>URNS TRIGGER AS $$
DECLARE
  module_id_val UUID;
  user_id_val UUID;
  total_lessons INTEGER;
  completed_lessons INTEGER;
BEGIN
  -- Get the user ID
  user_id_val := NEW.user_id;
  
  -- Get the module ID for this lesson
  SELECT module_id INTO module_id_val FROM public.lessons WHERE id = NEW.lesson_id;
  
  IF module_id_val IS NULL THEN
    RAISE NOTICE 'No module found for lesson %', NEW.lesson_id;
    RETURN NEW;
  END IF;
  
  -- Count total lessons for this module
  SELECT COUNT(*) INTO total_lessons FROM public.lessons WHERE module_id = module_id_val;
  
  -- Count completed lessons for this user and module
  SELECT COUNT(*) INTO completed_lessons 
  FROM public.user_lesson_progress ulp
  JOIN public.lessons l ON ulp.lesson_id = l.id
  WHERE l.module_id = module_id_val 
    AND ulp.user_id = user_id_val 
    AND ulp.completed = TRUE;
  
  RAISE NOTICE 'Module % has % completed lessons out of %', module_id_val, completed_lessons, total_lessons;
  
  -- If all lessons are completed, mark the module as completed
  IF completed_lessons = total_lessons THEN
    UPDATE public.modules
    SET 
      is_completed = TRUE,
      updated_at = NOW()
    WHERE id = module_id_val;
    
    RAISE NOTICE 'Marked module % as completed', module_id_val;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to update module completion when lesson completion changes
DROP TRIGGER IF EXISTS on_lesson_completion ON public.user_lesson_progress;
CREATE TRIGGER on_lesson_completion
  AFTER INSERT OR UPDATE OF completed ON public.user_lesson_progress
  FOR EACH ROW
  WHEN (NEW.completed = TRUE)
  EXECUTE FUNCTION update_module_completion();
