import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/context/AuthContext';
import { 
  checkModuleAccess, 
  checkLessonAccess, 
  getCourseModuleAccess, 
  getModuleLessonAccess,
  ModuleAccessInfo,
  LessonAccessInfo
} from '@/services/course/accessControlService';

/**
 * Hook to check if a user has access to a specific module
 */
export function useModuleAccess(moduleId: string) {
  const { user } = useAuth();
  
  return useQuery({
    queryKey: ['module-access', user?.id, moduleId],
    queryFn: async () => {
      if (!user?.id || !moduleId) {
        return { hasAccess: false, reason: 'No user or module ID' };
      }
      return await checkModuleAccess(user.id, moduleId);
    },
    enabled: !!user?.id && !!moduleId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to check if a user has access to a specific lesson
 */
export function useLessonAccess(lessonId: string) {
  const { user } = useAuth();
  
  return useQuery({
    queryKey: ['lesson-access', user?.id, lessonId],
    queryFn: async () => {
      if (!user?.id || !lessonId) {
        return { hasAccess: false, reason: 'No user or lesson ID' };
      }
      return await checkLessonAccess(user.id, lessonId);
    },
    enabled: !!user?.id && !!lessonId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get access information for all modules in a course
 */
export function useCourseModuleAccess(courseId: string): {
  data: ModuleAccessInfo[] | undefined;
  isLoading: boolean;
  error: Error | null;
} {
  const { user } = useAuth();
  
  const query = useQuery({
    queryKey: ['course-module-access', user?.id, courseId],
    queryFn: async () => {
      if (!user?.id || !courseId) {
        return [];
      }
      return await getCourseModuleAccess(user.id, courseId);
    },
    enabled: !!user?.id && !!courseId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  return {
    data: query.data,
    isLoading: query.isLoading,
    error: query.error
  };
}

/**
 * Hook to get access information for all lessons in a module
 */
export function useModuleLessonAccess(moduleId: string): {
  data: LessonAccessInfo[] | undefined;
  isLoading: boolean;
  error: Error | null;
} {
  const { user } = useAuth();
  
  const query = useQuery({
    queryKey: ['module-lesson-access', user?.id, moduleId],
    queryFn: async () => {
      if (!user?.id || !moduleId) {
        return [];
      }
      return await getModuleLessonAccess(user.id, moduleId);
    },
    enabled: !!user?.id && !!moduleId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  return {
    data: query.data,
    isLoading: query.isLoading,
    error: query.error
  };
}

/**
 * Hook to check if the current user is a teacher
 */
export function useIsTeacher() {
  const { user } = useAuth();
  
  return useQuery({
    queryKey: ['user-role', user?.id],
    queryFn: async () => {
      if (!user?.id) return false;
      
      const { getUserRole } = await import('@/services/auth/userRoleService');
      const role = await getUserRole(user.id);
      return role === 'teacher';
    },
    enabled: !!user?.id,
    staleTime: 10 * 60 * 1000, // 10 minutes - roles don't change often
  });
}
