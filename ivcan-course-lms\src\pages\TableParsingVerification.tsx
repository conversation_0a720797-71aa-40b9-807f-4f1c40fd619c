import React from 'react';
import { MarkdownPreview } from '@/components/ui/markdown-preview';
import { markdownToHtml } from '@/lib/content-converter';

// Test the exact table from the database
const realTableMarkdown = `#### Types of IV cannula

| SIZES | COLOR          | FLOW RATE     | INDICATIONS                     |
|-------|----------------|---------------|---------------------------------|
| 14 G  | Orange         | Faster flow   | Trauma, surgical procedures     |
| 16 G  | Grey           | Faster flow   | Trauma, surgical procedures     |
| 18 G  | Green          | Medium flow   | Trauma, quick blood transfusion |
| 20 G  | Pink           | Medium flow   | Normal IV or blood transfusion  |
| 22 G  | Blue           | Slower flow   | Children, older adults          |
| 24 G  | Yellow         | Slower flow rate | Neonates, children, old elderly |
| 26 G  | Purple/violet  | Slower flow rate | Neonates                        |

This text should appear after the table.`;

export default function TableParsingVerification() {
  // Test the markdown parsing directly
  const directHtml = markdownToHtml(realTableMarkdown);
  
  return (
    <div className="container mx-auto p-8 max-w-6xl">
      <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6">
        <h1 className="text-3xl font-bold mb-6">Table Parsing Verification</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column: Raw Markdown */}
          <div>
            <h2 className="text-xl font-semibold mb-4">Raw Markdown (from Database)</h2>
            <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-md text-sm overflow-x-auto whitespace-pre-wrap">
              {realTableMarkdown}
            </pre>
          </div>
          
          {/* Right Column: Rendered Output */}
          <div>
            <h2 className="text-xl font-semibold mb-4">Rendered Output</h2>
            <div className="border border-gray-200 dark:border-gray-700 rounded-md p-4">
              <MarkdownPreview 
                content={realTableMarkdown}
                className="professional-prose"
              />
            </div>
          </div>
        </div>
        
        <div className="mt-8">
          <h2 className="text-xl font-semibold mb-4">Direct HTML Output (Debug)</h2>
          <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-md">
            <pre className="text-sm overflow-x-auto whitespace-pre-wrap">
              {directHtml}
            </pre>
          </div>
        </div>
        
        <div className="mt-8">
          <h2 className="text-xl font-semibold mb-4">Verification Checklist</h2>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className={directHtml.includes('<table>') ? 'text-green-600' : 'text-red-600'}>
                {directHtml.includes('<table>') ? '✅' : '❌'}
              </span>
              <span>Table HTML tags are generated</span>
            </div>
            <div className="flex items-center gap-2">
              <span className={directHtml.includes('<thead>') ? 'text-green-600' : 'text-red-600'}>
                {directHtml.includes('<thead>') ? '✅' : '❌'}
              </span>
              <span>Table header is properly structured</span>
            </div>
            <div className="flex items-center gap-2">
              <span className={directHtml.includes('<tbody>') ? 'text-green-600' : 'text-red-600'}>
                {directHtml.includes('<tbody>') ? '✅' : '❌'}
              </span>
              <span>Table body is properly structured</span>
            </div>
            <div className="flex items-center gap-2">
              <span className={!directHtml.includes('| SIZES |') ? 'text-green-600' : 'text-red-600'}>
                {!directHtml.includes('| SIZES |') ? '✅' : '❌'}
              </span>
              <span>Raw markdown pipes are converted (not visible in output)</span>
            </div>
            <div className="flex items-center gap-2">
              <span className={directHtml.includes('Orange') && directHtml.includes('<td>') ? 'text-green-600' : 'text-red-600'}>
                {directHtml.includes('Orange') && directHtml.includes('<td>') ? '✅' : '❌'}
              </span>
              <span>Table data is properly wrapped in TD tags</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
