// This script ensures dark mode is properly applied
(function() {
  // Check if dark mode is enabled in localStorage
  const storedTheme = localStorage.getItem('vite-ui-theme');
  
  // Check if system prefers dark mode
  const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
  
  // Apply dark mode if either stored theme is dark or system prefers dark
  if (storedTheme === 'dark' || (storedTheme === 'system' && systemPrefersDark)) {
    document.documentElement.classList.add('dark');
    document.documentElement.classList.add('dark-mode');
    document.documentElement.dataset.theme = 'dark';
    document.documentElement.dataset.actualTheme = 'dark';
  }
})();
