import React from 'react';
import { Link } from 'react-router-dom';
import { BookOpen, ActivitySquare, BarChart3 } from 'lucide-react';

const AdminSidebar = () => {
  const location = window.location;

  return (
    <div className="flex flex-col h-full">
      <Link
        to="/admin/courses"
        className={`flex items-center p-3 rounded-md ${
          location.pathname === "/admin/courses" ? "bg-primary text-white" : "hover:bg-gray-100 dark:hover:bg-gray-800"
        }`}
      >
        <BookOpen className="w-5 h-5 mr-3" />
        <span>Courses</span>
      </Link>
      
      <Link
        to="/admin/progress"
        className={`flex items-center p-3 rounded-md ${
          location.pathname === "/admin/progress" ? "bg-primary text-white" : "hover:bg-gray-100 dark:hover:bg-gray-800"
        }`}
      >
        <ActivitySquare className="w-5 h-5 mr-3" />
        <span>Completion Diagnostics</span>
      </Link>

      <Link
        to="/admin/analytics"
        className={`flex items-center p-3 rounded-md ${
          location.pathname === "/admin/analytics" ? "bg-primary text-white" : "hover:bg-gray-100 dark:hover:bg-gray-800"
        }`}
      >
        <BarChart3 className="w-5 h-5 mr-3" />
        <span>Test Analytics</span>
      </Link>
    </div>
  );
};

export default AdminSidebar; 