import{j as s}from"./vendor-react.BcAa1DKr.js";import{L as t}from"./Layout.DRjmVYQG.js";import{T as e}from"./TestAnalyticsDashboard.B08xtLlk.js";import"./vendor.DQpuTRuB.js";import"./index.BLDhDn0D.js";import"./vendor-supabase.sufZ44-y.js";import"./card.B9V6b2DK.js";import"./badge.C87ZuIxl.js";import"./module-test.BaVeK2uM.js";import"./progress.BMuwHUJX.js";const x=()=>s.jsx(t,{children:s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold",children:"Test Analytics"}),s.jsx("p",{className:"text-gray-500",children:"View detailed test responses and generate reports for individual students"})]}),s.jsx(e,{})]})});export{x as default};
