# Course Management API

This directory contains the API for managing courses, modules, lessons, and user progress in the LMS.

## Overview

The course management API provides a comprehensive set of functions for:

1. **Course Management**: Create, read, update, and delete courses
2. **Module Management**: Create, read, update, and delete modules within courses
3. **Lesson Management**: Create, read, update, and delete lessons within modules
4. **Enrollment Management**: Enroll users in courses and track enrollment status
5. **Progress Tracking**: Track user progress through courses, modules, and lessons

## API Structure

The API is organized into several files:

- `types.ts`: Type definitions for all course-related data
- `courseApi.ts`: Core functions for managing courses, modules, and lessons
- `enrollmentApi.ts`: Functions for managing user enrollment in courses
- `progressApi.ts`: Functions for tracking user progress
- `utils.ts`: Utility functions for data conversion and formatting

## Usage Examples

### Course Management

```typescript
import { 
  createCourse, 
  fetchCourseBySlug, 
  updateCourse 
} from '@/services/course/courseApi';

// Create a new course
const newCourse = await createCourse({
  title: 'Introduction to React',
  slug: 'intro-to-react',
  description: 'Learn the basics of React',
  instructor: '<PERSON>'
});

// Fetch a course by slug
const course = await fetchCourseBySlug('intro-to-react');

// Update a course
const updatedCourse = await updateCourse(course.id, {
  description: 'Updated course description'
});
```

### Module Management

```typescript
import { 
  createModule, 
  updateModule 
} from '@/services/course/courseApi';

// Create a new module
const newModule = await createModule({
  course_id: courseId,
  title: 'Getting Started',
  slug: 'getting-started',
  module_number: 1
});

// Update a module
const updatedModule = await updateModule(moduleId, {
  title: 'Updated Module Title'
});
```

### Lesson Management

```typescript
import { 
  createLesson, 
  updateLesson 
} from '@/services/course/courseApi';

// Create a new lesson
const newLesson = await createLesson({
  module_id: moduleId,
  title: 'Introduction',
  slug: 'introduction',
  duration: '15:00',
  type: 'lesson',
  content: 'Lesson content goes here'
});

// Update a lesson
const updatedLesson = await updateLesson(lessonId, {
  content: 'Updated lesson content'
});
```

### Enrollment Management

```typescript
import { 
  enrollInCourse, 
  getEnrollmentStatus,
  markCourseAsCompleted
} from '@/services/course/enrollmentApi';

// Enroll a user in a course
await enrollInCourse(courseId, userId);

// Check enrollment status
const enrollment = await getEnrollmentStatus(courseId, userId);

// Mark a course as completed
await markCourseAsCompleted(courseId, userId);
```

### Progress Tracking

```typescript
import {
  completeLessonProgress,
  trackCourseTime,
  getLessonProgress,
  getCourseProgress
} from '@/services/course/progressApi';

// Mark a lesson as completed
await completeLessonProgress(lessonId, userId);

// Track time spent on a course
await trackCourseTime(courseId, userId, 0.5); // 30 minutes

// Get lesson progress
const lessonProgress = await getLessonProgress(lessonId, userId);

// Get course progress
const courseProgress = await getCourseProgress(courseId, userId);
```

## Real-time Updates

The API supports real-time updates using Supabase's real-time functionality. To subscribe to real-time updates for a course, use the `useRealtimeCourseSync` hook:

```typescript
import { useRealtimeCourseSync } from '@/services/course';

// In your component
useRealtimeCourseSync(courseId);
```

This will automatically update the UI when course data changes in the database.

## Testing

To test the API, you can use the test functions in `src/tests/courseApi.test.ts`. These tests will create test data in your Supabase database and verify that all API functions work correctly.

```typescript
import { runTests } from '@/tests/courseApi.test';

// Run the tests
runTests();
```

## Database Schema

The API relies on the following database tables:

- `courses`: Stores course information
- `modules`: Stores module information
- `lessons`: Stores lesson information
- `user_course_enrollment`: Tracks user enrollment in courses
- `user_lesson_progress`: Tracks user progress through lessons
- `user_course_progress`: Tracks user progress through courses

See the migration files in `supabase/migrations` for the complete schema definition.
