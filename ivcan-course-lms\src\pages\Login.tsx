import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { ChevronLeft, GraduationCap } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import LoginForm from '@/components/auth/LoginForm';
import SignupForm from '@/components/auth/SignupForm';
import ForgotPasswordForm from '@/components/auth/ForgotPasswordForm';
import SocialLoginButtons from '@/components/auth/SocialLoginButtons';
import { motion } from 'framer-motion';
import { toast } from 'sonner';

const Login = () => {
  const location = useLocation();
  const [isLogin, setIsLogin] = useState(!(location.state?.showSignup === true));
  const [isForgotPassword, setIsForgotPassword] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [redirectAttempted, setRedirectAttempted] = useState(false);
  const navigate = useNavigate();
  const { user, loading } = useAuth();

  // Get the return URL from query parameters
  const getReturnUrl = () => {
    const searchParams = new URLSearchParams(location.search);
    const returnTo = searchParams.get('returnTo');
    return returnTo ? decodeURIComponent(returnTo) : '/dashboard';
  };

  useEffect(() => {
    // Only redirect if we have a user and haven't already attempted a redirect
    if (user && !redirectAttempted && !loading) {
      console.log('User detected in Login page, redirecting to dashboard or return URL');
      setRedirectAttempted(true);

      // Use window.location.href for a full page reload to ensure clean state
      const returnUrl = getReturnUrl();
      console.log('Redirecting to:', returnUrl);

      // Small delay to ensure state updates are processed
      setTimeout(() => {
        window.location.href = returnUrl;
      }, 100);

      toast.success('Welcome back!');
    }
  }, [user, navigate, redirectAttempted, loading, location]);

  return (
    <div className="min-h-screen flex flex-col lg:flex-row">
      {/* Left side - Brand column */}
      <div className="hidden lg:flex lg:w-1/2 bg-primary/10 flex-col items-center justify-center p-12 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-primary/30 z-0" />
        <div className="absolute top-0 left-0 w-full h-full">
          {Array.from({ length: 5 }).map((_, i) => (
            <motion.div 
              key={i}
              className="absolute bg-primary/10 rounded-full"
              style={{
                width: Math.random() * 300 + 50,
                height: Math.random() * 300 + 50,
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                filter: 'blur(60px)',
              }}
              animate={{
                y: [0, Math.random() * 40 - 20, 0],
                x: [0, Math.random() * 40 - 20, 0],
                opacity: [0.3, 0.6, 0.3],
              }}
              transition={{
                duration: 8 + Math.random() * 5,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
          ))}
        </div>
        
        <div className="relative z-10 text-center space-y-8">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.6, type: "spring" }}
            className="flex justify-center"
          >
            <div className="p-4 rounded-full bg-primary/20 backdrop-blur-md">
              <GraduationCap className="w-24 h-24 text-primary" />
            </div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.8 }}
          >
            <h1 className="text-4xl font-bold text-foreground mb-4">e4mi</h1>
            <p className="text-xl text-muted-foreground">eLearning for Medical Imaging Workforce</p>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6, duration: 0.8 }}
            className="max-w-md mx-auto mt-8"
          >
            <p className="text-muted-foreground text-md">
              Join our platform to enhance your medical imaging skills with evidence-based learning resources.
            </p>
          </motion.div>
        </div>
      </div>
      
      {/* Right side - Auth form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-4 sm:p-6 lg:p-8">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-md"
        >
          <div className="bg-card p-8 rounded-2xl shadow-sm border border-border space-y-6">
            {/* Back Button */}
            <Link
              to="/"
              className="inline-flex items-center px-3 py-1.5 bg-card border border-border rounded-md text-sm font-medium text-foreground hover:bg-primary hover:text-white hover:border-primary transition-colors"
            >
              <ChevronLeft className="w-4 h-4 mr-1.5" />
              Back to Home
            </Link>

            {/* Auth Header */}
            {!isForgotPassword && (
              <div>
                <motion.h2
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-center text-3xl font-bold tracking-tight text-foreground mb-2"
                >
                  {isLogin ? "Welcome Back" : "Create Account"}
                </motion.h2>
                <motion.p
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                  className="text-center text-sm text-muted-foreground"
                >
                  {isLogin
                    ? "Sign in to continue your learning journey"
                    : "Join our medical imaging e-learning platform"
                  }
                </motion.p>
              </div>
            )}

            <div className="space-y-6">
              {isForgotPassword ? (
                <ForgotPasswordForm onBack={() => setIsForgotPassword(false)} />
              ) : (
                <>
                  {/* Social Login Buttons */}
                  <SocialLoginButtons />

                  {/* Divider */}
                  <div className="relative">
                    <div className="absolute inset-0 flex items-center">
                      <div className="w-full border-t border-border" />
                    </div>
                    <div className="relative flex justify-center text-sm">
                      <span className="px-4 bg-card text-muted-foreground">OR</span>
                    </div>
                  </div>

                  {/* Forms */}
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.2 }}
                  >
                    {isLogin ? (
                      <LoginForm
                        email={email}
                        setEmail={setEmail}
                        password={password}
                        setPassword={setPassword}
                        isSubmitting={isSubmitting}
                        onForgotPassword={() => setIsForgotPassword(true)}
                      />
                    ) : (
                      <SignupForm
                        email={email}
                        setEmail={setEmail}
                        password={password}
                        setPassword={setPassword}
                        name={name}
                        setName={setName}
                        isSubmitting={isSubmitting}
                      />
                    )}
                  </motion.div>

                  {/* Toggle Login/Signup */}
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.3 }}
                    className="text-center mt-6 pt-4 border-t border-border"
                  >
                    <p className="text-sm text-foreground flex flex-wrap justify-center items-center gap-2">
                      {isLogin ? "Don't have an account yet? " : "Already have an account? "}
                      <button
                        type="button"
                        className="px-3 py-1.5 bg-primary border border-primary rounded-md font-regular text-white hover:bg-primary/90 transition-colors"
                        onClick={() => {
                          setIsLogin(!isLogin);
                          setEmail('');
                          setPassword('');
                          setName('');
                        }}
                      >
                        {isLogin ? "Sign Up" : "Sign In"}
                      </button>
                    </p>
                  </motion.div>
                </>
              )}
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Login;
