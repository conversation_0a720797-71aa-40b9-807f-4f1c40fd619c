import { useEffect } from 'react';
import { useTheme } from './enhanced-theme-provider';

/**
 * This component forces the application to use the Red theme
 * while preserving dark/light mode functionality
 */
export function ForceRedTheme() {
  const { theme } = useTheme();

  useEffect(() => {
    // Apply Red theme on component mount while preserving dark/light mode
    const applyRedTheme = () => {
      // Clear any saved theme preferences
      localStorage.removeItem('selectedTheme');
      localStorage.setItem('selectedTheme', 'Red');

      // Set the theme CSS variables directly
      const root = document.documentElement;
      const isDarkMode = root.classList.contains('dark');

      // Red theme values - different for light and dark modes
      if (isDarkMode) {
        // Dark mode red theme values
        root.style.setProperty('--primary', 'hsl(0 70% 50%)'); // Red #E63946
        root.style.setProperty('--primary-light', 'hsl(0 70% 60%)');
        root.style.setProperty('--primary-dark', 'hsl(0 70% 40%)');
        root.style.setProperty('--accent', 'hsl(0 60% 30%)');
      } else {
        // Light mode red theme values
        root.style.setProperty('--primary', 'hsl(0 70% 50%)'); // Red #E63946
        root.style.setProperty('--primary-light', 'hsl(0 70% 60%)');
        root.style.setProperty('--primary-dark', 'hsl(0 70% 40%)');
        root.style.setProperty('--accent', 'hsl(0 60% 30%)');
      }

      // Apply to tailwind classes directly
      document.body.classList.add('red-theme');

      // Update any inline styles that might be using theme colors
      const allElements = document.querySelectorAll('*');
      allElements.forEach(el => {
        if (el instanceof HTMLElement) {
          // Replace any green colors with red in inline styles
          if (el.style.color === '#25D366' || el.style.color === 'rgb(37, 211, 102)') {
            el.style.color = '#E63946';
          }
          if (el.style.backgroundColor === '#25D366' || el.style.backgroundColor === 'rgb(37, 211, 102)') {
            el.style.backgroundColor = '#E63946';
          }
          if (el.style.borderColor === '#25D366' || el.style.borderColor === 'rgb(37, 211, 102)') {
            el.style.borderColor = '#E63946';
          }
        }
      });

      console.log(`Theme has been forced to Red (${isDarkMode ? 'dark' : 'light'} mode)`);
    };

    // Apply immediately
    applyRedTheme();

    // Also apply after a short delay to ensure it overrides any other theme settings
    const timeoutId = setTimeout(applyRedTheme, 100);

    // Apply again after the page has fully loaded
    window.addEventListener('load', applyRedTheme);

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('load', applyRedTheme);
    };
  }, [theme]); // Re-apply when theme changes

  // This component doesn't render anything
  return null;
}

export default ForceRedTheme;
