var g=(d,x,r)=>new Promise((h,c)=>{var m=o=>{try{u(r.next(o))}catch(p){c(p)}},l=o=>{try{u(r.throw(o))}catch(p){c(p)}},u=o=>o.done?h(o.value):Promise.resolve(o.value).then(m,l);u((r=r.apply(d,x)).next())});import{r as f,j as e,bt as w,br as y}from"./vendor-react.BcAa1DKr.js";import{S as k}from"./simple-markdown-editor.DQ2eiB7n.js";import{C as t,a as s,b as a,c as v,d as n}from"./card.B9V6b2DK.js";import{B as i}from"./badge.C87ZuIxl.js";import{B as b}from"./index.BLDhDn0D.js";import{a2 as j}from"./vendor.DQpuTRuB.js";import"./tabs.B0SF6qIv.js";import"./label.D4YlnUPk.js";import"./markdown-preview.Bw0U-NJA.js";import"./content-converter.L-GziWIP.js";import"./tiptap-image-upload.B_U9gNrF.js";import"./vendor-supabase.sufZ44-y.js";const O=()=>{const[d,x]=f.useState(`# Simple Markdown Editor

This is a **clean** and *simple* markdown editor that focuses on essential features.

## What You Can Do

### Text Formatting
- **Bold text** - Use the B button or **text**
- *Italic text* - Use the I button or *text*
- \`Inline code\` - Use the code button or \`code\`

### Lists
1. Numbered lists
2. Easy to create
3. Just click the numbered list button

- Bullet points
- Also simple
- Click the list button

### Links and Images

Add links easily: [Example Link](https://example.com)

Click the image button to upload or add image URLs:

![Sample Image](https://via.placeholder.com/400x200/3B82F6/FFFFFF?text=Simple+Editor)

### Tables

Click the table button to create tables:

| Feature | Simple | Functional |
| --- | --- | --- |
| **Bold/Italic** | ✅ | ✅ |
| **Lists** | ✅ | ✅ |
| **Images** | ✅ | ✅ |
| **Tables** | ✅ | ✅ |
| **Preview** | ✅ | ✅ |

## Why Simple is Better

1. **Easy to Use** - Only essential buttons
2. **Fast** - No complex features to slow you down
3. **Clean** - Focused interface
4. **Functional** - Everything you need for lessons

Try editing this content - it's that simple!`),r=()=>g(void 0,null,function*(){try{yield navigator.clipboard.writeText(d),j.success("Markdown copied to clipboard!")}catch(c){j.error("Failed to copy to clipboard")}}),h=()=>{const c=new Blob([d],{type:"text/markdown"}),m=URL.createObjectURL(c),l=document.createElement("a");l.href=m,l.download="lesson-content.md",document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL(m),j.success("Markdown file downloaded!")};return e.jsx("div",{className:"min-h-screen bg-background p-4",children:e.jsxs("div",{className:"max-w-7xl mx-auto space-y-6",children:[e.jsxs("div",{className:"text-center space-y-4",children:[e.jsx("h1",{className:"text-4xl font-bold text-foreground",children:"Simple Markdown Editor"}),e.jsx("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"A clean, simple, and functional markdown editor for creating lesson content. Easy to use with essential features only."}),e.jsxs("div",{className:"flex flex-wrap justify-center gap-2",children:[e.jsx(i,{variant:"secondary",children:"Simple & Clean"}),e.jsx(i,{variant:"secondary",children:"Essential Features"}),e.jsx(i,{variant:"secondary",children:"Image Upload"}),e.jsx(i,{variant:"secondary",children:"Table Support"}),e.jsx(i,{variant:"secondary",children:"Live Preview"}),e.jsx(i,{variant:"secondary",children:"Markdown Output"})]})]}),e.jsxs(t,{children:[e.jsx(s,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx(a,{children:"Lesson Content Editor"}),e.jsx(v,{children:"Edit your lesson content using the rich text editor. The content is automatically converted to Markdown format for storage and portability."})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(b,{variant:"outline",size:"sm",onClick:r,className:"flex items-center gap-2",children:[e.jsx(w,{className:"h-4 w-4"}),"Copy Markdown"]}),e.jsxs(b,{variant:"outline",size:"sm",onClick:h,className:"flex items-center gap-2",children:[e.jsx(y,{className:"h-4 w-4"}),"Download"]})]})]})}),e.jsx(n,{children:e.jsx(k,{initialContent:d,onChange:x,placeholder:"Start writing your lesson content here...",minHeight:500,courseId:"demo-course",moduleId:"demo-module"})})]}),e.jsxs("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:[e.jsxs(t,{children:[e.jsx(s,{children:e.jsx(a,{className:"text-lg",children:"Simple & Clean"})}),e.jsx(n,{children:e.jsx("p",{className:"text-sm text-muted-foreground",children:"Only essential formatting buttons. No clutter, no confusion. Just what you need to create great content."})})]}),e.jsxs(t,{children:[e.jsx(s,{children:e.jsx(a,{className:"text-lg",children:"Essential Features"})}),e.jsx(n,{children:e.jsx("p",{className:"text-sm text-muted-foreground",children:"Bold, italic, code, lists, links, images, and tables. Everything you need for lesson content."})})]}),e.jsxs(t,{children:[e.jsx(s,{children:e.jsx(a,{className:"text-lg",children:"Live Preview"})}),e.jsx(n,{children:e.jsx("p",{className:"text-sm text-muted-foreground",children:"Switch between Write and Preview modes to see exactly how your content will look to students."})})]}),e.jsxs(t,{children:[e.jsx(s,{children:e.jsx(a,{className:"text-lg",children:"Image Upload"})}),e.jsx(n,{children:e.jsx("p",{className:"text-sm text-muted-foreground",children:"Click the image button to upload files or add URLs. Simple and straightforward."})})]}),e.jsxs(t,{children:[e.jsx(s,{children:e.jsx(a,{className:"text-lg",children:"Table Support"})}),e.jsx(n,{children:e.jsx("p",{className:"text-sm text-muted-foreground",children:"Click the table button to insert a 3x3 table. Perfect for organizing information."})})]}),e.jsxs(t,{children:[e.jsx(s,{children:e.jsx(a,{className:"text-lg",children:"Markdown Output"})}),e.jsx(n,{children:e.jsx("p",{className:"text-sm text-muted-foreground",children:"All content is saved as clean Markdown that works everywhere. Portable and future-proof."})})]})]}),e.jsxs(t,{children:[e.jsx(s,{children:e.jsx(a,{children:"How to Use"})}),e.jsx(n,{className:"space-y-4",children:e.jsxs("div",{className:"grid md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold mb-2",children:"Write Mode"}),e.jsxs("ul",{className:"text-sm text-muted-foreground space-y-1",children:[e.jsx("li",{children:"• Click toolbar buttons for formatting"}),e.jsx("li",{children:"• B = Bold, I = Italic, Code = Inline code"}),e.jsx("li",{children:"• List buttons for bullet and numbered lists"}),e.jsx("li",{children:"• Link, Image, and Table buttons for media"})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold mb-2",children:"Preview Mode"}),e.jsxs("ul",{className:"text-sm text-muted-foreground space-y-1",children:[e.jsx("li",{children:'• Click "Preview" tab to see final result'}),e.jsx("li",{children:"• Clean styling applied automatically"}),e.jsx("li",{children:"• Tables and images display properly"}),e.jsx("li",{children:"• Links are clickable in preview"})]})]})]})})]})]})})};export{O as default};
