var E=(h,b,v)=>new Promise((f,y)=>{var w=u=>{try{l(v.next(u))}catch(g){y(g)}},C=u=>{try{l(v.throw(u))}catch(g){y(g)}},l=u=>u.done?f(u.value):Promise.resolve(u.value).then(w,C);l((v=v.apply(h,b)).next())});import{r as c,j as d,bt as A,br as N,X as R}from"./vendor-react.BcAa1DKr.js";import{c as z,B as T}from"./index.BLDhDn0D.js";import{m as O}from"./content-converter.L-GziWIP.js";import{aA as I,a2 as x,aB as q}from"./vendor.DQpuTRuB.js";function V({content:h,className:b="",style:v,allowHtml:f=!0,enableImageZoom:y=!0,enableCodeCopy:w=!0,enableExport:C=!1}){const l=c.useRef(null),[u,g]=c.useState(null),[M,L]=c.useState(!1),p=c.useRef(null),B=c.useCallback(a=>{let e=a;return e=e.replace(/<table([^>]*)>/gi,'<div class="professional-table-wrapper"><table class="professional-table"$1>'),e=e.replace(/<\/table>/gi,"</table></div>"),e=e.replace(/<thead([^>]*)>/gi,"<thead$1>"),e=e.replace(/<(\/?)thead([^>]*)>/gi,(r,t)=>r),e=e.replace(/<tr([^>]*)>/gi,(r,t,s)=>{const o=e.substring(0,s),n=o.lastIndexOf("<thead"),i=o.lastIndexOf("</thead>");return n>i?`<tr class="table-header-row"${t}>`:`<tr class="table-row"${t}>`}),e=e.replace(/<th([^>]*)>/gi,'<th class="table-header-cell"$1>'),e=e.replace(/<td([^>]*)>/gi,'<td class="table-cell"$1>'),e=e.replace(/<details([^>]*?)>([\s\S]*?)<\/details>/gi,(r,t,s)=>{const o=s.match(/<summary([^>]*?)>(.*?)<\/summary>([\s\S]*)/i);if(o){const[,n,i,k]=o;return`<div class="professional-accordion">
            <details class="accordion-details"${t}>
              <summary class="accordion-summary"${n}>
                <span class="accordion-title">${i}</span>
                <span class="accordion-icon">
                  <svg class="chevron-down" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="6,9 12,15 18,9"></polyline>
                  </svg>
                </span>
              </summary>
              <div class="accordion-content">
                <div class="accordion-inner">${k.trim()}</div>
              </div>
            </details>
          </div>`}return r}),e=e.replace(/<li([^>]*?)>\s*<input\s+type="checkbox"([^>]*?)>\s*(.*?)<\/li>/gi,(r,t,s,o)=>{const n=s.includes("checked");return`<li${t}>
          <div class="professional-task-item">
            <div class="task-checkbox-wrapper">
              <input type="checkbox" ${s} class="professional-checkbox">
              <span class="checkmark"></span>
            </div>
            <span class="task-text ${n?"completed":""}">${o}</span>
          </div>
        </li>`}),e=e.replace(/<img([^>]*?)>/gi,(r,t)=>t.includes("class=")?r.replace(/class="([^"]*)"/,'class="$1 professional-image"'):`<img${t} class="professional-image">`),e=e.replace(/<img([^>]*?)alt="([^"]*?)"([^>]*?)>/gi,(r,t,s,o)=>s.trim()?`<figure class="professional-image-figure">
            <img${t}alt="${s}"${o} class="professional-image">
            <figcaption class="image-caption">${s}</figcaption>
          </figure>`:r),e},[]),m=c.useMemo(()=>{if(!h)return"";try{let a=h;a=a.replace(/^>\s*\[!(INFO|WARNING|SUCCESS|ERROR|TIP|NOTE|IMPORTANT|CAUTION)\]\s*\n((?:^>.*\n?)*)/gim,(r,t,s)=>{const o=s.replace(/^>\s*/gm,""),n=t.toLowerCase(),i={info:'<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line></svg>',warning:'<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>',success:'<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22,4 12,14.01 9,11.01"></polyline></svg>',error:'<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="10"></circle><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line></svg>',tip:'<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M9 12l2 2 4-4"></path><path d="M21 12c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z"></path><path d="M3 12c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z"></path><path d="M12 21c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z"></path><path d="M12 3c.552 0 1-.448 1-1s-.448-1-1-1-1 .448-1 1 .448 1 1 1z"></path></svg>',note:'<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14,2 14,8 20,8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10,9 9,9 8,9"></polyline></svg>',important:'<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>',caution:'<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>'},k=i[n]||i.info;return`<div class="professional-callout callout-${n}">
            <div class="callout-header">
              <div class="callout-icon">${k}</div>
              <div class="callout-title">${t}</div>
            </div>
            <div class="callout-content">${o}</div>
          </div>`});let e=O(a);return e=B(e),I.sanitize(e,{ADD_TAGS:f?["iframe","img","details","summary","input","figure","figcaption","svg","path","polyline","line","circle"]:["img","details","summary","input","figure","figcaption","svg","path","polyline","line","circle"],ADD_ATTR:f?["frameborder","allowfullscreen","allow","src","width","height","style","type","checked","disabled","class","data-accordion-id","data-indent","data-column","data-row","loading","viewBox","fill","stroke","stroke-width","points","cx","cy","r","x1","y1","x2","y2","d"]:["src","width","height","style","type","checked","disabled","class","data-accordion-id","data-indent","data-column","data-row","loading","viewBox","fill","stroke","stroke-width","points","cx","cy","r","x1","y1","x2","y2","d"]})}catch(a){return console.error("Error processing markdown content:",a),""}},[h,f]);c.useEffect(()=>{if(!l.current||!m)return;const a=new Map;return y&&l.current.querySelectorAll(".professional-image").forEach(s=>{const o=()=>{g(s.src)};s.style.cursor="zoom-in",s.addEventListener("click",o),a.set(s,o);const n=()=>{s.style.transform="scale(1.02)",s.style.transition="transform 0.3s ease"},i=()=>{s.style.transform="scale(1)"};s.addEventListener("mouseenter",n),s.addEventListener("mouseleave",i),a.set(`${s}-enter`,n),a.set(`${s}-leave`,i)}),l.current.querySelectorAll(".professional-accordion").forEach(t=>{const s=t.querySelector(".accordion-details"),o=t.querySelector(".accordion-summary"),n=t.querySelector(".chevron-down");if(s&&o&&n){const i=()=>{s.hasAttribute("open")?(n.style.transform="rotate(180deg)",t.classList.add("expanded")):(n.style.transform="rotate(0deg)",t.classList.remove("expanded"))};s.addEventListener("toggle",i),a.set(`${t}-toggle`,i),n.style.transition="transform 0.3s ease"}}),l.current.querySelectorAll(".professional-table").forEach(t=>{t.querySelectorAll(".table-row").forEach(o=>{const n=()=>{o.classList.add("hovered")},i=()=>{o.classList.remove("hovered")};o.addEventListener("mouseenter",n),o.addEventListener("mouseleave",i),a.set(`${o}-enter`,n),a.set(`${o}-leave`,i)})}),()=>{a.forEach((t,s)=>{typeof s!="string"&&(s.removeEventListener("click",t),s.removeEventListener("mouseenter",t),s.removeEventListener("mouseleave",t),s.removeEventListener("toggle",t))})}},[m,y]),c.useEffect(()=>{if(!l.current||!m||!w)return;l.current.querySelectorAll("pre code").forEach(e=>{const r=e.parentElement;if(!r)return;const t=document.createElement("button");t.className="code-copy-button",t.innerHTML='<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect><path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path></svg>',t.title="Copy code",t.addEventListener("click",()=>E(this,null,function*(){try{yield navigator.clipboard.writeText(e.textContent||""),t.innerHTML='<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="20,6 9,17 4,12"></polyline></svg>',setTimeout(()=>{t.innerHTML='<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect><path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path></svg>'},2e3),x.success("Code copied to clipboard!")}catch(s){x.error("Failed to copy code")}})),r.style.position="relative",r.appendChild(t)})},[m,w]),c.useEffect(()=>{if(!(!l.current||!m||M))return p.current&&(clearTimeout(p.current),p.current=null),p.current=setTimeout(()=>{var a;try{const e=(a=l.current)==null?void 0:a.querySelectorAll("pre code");e==null||e.forEach(r=>{q.highlightElement(r)}),L(!0)}catch(e){console.error("Error highlighting code:",e)}},100),()=>{p.current&&(clearTimeout(p.current),p.current=null)}},[m,M]),c.useEffect(()=>{L(!1)},[h]);const $=c.useCallback(()=>{g(null)},[]),j=c.useCallback(()=>{if(!h)return;const a=new Blob([h],{type:"text/markdown"}),e=URL.createObjectURL(a),r=document.createElement("a");r.href=e,r.download="content.md",document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(e),x.success("Markdown exported!")},[h]),S=c.useCallback(()=>E(this,null,function*(){if(h)try{yield navigator.clipboard.writeText(h),x.success("Markdown copied to clipboard!")}catch(a){x.error("Failed to copy markdown")}}),[h]);return d.jsxs(d.Fragment,{children:[d.jsxs("div",{className:z("enhanced-markdown-preview",b),style:v,children:[C&&d.jsxs("div",{className:"flex items-center gap-2 mb-4 p-2 bg-muted/50 rounded-lg",children:[d.jsxs(T,{variant:"ghost",size:"sm",onClick:S,children:[d.jsx(A,{className:"h-4 w-4 mr-2"}),"Copy Markdown"]}),d.jsxs(T,{variant:"ghost",size:"sm",onClick:j,children:[d.jsx(N,{className:"h-4 w-4 mr-2"}),"Export Markdown"]})]}),d.jsx("div",{ref:l,className:"markdown-content professional-lesson-preview",dangerouslySetInnerHTML:{__html:m}})]}),u&&d.jsxs("div",{className:"fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4",onClick:$,children:[d.jsx("button",{className:"absolute top-4 right-4 bg-white/10 hover:bg-white/20 rounded-full p-2 transition-colors",onClick:$,children:d.jsx(R,{className:"h-6 w-6 text-white"})}),d.jsx("img",{src:u,alt:"Zoomed",className:"max-h-[85vh] max-w-[85vw] object-contain rounded-lg"})]})]})}export{V as E};
