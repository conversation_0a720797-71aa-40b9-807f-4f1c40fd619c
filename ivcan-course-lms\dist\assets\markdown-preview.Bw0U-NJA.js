var z=Object.defineProperty;var S=Object.getOwnPropertySymbols;var M=Object.prototype.hasOwnProperty,_=Object.prototype.propertyIsEnumerable;var T=(s,e,n)=>e in s?z(s,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):s[e]=n,I=(s,e)=>{for(var n in e||(e={}))M.call(e,n)&&T(s,n,e[n]);if(S)for(var n of S(e))_.call(e,n)&&T(s,n,e[n]);return s};var E=(s,e,n)=>T(s,typeof e!="symbol"?e+"":e,n);import{r as w,j as x,X as N,R as P}from"./vendor-react.BcAa1DKr.js";import{m as $}from"./content-converter.L-GziWIP.js";import{c as q}from"./index.BLDhDn0D.js";import{aA as A,aB as j}from"./vendor.DQpuTRuB.js";const O={"default-src":["'self'"],"script-src":["'self'","'unsafe-inline'"],"style-src":["'self'","'unsafe-inline'"],"img-src":["'self'","data:","https:","blob:","*.supabase.co"],"media-src":["'self'","https:","*.supabase.co"],"frame-src":["'self'","https://www.youtube.com","https://youtube.com"],"connect-src":["'self'","https:","*.supabase.co"],"font-src":["'self'","data:","https:"],"object-src":["'none'"],"base-uri":["'self'"],"form-action":["'self'"],"frame-ancestors":["'none'"],"upgrade-insecure-requests":[]},U={basic:["p","br","strong","em","u","del","mark","code","pre","h1","h2","h3","h4","h5","h6","ul","ol","li","blockquote","a","img","hr"],extended:["p","br","strong","em","u","del","mark","code","pre","h1","h2","h3","h4","h5","h6","ul","ol","li","blockquote","a","img","hr","table","thead","tbody","tr","th","td","div","span","details","summary","input"],full:["p","br","strong","em","u","del","mark","code","pre","h1","h2","h3","h4","h5","h6","ul","ol","li","blockquote","a","img","hr","table","thead","tbody","tr","th","td","div","span","details","summary","input","iframe","figure","figcaption","video","audio","source"]},B={basic:["href","src","alt","title","class","id","width","height","style"],extended:["href","src","alt","title","class","id","width","height","style","type","checked","disabled","colspan","rowspan","data-*"],full:["href","src","alt","title","class","id","width","height","style","type","checked","disabled","colspan","rowspan","data-*","frameborder","allowfullscreen","allow","controls","autoplay","muted","loop","loading"]},H={http:/^https?:\/\/[^\s<>"']+$/i,data:/^data:image\/[a-z]+;base64,[a-z0-9+/]+=*$/i,blob:/^blob:[a-z0-9-]+$/i,youtube:/^https:\/\/(www\.)?(youtube\.com\/embed\/|youtu\.be\/)[a-z0-9_-]+(\?[a-z0-9&=_-]*)?$/i};class F{constructor(e={level:"extended"}){E(this,"config");this.config=I({allowIframes:!1,allowedDomains:[],maxContentLength:1048576,validateUrls:!0},e)}sanitizeHtml(e){if(!e)return"";if(this.config.maxContentLength&&e.length>this.config.maxContentLength)throw new Error(`Content exceeds maximum length of ${this.config.maxContentLength} characters`);const n=U[this.config.level],h=B[this.config.level],i={ADD_TAGS:n,ADD_ATTR:h,ALLOW_DATA_ATTR:this.config.level!=="basic",FORBID_SCRIPT:!0,FORBID_TAGS:["script","object","embed","applet"],FORBID_ATTR:["onerror","onload","onclick","onmouseover"]};this.config.allowIframes&&this.config.level==="full"&&(i.ADD_TAGS.push("iframe"),i.ADD_ATTR.push("frameborder","allowfullscreen","allow")),this.config.validateUrls&&A.addHook("afterSanitizeAttributes",d=>{var v;if(d.hasAttribute("src")){const m=d.getAttribute("src");m&&!this.isUrlSafe(m)&&d.removeAttribute("src")}if(d.hasAttribute("href")){const m=d.getAttribute("href");m&&!this.isUrlSafe(m)&&d.removeAttribute("href")}if(d.tagName==="IFRAME"){const m=d.getAttribute("src");(!m||!this.isIframeSafe(m))&&((v=d.parentNode)==null||v.removeChild(d))}});const p=A.sanitize(e,i);return A.removeAllHooks(),p}isUrlSafe(e){if(!e||!Object.values(H).some(h=>h.test(e)))return!1;if(this.config.allowedDomains&&this.config.allowedDomains.length>0)try{const i=new URL(e).hostname.toLowerCase();return this.config.allowedDomains.some(p=>i===p||i.endsWith("."+p))}catch(h){return!1}return!0}isIframeSafe(e){return this.config.allowIframes?H.youtube.test(e):!1}validateContent(e){const n=[];this.config.maxContentLength&&e.length>this.config.maxContentLength&&n.push(`Content exceeds maximum length of ${this.config.maxContentLength} characters`),[/<script[^>]*>/i,/javascript:/i,/vbscript:/i,/data:text\/html/i,/on\w+\s*=/i].forEach(i=>{i.test(e)&&n.push(`Content contains potentially dangerous pattern: ${i.source}`)});try{const i=new TextEncoder().encode(e);new TextDecoder("utf-8",{fatal:!0}).decode(i)!==e&&n.push("Content contains invalid UTF-8 encoding")}catch(i){n.push("Content contains invalid UTF-8 encoding")}return{valid:n.length===0,errors:n}}generateCSPHeader(){return Object.entries(O).map(([n,h])=>h.length===0?n:`${n} ${h.join(" ")}`).join("; ")}escapeHtml(e){const n=document.createElement("div");return n.textContent=e,n.innerHTML}unescapeHtml(e){const n=document.createElement("div");return n.innerHTML=e,n.textContent||n.innerText||""}}const D=new F({level:"extended",allowIframes:!0,allowedDomains:["youtube.com","www.youtube.com","youtu.be","supabase.co","jibspqwieubavucdtccv.supabase.co"],maxContentLength:1048576,validateUrls:!0});function W(s){return D.validateContent(s)}const k=typeof window!="undefined",G=s=>{if(!k)return 0;const e=window;return"requestIdleCallback"in e?e.requestIdleCallback(s,{timeout:2e3}):e.setTimeout(s,50)},Z=s=>{if(!k)return;const e=window;"cancelIdleCallback"in e?e.cancelIdleCallback(s):e.clearTimeout(s)},V=(s,e)=>{if(!s)return()=>{};const{onHighlightCode:n,onEnhanceTables:h,onEnhanceLinks:i,onComplete:p}=e,d=G(()=>{if(!s)return;(()=>{const m=()=>{if(!s)return;const r=s.querySelectorAll("pre code");if(r.length>0){let t=0;const u=()=>{const c=Math.min(t+3,r.length);for(let o=t;o<c;o++)try{n?n(r[o]):j.highlightElement(r[o])}catch(a){console.error("Error highlighting code block:",a)}t=c,t<r.length?window.setTimeout(u,10):window.setTimeout(C,10)};u()}else C()},C=()=>{if(!s||!h){g();return}const r=s.querySelectorAll("table:not(.enhanced-table)");r.length>0&&r.forEach(t=>{t instanceof HTMLTableElement&&h(t)}),g()},g=()=>{if(!s||!i){y();return}const r=s.querySelectorAll("a:not(.enhanced-link)");if(r.length>0){let t=0;const u=()=>{const c=Math.min(t+10,r.length);for(let o=t;o<c;o++)r[o]instanceof HTMLAnchorElement&&i(r[o]);t=c,t<r.length?window.setTimeout(u,0):y()};u()}else y()},y=()=>{p&&p()};k?window.requestAnimationFrame(()=>m()):m()})()});return()=>Z(d)};function X({content:s,className:e,allowHtml:n=!1,securityLevel:h="extended"}){const i=w.useRef(null),[p,d]=w.useState(null),[v,m]=w.useState(!1),C=w.useRef(null),g=w.useMemo(()=>{if(!s)return"";try{const r=W(s);r.valid||console.warn("Content security validation failed:",r.errors);let t=s;t=t.replace(/!\[(.*?)\]\((data:image\/[^)]+)\)/g,(c,o,a)=>`![${o}](${a})`),t=t.replace(/!\[(.*?)\]\((.*?)(?:\s+(\d+)x(\d+))?\)/g,(c,o,a,f,b)=>f&&b?`<img src="${a}" alt="${o}" width="${f}" height="${b}" style="aspect-ratio: ${f}/${b}; max-width: 100%; height: auto;">`:c);let u=$(t);const l=r.valid?h:"basic";return D.sanitizeHtml(u)}catch(r){return console.error("Error processing markdown content:",r),'<p class="text-destructive">Error processing content</p>'}},[s,h]);w.useEffect(()=>{if(!i.current||!g)return;const r=new Map,t=new Set,l=setTimeout(()=>{var a;const c=(a=i.current)==null?void 0:a.querySelectorAll("img:not(.emoji)");if(!c)return;const o=(f,b)=>{f.preventDefault(),b&&d(b)};c.forEach(f=>{if(!f.hasAttribute("data-zoom-enabled")){f.setAttribute("data-zoom-enabled","true"),f instanceof HTMLElement&&(f.style.cursor="zoom-in");const b=f.getAttribute("src"),L=R=>o(R,b);f.addEventListener("click",L),r.set(f,L),t.add(f)}})},50);return()=>{clearTimeout(l),t.forEach(c=>{const o=r.get(c);o&&c instanceof Element&&c.removeEventListener("click",o)}),r.clear(),t.clear()}},[g,d]),w.useEffect(()=>!i.current||!g||v?void 0:(C.current&&(clearTimeout(C.current),C.current=null),V(i.current,{onHighlightCode:t=>{var u;try{j.highlightElement(t);const l=t.parentElement;if(l&&!l.classList.contains("enhanced")){l.classList.add("enhanced"),l.classList.add("line-numbers");const c=((u=t.className.match(/language-(\w+)/))==null?void 0:u[1])||"";if(c){const a=document.createElement("div");a.className="code-language absolute top-2 left-2 px-2 py-1 rounded bg-muted/80 text-xs font-mono",a.textContent=c,l.style.position="relative",l.appendChild(a)}const o=document.createElement("button");o.className="copy-button absolute top-2 right-2 p-1.5 rounded bg-muted/80 hover:bg-muted text-xs",o.innerHTML="Copy",o.onclick=a=>{a.preventDefault(),a.stopPropagation(),navigator.clipboard.writeText(t.textContent||""),o.innerHTML="Copied!",setTimeout(()=>{o.innerHTML="Copy"},2e3)},l.appendChild(o)}}catch(l){console.error("Error highlighting code:",l)}},onEnhanceTables:t=>{var c,o;if(t.classList.add("enhanced-table"),!((c=t.parentElement)!=null&&c.classList.contains("table-wrapper"))){const a=document.createElement("div");a.className="table-wrapper",(o=t.parentNode)==null||o.insertBefore(a,t),a.appendChild(t)}t.classList.add("w-full","border-collapse"),t.querySelectorAll("th").forEach(a=>{a.classList.add("bg-muted/80","font-semibold","text-foreground")}),t.querySelectorAll("td").forEach(a=>{a.classList.add("text-foreground")})},onEnhanceLinks:t=>{t.classList.add("enhanced-link");const u=t.getAttribute("href");if(u&&(u.startsWith("http://")||u.startsWith("https://"))&&(t.setAttribute("target","_blank"),t.setAttribute("rel","noopener noreferrer"),!t.querySelector(".external-link-icon"))){const l=document.createElement("span");l.className="external-link-icon inline-block ml-1",l.innerHTML='<svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" y1="14" x2="21" y2="3"></line></svg>',t.appendChild(l)}},onComplete:()=>{m(!0)}})),[g,v]);const y=w.useCallback(()=>{d(null)},[]);return x.jsxs(x.Fragment,{children:[x.jsx("div",{ref:i,className:q("markdown-preview",e),dangerouslySetInnerHTML:{__html:g}}),p&&x.jsxs("div",{className:"fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4",onClick:y,children:[x.jsx("button",{className:"absolute top-4 right-4 bg-white/10 rounded-full p-2",onClick:y,children:x.jsx(N,{className:"h-6 w-6 text-white"})}),x.jsx("img",{src:p,alt:"Zoomed",className:"max-h-[85vh] max-w-[85vw] object-contain"})]})]})}P.memo(X);export{X as M,D as c};
