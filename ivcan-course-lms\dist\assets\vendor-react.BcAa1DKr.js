var Og=Object.defineProperty,Ig=Object.defineProperties;var Lg=Object.getOwnPropertyDescriptors;var fi=Object.getOwnPropertySymbols;var Rd=Object.prototype.hasOwnProperty,_d=Object.prototype.propertyIsEnumerable;var Pd=(e,t,n)=>t in e?Og(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,m=(e,t)=>{for(var n in t||(t={}))Rd.call(t,n)&&Pd(e,n,t[n]);if(fi)for(var n of fi(t))_d.call(t,n)&&Pd(e,n,t[n]);return e},R=(e,t)=>Ig(e,Lg(t));var N=(e,t)=>{var n={};for(var r in e)Rd.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&fi)for(var r of fi(e))t.indexOf(r)<0&&_d.call(e,r)&&(n[r]=e[r]);return n};var Wt=(e,t,n)=>new Promise((r,o)=>{var i=a=>{try{l(n.next(a))}catch(c){o(c)}},s=a=>{try{l(n.throw(a))}catch(c){o(c)}},l=a=>a.done?r(a.value):Promise.resolve(a.value).then(i,s);l((n=n.apply(e,t)).next())});import{g as Bp,s as $g,c as Fg,o as jg,a as Vg,f as zg,d as Ug,h as Bg,e as Td,l as Wg,i as Wp,n as Hp,j as Hg,Q as Kg,M as Yg,k as We,m as Ou,r as Iu,p as Qi,q as Kp,t as Gg,A as Yp,u as $o,v as Qg,w as Nd,x as qg,y as Ad,z as Gp,_ as Qp,B as qp,C as xt,D as Xg,E as Xp,F as Zp,G as Jp,H as _e,I as pa,J as Zg,K as Jg,L as ew,N as bd,O as tw,P as nw,R as rw,S as ow,T as iw,U as sw,V as lw}from"./vendor.DQpuTRuB.js";function aw(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const i=Object.getOwnPropertyDescriptor(r,o);i&&Object.defineProperty(e,o,i.get?i:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var eh={exports:{}},Ds={},th={exports:{}},J={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ni=Symbol.for("react.element"),uw=Symbol.for("react.portal"),cw=Symbol.for("react.fragment"),dw=Symbol.for("react.strict_mode"),fw=Symbol.for("react.profiler"),pw=Symbol.for("react.provider"),hw=Symbol.for("react.context"),vw=Symbol.for("react.forward_ref"),mw=Symbol.for("react.suspense"),yw=Symbol.for("react.memo"),gw=Symbol.for("react.lazy"),Md=Symbol.iterator;function ww(e){return e===null||typeof e!="object"?null:(e=Md&&e[Md]||e["@@iterator"],typeof e=="function"?e:null)}var nh={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},rh=Object.assign,oh={};function Zr(e,t,n){this.props=e,this.context=t,this.refs=oh,this.updater=n||nh}Zr.prototype.isReactComponent={};Zr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Zr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function ih(){}ih.prototype=Zr.prototype;function Lu(e,t,n){this.props=e,this.context=t,this.refs=oh,this.updater=n||nh}var $u=Lu.prototype=new ih;$u.constructor=Lu;rh($u,Zr.prototype);$u.isPureReactComponent=!0;var Dd=Array.isArray,sh=Object.prototype.hasOwnProperty,Fu={current:null},lh={key:!0,ref:!0,__self:!0,__source:!0};function ah(e,t,n){var r,o={},i=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(i=""+t.key),t)sh.call(t,r)&&!lh.hasOwnProperty(r)&&(o[r]=t[r]);var l=arguments.length-2;if(l===1)o.children=n;else if(1<l){for(var a=Array(l),c=0;c<l;c++)a[c]=arguments[c+2];o.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)o[r]===void 0&&(o[r]=l[r]);return{$$typeof:ni,type:e,key:i,ref:s,props:o,_owner:Fu.current}}function Sw(e,t){return{$$typeof:ni,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ju(e){return typeof e=="object"&&e!==null&&e.$$typeof===ni}function Cw(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Od=/\/+/g;function _l(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Cw(""+e.key):t.toString(36)}function Ii(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case ni:case uw:s=!0}}if(s)return s=e,o=o(s),e=r===""?"."+_l(s,0):r,Dd(o)?(n="",e!=null&&(n=e.replace(Od,"$&/")+"/"),Ii(o,t,n,"",function(c){return c})):o!=null&&(ju(o)&&(o=Sw(o,n+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(Od,"$&/")+"/")+e)),t.push(o)),1;if(s=0,r=r===""?".":r+":",Dd(e))for(var l=0;l<e.length;l++){i=e[l];var a=r+_l(i,l);s+=Ii(i,t,n,a,o)}else if(a=ww(e),typeof a=="function")for(e=a.call(e),l=0;!(i=e.next()).done;)i=i.value,a=r+_l(i,l++),s+=Ii(i,t,n,a,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function pi(e,t,n){if(e==null)return e;var r=[],o=0;return Ii(e,r,"","",function(i){return t.call(n,i,o++)}),r}function xw(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var rt={current:null},Li={transition:null},Ew={ReactCurrentDispatcher:rt,ReactCurrentBatchConfig:Li,ReactCurrentOwner:Fu};function uh(){throw Error("act(...) is not supported in production builds of React.")}J.Children={map:pi,forEach:function(e,t,n){pi(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return pi(e,function(){t++}),t},toArray:function(e){return pi(e,function(t){return t})||[]},only:function(e){if(!ju(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};J.Component=Zr;J.Fragment=cw;J.Profiler=fw;J.PureComponent=Lu;J.StrictMode=dw;J.Suspense=mw;J.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Ew;J.act=uh;J.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=rh({},e.props),o=e.key,i=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,s=Fu.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)sh.call(t,a)&&!lh.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var c=0;c<a;c++)l[c]=arguments[c+2];r.children=l}return{$$typeof:ni,type:e.type,key:o,ref:i,props:r,_owner:s}};J.createContext=function(e){return e={$$typeof:hw,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:pw,_context:e},e.Consumer=e};J.createElement=ah;J.createFactory=function(e){var t=ah.bind(null,e);return t.type=e,t};J.createRef=function(){return{current:null}};J.forwardRef=function(e){return{$$typeof:vw,render:e}};J.isValidElement=ju;J.lazy=function(e){return{$$typeof:gw,_payload:{_status:-1,_result:e},_init:xw}};J.memo=function(e,t){return{$$typeof:yw,type:e,compare:t===void 0?null:t}};J.startTransition=function(e){var t=Li.transition;Li.transition={};try{e()}finally{Li.transition=t}};J.unstable_act=uh;J.useCallback=function(e,t){return rt.current.useCallback(e,t)};J.useContext=function(e){return rt.current.useContext(e)};J.useDebugValue=function(){};J.useDeferredValue=function(e){return rt.current.useDeferredValue(e)};J.useEffect=function(e,t){return rt.current.useEffect(e,t)};J.useId=function(){return rt.current.useId()};J.useImperativeHandle=function(e,t,n){return rt.current.useImperativeHandle(e,t,n)};J.useInsertionEffect=function(e,t){return rt.current.useInsertionEffect(e,t)};J.useLayoutEffect=function(e,t){return rt.current.useLayoutEffect(e,t)};J.useMemo=function(e,t){return rt.current.useMemo(e,t)};J.useReducer=function(e,t,n){return rt.current.useReducer(e,t,n)};J.useRef=function(e){return rt.current.useRef(e)};J.useState=function(e){return rt.current.useState(e)};J.useSyncExternalStore=function(e,t,n){return rt.current.useSyncExternalStore(e,t,n)};J.useTransition=function(){return rt.current.useTransition()};J.version="18.3.1";th.exports=J;var u=th.exports;const $=Bp(u),ch=aw({__proto__:null,default:$},[u]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var kw=u,Pw=Symbol.for("react.element"),Rw=Symbol.for("react.fragment"),_w=Object.prototype.hasOwnProperty,Tw=kw.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Nw={key:!0,ref:!0,__self:!0,__source:!0};function dh(e,t,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)_w.call(t,r)&&!Nw.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:Pw,type:e,key:i,ref:s,props:o,_owner:Tw.current}}Ds.Fragment=Rw;Ds.jsx=dh;Ds.jsxs=dh;eh.exports=Ds;var x=eh.exports,fh={exports:{}},mt={};/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Aw=u,vt=$g;function L(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var ph=new Set,Fo={};function ar(e,t){Br(e,t),Br(e+"Capture",t)}function Br(e,t){for(Fo[e]=t,e=0;e<t.length;e++)ph.add(t[e])}var un=!(typeof window=="undefined"||typeof window.document=="undefined"||typeof window.document.createElement=="undefined"),ha=Object.prototype.hasOwnProperty,bw=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Id={},Ld={};function Mw(e){return ha.call(Ld,e)?!0:ha.call(Id,e)?!1:bw.test(e)?Ld[e]=!0:(Id[e]=!0,!1)}function Dw(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Ow(e,t,n,r){if(t===null||typeof t=="undefined"||Dw(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ot(e,t,n,r,o,i,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=s}var He={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){He[e]=new ot(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];He[t]=new ot(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){He[e]=new ot(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){He[e]=new ot(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){He[e]=new ot(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){He[e]=new ot(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){He[e]=new ot(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){He[e]=new ot(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){He[e]=new ot(e,5,!1,e.toLowerCase(),null,!1,!1)});var Vu=/[\-:]([a-z])/g;function zu(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Vu,zu);He[t]=new ot(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Vu,zu);He[t]=new ot(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Vu,zu);He[t]=new ot(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){He[e]=new ot(e,1,!1,e.toLowerCase(),null,!1,!1)});He.xlinkHref=new ot("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){He[e]=new ot(e,1,!1,e.toLowerCase(),null,!0,!0)});function Uu(e,t,n,r){var o=He.hasOwnProperty(t)?He[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Ow(t,n,o,r)&&(n=null),r||o===null?Mw(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var pn=Aw.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,hi=Symbol.for("react.element"),xr=Symbol.for("react.portal"),Er=Symbol.for("react.fragment"),Bu=Symbol.for("react.strict_mode"),va=Symbol.for("react.profiler"),hh=Symbol.for("react.provider"),vh=Symbol.for("react.context"),Wu=Symbol.for("react.forward_ref"),ma=Symbol.for("react.suspense"),ya=Symbol.for("react.suspense_list"),Hu=Symbol.for("react.memo"),En=Symbol.for("react.lazy"),mh=Symbol.for("react.offscreen"),$d=Symbol.iterator;function ao(e){return e===null||typeof e!="object"?null:(e=$d&&e[$d]||e["@@iterator"],typeof e=="function"?e:null)}var we=Object.assign,Tl;function wo(e){if(Tl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Tl=t&&t[1]||""}return`
`+Tl+e}var Nl=!1;function Al(e,t){if(!e||Nl)return"";Nl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var o=c.stack.split(`
`),i=r.stack.split(`
`),s=o.length-1,l=i.length-1;1<=s&&0<=l&&o[s]!==i[l];)l--;for(;1<=s&&0<=l;s--,l--)if(o[s]!==i[l]){if(s!==1||l!==1)do if(s--,l--,0>l||o[s]!==i[l]){var a=`
`+o[s].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=s&&0<=l);break}}}finally{Nl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?wo(e):""}function Iw(e){switch(e.tag){case 5:return wo(e.type);case 16:return wo("Lazy");case 13:return wo("Suspense");case 19:return wo("SuspenseList");case 0:case 2:case 15:return e=Al(e.type,!1),e;case 11:return e=Al(e.type.render,!1),e;case 1:return e=Al(e.type,!0),e;default:return""}}function ga(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Er:return"Fragment";case xr:return"Portal";case va:return"Profiler";case Bu:return"StrictMode";case ma:return"Suspense";case ya:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case vh:return(e.displayName||"Context")+".Consumer";case hh:return(e._context.displayName||"Context")+".Provider";case Wu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Hu:return t=e.displayName||null,t!==null?t:ga(e.type)||"Memo";case En:t=e._payload,e=e._init;try{return ga(e(t))}catch(n){}}return null}function Lw(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ga(t);case 8:return t===Bu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function jn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function yh(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function $w(e){var t=yh(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n!="undefined"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(s){r=""+s,i.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function vi(e){e._valueTracker||(e._valueTracker=$w(e))}function gh(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=yh(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function qi(e){if(e=e||(typeof document!="undefined"?document:void 0),typeof e=="undefined")return null;try{return e.activeElement||e.body}catch(t){return e.body}}function wa(e,t){var n=t.checked;return we({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n!=null?n:e._wrapperState.initialChecked})}function Fd(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=jn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function wh(e,t){t=t.checked,t!=null&&Uu(e,"checked",t,!1)}function Sa(e,t){wh(e,t);var n=jn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Ca(e,t.type,n):t.hasOwnProperty("defaultValue")&&Ca(e,t.type,jn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function jd(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Ca(e,t,n){(t!=="number"||qi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var So=Array.isArray;function Or(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+jn(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function xa(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(L(91));return we({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Vd(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(L(92));if(So(n)){if(1<n.length)throw Error(L(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:jn(n)}}function Sh(e,t){var n=jn(t.value),r=jn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function zd(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Ch(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Ea(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Ch(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var mi,xh=function(e){return typeof MSApp!="undefined"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(mi=mi||document.createElement("div"),mi.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=mi.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function jo(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var ko={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Fw=["Webkit","ms","Moz","O"];Object.keys(ko).forEach(function(e){Fw.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),ko[t]=ko[e]})});function Eh(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||ko.hasOwnProperty(e)&&ko[e]?(""+t).trim():t+"px"}function kh(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=Eh(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var jw=we({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ka(e,t){if(t){if(jw[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(L(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(L(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(L(61))}if(t.style!=null&&typeof t.style!="object")throw Error(L(62))}}function Pa(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ra=null;function Ku(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var _a=null,Ir=null,Lr=null;function Ud(e){if(e=ii(e)){if(typeof _a!="function")throw Error(L(280));var t=e.stateNode;t&&(t=Fs(t),_a(e.stateNode,e.type,t))}}function Ph(e){Ir?Lr?Lr.push(e):Lr=[e]:Ir=e}function Rh(){if(Ir){var e=Ir,t=Lr;if(Lr=Ir=null,Ud(e),t)for(e=0;e<t.length;e++)Ud(t[e])}}function _h(e,t){return e(t)}function Th(){}var bl=!1;function Nh(e,t,n){if(bl)return e(t,n);bl=!0;try{return _h(e,t,n)}finally{bl=!1,(Ir!==null||Lr!==null)&&(Th(),Rh())}}function Vo(e,t){var n=e.stateNode;if(n===null)return null;var r=Fs(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(L(231,t,typeof n));return n}var Ta=!1;if(un)try{var uo={};Object.defineProperty(uo,"passive",{get:function(){Ta=!0}}),window.addEventListener("test",uo,uo),window.removeEventListener("test",uo,uo)}catch(e){Ta=!1}function Vw(e,t,n,r,o,i,s,l,a){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(d){this.onError(d)}}var Po=!1,Xi=null,Zi=!1,Na=null,zw={onError:function(e){Po=!0,Xi=e}};function Uw(e,t,n,r,o,i,s,l,a){Po=!1,Xi=null,Vw.apply(zw,arguments)}function Bw(e,t,n,r,o,i,s,l,a){if(Uw.apply(this,arguments),Po){if(Po){var c=Xi;Po=!1,Xi=null}else throw Error(L(198));Zi||(Zi=!0,Na=c)}}function ur(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Ah(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Bd(e){if(ur(e)!==e)throw Error(L(188))}function Ww(e){var t=e.alternate;if(!t){if(t=ur(e),t===null)throw Error(L(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return Bd(o),e;if(i===r)return Bd(o),t;i=i.sibling}throw Error(L(188))}if(n.return!==r.return)n=o,r=i;else{for(var s=!1,l=o.child;l;){if(l===n){s=!0,n=o,r=i;break}if(l===r){s=!0,r=o,n=i;break}l=l.sibling}if(!s){for(l=i.child;l;){if(l===n){s=!0,n=i,r=o;break}if(l===r){s=!0,r=i,n=o;break}l=l.sibling}if(!s)throw Error(L(189))}}if(n.alternate!==r)throw Error(L(190))}if(n.tag!==3)throw Error(L(188));return n.stateNode.current===n?e:t}function bh(e){return e=Ww(e),e!==null?Mh(e):null}function Mh(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Mh(e);if(t!==null)return t;e=e.sibling}return null}var Dh=vt.unstable_scheduleCallback,Wd=vt.unstable_cancelCallback,Hw=vt.unstable_shouldYield,Kw=vt.unstable_requestPaint,ke=vt.unstable_now,Yw=vt.unstable_getCurrentPriorityLevel,Yu=vt.unstable_ImmediatePriority,Oh=vt.unstable_UserBlockingPriority,Ji=vt.unstable_NormalPriority,Gw=vt.unstable_LowPriority,Ih=vt.unstable_IdlePriority,Os=null,Qt=null;function Qw(e){if(Qt&&typeof Qt.onCommitFiberRoot=="function")try{Qt.onCommitFiberRoot(Os,e,void 0,(e.current.flags&128)===128)}catch(t){}}var $t=Math.clz32?Math.clz32:Zw,qw=Math.log,Xw=Math.LN2;function Zw(e){return e>>>=0,e===0?32:31-(qw(e)/Xw|0)|0}var yi=64,gi=4194304;function Co(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function es(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,s=n&268435455;if(s!==0){var l=s&~o;l!==0?r=Co(l):(i&=s,i!==0&&(r=Co(i)))}else s=n&~o,s!==0?r=Co(s):i!==0&&(r=Co(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-$t(t),o=1<<n,r|=e[n],t&=~o;return r}function Jw(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function eS(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var s=31-$t(i),l=1<<s,a=o[s];a===-1?(!(l&n)||l&r)&&(o[s]=Jw(l,t)):a<=t&&(e.expiredLanes|=l),i&=~l}}function Aa(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Lh(){var e=yi;return yi<<=1,!(yi&4194240)&&(yi=64),e}function Ml(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function ri(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-$t(t),e[t]=n}function tS(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-$t(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function Gu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-$t(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var le=0;function $h(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Fh,Qu,jh,Vh,zh,ba=!1,wi=[],An=null,bn=null,Mn=null,zo=new Map,Uo=new Map,Pn=[],nS="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Hd(e,t){switch(e){case"focusin":case"focusout":An=null;break;case"dragenter":case"dragleave":bn=null;break;case"mouseover":case"mouseout":Mn=null;break;case"pointerover":case"pointerout":zo.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Uo.delete(t.pointerId)}}function co(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=ii(t),t!==null&&Qu(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function rS(e,t,n,r,o){switch(t){case"focusin":return An=co(An,e,t,n,r,o),!0;case"dragenter":return bn=co(bn,e,t,n,r,o),!0;case"mouseover":return Mn=co(Mn,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return zo.set(i,co(zo.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,Uo.set(i,co(Uo.get(i)||null,e,t,n,r,o)),!0}return!1}function Uh(e){var t=Gn(e.target);if(t!==null){var n=ur(t);if(n!==null){if(t=n.tag,t===13){if(t=Ah(n),t!==null){e.blockedOn=t,zh(e.priority,function(){jh(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function $i(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Ma(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Ra=r,n.target.dispatchEvent(r),Ra=null}else return t=ii(n),t!==null&&Qu(t),e.blockedOn=n,!1;t.shift()}return!0}function Kd(e,t,n){$i(e)&&n.delete(t)}function oS(){ba=!1,An!==null&&$i(An)&&(An=null),bn!==null&&$i(bn)&&(bn=null),Mn!==null&&$i(Mn)&&(Mn=null),zo.forEach(Kd),Uo.forEach(Kd)}function fo(e,t){e.blockedOn===t&&(e.blockedOn=null,ba||(ba=!0,vt.unstable_scheduleCallback(vt.unstable_NormalPriority,oS)))}function Bo(e){function t(o){return fo(o,e)}if(0<wi.length){fo(wi[0],e);for(var n=1;n<wi.length;n++){var r=wi[n];r.blockedOn===e&&(r.blockedOn=null)}}for(An!==null&&fo(An,e),bn!==null&&fo(bn,e),Mn!==null&&fo(Mn,e),zo.forEach(t),Uo.forEach(t),n=0;n<Pn.length;n++)r=Pn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Pn.length&&(n=Pn[0],n.blockedOn===null);)Uh(n),n.blockedOn===null&&Pn.shift()}var $r=pn.ReactCurrentBatchConfig,ts=!0;function iS(e,t,n,r){var o=le,i=$r.transition;$r.transition=null;try{le=1,qu(e,t,n,r)}finally{le=o,$r.transition=i}}function sS(e,t,n,r){var o=le,i=$r.transition;$r.transition=null;try{le=4,qu(e,t,n,r)}finally{le=o,$r.transition=i}}function qu(e,t,n,r){if(ts){var o=Ma(e,t,n,r);if(o===null)Ul(e,t,r,ns,n),Hd(e,r);else if(rS(o,e,t,n,r))r.stopPropagation();else if(Hd(e,r),t&4&&-1<nS.indexOf(e)){for(;o!==null;){var i=ii(o);if(i!==null&&Fh(i),i=Ma(e,t,n,r),i===null&&Ul(e,t,r,ns,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else Ul(e,t,r,null,n)}}var ns=null;function Ma(e,t,n,r){if(ns=null,e=Ku(r),e=Gn(e),e!==null)if(t=ur(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Ah(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ns=e,null}function Bh(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Yw()){case Yu:return 1;case Oh:return 4;case Ji:case Gw:return 16;case Ih:return 536870912;default:return 16}default:return 16}}var Tn=null,Xu=null,Fi=null;function Wh(){if(Fi)return Fi;var e,t=Xu,n=t.length,r,o="value"in Tn?Tn.value:Tn.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===o[i-r];r++);return Fi=o.slice(e,1<r?1-r:void 0)}function ji(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Si(){return!0}function Yd(){return!1}function yt(e){function t(n,r,o,i,s){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(i):i[l]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Si:Yd,this.isPropagationStopped=Yd,this}return we(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Si)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Si)},persist:function(){},isPersistent:Si}),t}var Jr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Zu=yt(Jr),oi=we({},Jr,{view:0,detail:0}),lS=yt(oi),Dl,Ol,po,Is=we({},oi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ju,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==po&&(po&&e.type==="mousemove"?(Dl=e.screenX-po.screenX,Ol=e.screenY-po.screenY):Ol=Dl=0,po=e),Dl)},movementY:function(e){return"movementY"in e?e.movementY:Ol}}),Gd=yt(Is),aS=we({},Is,{dataTransfer:0}),uS=yt(aS),cS=we({},oi,{relatedTarget:0}),Il=yt(cS),dS=we({},Jr,{animationName:0,elapsedTime:0,pseudoElement:0}),fS=yt(dS),pS=we({},Jr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),hS=yt(pS),vS=we({},Jr,{data:0}),Qd=yt(vS),mS={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},yS={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},gS={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function wS(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=gS[e])?!!t[e]:!1}function Ju(){return wS}var SS=we({},oi,{key:function(e){if(e.key){var t=mS[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ji(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?yS[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ju,charCode:function(e){return e.type==="keypress"?ji(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ji(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),CS=yt(SS),xS=we({},Is,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),qd=yt(xS),ES=we({},oi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ju}),kS=yt(ES),PS=we({},Jr,{propertyName:0,elapsedTime:0,pseudoElement:0}),RS=yt(PS),_S=we({},Is,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),TS=yt(_S),NS=[9,13,27,32],ec=un&&"CompositionEvent"in window,Ro=null;un&&"documentMode"in document&&(Ro=document.documentMode);var AS=un&&"TextEvent"in window&&!Ro,Hh=un&&(!ec||Ro&&8<Ro&&11>=Ro),Xd=" ",Zd=!1;function Kh(e,t){switch(e){case"keyup":return NS.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Yh(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var kr=!1;function bS(e,t){switch(e){case"compositionend":return Yh(t);case"keypress":return t.which!==32?null:(Zd=!0,Xd);case"textInput":return e=t.data,e===Xd&&Zd?null:e;default:return null}}function MS(e,t){if(kr)return e==="compositionend"||!ec&&Kh(e,t)?(e=Wh(),Fi=Xu=Tn=null,kr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Hh&&t.locale!=="ko"?null:t.data;default:return null}}var DS={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Jd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!DS[e.type]:t==="textarea"}function Gh(e,t,n,r){Ph(r),t=rs(t,"onChange"),0<t.length&&(n=new Zu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var _o=null,Wo=null;function OS(e){iv(e,0)}function Ls(e){var t=_r(e);if(gh(t))return e}function IS(e,t){if(e==="change")return t}var Qh=!1;if(un){var Ll;if(un){var $l="oninput"in document;if(!$l){var ef=document.createElement("div");ef.setAttribute("oninput","return;"),$l=typeof ef.oninput=="function"}Ll=$l}else Ll=!1;Qh=Ll&&(!document.documentMode||9<document.documentMode)}function tf(){_o&&(_o.detachEvent("onpropertychange",qh),Wo=_o=null)}function qh(e){if(e.propertyName==="value"&&Ls(Wo)){var t=[];Gh(t,Wo,e,Ku(e)),Nh(OS,t)}}function LS(e,t,n){e==="focusin"?(tf(),_o=t,Wo=n,_o.attachEvent("onpropertychange",qh)):e==="focusout"&&tf()}function $S(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ls(Wo)}function FS(e,t){if(e==="click")return Ls(t)}function jS(e,t){if(e==="input"||e==="change")return Ls(t)}function VS(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Vt=typeof Object.is=="function"?Object.is:VS;function Ho(e,t){if(Vt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!ha.call(t,o)||!Vt(e[o],t[o]))return!1}return!0}function nf(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function rf(e,t){var n=nf(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=nf(n)}}function Xh(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Xh(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Zh(){for(var e=window,t=qi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch(r){n=!1}if(n)e=t.contentWindow;else break;t=qi(e.document)}return t}function tc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function zS(e){var t=Zh(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Xh(n.ownerDocument.documentElement,n)){if(r!==null&&tc(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=rf(n,i);var s=rf(n,r);o&&s&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var US=un&&"documentMode"in document&&11>=document.documentMode,Pr=null,Da=null,To=null,Oa=!1;function of(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Oa||Pr==null||Pr!==qi(r)||(r=Pr,"selectionStart"in r&&tc(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),To&&Ho(To,r)||(To=r,r=rs(Da,"onSelect"),0<r.length&&(t=new Zu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Pr)))}function Ci(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Rr={animationend:Ci("Animation","AnimationEnd"),animationiteration:Ci("Animation","AnimationIteration"),animationstart:Ci("Animation","AnimationStart"),transitionend:Ci("Transition","TransitionEnd")},Fl={},Jh={};un&&(Jh=document.createElement("div").style,"AnimationEvent"in window||(delete Rr.animationend.animation,delete Rr.animationiteration.animation,delete Rr.animationstart.animation),"TransitionEvent"in window||delete Rr.transitionend.transition);function $s(e){if(Fl[e])return Fl[e];if(!Rr[e])return e;var t=Rr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Jh)return Fl[e]=t[n];return e}var ev=$s("animationend"),tv=$s("animationiteration"),nv=$s("animationstart"),rv=$s("transitionend"),ov=new Map,sf="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function zn(e,t){ov.set(e,t),ar(t,[e])}for(var jl=0;jl<sf.length;jl++){var Vl=sf[jl],BS=Vl.toLowerCase(),WS=Vl[0].toUpperCase()+Vl.slice(1);zn(BS,"on"+WS)}zn(ev,"onAnimationEnd");zn(tv,"onAnimationIteration");zn(nv,"onAnimationStart");zn("dblclick","onDoubleClick");zn("focusin","onFocus");zn("focusout","onBlur");zn(rv,"onTransitionEnd");Br("onMouseEnter",["mouseout","mouseover"]);Br("onMouseLeave",["mouseout","mouseover"]);Br("onPointerEnter",["pointerout","pointerover"]);Br("onPointerLeave",["pointerout","pointerover"]);ar("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));ar("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));ar("onBeforeInput",["compositionend","keypress","textInput","paste"]);ar("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));ar("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));ar("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var xo="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),HS=new Set("cancel close invalid load scroll toggle".split(" ").concat(xo));function lf(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Bw(r,t,void 0,e),e.currentTarget=null}function iv(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var s=r.length-1;0<=s;s--){var l=r[s],a=l.instance,c=l.currentTarget;if(l=l.listener,a!==i&&o.isPropagationStopped())break e;lf(o,l,c),i=a}else for(s=0;s<r.length;s++){if(l=r[s],a=l.instance,c=l.currentTarget,l=l.listener,a!==i&&o.isPropagationStopped())break e;lf(o,l,c),i=a}}}if(Zi)throw e=Na,Zi=!1,Na=null,e}function de(e,t){var n=t[ja];n===void 0&&(n=t[ja]=new Set);var r=e+"__bubble";n.has(r)||(sv(t,e,2,!1),n.add(r))}function zl(e,t,n){var r=0;t&&(r|=4),sv(n,e,r,t)}var xi="_reactListening"+Math.random().toString(36).slice(2);function Ko(e){if(!e[xi]){e[xi]=!0,ph.forEach(function(n){n!=="selectionchange"&&(HS.has(n)||zl(n,!1,e),zl(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[xi]||(t[xi]=!0,zl("selectionchange",!1,t))}}function sv(e,t,n,r){switch(Bh(t)){case 1:var o=iS;break;case 4:o=sS;break;default:o=qu}n=o.bind(null,t,n,e),o=void 0,!Ta||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Ul(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var l=r.stateNode.containerInfo;if(l===o||l.nodeType===8&&l.parentNode===o)break;if(s===4)for(s=r.return;s!==null;){var a=s.tag;if((a===3||a===4)&&(a=s.stateNode.containerInfo,a===o||a.nodeType===8&&a.parentNode===o))return;s=s.return}for(;l!==null;){if(s=Gn(l),s===null)return;if(a=s.tag,a===5||a===6){r=i=s;continue e}l=l.parentNode}}r=r.return}Nh(function(){var c=i,d=Ku(n),h=[];e:{var p=ov.get(e);if(p!==void 0){var S=Zu,C=e;switch(e){case"keypress":if(ji(n)===0)break e;case"keydown":case"keyup":S=CS;break;case"focusin":C="focus",S=Il;break;case"focusout":C="blur",S=Il;break;case"beforeblur":case"afterblur":S=Il;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":S=Gd;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":S=uS;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":S=kS;break;case ev:case tv:case nv:S=fS;break;case rv:S=RS;break;case"scroll":S=lS;break;case"wheel":S=TS;break;case"copy":case"cut":case"paste":S=hS;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":S=qd}var g=(t&4)!==0,w=!g&&e==="scroll",v=g?p!==null?p+"Capture":null:p;g=[];for(var f=c,y;f!==null;){y=f;var E=y.stateNode;if(y.tag===5&&E!==null&&(y=E,v!==null&&(E=Vo(f,v),E!=null&&g.push(Yo(f,E,y)))),w)break;f=f.return}0<g.length&&(p=new S(p,C,null,n,d),h.push({event:p,listeners:g}))}}if(!(t&7)){e:{if(p=e==="mouseover"||e==="pointerover",S=e==="mouseout"||e==="pointerout",p&&n!==Ra&&(C=n.relatedTarget||n.fromElement)&&(Gn(C)||C[cn]))break e;if((S||p)&&(p=d.window===d?d:(p=d.ownerDocument)?p.defaultView||p.parentWindow:window,S?(C=n.relatedTarget||n.toElement,S=c,C=C?Gn(C):null,C!==null&&(w=ur(C),C!==w||C.tag!==5&&C.tag!==6)&&(C=null)):(S=null,C=c),S!==C)){if(g=Gd,E="onMouseLeave",v="onMouseEnter",f="mouse",(e==="pointerout"||e==="pointerover")&&(g=qd,E="onPointerLeave",v="onPointerEnter",f="pointer"),w=S==null?p:_r(S),y=C==null?p:_r(C),p=new g(E,f+"leave",S,n,d),p.target=w,p.relatedTarget=y,E=null,Gn(d)===c&&(g=new g(v,f+"enter",C,n,d),g.target=y,g.relatedTarget=w,E=g),w=E,S&&C)t:{for(g=S,v=C,f=0,y=g;y;y=mr(y))f++;for(y=0,E=v;E;E=mr(E))y++;for(;0<f-y;)g=mr(g),f--;for(;0<y-f;)v=mr(v),y--;for(;f--;){if(g===v||v!==null&&g===v.alternate)break t;g=mr(g),v=mr(v)}g=null}else g=null;S!==null&&af(h,p,S,g,!1),C!==null&&w!==null&&af(h,w,C,g,!0)}}e:{if(p=c?_r(c):window,S=p.nodeName&&p.nodeName.toLowerCase(),S==="select"||S==="input"&&p.type==="file")var P=IS;else if(Jd(p))if(Qh)P=jS;else{P=$S;var T=LS}else(S=p.nodeName)&&S.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(P=FS);if(P&&(P=P(e,c))){Gh(h,P,n,d);break e}T&&T(e,p,c),e==="focusout"&&(T=p._wrapperState)&&T.controlled&&p.type==="number"&&Ca(p,"number",p.value)}switch(T=c?_r(c):window,e){case"focusin":(Jd(T)||T.contentEditable==="true")&&(Pr=T,Da=c,To=null);break;case"focusout":To=Da=Pr=null;break;case"mousedown":Oa=!0;break;case"contextmenu":case"mouseup":case"dragend":Oa=!1,of(h,n,d);break;case"selectionchange":if(US)break;case"keydown":case"keyup":of(h,n,d)}var b;if(ec)e:{switch(e){case"compositionstart":var M="onCompositionStart";break e;case"compositionend":M="onCompositionEnd";break e;case"compositionupdate":M="onCompositionUpdate";break e}M=void 0}else kr?Kh(e,n)&&(M="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(M="onCompositionStart");M&&(Hh&&n.locale!=="ko"&&(kr||M!=="onCompositionStart"?M==="onCompositionEnd"&&kr&&(b=Wh()):(Tn=d,Xu="value"in Tn?Tn.value:Tn.textContent,kr=!0)),T=rs(c,M),0<T.length&&(M=new Qd(M,e,null,n,d),h.push({event:M,listeners:T}),b?M.data=b:(b=Yh(n),b!==null&&(M.data=b)))),(b=AS?bS(e,n):MS(e,n))&&(c=rs(c,"onBeforeInput"),0<c.length&&(d=new Qd("onBeforeInput","beforeinput",null,n,d),h.push({event:d,listeners:c}),d.data=b))}iv(h,t)})}function Yo(e,t,n){return{instance:e,listener:t,currentTarget:n}}function rs(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=Vo(e,n),i!=null&&r.unshift(Yo(e,i,o)),i=Vo(e,t),i!=null&&r.push(Yo(e,i,o))),e=e.return}return r}function mr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function af(e,t,n,r,o){for(var i=t._reactName,s=[];n!==null&&n!==r;){var l=n,a=l.alternate,c=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&c!==null&&(l=c,o?(a=Vo(n,i),a!=null&&s.unshift(Yo(n,a,l))):o||(a=Vo(n,i),a!=null&&s.push(Yo(n,a,l)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var KS=/\r\n?/g,YS=/\u0000|\uFFFD/g;function uf(e){return(typeof e=="string"?e:""+e).replace(KS,`
`).replace(YS,"")}function Ei(e,t,n){if(t=uf(t),uf(e)!==t&&n)throw Error(L(425))}function os(){}var Ia=null,La=null;function $a(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Fa=typeof setTimeout=="function"?setTimeout:void 0,GS=typeof clearTimeout=="function"?clearTimeout:void 0,cf=typeof Promise=="function"?Promise:void 0,QS=typeof queueMicrotask=="function"?queueMicrotask:typeof cf!="undefined"?function(e){return cf.resolve(null).then(e).catch(qS)}:Fa;function qS(e){setTimeout(function(){throw e})}function Bl(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),Bo(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Bo(t)}function Dn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function df(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var eo=Math.random().toString(36).slice(2),Yt="__reactFiber$"+eo,Go="__reactProps$"+eo,cn="__reactContainer$"+eo,ja="__reactEvents$"+eo,XS="__reactListeners$"+eo,ZS="__reactHandles$"+eo;function Gn(e){var t=e[Yt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[cn]||n[Yt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=df(e);e!==null;){if(n=e[Yt])return n;e=df(e)}return t}e=n,n=e.parentNode}return null}function ii(e){return e=e[Yt]||e[cn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function _r(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(L(33))}function Fs(e){return e[Go]||null}var Va=[],Tr=-1;function Un(e){return{current:e}}function pe(e){0>Tr||(e.current=Va[Tr],Va[Tr]=null,Tr--)}function ce(e,t){Tr++,Va[Tr]=e.current,e.current=t}var Vn={},Xe=Un(Vn),at=Un(!1),tr=Vn;function Wr(e,t){var n=e.type.contextTypes;if(!n)return Vn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function ut(e){return e=e.childContextTypes,e!=null}function is(){pe(at),pe(Xe)}function ff(e,t,n){if(Xe.current!==Vn)throw Error(L(168));ce(Xe,t),ce(at,n)}function lv(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(L(108,Lw(e)||"Unknown",o));return we({},n,r)}function ss(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Vn,tr=Xe.current,ce(Xe,e),ce(at,at.current),!0}function pf(e,t,n){var r=e.stateNode;if(!r)throw Error(L(169));n?(e=lv(e,t,tr),r.__reactInternalMemoizedMergedChildContext=e,pe(at),pe(Xe),ce(Xe,e)):pe(at),ce(at,n)}var rn=null,js=!1,Wl=!1;function av(e){rn===null?rn=[e]:rn.push(e)}function JS(e){js=!0,av(e)}function Bn(){if(!Wl&&rn!==null){Wl=!0;var e=0,t=le;try{var n=rn;for(le=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}rn=null,js=!1}catch(o){throw rn!==null&&(rn=rn.slice(e+1)),Dh(Yu,Bn),o}finally{le=t,Wl=!1}}return null}var Nr=[],Ar=0,ls=null,as=0,St=[],Ct=0,nr=null,sn=1,ln="";function Kn(e,t){Nr[Ar++]=as,Nr[Ar++]=ls,ls=e,as=t}function uv(e,t,n){St[Ct++]=sn,St[Ct++]=ln,St[Ct++]=nr,nr=e;var r=sn;e=ln;var o=32-$t(r)-1;r&=~(1<<o),n+=1;var i=32-$t(t)+o;if(30<i){var s=o-o%5;i=(r&(1<<s)-1).toString(32),r>>=s,o-=s,sn=1<<32-$t(t)+o|n<<o|r,ln=i+e}else sn=1<<i|n<<o|r,ln=e}function nc(e){e.return!==null&&(Kn(e,1),uv(e,1,0))}function rc(e){for(;e===ls;)ls=Nr[--Ar],Nr[Ar]=null,as=Nr[--Ar],Nr[Ar]=null;for(;e===nr;)nr=St[--Ct],St[Ct]=null,ln=St[--Ct],St[Ct]=null,sn=St[--Ct],St[Ct]=null}var ht=null,pt=null,ve=!1,Ot=null;function cv(e,t){var n=Et(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function hf(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,ht=e,pt=Dn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,ht=e,pt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=nr!==null?{id:sn,overflow:ln}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Et(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,ht=e,pt=null,!0):!1;default:return!1}}function za(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ua(e){if(ve){var t=pt;if(t){var n=t;if(!hf(e,t)){if(za(e))throw Error(L(418));t=Dn(n.nextSibling);var r=ht;t&&hf(e,t)?cv(r,n):(e.flags=e.flags&-4097|2,ve=!1,ht=e)}}else{if(za(e))throw Error(L(418));e.flags=e.flags&-4097|2,ve=!1,ht=e}}}function vf(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;ht=e}function ki(e){if(e!==ht)return!1;if(!ve)return vf(e),ve=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!$a(e.type,e.memoizedProps)),t&&(t=pt)){if(za(e))throw dv(),Error(L(418));for(;t;)cv(e,t),t=Dn(t.nextSibling)}if(vf(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(L(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){pt=Dn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}pt=null}}else pt=ht?Dn(e.stateNode.nextSibling):null;return!0}function dv(){for(var e=pt;e;)e=Dn(e.nextSibling)}function Hr(){pt=ht=null,ve=!1}function oc(e){Ot===null?Ot=[e]:Ot.push(e)}var e2=pn.ReactCurrentBatchConfig;function ho(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(L(309));var r=n.stateNode}if(!r)throw Error(L(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(s){var l=o.refs;s===null?delete l[i]:l[i]=s},t._stringRef=i,t)}if(typeof e!="string")throw Error(L(284));if(!n._owner)throw Error(L(290,e))}return e}function Pi(e,t){throw e=Object.prototype.toString.call(t),Error(L(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function mf(e){var t=e._init;return t(e._payload)}function fv(e){function t(v,f){if(e){var y=v.deletions;y===null?(v.deletions=[f],v.flags|=16):y.push(f)}}function n(v,f){if(!e)return null;for(;f!==null;)t(v,f),f=f.sibling;return null}function r(v,f){for(v=new Map;f!==null;)f.key!==null?v.set(f.key,f):v.set(f.index,f),f=f.sibling;return v}function o(v,f){return v=$n(v,f),v.index=0,v.sibling=null,v}function i(v,f,y){return v.index=y,e?(y=v.alternate,y!==null?(y=y.index,y<f?(v.flags|=2,f):y):(v.flags|=2,f)):(v.flags|=1048576,f)}function s(v){return e&&v.alternate===null&&(v.flags|=2),v}function l(v,f,y,E){return f===null||f.tag!==6?(f=Xl(y,v.mode,E),f.return=v,f):(f=o(f,y),f.return=v,f)}function a(v,f,y,E){var P=y.type;return P===Er?d(v,f,y.props.children,E,y.key):f!==null&&(f.elementType===P||typeof P=="object"&&P!==null&&P.$$typeof===En&&mf(P)===f.type)?(E=o(f,y.props),E.ref=ho(v,f,y),E.return=v,E):(E=Ki(y.type,y.key,y.props,null,v.mode,E),E.ref=ho(v,f,y),E.return=v,E)}function c(v,f,y,E){return f===null||f.tag!==4||f.stateNode.containerInfo!==y.containerInfo||f.stateNode.implementation!==y.implementation?(f=Zl(y,v.mode,E),f.return=v,f):(f=o(f,y.children||[]),f.return=v,f)}function d(v,f,y,E,P){return f===null||f.tag!==7?(f=Jn(y,v.mode,E,P),f.return=v,f):(f=o(f,y),f.return=v,f)}function h(v,f,y){if(typeof f=="string"&&f!==""||typeof f=="number")return f=Xl(""+f,v.mode,y),f.return=v,f;if(typeof f=="object"&&f!==null){switch(f.$$typeof){case hi:return y=Ki(f.type,f.key,f.props,null,v.mode,y),y.ref=ho(v,null,f),y.return=v,y;case xr:return f=Zl(f,v.mode,y),f.return=v,f;case En:var E=f._init;return h(v,E(f._payload),y)}if(So(f)||ao(f))return f=Jn(f,v.mode,y,null),f.return=v,f;Pi(v,f)}return null}function p(v,f,y,E){var P=f!==null?f.key:null;if(typeof y=="string"&&y!==""||typeof y=="number")return P!==null?null:l(v,f,""+y,E);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case hi:return y.key===P?a(v,f,y,E):null;case xr:return y.key===P?c(v,f,y,E):null;case En:return P=y._init,p(v,f,P(y._payload),E)}if(So(y)||ao(y))return P!==null?null:d(v,f,y,E,null);Pi(v,y)}return null}function S(v,f,y,E,P){if(typeof E=="string"&&E!==""||typeof E=="number")return v=v.get(y)||null,l(f,v,""+E,P);if(typeof E=="object"&&E!==null){switch(E.$$typeof){case hi:return v=v.get(E.key===null?y:E.key)||null,a(f,v,E,P);case xr:return v=v.get(E.key===null?y:E.key)||null,c(f,v,E,P);case En:var T=E._init;return S(v,f,y,T(E._payload),P)}if(So(E)||ao(E))return v=v.get(y)||null,d(f,v,E,P,null);Pi(f,E)}return null}function C(v,f,y,E){for(var P=null,T=null,b=f,M=f=0,A=null;b!==null&&M<y.length;M++){b.index>M?(A=b,b=null):A=b.sibling;var I=p(v,b,y[M],E);if(I===null){b===null&&(b=A);break}e&&b&&I.alternate===null&&t(v,b),f=i(I,f,M),T===null?P=I:T.sibling=I,T=I,b=A}if(M===y.length)return n(v,b),ve&&Kn(v,M),P;if(b===null){for(;M<y.length;M++)b=h(v,y[M],E),b!==null&&(f=i(b,f,M),T===null?P=b:T.sibling=b,T=b);return ve&&Kn(v,M),P}for(b=r(v,b);M<y.length;M++)A=S(b,v,M,y[M],E),A!==null&&(e&&A.alternate!==null&&b.delete(A.key===null?M:A.key),f=i(A,f,M),T===null?P=A:T.sibling=A,T=A);return e&&b.forEach(function(B){return t(v,B)}),ve&&Kn(v,M),P}function g(v,f,y,E){var P=ao(y);if(typeof P!="function")throw Error(L(150));if(y=P.call(y),y==null)throw Error(L(151));for(var T=P=null,b=f,M=f=0,A=null,I=y.next();b!==null&&!I.done;M++,I=y.next()){b.index>M?(A=b,b=null):A=b.sibling;var B=p(v,b,I.value,E);if(B===null){b===null&&(b=A);break}e&&b&&B.alternate===null&&t(v,b),f=i(B,f,M),T===null?P=B:T.sibling=B,T=B,b=A}if(I.done)return n(v,b),ve&&Kn(v,M),P;if(b===null){for(;!I.done;M++,I=y.next())I=h(v,I.value,E),I!==null&&(f=i(I,f,M),T===null?P=I:T.sibling=I,T=I);return ve&&Kn(v,M),P}for(b=r(v,b);!I.done;M++,I=y.next())I=S(b,v,M,I.value,E),I!==null&&(e&&I.alternate!==null&&b.delete(I.key===null?M:I.key),f=i(I,f,M),T===null?P=I:T.sibling=I,T=I);return e&&b.forEach(function(j){return t(v,j)}),ve&&Kn(v,M),P}function w(v,f,y,E){if(typeof y=="object"&&y!==null&&y.type===Er&&y.key===null&&(y=y.props.children),typeof y=="object"&&y!==null){switch(y.$$typeof){case hi:e:{for(var P=y.key,T=f;T!==null;){if(T.key===P){if(P=y.type,P===Er){if(T.tag===7){n(v,T.sibling),f=o(T,y.props.children),f.return=v,v=f;break e}}else if(T.elementType===P||typeof P=="object"&&P!==null&&P.$$typeof===En&&mf(P)===T.type){n(v,T.sibling),f=o(T,y.props),f.ref=ho(v,T,y),f.return=v,v=f;break e}n(v,T);break}else t(v,T);T=T.sibling}y.type===Er?(f=Jn(y.props.children,v.mode,E,y.key),f.return=v,v=f):(E=Ki(y.type,y.key,y.props,null,v.mode,E),E.ref=ho(v,f,y),E.return=v,v=E)}return s(v);case xr:e:{for(T=y.key;f!==null;){if(f.key===T)if(f.tag===4&&f.stateNode.containerInfo===y.containerInfo&&f.stateNode.implementation===y.implementation){n(v,f.sibling),f=o(f,y.children||[]),f.return=v,v=f;break e}else{n(v,f);break}else t(v,f);f=f.sibling}f=Zl(y,v.mode,E),f.return=v,v=f}return s(v);case En:return T=y._init,w(v,f,T(y._payload),E)}if(So(y))return C(v,f,y,E);if(ao(y))return g(v,f,y,E);Pi(v,y)}return typeof y=="string"&&y!==""||typeof y=="number"?(y=""+y,f!==null&&f.tag===6?(n(v,f.sibling),f=o(f,y),f.return=v,v=f):(n(v,f),f=Xl(y,v.mode,E),f.return=v,v=f),s(v)):n(v,f)}return w}var Kr=fv(!0),pv=fv(!1),us=Un(null),cs=null,br=null,ic=null;function sc(){ic=br=cs=null}function lc(e){var t=us.current;pe(us),e._currentValue=t}function Ba(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Fr(e,t){cs=e,ic=br=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(lt=!0),e.firstContext=null)}function Pt(e){var t=e._currentValue;if(ic!==e)if(e={context:e,memoizedValue:t,next:null},br===null){if(cs===null)throw Error(L(308));br=e,cs.dependencies={lanes:0,firstContext:e}}else br=br.next=e;return t}var Qn=null;function ac(e){Qn===null?Qn=[e]:Qn.push(e)}function hv(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,ac(t)):(n.next=o.next,o.next=n),t.interleaved=n,dn(e,r)}function dn(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var kn=!1;function uc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function vv(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function an(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function On(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,oe&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,dn(e,n)}return o=r.interleaved,o===null?(t.next=t,ac(r)):(t.next=o.next,o.next=t),r.interleaved=t,dn(e,n)}function Vi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Gu(e,n)}}function yf(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=s:i=i.next=s,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function ds(e,t,n,r){var o=e.updateQueue;kn=!1;var i=o.firstBaseUpdate,s=o.lastBaseUpdate,l=o.shared.pending;if(l!==null){o.shared.pending=null;var a=l,c=a.next;a.next=null,s===null?i=c:s.next=c,s=a;var d=e.alternate;d!==null&&(d=d.updateQueue,l=d.lastBaseUpdate,l!==s&&(l===null?d.firstBaseUpdate=c:l.next=c,d.lastBaseUpdate=a))}if(i!==null){var h=o.baseState;s=0,d=c=a=null,l=i;do{var p=l.lane,S=l.eventTime;if((r&p)===p){d!==null&&(d=d.next={eventTime:S,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var C=e,g=l;switch(p=t,S=n,g.tag){case 1:if(C=g.payload,typeof C=="function"){h=C.call(S,h,p);break e}h=C;break e;case 3:C.flags=C.flags&-65537|128;case 0:if(C=g.payload,p=typeof C=="function"?C.call(S,h,p):C,p==null)break e;h=we({},h,p);break e;case 2:kn=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,p=o.effects,p===null?o.effects=[l]:p.push(l))}else S={eventTime:S,lane:p,tag:l.tag,payload:l.payload,callback:l.callback,next:null},d===null?(c=d=S,a=h):d=d.next=S,s|=p;if(l=l.next,l===null){if(l=o.shared.pending,l===null)break;p=l,l=p.next,p.next=null,o.lastBaseUpdate=p,o.shared.pending=null}}while(!0);if(d===null&&(a=h),o.baseState=a,o.firstBaseUpdate=c,o.lastBaseUpdate=d,t=o.shared.interleaved,t!==null){o=t;do s|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);or|=s,e.lanes=s,e.memoizedState=h}}function gf(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(L(191,o));o.call(r)}}}var si={},qt=Un(si),Qo=Un(si),qo=Un(si);function qn(e){if(e===si)throw Error(L(174));return e}function cc(e,t){switch(ce(qo,t),ce(Qo,e),ce(qt,si),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Ea(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Ea(t,e)}pe(qt),ce(qt,t)}function Yr(){pe(qt),pe(Qo),pe(qo)}function mv(e){qn(qo.current);var t=qn(qt.current),n=Ea(t,e.type);t!==n&&(ce(Qo,e),ce(qt,n))}function dc(e){Qo.current===e&&(pe(qt),pe(Qo))}var ye=Un(0);function fs(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Hl=[];function fc(){for(var e=0;e<Hl.length;e++)Hl[e]._workInProgressVersionPrimary=null;Hl.length=0}var zi=pn.ReactCurrentDispatcher,Kl=pn.ReactCurrentBatchConfig,rr=0,ge=null,Me=null,$e=null,ps=!1,No=!1,Xo=0,t2=0;function Ge(){throw Error(L(321))}function pc(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Vt(e[n],t[n]))return!1;return!0}function hc(e,t,n,r,o,i){if(rr=i,ge=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,zi.current=e===null||e.memoizedState===null?i2:s2,e=n(r,o),No){i=0;do{if(No=!1,Xo=0,25<=i)throw Error(L(301));i+=1,$e=Me=null,t.updateQueue=null,zi.current=l2,e=n(r,o)}while(No)}if(zi.current=hs,t=Me!==null&&Me.next!==null,rr=0,$e=Me=ge=null,ps=!1,t)throw Error(L(300));return e}function vc(){var e=Xo!==0;return Xo=0,e}function Kt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return $e===null?ge.memoizedState=$e=e:$e=$e.next=e,$e}function Rt(){if(Me===null){var e=ge.alternate;e=e!==null?e.memoizedState:null}else e=Me.next;var t=$e===null?ge.memoizedState:$e.next;if(t!==null)$e=t,Me=e;else{if(e===null)throw Error(L(310));Me=e,e={memoizedState:Me.memoizedState,baseState:Me.baseState,baseQueue:Me.baseQueue,queue:Me.queue,next:null},$e===null?ge.memoizedState=$e=e:$e=$e.next=e}return $e}function Zo(e,t){return typeof t=="function"?t(e):t}function Yl(e){var t=Rt(),n=t.queue;if(n===null)throw Error(L(311));n.lastRenderedReducer=e;var r=Me,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var s=o.next;o.next=i.next,i.next=s}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var l=s=null,a=null,c=i;do{var d=c.lane;if((rr&d)===d)a!==null&&(a=a.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var h={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};a===null?(l=a=h,s=r):a=a.next=h,ge.lanes|=d,or|=d}c=c.next}while(c!==null&&c!==i);a===null?s=r:a.next=l,Vt(r,t.memoizedState)||(lt=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,ge.lanes|=i,or|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Gl(e){var t=Rt(),n=t.queue;if(n===null)throw Error(L(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var s=o=o.next;do i=e(i,s.action),s=s.next;while(s!==o);Vt(i,t.memoizedState)||(lt=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function yv(){}function gv(e,t){var n=ge,r=Rt(),o=t(),i=!Vt(r.memoizedState,o);if(i&&(r.memoizedState=o,lt=!0),r=r.queue,mc(Cv.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||$e!==null&&$e.memoizedState.tag&1){if(n.flags|=2048,Jo(9,Sv.bind(null,n,r,o,t),void 0,null),Fe===null)throw Error(L(349));rr&30||wv(n,t,o)}return o}function wv(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ge.updateQueue,t===null?(t={lastEffect:null,stores:null},ge.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Sv(e,t,n,r){t.value=n,t.getSnapshot=r,xv(t)&&Ev(e)}function Cv(e,t,n){return n(function(){xv(t)&&Ev(e)})}function xv(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Vt(e,n)}catch(r){return!0}}function Ev(e){var t=dn(e,1);t!==null&&Ft(t,e,1,-1)}function wf(e){var t=Kt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Zo,lastRenderedState:e},t.queue=e,e=e.dispatch=o2.bind(null,ge,e),[t.memoizedState,e]}function Jo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=ge.updateQueue,t===null?(t={lastEffect:null,stores:null},ge.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function kv(){return Rt().memoizedState}function Ui(e,t,n,r){var o=Kt();ge.flags|=e,o.memoizedState=Jo(1|t,n,void 0,r===void 0?null:r)}function Vs(e,t,n,r){var o=Rt();r=r===void 0?null:r;var i=void 0;if(Me!==null){var s=Me.memoizedState;if(i=s.destroy,r!==null&&pc(r,s.deps)){o.memoizedState=Jo(t,n,i,r);return}}ge.flags|=e,o.memoizedState=Jo(1|t,n,i,r)}function Sf(e,t){return Ui(8390656,8,e,t)}function mc(e,t){return Vs(2048,8,e,t)}function Pv(e,t){return Vs(4,2,e,t)}function Rv(e,t){return Vs(4,4,e,t)}function _v(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Tv(e,t,n){return n=n!=null?n.concat([e]):null,Vs(4,4,_v.bind(null,t,e),n)}function yc(){}function Nv(e,t){var n=Rt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&pc(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Av(e,t){var n=Rt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&pc(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function bv(e,t,n){return rr&21?(Vt(n,t)||(n=Lh(),ge.lanes|=n,or|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,lt=!0),e.memoizedState=n)}function n2(e,t){var n=le;le=n!==0&&4>n?n:4,e(!0);var r=Kl.transition;Kl.transition={};try{e(!1),t()}finally{le=n,Kl.transition=r}}function Mv(){return Rt().memoizedState}function r2(e,t,n){var r=Ln(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Dv(e))Ov(t,n);else if(n=hv(e,t,n,r),n!==null){var o=nt();Ft(n,e,r,o),Iv(n,t,r)}}function o2(e,t,n){var r=Ln(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Dv(e))Ov(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var s=t.lastRenderedState,l=i(s,n);if(o.hasEagerState=!0,o.eagerState=l,Vt(l,s)){var a=t.interleaved;a===null?(o.next=o,ac(t)):(o.next=a.next,a.next=o),t.interleaved=o;return}}catch(c){}finally{}n=hv(e,t,o,r),n!==null&&(o=nt(),Ft(n,e,r,o),Iv(n,t,r))}}function Dv(e){var t=e.alternate;return e===ge||t!==null&&t===ge}function Ov(e,t){No=ps=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Iv(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Gu(e,n)}}var hs={readContext:Pt,useCallback:Ge,useContext:Ge,useEffect:Ge,useImperativeHandle:Ge,useInsertionEffect:Ge,useLayoutEffect:Ge,useMemo:Ge,useReducer:Ge,useRef:Ge,useState:Ge,useDebugValue:Ge,useDeferredValue:Ge,useTransition:Ge,useMutableSource:Ge,useSyncExternalStore:Ge,useId:Ge,unstable_isNewReconciler:!1},i2={readContext:Pt,useCallback:function(e,t){return Kt().memoizedState=[e,t===void 0?null:t],e},useContext:Pt,useEffect:Sf,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Ui(4194308,4,_v.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ui(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ui(4,2,e,t)},useMemo:function(e,t){var n=Kt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Kt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=r2.bind(null,ge,e),[r.memoizedState,e]},useRef:function(e){var t=Kt();return e={current:e},t.memoizedState=e},useState:wf,useDebugValue:yc,useDeferredValue:function(e){return Kt().memoizedState=e},useTransition:function(){var e=wf(!1),t=e[0];return e=n2.bind(null,e[1]),Kt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ge,o=Kt();if(ve){if(n===void 0)throw Error(L(407));n=n()}else{if(n=t(),Fe===null)throw Error(L(349));rr&30||wv(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,Sf(Cv.bind(null,r,i,e),[e]),r.flags|=2048,Jo(9,Sv.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=Kt(),t=Fe.identifierPrefix;if(ve){var n=ln,r=sn;n=(r&~(1<<32-$t(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Xo++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=t2++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},s2={readContext:Pt,useCallback:Nv,useContext:Pt,useEffect:mc,useImperativeHandle:Tv,useInsertionEffect:Pv,useLayoutEffect:Rv,useMemo:Av,useReducer:Yl,useRef:kv,useState:function(){return Yl(Zo)},useDebugValue:yc,useDeferredValue:function(e){var t=Rt();return bv(t,Me.memoizedState,e)},useTransition:function(){var e=Yl(Zo)[0],t=Rt().memoizedState;return[e,t]},useMutableSource:yv,useSyncExternalStore:gv,useId:Mv,unstable_isNewReconciler:!1},l2={readContext:Pt,useCallback:Nv,useContext:Pt,useEffect:mc,useImperativeHandle:Tv,useInsertionEffect:Pv,useLayoutEffect:Rv,useMemo:Av,useReducer:Gl,useRef:kv,useState:function(){return Gl(Zo)},useDebugValue:yc,useDeferredValue:function(e){var t=Rt();return Me===null?t.memoizedState=e:bv(t,Me.memoizedState,e)},useTransition:function(){var e=Gl(Zo)[0],t=Rt().memoizedState;return[e,t]},useMutableSource:yv,useSyncExternalStore:gv,useId:Mv,unstable_isNewReconciler:!1};function Mt(e,t){if(e&&e.defaultProps){t=we({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Wa(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:we({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var zs={isMounted:function(e){return(e=e._reactInternals)?ur(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=nt(),o=Ln(e),i=an(r,o);i.payload=t,n!=null&&(i.callback=n),t=On(e,i,o),t!==null&&(Ft(t,e,o,r),Vi(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=nt(),o=Ln(e),i=an(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=On(e,i,o),t!==null&&(Ft(t,e,o,r),Vi(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=nt(),r=Ln(e),o=an(n,r);o.tag=2,t!=null&&(o.callback=t),t=On(e,o,r),t!==null&&(Ft(t,e,r,n),Vi(t,e,r))}};function Cf(e,t,n,r,o,i,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,s):t.prototype&&t.prototype.isPureReactComponent?!Ho(n,r)||!Ho(o,i):!0}function Lv(e,t,n){var r=!1,o=Vn,i=t.contextType;return typeof i=="object"&&i!==null?i=Pt(i):(o=ut(t)?tr:Xe.current,r=t.contextTypes,i=(r=r!=null)?Wr(e,o):Vn),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=zs,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function xf(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&zs.enqueueReplaceState(t,t.state,null)}function Ha(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},uc(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=Pt(i):(i=ut(t)?tr:Xe.current,o.context=Wr(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Wa(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&zs.enqueueReplaceState(o,o.state,null),ds(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function Gr(e,t){try{var n="",r=t;do n+=Iw(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function Ql(e,t,n){return{value:e,source:null,stack:n!=null?n:null,digest:t!=null?t:null}}function Ka(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var a2=typeof WeakMap=="function"?WeakMap:Map;function $v(e,t,n){n=an(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){ms||(ms=!0,nu=r),Ka(e,t)},n}function Fv(e,t,n){n=an(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Ka(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Ka(e,t),typeof r!="function"&&(In===null?In=new Set([this]):In.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function Ef(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new a2;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=x2.bind(null,e,t,n),t.then(e,e))}function kf(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Pf(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=an(-1,1),t.tag=2,On(n,t,1))),n.lanes|=1),e)}var u2=pn.ReactCurrentOwner,lt=!1;function et(e,t,n,r){t.child=e===null?pv(t,null,n,r):Kr(t,e.child,n,r)}function Rf(e,t,n,r,o){n=n.render;var i=t.ref;return Fr(t,o),r=hc(e,t,n,r,i,o),n=vc(),e!==null&&!lt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,fn(e,t,o)):(ve&&n&&nc(t),t.flags|=1,et(e,t,r,o),t.child)}function _f(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!Pc(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,jv(e,t,i,r,o)):(e=Ki(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var s=i.memoizedProps;if(n=n.compare,n=n!==null?n:Ho,n(s,r)&&e.ref===t.ref)return fn(e,t,o)}return t.flags|=1,e=$n(i,r),e.ref=t.ref,e.return=t,t.child=e}function jv(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(Ho(i,r)&&e.ref===t.ref)if(lt=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(lt=!0);else return t.lanes=e.lanes,fn(e,t,o)}return Ya(e,t,n,r,o)}function Vv(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ce(Dr,dt),dt|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ce(Dr,dt),dt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,ce(Dr,dt),dt|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,ce(Dr,dt),dt|=r;return et(e,t,o,n),t.child}function zv(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ya(e,t,n,r,o){var i=ut(n)?tr:Xe.current;return i=Wr(t,i),Fr(t,o),n=hc(e,t,n,r,i,o),r=vc(),e!==null&&!lt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,fn(e,t,o)):(ve&&r&&nc(t),t.flags|=1,et(e,t,n,o),t.child)}function Tf(e,t,n,r,o){if(ut(n)){var i=!0;ss(t)}else i=!1;if(Fr(t,o),t.stateNode===null)Bi(e,t),Lv(t,n,r),Ha(t,n,r,o),r=!0;else if(e===null){var s=t.stateNode,l=t.memoizedProps;s.props=l;var a=s.context,c=n.contextType;typeof c=="object"&&c!==null?c=Pt(c):(c=ut(n)?tr:Xe.current,c=Wr(t,c));var d=n.getDerivedStateFromProps,h=typeof d=="function"||typeof s.getSnapshotBeforeUpdate=="function";h||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==r||a!==c)&&xf(t,s,r,c),kn=!1;var p=t.memoizedState;s.state=p,ds(t,r,s,o),a=t.memoizedState,l!==r||p!==a||at.current||kn?(typeof d=="function"&&(Wa(t,n,d,r),a=t.memoizedState),(l=kn||Cf(t,n,l,r,p,a,c))?(h||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),s.props=r,s.state=a,s.context=c,r=l):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,vv(e,t),l=t.memoizedProps,c=t.type===t.elementType?l:Mt(t.type,l),s.props=c,h=t.pendingProps,p=s.context,a=n.contextType,typeof a=="object"&&a!==null?a=Pt(a):(a=ut(n)?tr:Xe.current,a=Wr(t,a));var S=n.getDerivedStateFromProps;(d=typeof S=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==h||p!==a)&&xf(t,s,r,a),kn=!1,p=t.memoizedState,s.state=p,ds(t,r,s,o);var C=t.memoizedState;l!==h||p!==C||at.current||kn?(typeof S=="function"&&(Wa(t,n,S,r),C=t.memoizedState),(c=kn||Cf(t,n,c,r,p,C,a)||!1)?(d||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,C,a),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,C,a)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=C),s.props=r,s.state=C,s.context=a,r=c):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return Ga(e,t,n,r,i,o)}function Ga(e,t,n,r,o,i){zv(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return o&&pf(t,n,!1),fn(e,t,i);r=t.stateNode,u2.current=t;var l=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=Kr(t,e.child,null,i),t.child=Kr(t,null,l,i)):et(e,t,l,i),t.memoizedState=r.state,o&&pf(t,n,!0),t.child}function Uv(e){var t=e.stateNode;t.pendingContext?ff(e,t.pendingContext,t.pendingContext!==t.context):t.context&&ff(e,t.context,!1),cc(e,t.containerInfo)}function Nf(e,t,n,r,o){return Hr(),oc(o),t.flags|=256,et(e,t,n,r),t.child}var Qa={dehydrated:null,treeContext:null,retryLane:0};function qa(e){return{baseLanes:e,cachePool:null,transitions:null}}function Bv(e,t,n){var r=t.pendingProps,o=ye.current,i=!1,s=(t.flags&128)!==0,l;if((l=s)||(l=e!==null&&e.memoizedState===null?!1:(o&2)!==0),l?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),ce(ye,o&1),e===null)return Ua(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,i?(r=t.mode,i=t.child,s={mode:"hidden",children:s},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=s):i=Ws(s,r,0,null),e=Jn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=qa(n),t.memoizedState=Qa,e):gc(t,s));if(o=e.memoizedState,o!==null&&(l=o.dehydrated,l!==null))return c2(e,t,s,r,l,o,n);if(i){i=r.fallback,s=t.mode,o=e.child,l=o.sibling;var a={mode:"hidden",children:r.children};return!(s&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=$n(o,a),r.subtreeFlags=o.subtreeFlags&14680064),l!==null?i=$n(l,i):(i=Jn(i,s,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,s=e.child.memoizedState,s=s===null?qa(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=Qa,r}return i=e.child,e=i.sibling,r=$n(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function gc(e,t){return t=Ws({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Ri(e,t,n,r){return r!==null&&oc(r),Kr(t,e.child,null,n),e=gc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function c2(e,t,n,r,o,i,s){if(n)return t.flags&256?(t.flags&=-257,r=Ql(Error(L(422))),Ri(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=Ws({mode:"visible",children:r.children},o,0,null),i=Jn(i,o,s,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&Kr(t,e.child,null,s),t.child.memoizedState=qa(s),t.memoizedState=Qa,i);if(!(t.mode&1))return Ri(e,t,s,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var l=r.dgst;return r=l,i=Error(L(419)),r=Ql(i,r,void 0),Ri(e,t,s,r)}if(l=(s&e.childLanes)!==0,lt||l){if(r=Fe,r!==null){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|s)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,dn(e,o),Ft(r,e,o,-1))}return kc(),r=Ql(Error(L(421))),Ri(e,t,s,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=E2.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,pt=Dn(o.nextSibling),ht=t,ve=!0,Ot=null,e!==null&&(St[Ct++]=sn,St[Ct++]=ln,St[Ct++]=nr,sn=e.id,ln=e.overflow,nr=t),t=gc(t,r.children),t.flags|=4096,t)}function Af(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Ba(e.return,t,n)}function ql(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function Wv(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(et(e,t,r.children,n),r=ye.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Af(e,n,t);else if(e.tag===19)Af(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(ce(ye,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&fs(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),ql(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&fs(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}ql(t,!0,n,null,i);break;case"together":ql(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Bi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function fn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),or|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(L(153));if(t.child!==null){for(e=t.child,n=$n(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=$n(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function d2(e,t,n){switch(t.tag){case 3:Uv(t),Hr();break;case 5:mv(t);break;case 1:ut(t.type)&&ss(t);break;case 4:cc(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;ce(us,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(ce(ye,ye.current&1),t.flags|=128,null):n&t.child.childLanes?Bv(e,t,n):(ce(ye,ye.current&1),e=fn(e,t,n),e!==null?e.sibling:null);ce(ye,ye.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Wv(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),ce(ye,ye.current),r)break;return null;case 22:case 23:return t.lanes=0,Vv(e,t,n)}return fn(e,t,n)}var Hv,Xa,Kv,Yv;Hv=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Xa=function(){};Kv=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,qn(qt.current);var i=null;switch(n){case"input":o=wa(e,o),r=wa(e,r),i=[];break;case"select":o=we({},o,{value:void 0}),r=we({},r,{value:void 0}),i=[];break;case"textarea":o=xa(e,o),r=xa(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=os)}ka(n,r);var s;n=null;for(c in o)if(!r.hasOwnProperty(c)&&o.hasOwnProperty(c)&&o[c]!=null)if(c==="style"){var l=o[c];for(s in l)l.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(Fo.hasOwnProperty(c)?i||(i=[]):(i=i||[]).push(c,null));for(c in r){var a=r[c];if(l=o!=null?o[c]:void 0,r.hasOwnProperty(c)&&a!==l&&(a!=null||l!=null))if(c==="style")if(l){for(s in l)!l.hasOwnProperty(s)||a&&a.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in a)a.hasOwnProperty(s)&&l[s]!==a[s]&&(n||(n={}),n[s]=a[s])}else n||(i||(i=[]),i.push(c,n)),n=a;else c==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(i=i||[]).push(c,a)):c==="children"?typeof a!="string"&&typeof a!="number"||(i=i||[]).push(c,""+a):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(Fo.hasOwnProperty(c)?(a!=null&&c==="onScroll"&&de("scroll",e),i||l===a||(i=[])):(i=i||[]).push(c,a))}n&&(i=i||[]).push("style",n);var c=i;(t.updateQueue=c)&&(t.flags|=4)}};Yv=function(e,t,n,r){n!==r&&(t.flags|=4)};function vo(e,t){if(!ve)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Qe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function f2(e,t,n){var r=t.pendingProps;switch(rc(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Qe(t),null;case 1:return ut(t.type)&&is(),Qe(t),null;case 3:return r=t.stateNode,Yr(),pe(at),pe(Xe),fc(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(ki(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ot!==null&&(iu(Ot),Ot=null))),Xa(e,t),Qe(t),null;case 5:dc(t);var o=qn(qo.current);if(n=t.type,e!==null&&t.stateNode!=null)Kv(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(L(166));return Qe(t),null}if(e=qn(qt.current),ki(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[Yt]=t,r[Go]=i,e=(t.mode&1)!==0,n){case"dialog":de("cancel",r),de("close",r);break;case"iframe":case"object":case"embed":de("load",r);break;case"video":case"audio":for(o=0;o<xo.length;o++)de(xo[o],r);break;case"source":de("error",r);break;case"img":case"image":case"link":de("error",r),de("load",r);break;case"details":de("toggle",r);break;case"input":Fd(r,i),de("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},de("invalid",r);break;case"textarea":Vd(r,i),de("invalid",r)}ka(n,i),o=null;for(var s in i)if(i.hasOwnProperty(s)){var l=i[s];s==="children"?typeof l=="string"?r.textContent!==l&&(i.suppressHydrationWarning!==!0&&Ei(r.textContent,l,e),o=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(i.suppressHydrationWarning!==!0&&Ei(r.textContent,l,e),o=["children",""+l]):Fo.hasOwnProperty(s)&&l!=null&&s==="onScroll"&&de("scroll",r)}switch(n){case"input":vi(r),jd(r,i,!0);break;case"textarea":vi(r),zd(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=os)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Ch(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[Yt]=t,e[Go]=r,Hv(e,t,!1,!1),t.stateNode=e;e:{switch(s=Pa(n,r),n){case"dialog":de("cancel",e),de("close",e),o=r;break;case"iframe":case"object":case"embed":de("load",e),o=r;break;case"video":case"audio":for(o=0;o<xo.length;o++)de(xo[o],e);o=r;break;case"source":de("error",e),o=r;break;case"img":case"image":case"link":de("error",e),de("load",e),o=r;break;case"details":de("toggle",e),o=r;break;case"input":Fd(e,r),o=wa(e,r),de("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=we({},r,{value:void 0}),de("invalid",e);break;case"textarea":Vd(e,r),o=xa(e,r),de("invalid",e);break;default:o=r}ka(n,o),l=o;for(i in l)if(l.hasOwnProperty(i)){var a=l[i];i==="style"?kh(e,a):i==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&xh(e,a)):i==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&jo(e,a):typeof a=="number"&&jo(e,""+a):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Fo.hasOwnProperty(i)?a!=null&&i==="onScroll"&&de("scroll",e):a!=null&&Uu(e,i,a,s))}switch(n){case"input":vi(e),jd(e,r,!1);break;case"textarea":vi(e),zd(e);break;case"option":r.value!=null&&e.setAttribute("value",""+jn(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Or(e,!!r.multiple,i,!1):r.defaultValue!=null&&Or(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=os)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Qe(t),null;case 6:if(e&&t.stateNode!=null)Yv(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(L(166));if(n=qn(qo.current),qn(qt.current),ki(t)){if(r=t.stateNode,n=t.memoizedProps,r[Yt]=t,(i=r.nodeValue!==n)&&(e=ht,e!==null))switch(e.tag){case 3:Ei(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ei(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Yt]=t,t.stateNode=r}return Qe(t),null;case 13:if(pe(ye),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ve&&pt!==null&&t.mode&1&&!(t.flags&128))dv(),Hr(),t.flags|=98560,i=!1;else if(i=ki(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(L(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(L(317));i[Yt]=t}else Hr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Qe(t),i=!1}else Ot!==null&&(iu(Ot),Ot=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||ye.current&1?De===0&&(De=3):kc())),t.updateQueue!==null&&(t.flags|=4),Qe(t),null);case 4:return Yr(),Xa(e,t),e===null&&Ko(t.stateNode.containerInfo),Qe(t),null;case 10:return lc(t.type._context),Qe(t),null;case 17:return ut(t.type)&&is(),Qe(t),null;case 19:if(pe(ye),i=t.memoizedState,i===null)return Qe(t),null;if(r=(t.flags&128)!==0,s=i.rendering,s===null)if(r)vo(i,!1);else{if(De!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=fs(e),s!==null){for(t.flags|=128,vo(i,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,s=i.alternate,s===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return ce(ye,ye.current&1|2),t.child}e=e.sibling}i.tail!==null&&ke()>Qr&&(t.flags|=128,r=!0,vo(i,!1),t.lanes=4194304)}else{if(!r)if(e=fs(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),vo(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!ve)return Qe(t),null}else 2*ke()-i.renderingStartTime>Qr&&n!==1073741824&&(t.flags|=128,r=!0,vo(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(n=i.last,n!==null?n.sibling=s:t.child=s,i.last=s)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=ke(),t.sibling=null,n=ye.current,ce(ye,r?n&1|2:n&1),t):(Qe(t),null);case 22:case 23:return Ec(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?dt&1073741824&&(Qe(t),t.subtreeFlags&6&&(t.flags|=8192)):Qe(t),null;case 24:return null;case 25:return null}throw Error(L(156,t.tag))}function p2(e,t){switch(rc(t),t.tag){case 1:return ut(t.type)&&is(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Yr(),pe(at),pe(Xe),fc(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return dc(t),null;case 13:if(pe(ye),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(L(340));Hr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return pe(ye),null;case 4:return Yr(),null;case 10:return lc(t.type._context),null;case 22:case 23:return Ec(),null;case 24:return null;default:return null}}var _i=!1,qe=!1,h2=typeof WeakSet=="function"?WeakSet:Set,U=null;function Mr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Ce(e,t,r)}else n.current=null}function Za(e,t,n){try{n()}catch(r){Ce(e,t,r)}}var bf=!1;function v2(e,t){if(Ia=ts,e=Zh(),tc(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch(E){n=null;break e}var s=0,l=-1,a=-1,c=0,d=0,h=e,p=null;t:for(;;){for(var S;h!==n||o!==0&&h.nodeType!==3||(l=s+o),h!==i||r!==0&&h.nodeType!==3||(a=s+r),h.nodeType===3&&(s+=h.nodeValue.length),(S=h.firstChild)!==null;)p=h,h=S;for(;;){if(h===e)break t;if(p===n&&++c===o&&(l=s),p===i&&++d===r&&(a=s),(S=h.nextSibling)!==null)break;h=p,p=h.parentNode}h=S}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(La={focusedElem:e,selectionRange:n},ts=!1,U=t;U!==null;)if(t=U,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,U=e;else for(;U!==null;){t=U;try{var C=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(C!==null){var g=C.memoizedProps,w=C.memoizedState,v=t.stateNode,f=v.getSnapshotBeforeUpdate(t.elementType===t.type?g:Mt(t.type,g),w);v.__reactInternalSnapshotBeforeUpdate=f}break;case 3:var y=t.stateNode.containerInfo;y.nodeType===1?y.textContent="":y.nodeType===9&&y.documentElement&&y.removeChild(y.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(L(163))}}catch(E){Ce(t,t.return,E)}if(e=t.sibling,e!==null){e.return=t.return,U=e;break}U=t.return}return C=bf,bf=!1,C}function Ao(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&Za(t,n,i)}o=o.next}while(o!==r)}}function Us(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Ja(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Gv(e){var t=e.alternate;t!==null&&(e.alternate=null,Gv(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Yt],delete t[Go],delete t[ja],delete t[XS],delete t[ZS])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Qv(e){return e.tag===5||e.tag===3||e.tag===4}function Mf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Qv(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function eu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=os));else if(r!==4&&(e=e.child,e!==null))for(eu(e,t,n),e=e.sibling;e!==null;)eu(e,t,n),e=e.sibling}function tu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(tu(e,t,n),e=e.sibling;e!==null;)tu(e,t,n),e=e.sibling}var Ue=null,Dt=!1;function Sn(e,t,n){for(n=n.child;n!==null;)qv(e,t,n),n=n.sibling}function qv(e,t,n){if(Qt&&typeof Qt.onCommitFiberUnmount=="function")try{Qt.onCommitFiberUnmount(Os,n)}catch(l){}switch(n.tag){case 5:qe||Mr(n,t);case 6:var r=Ue,o=Dt;Ue=null,Sn(e,t,n),Ue=r,Dt=o,Ue!==null&&(Dt?(e=Ue,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Ue.removeChild(n.stateNode));break;case 18:Ue!==null&&(Dt?(e=Ue,n=n.stateNode,e.nodeType===8?Bl(e.parentNode,n):e.nodeType===1&&Bl(e,n),Bo(e)):Bl(Ue,n.stateNode));break;case 4:r=Ue,o=Dt,Ue=n.stateNode.containerInfo,Dt=!0,Sn(e,t,n),Ue=r,Dt=o;break;case 0:case 11:case 14:case 15:if(!qe&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,s=i.destroy;i=i.tag,s!==void 0&&(i&2||i&4)&&Za(n,t,s),o=o.next}while(o!==r)}Sn(e,t,n);break;case 1:if(!qe&&(Mr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){Ce(n,t,l)}Sn(e,t,n);break;case 21:Sn(e,t,n);break;case 22:n.mode&1?(qe=(r=qe)||n.memoizedState!==null,Sn(e,t,n),qe=r):Sn(e,t,n);break;default:Sn(e,t,n)}}function Df(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new h2),t.forEach(function(r){var o=k2.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function At(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,s=t,l=s;e:for(;l!==null;){switch(l.tag){case 5:Ue=l.stateNode,Dt=!1;break e;case 3:Ue=l.stateNode.containerInfo,Dt=!0;break e;case 4:Ue=l.stateNode.containerInfo,Dt=!0;break e}l=l.return}if(Ue===null)throw Error(L(160));qv(i,s,o),Ue=null,Dt=!1;var a=o.alternate;a!==null&&(a.return=null),o.return=null}catch(c){Ce(o,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Xv(t,e),t=t.sibling}function Xv(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(At(t,e),Ht(e),r&4){try{Ao(3,e,e.return),Us(3,e)}catch(g){Ce(e,e.return,g)}try{Ao(5,e,e.return)}catch(g){Ce(e,e.return,g)}}break;case 1:At(t,e),Ht(e),r&512&&n!==null&&Mr(n,n.return);break;case 5:if(At(t,e),Ht(e),r&512&&n!==null&&Mr(n,n.return),e.flags&32){var o=e.stateNode;try{jo(o,"")}catch(g){Ce(e,e.return,g)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,s=n!==null?n.memoizedProps:i,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&i.type==="radio"&&i.name!=null&&wh(o,i),Pa(l,s);var c=Pa(l,i);for(s=0;s<a.length;s+=2){var d=a[s],h=a[s+1];d==="style"?kh(o,h):d==="dangerouslySetInnerHTML"?xh(o,h):d==="children"?jo(o,h):Uu(o,d,h,c)}switch(l){case"input":Sa(o,i);break;case"textarea":Sh(o,i);break;case"select":var p=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var S=i.value;S!=null?Or(o,!!i.multiple,S,!1):p!==!!i.multiple&&(i.defaultValue!=null?Or(o,!!i.multiple,i.defaultValue,!0):Or(o,!!i.multiple,i.multiple?[]:"",!1))}o[Go]=i}catch(g){Ce(e,e.return,g)}}break;case 6:if(At(t,e),Ht(e),r&4){if(e.stateNode===null)throw Error(L(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(g){Ce(e,e.return,g)}}break;case 3:if(At(t,e),Ht(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Bo(t.containerInfo)}catch(g){Ce(e,e.return,g)}break;case 4:At(t,e),Ht(e);break;case 13:At(t,e),Ht(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(Cc=ke())),r&4&&Df(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(qe=(c=qe)||d,At(t,e),qe=c):At(t,e),Ht(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!d&&e.mode&1)for(U=e,d=e.child;d!==null;){for(h=U=d;U!==null;){switch(p=U,S=p.child,p.tag){case 0:case 11:case 14:case 15:Ao(4,p,p.return);break;case 1:Mr(p,p.return);var C=p.stateNode;if(typeof C.componentWillUnmount=="function"){r=p,n=p.return;try{t=r,C.props=t.memoizedProps,C.state=t.memoizedState,C.componentWillUnmount()}catch(g){Ce(r,n,g)}}break;case 5:Mr(p,p.return);break;case 22:if(p.memoizedState!==null){If(h);continue}}S!==null?(S.return=p,U=S):If(h)}d=d.sibling}e:for(d=null,h=e;;){if(h.tag===5){if(d===null){d=h;try{o=h.stateNode,c?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(l=h.stateNode,a=h.memoizedProps.style,s=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=Eh("display",s))}catch(g){Ce(e,e.return,g)}}}else if(h.tag===6){if(d===null)try{h.stateNode.nodeValue=c?"":h.memoizedProps}catch(g){Ce(e,e.return,g)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===e)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===e)break e;for(;h.sibling===null;){if(h.return===null||h.return===e)break e;d===h&&(d=null),h=h.return}d===h&&(d=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:At(t,e),Ht(e),r&4&&Df(e);break;case 21:break;default:At(t,e),Ht(e)}}function Ht(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Qv(n)){var r=n;break e}n=n.return}throw Error(L(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(jo(o,""),r.flags&=-33);var i=Mf(e);tu(e,i,o);break;case 3:case 4:var s=r.stateNode.containerInfo,l=Mf(e);eu(e,l,s);break;default:throw Error(L(161))}}catch(a){Ce(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function m2(e,t,n){U=e,Zv(e)}function Zv(e,t,n){for(var r=(e.mode&1)!==0;U!==null;){var o=U,i=o.child;if(o.tag===22&&r){var s=o.memoizedState!==null||_i;if(!s){var l=o.alternate,a=l!==null&&l.memoizedState!==null||qe;l=_i;var c=qe;if(_i=s,(qe=a)&&!c)for(U=o;U!==null;)s=U,a=s.child,s.tag===22&&s.memoizedState!==null?Lf(o):a!==null?(a.return=s,U=a):Lf(o);for(;i!==null;)U=i,Zv(i),i=i.sibling;U=o,_i=l,qe=c}Of(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,U=i):Of(e)}}function Of(e){for(;U!==null;){var t=U;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:qe||Us(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!qe)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:Mt(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&gf(t,i,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}gf(t,s,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var d=c.memoizedState;if(d!==null){var h=d.dehydrated;h!==null&&Bo(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(L(163))}qe||t.flags&512&&Ja(t)}catch(p){Ce(t,t.return,p)}}if(t===e){U=null;break}if(n=t.sibling,n!==null){n.return=t.return,U=n;break}U=t.return}}function If(e){for(;U!==null;){var t=U;if(t===e){U=null;break}var n=t.sibling;if(n!==null){n.return=t.return,U=n;break}U=t.return}}function Lf(e){for(;U!==null;){var t=U;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Us(4,t)}catch(a){Ce(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(a){Ce(t,o,a)}}var i=t.return;try{Ja(t)}catch(a){Ce(t,i,a)}break;case 5:var s=t.return;try{Ja(t)}catch(a){Ce(t,s,a)}}}catch(a){Ce(t,t.return,a)}if(t===e){U=null;break}var l=t.sibling;if(l!==null){l.return=t.return,U=l;break}U=t.return}}var y2=Math.ceil,vs=pn.ReactCurrentDispatcher,wc=pn.ReactCurrentOwner,kt=pn.ReactCurrentBatchConfig,oe=0,Fe=null,Te=null,Be=0,dt=0,Dr=Un(0),De=0,ei=null,or=0,Bs=0,Sc=0,bo=null,st=null,Cc=0,Qr=1/0,nn=null,ms=!1,nu=null,In=null,Ti=!1,Nn=null,ys=0,Mo=0,ru=null,Wi=-1,Hi=0;function nt(){return oe&6?ke():Wi!==-1?Wi:Wi=ke()}function Ln(e){return e.mode&1?oe&2&&Be!==0?Be&-Be:e2.transition!==null?(Hi===0&&(Hi=Lh()),Hi):(e=le,e!==0||(e=window.event,e=e===void 0?16:Bh(e.type)),e):1}function Ft(e,t,n,r){if(50<Mo)throw Mo=0,ru=null,Error(L(185));ri(e,n,r),(!(oe&2)||e!==Fe)&&(e===Fe&&(!(oe&2)&&(Bs|=n),De===4&&Rn(e,Be)),ct(e,r),n===1&&oe===0&&!(t.mode&1)&&(Qr=ke()+500,js&&Bn()))}function ct(e,t){var n=e.callbackNode;eS(e,t);var r=es(e,e===Fe?Be:0);if(r===0)n!==null&&Wd(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Wd(n),t===1)e.tag===0?JS($f.bind(null,e)):av($f.bind(null,e)),QS(function(){!(oe&6)&&Bn()}),n=null;else{switch($h(r)){case 1:n=Yu;break;case 4:n=Oh;break;case 16:n=Ji;break;case 536870912:n=Ih;break;default:n=Ji}n=sm(n,Jv.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Jv(e,t){if(Wi=-1,Hi=0,oe&6)throw Error(L(327));var n=e.callbackNode;if(jr()&&e.callbackNode!==n)return null;var r=es(e,e===Fe?Be:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=gs(e,r);else{t=r;var o=oe;oe|=2;var i=tm();(Fe!==e||Be!==t)&&(nn=null,Qr=ke()+500,Zn(e,t));do try{S2();break}catch(l){em(e,l)}while(!0);sc(),vs.current=i,oe=o,Te!==null?t=0:(Fe=null,Be=0,t=De)}if(t!==0){if(t===2&&(o=Aa(e),o!==0&&(r=o,t=ou(e,o))),t===1)throw n=ei,Zn(e,0),Rn(e,r),ct(e,ke()),n;if(t===6)Rn(e,r);else{if(o=e.current.alternate,!(r&30)&&!g2(o)&&(t=gs(e,r),t===2&&(i=Aa(e),i!==0&&(r=i,t=ou(e,i))),t===1))throw n=ei,Zn(e,0),Rn(e,r),ct(e,ke()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(L(345));case 2:Yn(e,st,nn);break;case 3:if(Rn(e,r),(r&130023424)===r&&(t=Cc+500-ke(),10<t)){if(es(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){nt(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Fa(Yn.bind(null,e,st,nn),t);break}Yn(e,st,nn);break;case 4:if(Rn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var s=31-$t(r);i=1<<s,s=t[s],s>o&&(o=s),r&=~i}if(r=o,r=ke()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*y2(r/1960))-r,10<r){e.timeoutHandle=Fa(Yn.bind(null,e,st,nn),r);break}Yn(e,st,nn);break;case 5:Yn(e,st,nn);break;default:throw Error(L(329))}}}return ct(e,ke()),e.callbackNode===n?Jv.bind(null,e):null}function ou(e,t){var n=bo;return e.current.memoizedState.isDehydrated&&(Zn(e,t).flags|=256),e=gs(e,t),e!==2&&(t=st,st=n,t!==null&&iu(t)),e}function iu(e){st===null?st=e:st.push.apply(st,e)}function g2(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!Vt(i(),o))return!1}catch(s){return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Rn(e,t){for(t&=~Sc,t&=~Bs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-$t(t),r=1<<n;e[n]=-1,t&=~r}}function $f(e){if(oe&6)throw Error(L(327));jr();var t=es(e,0);if(!(t&1))return ct(e,ke()),null;var n=gs(e,t);if(e.tag!==0&&n===2){var r=Aa(e);r!==0&&(t=r,n=ou(e,r))}if(n===1)throw n=ei,Zn(e,0),Rn(e,t),ct(e,ke()),n;if(n===6)throw Error(L(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Yn(e,st,nn),ct(e,ke()),null}function xc(e,t){var n=oe;oe|=1;try{return e(t)}finally{oe=n,oe===0&&(Qr=ke()+500,js&&Bn())}}function ir(e){Nn!==null&&Nn.tag===0&&!(oe&6)&&jr();var t=oe;oe|=1;var n=kt.transition,r=le;try{if(kt.transition=null,le=1,e)return e()}finally{le=r,kt.transition=n,oe=t,!(oe&6)&&Bn()}}function Ec(){dt=Dr.current,pe(Dr)}function Zn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,GS(n)),Te!==null)for(n=Te.return;n!==null;){var r=n;switch(rc(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&is();break;case 3:Yr(),pe(at),pe(Xe),fc();break;case 5:dc(r);break;case 4:Yr();break;case 13:pe(ye);break;case 19:pe(ye);break;case 10:lc(r.type._context);break;case 22:case 23:Ec()}n=n.return}if(Fe=e,Te=e=$n(e.current,null),Be=dt=t,De=0,ei=null,Sc=Bs=or=0,st=bo=null,Qn!==null){for(t=0;t<Qn.length;t++)if(n=Qn[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var s=i.next;i.next=o,r.next=s}n.pending=r}Qn=null}return e}function em(e,t){do{var n=Te;try{if(sc(),zi.current=hs,ps){for(var r=ge.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}ps=!1}if(rr=0,$e=Me=ge=null,No=!1,Xo=0,wc.current=null,n===null||n.return===null){De=1,ei=t,Te=null;break}e:{var i=e,s=n.return,l=n,a=t;if(t=Be,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var c=a,d=l,h=d.tag;if(!(d.mode&1)&&(h===0||h===11||h===15)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var S=kf(s);if(S!==null){S.flags&=-257,Pf(S,s,l,i,t),S.mode&1&&Ef(i,c,t),t=S,a=c;var C=t.updateQueue;if(C===null){var g=new Set;g.add(a),t.updateQueue=g}else C.add(a);break e}else{if(!(t&1)){Ef(i,c,t),kc();break e}a=Error(L(426))}}else if(ve&&l.mode&1){var w=kf(s);if(w!==null){!(w.flags&65536)&&(w.flags|=256),Pf(w,s,l,i,t),oc(Gr(a,l));break e}}i=a=Gr(a,l),De!==4&&(De=2),bo===null?bo=[i]:bo.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var v=$v(i,a,t);yf(i,v);break e;case 1:l=a;var f=i.type,y=i.stateNode;if(!(i.flags&128)&&(typeof f.getDerivedStateFromError=="function"||y!==null&&typeof y.componentDidCatch=="function"&&(In===null||!In.has(y)))){i.flags|=65536,t&=-t,i.lanes|=t;var E=Fv(i,l,t);yf(i,E);break e}}i=i.return}while(i!==null)}rm(n)}catch(P){t=P,Te===n&&n!==null&&(Te=n=n.return);continue}break}while(!0)}function tm(){var e=vs.current;return vs.current=hs,e===null?hs:e}function kc(){(De===0||De===3||De===2)&&(De=4),Fe===null||!(or&268435455)&&!(Bs&268435455)||Rn(Fe,Be)}function gs(e,t){var n=oe;oe|=2;var r=tm();(Fe!==e||Be!==t)&&(nn=null,Zn(e,t));do try{w2();break}catch(o){em(e,o)}while(!0);if(sc(),oe=n,vs.current=r,Te!==null)throw Error(L(261));return Fe=null,Be=0,De}function w2(){for(;Te!==null;)nm(Te)}function S2(){for(;Te!==null&&!Hw();)nm(Te)}function nm(e){var t=im(e.alternate,e,dt);e.memoizedProps=e.pendingProps,t===null?rm(e):Te=t,wc.current=null}function rm(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=p2(n,t),n!==null){n.flags&=32767,Te=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{De=6,Te=null;return}}else if(n=f2(n,t,dt),n!==null){Te=n;return}if(t=t.sibling,t!==null){Te=t;return}Te=t=e}while(t!==null);De===0&&(De=5)}function Yn(e,t,n){var r=le,o=kt.transition;try{kt.transition=null,le=1,C2(e,t,n,r)}finally{kt.transition=o,le=r}return null}function C2(e,t,n,r){do jr();while(Nn!==null);if(oe&6)throw Error(L(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(L(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(tS(e,i),e===Fe&&(Te=Fe=null,Be=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Ti||(Ti=!0,sm(Ji,function(){return jr(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=kt.transition,kt.transition=null;var s=le;le=1;var l=oe;oe|=4,wc.current=null,v2(e,n),Xv(n,e),zS(La),ts=!!Ia,La=Ia=null,e.current=n,m2(n),Kw(),oe=l,le=s,kt.transition=i}else e.current=n;if(Ti&&(Ti=!1,Nn=e,ys=o),i=e.pendingLanes,i===0&&(In=null),Qw(n.stateNode),ct(e,ke()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(ms)throw ms=!1,e=nu,nu=null,e;return ys&1&&e.tag!==0&&jr(),i=e.pendingLanes,i&1?e===ru?Mo++:(Mo=0,ru=e):Mo=0,Bn(),null}function jr(){if(Nn!==null){var e=$h(ys),t=kt.transition,n=le;try{if(kt.transition=null,le=16>e?16:e,Nn===null)var r=!1;else{if(e=Nn,Nn=null,ys=0,oe&6)throw Error(L(331));var o=oe;for(oe|=4,U=e.current;U!==null;){var i=U,s=i.child;if(U.flags&16){var l=i.deletions;if(l!==null){for(var a=0;a<l.length;a++){var c=l[a];for(U=c;U!==null;){var d=U;switch(d.tag){case 0:case 11:case 15:Ao(8,d,i)}var h=d.child;if(h!==null)h.return=d,U=h;else for(;U!==null;){d=U;var p=d.sibling,S=d.return;if(Gv(d),d===c){U=null;break}if(p!==null){p.return=S,U=p;break}U=S}}}var C=i.alternate;if(C!==null){var g=C.child;if(g!==null){C.child=null;do{var w=g.sibling;g.sibling=null,g=w}while(g!==null)}}U=i}}if(i.subtreeFlags&2064&&s!==null)s.return=i,U=s;else e:for(;U!==null;){if(i=U,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Ao(9,i,i.return)}var v=i.sibling;if(v!==null){v.return=i.return,U=v;break e}U=i.return}}var f=e.current;for(U=f;U!==null;){s=U;var y=s.child;if(s.subtreeFlags&2064&&y!==null)y.return=s,U=y;else e:for(s=f;U!==null;){if(l=U,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:Us(9,l)}}catch(P){Ce(l,l.return,P)}if(l===s){U=null;break e}var E=l.sibling;if(E!==null){E.return=l.return,U=E;break e}U=l.return}}if(oe=o,Bn(),Qt&&typeof Qt.onPostCommitFiberRoot=="function")try{Qt.onPostCommitFiberRoot(Os,e)}catch(P){}r=!0}return r}finally{le=n,kt.transition=t}}return!1}function Ff(e,t,n){t=Gr(n,t),t=$v(e,t,1),e=On(e,t,1),t=nt(),e!==null&&(ri(e,1,t),ct(e,t))}function Ce(e,t,n){if(e.tag===3)Ff(e,e,n);else for(;t!==null;){if(t.tag===3){Ff(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(In===null||!In.has(r))){e=Gr(n,e),e=Fv(t,e,1),t=On(t,e,1),e=nt(),t!==null&&(ri(t,1,e),ct(t,e));break}}t=t.return}}function x2(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=nt(),e.pingedLanes|=e.suspendedLanes&n,Fe===e&&(Be&n)===n&&(De===4||De===3&&(Be&130023424)===Be&&500>ke()-Cc?Zn(e,0):Sc|=n),ct(e,t)}function om(e,t){t===0&&(e.mode&1?(t=gi,gi<<=1,!(gi&130023424)&&(gi=4194304)):t=1);var n=nt();e=dn(e,t),e!==null&&(ri(e,t,n),ct(e,n))}function E2(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),om(e,n)}function k2(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(L(314))}r!==null&&r.delete(t),om(e,n)}var im;im=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||at.current)lt=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return lt=!1,d2(e,t,n);lt=!!(e.flags&131072)}else lt=!1,ve&&t.flags&1048576&&uv(t,as,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Bi(e,t),e=t.pendingProps;var o=Wr(t,Xe.current);Fr(t,n),o=hc(null,t,r,e,o,n);var i=vc();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,ut(r)?(i=!0,ss(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,uc(t),o.updater=zs,t.stateNode=o,o._reactInternals=t,Ha(t,r,e,n),t=Ga(null,t,r,!0,i,n)):(t.tag=0,ve&&i&&nc(t),et(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Bi(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=R2(r),e=Mt(r,e),o){case 0:t=Ya(null,t,r,e,n);break e;case 1:t=Tf(null,t,r,e,n);break e;case 11:t=Rf(null,t,r,e,n);break e;case 14:t=_f(null,t,r,Mt(r.type,e),n);break e}throw Error(L(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Mt(r,o),Ya(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Mt(r,o),Tf(e,t,r,o,n);case 3:e:{if(Uv(t),e===null)throw Error(L(387));r=t.pendingProps,i=t.memoizedState,o=i.element,vv(e,t),ds(t,r,null,n);var s=t.memoizedState;if(r=s.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=Gr(Error(L(423)),t),t=Nf(e,t,r,n,o);break e}else if(r!==o){o=Gr(Error(L(424)),t),t=Nf(e,t,r,n,o);break e}else for(pt=Dn(t.stateNode.containerInfo.firstChild),ht=t,ve=!0,Ot=null,n=pv(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Hr(),r===o){t=fn(e,t,n);break e}et(e,t,r,n)}t=t.child}return t;case 5:return mv(t),e===null&&Ua(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,s=o.children,$a(r,o)?s=null:i!==null&&$a(r,i)&&(t.flags|=32),zv(e,t),et(e,t,s,n),t.child;case 6:return e===null&&Ua(t),null;case 13:return Bv(e,t,n);case 4:return cc(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Kr(t,null,r,n):et(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Mt(r,o),Rf(e,t,r,o,n);case 7:return et(e,t,t.pendingProps,n),t.child;case 8:return et(e,t,t.pendingProps.children,n),t.child;case 12:return et(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,s=o.value,ce(us,r._currentValue),r._currentValue=s,i!==null)if(Vt(i.value,s)){if(i.children===o.children&&!at.current){t=fn(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var l=i.dependencies;if(l!==null){s=i.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(i.tag===1){a=an(-1,n&-n),a.tag=2;var c=i.updateQueue;if(c!==null){c=c.shared;var d=c.pending;d===null?a.next=a:(a.next=d.next,d.next=a),c.pending=a}}i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),Ba(i.return,n,t),l.lanes|=n;break}a=a.next}}else if(i.tag===10)s=i.type===t.type?null:i.child;else if(i.tag===18){if(s=i.return,s===null)throw Error(L(341));s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),Ba(s,n,t),s=i.sibling}else s=i.child;if(s!==null)s.return=i;else for(s=i;s!==null;){if(s===t){s=null;break}if(i=s.sibling,i!==null){i.return=s.return,s=i;break}s=s.return}i=s}et(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Fr(t,n),o=Pt(o),r=r(o),t.flags|=1,et(e,t,r,n),t.child;case 14:return r=t.type,o=Mt(r,t.pendingProps),o=Mt(r.type,o),_f(e,t,r,o,n);case 15:return jv(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Mt(r,o),Bi(e,t),t.tag=1,ut(r)?(e=!0,ss(t)):e=!1,Fr(t,n),Lv(t,r,o),Ha(t,r,o,n),Ga(null,t,r,!0,e,n);case 19:return Wv(e,t,n);case 22:return Vv(e,t,n)}throw Error(L(156,t.tag))};function sm(e,t){return Dh(e,t)}function P2(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Et(e,t,n,r){return new P2(e,t,n,r)}function Pc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function R2(e){if(typeof e=="function")return Pc(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Wu)return 11;if(e===Hu)return 14}return 2}function $n(e,t){var n=e.alternate;return n===null?(n=Et(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Ki(e,t,n,r,o,i){var s=2;if(r=e,typeof e=="function")Pc(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case Er:return Jn(n.children,o,i,t);case Bu:s=8,o|=8;break;case va:return e=Et(12,n,t,o|2),e.elementType=va,e.lanes=i,e;case ma:return e=Et(13,n,t,o),e.elementType=ma,e.lanes=i,e;case ya:return e=Et(19,n,t,o),e.elementType=ya,e.lanes=i,e;case mh:return Ws(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case hh:s=10;break e;case vh:s=9;break e;case Wu:s=11;break e;case Hu:s=14;break e;case En:s=16,r=null;break e}throw Error(L(130,e==null?e:typeof e,""))}return t=Et(s,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function Jn(e,t,n,r){return e=Et(7,e,r,t),e.lanes=n,e}function Ws(e,t,n,r){return e=Et(22,e,r,t),e.elementType=mh,e.lanes=n,e.stateNode={isHidden:!1},e}function Xl(e,t,n){return e=Et(6,e,null,t),e.lanes=n,e}function Zl(e,t,n){return t=Et(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function _2(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ml(0),this.expirationTimes=Ml(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ml(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Rc(e,t,n,r,o,i,s,l,a){return e=new _2(e,t,n,l,a),t===1?(t=1,i===!0&&(t|=8)):t=0,i=Et(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},uc(i),e}function T2(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:xr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function lm(e){if(!e)return Vn;e=e._reactInternals;e:{if(ur(e)!==e||e.tag!==1)throw Error(L(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(ut(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(L(171))}if(e.tag===1){var n=e.type;if(ut(n))return lv(e,n,t)}return t}function am(e,t,n,r,o,i,s,l,a){return e=Rc(n,r,!0,e,o,i,s,l,a),e.context=lm(null),n=e.current,r=nt(),o=Ln(n),i=an(r,o),i.callback=t!=null?t:null,On(n,i,o),e.current.lanes=o,ri(e,o,r),ct(e,r),e}function Hs(e,t,n,r){var o=t.current,i=nt(),s=Ln(o);return n=lm(n),t.context===null?t.context=n:t.pendingContext=n,t=an(i,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=On(o,t,s),e!==null&&(Ft(e,o,s,i),Vi(e,o,s)),s}function ws(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function jf(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function _c(e,t){jf(e,t),(e=e.alternate)&&jf(e,t)}function N2(){return null}var um=typeof reportError=="function"?reportError:function(e){console.error(e)};function Tc(e){this._internalRoot=e}Ks.prototype.render=Tc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(L(409));Hs(e,t,null,null)};Ks.prototype.unmount=Tc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;ir(function(){Hs(null,e,null,null)}),t[cn]=null}};function Ks(e){this._internalRoot=e}Ks.prototype.unstable_scheduleHydration=function(e){if(e){var t=Vh();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Pn.length&&t!==0&&t<Pn[n].priority;n++);Pn.splice(n,0,e),n===0&&Uh(e)}};function Nc(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ys(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Vf(){}function A2(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var c=ws(s);i.call(c)}}var s=am(t,r,e,0,null,!1,!1,"",Vf);return e._reactRootContainer=s,e[cn]=s.current,Ko(e.nodeType===8?e.parentNode:e),ir(),s}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var l=r;r=function(){var c=ws(a);l.call(c)}}var a=Rc(e,0,!1,null,null,!1,!1,"",Vf);return e._reactRootContainer=a,e[cn]=a.current,Ko(e.nodeType===8?e.parentNode:e),ir(function(){Hs(t,a,n,r)}),a}function Gs(e,t,n,r,o){var i=n._reactRootContainer;if(i){var s=i;if(typeof o=="function"){var l=o;o=function(){var a=ws(s);l.call(a)}}Hs(t,s,e,o)}else s=A2(n,t,e,o,r);return ws(s)}Fh=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Co(t.pendingLanes);n!==0&&(Gu(t,n|1),ct(t,ke()),!(oe&6)&&(Qr=ke()+500,Bn()))}break;case 13:ir(function(){var r=dn(e,1);if(r!==null){var o=nt();Ft(r,e,1,o)}}),_c(e,1)}};Qu=function(e){if(e.tag===13){var t=dn(e,134217728);if(t!==null){var n=nt();Ft(t,e,134217728,n)}_c(e,134217728)}};jh=function(e){if(e.tag===13){var t=Ln(e),n=dn(e,t);if(n!==null){var r=nt();Ft(n,e,t,r)}_c(e,t)}};Vh=function(){return le};zh=function(e,t){var n=le;try{return le=e,t()}finally{le=n}};_a=function(e,t,n){switch(t){case"input":if(Sa(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=Fs(r);if(!o)throw Error(L(90));gh(r),Sa(r,o)}}}break;case"textarea":Sh(e,n);break;case"select":t=n.value,t!=null&&Or(e,!!n.multiple,t,!1)}};_h=xc;Th=ir;var b2={usingClientEntryPoint:!1,Events:[ii,_r,Fs,Ph,Rh,xc]},mo={findFiberByHostInstance:Gn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},M2={bundleType:mo.bundleType,version:mo.version,rendererPackageName:mo.rendererPackageName,rendererConfig:mo.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:pn.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=bh(e),e===null?null:e.stateNode},findFiberByHostInstance:mo.findFiberByHostInstance||N2,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__!="undefined"){var Ni=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ni.isDisabled&&Ni.supportsFiber)try{Os=Ni.inject(M2),Qt=Ni}catch(e){}}mt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=b2;mt.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Nc(t))throw Error(L(200));return T2(e,t,null,n)};mt.createRoot=function(e,t){if(!Nc(e))throw Error(L(299));var n=!1,r="",o=um;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=Rc(e,1,!1,null,null,n,!1,r,o),e[cn]=t.current,Ko(e.nodeType===8?e.parentNode:e),new Tc(t)};mt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(L(188)):(e=Object.keys(e).join(","),Error(L(268,e)));return e=bh(t),e=e===null?null:e.stateNode,e};mt.flushSync=function(e){return ir(e)};mt.hydrate=function(e,t,n){if(!Ys(t))throw Error(L(200));return Gs(null,e,t,!0,n)};mt.hydrateRoot=function(e,t,n){if(!Nc(e))throw Error(L(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",s=um;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=am(t,null,e,1,n!=null?n:null,o,!1,i,s),e[cn]=t.current,Ko(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Ks(t)};mt.render=function(e,t,n){if(!Ys(t))throw Error(L(200));return Gs(null,e,t,!1,n)};mt.unmountComponentAtNode=function(e){if(!Ys(e))throw Error(L(40));return e._reactRootContainer?(ir(function(){Gs(null,null,e,!1,function(){e._reactRootContainer=null,e[cn]=null})}),!0):!1};mt.unstable_batchedUpdates=xc;mt.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ys(n))throw Error(L(200));if(e==null||e._reactInternals===void 0)throw Error(L(38));return Gs(e,t,n,!1,r)};mt.version="18.3.1-next-f1338f8080-20240426";function cm(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(cm)}catch(e){console.error(e)}}cm(),fh.exports=mt;var hn=fh.exports;const Ac=Bp(hn);var D2,zf=hn;D2=zf.createRoot,zf.hydrateRoot;function on(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Uf(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function O2(...e){return t=>{let n=!1;const r=e.map(o=>{const i=Uf(o,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let o=0;o<r.length;o++){const i=r[o];typeof i=="function"?i():Uf(e[o],null)}}}}function to(...e){return u.useCallback(O2(...e),e)}function I2(e,t){const n=u.createContext(t),r=i=>{const c=i,{children:s}=c,l=N(c,["children"]),a=u.useMemo(()=>l,Object.values(l));return x.jsx(n.Provider,{value:a,children:s})};r.displayName=e+"Provider";function o(i){const s=u.useContext(n);if(s)return s;if(t!==void 0)return t;throw new Error(`\`${i}\` must be used within \`${e}\``)}return[r,o]}function Ye(e,t=[]){let n=[];function r(i,s){const l=u.createContext(s),a=n.length;n=[...n,s];const c=h=>{var f;const v=h,{scope:p,children:S}=v,C=N(v,["scope","children"]),g=((f=p==null?void 0:p[e])==null?void 0:f[a])||l,w=u.useMemo(()=>C,Object.values(C));return x.jsx(g.Provider,{value:w,children:S})};c.displayName=i+"Provider";function d(h,p){var g;const S=((g=p==null?void 0:p[e])==null?void 0:g[a])||l,C=u.useContext(S);if(C)return C;if(s!==void 0)return s;throw new Error(`\`${h}\` must be used within \`${i}\``)}return[c,d]}const o=()=>{const i=n.map(s=>u.createContext(s));return function(l){const a=(l==null?void 0:l[e])||i;return u.useMemo(()=>({[`__scope${e}`]:R(m({},l),{[e]:a})}),[l,a])}};return o.scopeName=e,[r,L2(o,...t)]}function L2(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((l,{useScope:a,scopeName:c})=>{const h=a(i)[`__scope${c}`];return m(m({},l),h)},{});return u.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}function Bf(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function $2(...e){return t=>{let n=!1;const r=e.map(o=>{const i=Bf(o,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let o=0;o<r.length;o++){const i=r[o];typeof i=="function"?i():Bf(e[o],null)}}}}var Oe=u.forwardRef((e,t)=>{const s=e,{children:n}=s,r=N(s,["children"]),o=u.Children.toArray(n),i=o.find(F2);if(i){const l=i.props.children,a=o.map(c=>c===i?u.Children.count(l)>1?u.Children.only(null):u.isValidElement(l)?l.props.children:null:c);return x.jsx(su,R(m({},r),{ref:t,children:u.isValidElement(l)?u.cloneElement(l,void 0,a):null}))}return x.jsx(su,R(m({},r),{ref:t,children:n}))});Oe.displayName="Slot";var su=u.forwardRef((e,t)=>{const o=e,{children:n}=o,r=N(o,["children"]);if(u.isValidElement(n)){const i=V2(n),s=j2(r,n.props);return n.type!==u.Fragment&&(s.ref=t?$2(t,i):i),u.cloneElement(n,s)}return u.Children.count(n)>1?u.Children.only(null):null});su.displayName="SlotClone";var bc=({children:e})=>x.jsx(x.Fragment,{children:e});function F2(e){return u.isValidElement(e)&&e.type===bc}function j2(e,t){const n=m({},t);for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...l)=>{i(...l),o(...l)}:o&&(n[r]=o):r==="style"?n[r]=m(m({},o),i):r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return m(m({},e),n)}function V2(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var z2=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],cr=z2.reduce((e,t)=>{const n=u.forwardRef((r,o)=>{const a=r,{asChild:i}=a,s=N(a,["asChild"]),l=i?Oe:t;return typeof window!="undefined"&&(window[Symbol.for("radix-ui")]=!0),x.jsx(l,R(m({},s),{ref:o}))});return n.displayName=`Primitive.${t}`,R(m({},e),{[t]:n})},{});function U2(e,t){e&&hn.flushSync(()=>e.dispatchEvent(t))}function fe(e){const t=u.useRef(e);return u.useEffect(()=>{t.current=e}),u.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function Mc(e,t=globalThis==null?void 0:globalThis.document){const n=fe(e);u.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var B2="DismissableLayer",lu="dismissableLayer.update",W2="dismissableLayer.pointerDownOutside",H2="dismissableLayer.focusOutside",Wf,dm=u.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),fm=u.forwardRef((e,t)=>{var M;const b=e,{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:s,onDismiss:l}=b,a=N(b,["disableOutsidePointerEvents","onEscapeKeyDown","onPointerDownOutside","onFocusOutside","onInteractOutside","onDismiss"]),c=u.useContext(dm),[d,h]=u.useState(null),p=(M=d==null?void 0:d.ownerDocument)!=null?M:globalThis==null?void 0:globalThis.document,[,S]=u.useState({}),C=to(t,A=>h(A)),g=Array.from(c.layers),[w]=[...c.layersWithOutsidePointerEventsDisabled].slice(-1),v=g.indexOf(w),f=d?g.indexOf(d):-1,y=c.layersWithOutsidePointerEventsDisabled.size>0,E=f>=v,P=G2(A=>{const I=A.target,B=[...c.branches].some(j=>j.contains(I));!E||B||(o==null||o(A),s==null||s(A),A.defaultPrevented||l==null||l())},p),T=Q2(A=>{const I=A.target;[...c.branches].some(j=>j.contains(I))||(i==null||i(A),s==null||s(A),A.defaultPrevented||l==null||l())},p);return Mc(A=>{f===c.layers.size-1&&(r==null||r(A),!A.defaultPrevented&&l&&(A.preventDefault(),l()))},p),u.useEffect(()=>{if(d)return n&&(c.layersWithOutsidePointerEventsDisabled.size===0&&(Wf=p.body.style.pointerEvents,p.body.style.pointerEvents="none"),c.layersWithOutsidePointerEventsDisabled.add(d)),c.layers.add(d),Hf(),()=>{n&&c.layersWithOutsidePointerEventsDisabled.size===1&&(p.body.style.pointerEvents=Wf)}},[d,p,n,c]),u.useEffect(()=>()=>{d&&(c.layers.delete(d),c.layersWithOutsidePointerEventsDisabled.delete(d),Hf())},[d,c]),u.useEffect(()=>{const A=()=>S({});return document.addEventListener(lu,A),()=>document.removeEventListener(lu,A)},[]),x.jsx(cr.div,R(m({},a),{ref:C,style:m({pointerEvents:y?E?"auto":"none":void 0},e.style),onFocusCapture:on(e.onFocusCapture,T.onFocusCapture),onBlurCapture:on(e.onBlurCapture,T.onBlurCapture),onPointerDownCapture:on(e.onPointerDownCapture,P.onPointerDownCapture)}))});fm.displayName=B2;var K2="DismissableLayerBranch",Y2=u.forwardRef((e,t)=>{const n=u.useContext(dm),r=u.useRef(null),o=to(t,r);return u.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),x.jsx(cr.div,R(m({},e),{ref:o}))});Y2.displayName=K2;function G2(e,t=globalThis==null?void 0:globalThis.document){const n=fe(e),r=u.useRef(!1),o=u.useRef(()=>{});return u.useEffect(()=>{const i=l=>{if(l.target&&!r.current){let a=function(){pm(W2,n,c,{discrete:!0})};const c={originalEvent:l};l.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=a,t.addEventListener("click",o.current,{once:!0})):a()}else t.removeEventListener("click",o.current);r.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Q2(e,t=globalThis==null?void 0:globalThis.document){const n=fe(e),r=u.useRef(!1);return u.useEffect(()=>{const o=i=>{i.target&&!r.current&&pm(H2,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Hf(){const e=new CustomEvent(lu);document.dispatchEvent(e)}function pm(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?U2(o,i):o.dispatchEvent(i)}var xe=globalThis!=null&&globalThis.document?u.useLayoutEffect:()=>{},q2=ch.useId||(()=>{}),X2=0;function jt(e){const[t,n]=u.useState(q2());return xe(()=>{n(r=>r!=null?r:String(X2++))},[e]),t?`radix-${t}`:""}var Yi=typeof document!="undefined"?u.useLayoutEffect:u.useEffect;function Ss(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!Ss(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const i=o[r];if(!(i==="_owner"&&e.$$typeof)&&!Ss(e[i],t[i]))return!1}return!0}return e!==e&&t!==t}function hm(e){return typeof window=="undefined"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Kf(e,t){const n=hm(e);return Math.round(t*n)/n}function Jl(e){const t=u.useRef(e);return Yi(()=>{t.current=e}),t}function vm(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:s}={},transform:l=!0,whileElementsMounted:a,open:c}=e,[d,h]=u.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,S]=u.useState(r);Ss(p,r)||S(r);const[C,g]=u.useState(null),[w,v]=u.useState(null),f=u.useCallback(Y=>{Y!==T.current&&(T.current=Y,g(Y))},[]),y=u.useCallback(Y=>{Y!==b.current&&(b.current=Y,v(Y))},[]),E=i||C,P=s||w,T=u.useRef(null),b=u.useRef(null),M=u.useRef(d),A=a!=null,I=Jl(a),B=Jl(o),j=Jl(c),W=u.useCallback(()=>{if(!T.current||!b.current)return;const Y={placement:t,strategy:n,middleware:p};B.current&&(Y.platform=B.current),Fg(T.current,b.current,Y).then(he=>{const Re=R(m({},he),{isPositioned:j.current!==!1});Z.current&&!Ss(M.current,Re)&&(M.current=Re,hn.flushSync(()=>{h(Re)}))})},[p,t,n,B,j]);Yi(()=>{c===!1&&M.current.isPositioned&&(M.current.isPositioned=!1,h(Y=>R(m({},Y),{isPositioned:!1})))},[c]);const Z=u.useRef(!1);Yi(()=>(Z.current=!0,()=>{Z.current=!1}),[]),Yi(()=>{if(E&&(T.current=E),P&&(b.current=P),E&&P){if(I.current)return I.current(E,P,W);W()}},[E,P,W,I,A]);const ee=u.useMemo(()=>({reference:T,floating:b,setReference:f,setFloating:y}),[f,y]),Q=u.useMemo(()=>({reference:E,floating:P}),[E,P]),q=u.useMemo(()=>{const Y={position:n,left:0,top:0};if(!Q.floating)return Y;const he=Kf(Q.floating,d.x),Re=Kf(Q.floating,d.y);return l?m(R(m({},Y),{transform:"translate("+he+"px, "+Re+"px)"}),hm(Q.floating)>=1.5&&{willChange:"transform"}):{position:n,left:he,top:Re}},[n,l,Q.floating,d.x,d.y]);return u.useMemo(()=>R(m({},d),{update:W,refs:ee,elements:Q,floatingStyles:q}),[d,W,ee,Q,q])}const Z2=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?Td({element:r.current,padding:o}).fn(n):{}:r?Td({element:r,padding:o}).fn(n):{}}}},mm=(e,t)=>R(m({},jg(e)),{options:[e,t]}),ym=(e,t)=>R(m({},Vg(e)),{options:[e,t]}),gm=(e,t)=>R(m({},Wg(e)),{options:[e,t]}),wm=(e,t)=>R(m({},zg(e)),{options:[e,t]}),Sm=(e,t)=>R(m({},Ug(e)),{options:[e,t]}),Cm=(e,t)=>R(m({},Bg(e)),{options:[e,t]}),xm=(e,t)=>R(m({},Z2(e)),{options:[e,t]});var J2="Arrow",Em=u.forwardRef((e,t)=>{const s=e,{children:n,width:r=10,height:o=5}=s,i=N(s,["children","width","height"]);return x.jsx(cr.svg,R(m({},i),{ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:x.jsx("polygon",{points:"0,0 30,0 15,10"})}))});Em.displayName=J2;var eC=Em;function Qs(e){const[t,n]=u.useState(void 0);return xe(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const i=o[0];let s,l;if("borderBoxSize"in i){const a=i.borderBoxSize,c=Array.isArray(a)?a[0]:a;s=c.inlineSize,l=c.blockSize}else s=e.offsetWidth,l=e.offsetHeight;n({width:s,height:l})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var Dc="Popper",[km,Pm]=Ye(Dc),[tC,Rm]=km(Dc),_m=e=>{const{__scopePopper:t,children:n}=e,[r,o]=u.useState(null);return x.jsx(tC,{scope:t,anchor:r,onAnchorChange:o,children:n})};_m.displayName=Dc;var Tm="PopperAnchor",Nm=u.forwardRef((e,t)=>{const a=e,{__scopePopper:n,virtualRef:r}=a,o=N(a,["__scopePopper","virtualRef"]),i=Rm(Tm,n),s=u.useRef(null),l=to(t,s);return u.useEffect(()=>{i.onAnchorChange((r==null?void 0:r.current)||s.current)}),r?null:x.jsx(cr.div,R(m({},o),{ref:l}))});Nm.displayName=Tm;var Oc="PopperContent",[nC,rC]=km(Oc),Am=u.forwardRef((e,t)=>{var G,ie,Se,ne,te,re,Ae,Ie;const Ze=e,{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:s=0,arrowPadding:l=0,avoidCollisions:a=!0,collisionBoundary:c=[],collisionPadding:d=0,sticky:h="partial",hideWhenDetached:p=!1,updatePositionStrategy:S="optimized",onPlaced:C}=Ze,g=N(Ze,["__scopePopper","side","sideOffset","align","alignOffset","arrowPadding","avoidCollisions","collisionBoundary","collisionPadding","sticky","hideWhenDetached","updatePositionStrategy","onPlaced"]),w=Rm(Oc,n),[v,f]=u.useState(null),y=to(t,Le=>f(Le)),[E,P]=u.useState(null),T=Qs(E),b=(G=T==null?void 0:T.width)!=null?G:0,M=(ie=T==null?void 0:T.height)!=null?ie:0,A=r+(i!=="center"?"-"+i:""),I=typeof d=="number"?d:m({top:0,right:0,bottom:0,left:0},d),B=Array.isArray(c)?c:[c],j=B.length>0,W={padding:I,boundary:B.filter(iC),altBoundary:j},{refs:Z,floatingStyles:ee,placement:Q,isPositioned:q,middlewareData:Y}=vm({strategy:"fixed",placement:A,whileElementsMounted:(...Le)=>Wp(...Le,{animationFrame:S==="always"}),elements:{reference:w.anchor},middleware:[mm({mainAxis:o+M,alignmentAxis:s}),a&&ym(m({mainAxis:!0,crossAxis:!1,limiter:h==="partial"?gm():void 0},W)),a&&wm(m({},W)),Sm(R(m({},W),{apply:({elements:Le,rects:wt,availableWidth:en,availableHeight:lo})=>{const{width:ci,height:fr}=wt.reference,k=Le.floating.style;k.setProperty("--radix-popper-available-width",`${en}px`),k.setProperty("--radix-popper-available-height",`${lo}px`),k.setProperty("--radix-popper-anchor-width",`${ci}px`),k.setProperty("--radix-popper-anchor-height",`${fr}px`)}})),E&&xm({element:E,padding:l}),sC({arrowWidth:b,arrowHeight:M}),p&&Cm(m({strategy:"referenceHidden"},W))]}),[he,Re]=Dm(Q),Ve=fe(C);xe(()=>{q&&(Ve==null||Ve())},[q,Ve]);const Nt=(Se=Y.arrow)==null?void 0:Se.x,Bt=(ne=Y.arrow)==null?void 0:ne.y,Zt=((te=Y.arrow)==null?void 0:te.centerOffset)!==0,[it,Jt]=u.useState();return xe(()=>{v&&Jt(window.getComputedStyle(v).zIndex)},[v]),x.jsx("div",{ref:Z.setFloating,"data-radix-popper-content-wrapper":"",style:m(R(m({},ee),{transform:q?ee.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:it,"--radix-popper-transform-origin":[(re=Y.transformOrigin)==null?void 0:re.x,(Ae=Y.transformOrigin)==null?void 0:Ae.y].join(" ")}),((Ie=Y.hide)==null?void 0:Ie.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}),dir:e.dir,children:x.jsx(nC,{scope:n,placedSide:he,onArrowChange:P,arrowX:Nt,arrowY:Bt,shouldHideArrow:Zt,children:x.jsx(cr.div,R(m({"data-side":he,"data-align":Re},g),{ref:y,style:R(m({},g.style),{animation:q?void 0:"none"})}))})})});Am.displayName=Oc;var bm="PopperArrow",oC={top:"bottom",right:"left",bottom:"top",left:"right"},Mm=u.forwardRef(function(t,n){const l=t,{__scopePopper:r}=l,o=N(l,["__scopePopper"]),i=rC(bm,r),s=oC[i.placedSide];return x.jsx("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[s]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:x.jsx(eC,R(m({},o),{ref:n,style:R(m({},o.style),{display:"block"})}))})});Mm.displayName=bm;function iC(e){return e!==null}var sC=e=>({name:"transformOrigin",options:e,fn(t){var w,v,f,y,E;const{placement:n,rects:r,middlewareData:o}=t,s=((w=o.arrow)==null?void 0:w.centerOffset)!==0,l=s?0:e.arrowWidth,a=s?0:e.arrowHeight,[c,d]=Dm(n),h={start:"0%",center:"50%",end:"100%"}[d],p=((f=(v=o.arrow)==null?void 0:v.x)!=null?f:0)+l/2,S=((E=(y=o.arrow)==null?void 0:y.y)!=null?E:0)+a/2;let C="",g="";return c==="bottom"?(C=s?h:`${p}px`,g=`${-a}px`):c==="top"?(C=s?h:`${p}px`,g=`${r.floating.height+a}px`):c==="right"?(C=`${-a}px`,g=s?h:`${S}px`):c==="left"&&(C=`${r.floating.width+a}px`,g=s?h:`${S}px`),{data:{x:C,y:g}}}});function Dm(e){const[t,n="center"]=e.split("-");return[t,n]}var lC=_m,aC=Nm,uC=Am,cC=Mm;function Yf(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function dC(...e){return t=>{let n=!1;const r=e.map(o=>{const i=Yf(o,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let o=0;o<r.length;o++){const i=r[o];typeof i=="function"?i():Yf(e[o],null)}}}}function fC(...e){return u.useCallback(dC(...e),e)}function pC(e,t){return u.useReducer((n,r)=>{const o=t[n][r];return o!=null?o:n},e)}var _t=e=>{const{present:t,children:n}=e,r=hC(t),o=typeof n=="function"?n({present:r.isPresent}):u.Children.only(n),i=fC(r.ref,vC(o));return typeof n=="function"||r.isPresent?u.cloneElement(o,{ref:i}):null};_t.displayName="Presence";function hC(e){const[t,n]=u.useState(),r=u.useRef({}),o=u.useRef(e),i=u.useRef("none"),s=e?"mounted":"unmounted",[l,a]=pC(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return u.useEffect(()=>{const c=Ai(r.current);i.current=l==="mounted"?c:"none"},[l]),xe(()=>{const c=r.current,d=o.current;if(d!==e){const p=i.current,S=Ai(c);e?a("MOUNT"):S==="none"||(c==null?void 0:c.display)==="none"?a("UNMOUNT"):a(d&&p!==S?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,a]),xe(()=>{var c;if(t){let d;const h=(c=t.ownerDocument.defaultView)!=null?c:window,p=C=>{const w=Ai(r.current).includes(C.animationName);if(C.target===t&&w&&(a("ANIMATION_END"),!o.current)){const v=t.style.animationFillMode;t.style.animationFillMode="forwards",d=h.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=v)})}},S=C=>{C.target===t&&(i.current=Ai(r.current))};return t.addEventListener("animationstart",S),t.addEventListener("animationcancel",p),t.addEventListener("animationend",p),()=>{h.clearTimeout(d),t.removeEventListener("animationstart",S),t.removeEventListener("animationcancel",p),t.removeEventListener("animationend",p)}}else a("ANIMATION_END")},[t,a]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:u.useCallback(c=>{c&&(r.current=getComputedStyle(c)),n(c)},[])}}function Ai(e){return(e==null?void 0:e.animationName)||"none"}function vC(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function zt({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=mC({defaultProp:t,onChange:n}),i=e!==void 0,s=i?e:r,l=fe(n),a=u.useCallback(c=>{if(i){const h=typeof c=="function"?c(e):c;h!==e&&l(h)}else o(c)},[i,e,o,l]);return[s,a]}function mC({defaultProp:e,onChange:t}){const n=u.useState(e),[r]=n,o=u.useRef(r),i=fe(t);return u.useEffect(()=>{o.current!==r&&(i(r),o.current=r)},[r,o,i]),n}var yC="VisuallyHidden",Om=u.forwardRef((e,t)=>x.jsx(cr.span,R(m({},e),{ref:t,style:m({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"},e.style)})));Om.displayName=yC;var gC=Om,[qs,J_]=Ye("Tooltip",[Pm]),Xs=Pm(),Im="TooltipProvider",wC=700,au="tooltip.open",[SC,Ic]=qs(Im),Lm=e=>{const{__scopeTooltip:t,delayDuration:n=wC,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:i}=e,[s,l]=u.useState(!0),a=u.useRef(!1),c=u.useRef(0);return u.useEffect(()=>{const d=c.current;return()=>window.clearTimeout(d)},[]),x.jsx(SC,{scope:t,isOpenDelayed:s,delayDuration:n,onOpen:u.useCallback(()=>{window.clearTimeout(c.current),l(!1)},[]),onClose:u.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>l(!0),r)},[r]),isPointerInTransitRef:a,onPointerInTransitChange:u.useCallback(d=>{a.current=d},[]),disableHoverableContent:o,children:i})};Lm.displayName=Im;var Zs="Tooltip",[CC,Js]=qs(Zs),$m=e=>{const{__scopeTooltip:t,children:n,open:r,defaultOpen:o=!1,onOpenChange:i,disableHoverableContent:s,delayDuration:l}=e,a=Ic(Zs,e.__scopeTooltip),c=Xs(t),[d,h]=u.useState(null),p=jt(),S=u.useRef(0),C=s!=null?s:a.disableHoverableContent,g=l!=null?l:a.delayDuration,w=u.useRef(!1),[v=!1,f]=zt({prop:r,defaultProp:o,onChange:b=>{b?(a.onOpen(),document.dispatchEvent(new CustomEvent(au))):a.onClose(),i==null||i(b)}}),y=u.useMemo(()=>v?w.current?"delayed-open":"instant-open":"closed",[v]),E=u.useCallback(()=>{window.clearTimeout(S.current),S.current=0,w.current=!1,f(!0)},[f]),P=u.useCallback(()=>{window.clearTimeout(S.current),S.current=0,f(!1)},[f]),T=u.useCallback(()=>{window.clearTimeout(S.current),S.current=window.setTimeout(()=>{w.current=!0,f(!0),S.current=0},g)},[g,f]);return u.useEffect(()=>()=>{S.current&&(window.clearTimeout(S.current),S.current=0)},[]),x.jsx(lC,R(m({},c),{children:x.jsx(CC,{scope:t,contentId:p,open:v,stateAttribute:y,trigger:d,onTriggerChange:h,onTriggerEnter:u.useCallback(()=>{a.isOpenDelayed?T():E()},[a.isOpenDelayed,T,E]),onTriggerLeave:u.useCallback(()=>{C?P():(window.clearTimeout(S.current),S.current=0)},[P,C]),onOpen:E,onClose:P,disableHoverableContent:C,children:n})}))};$m.displayName=Zs;var uu="TooltipTrigger",Fm=u.forwardRef((e,t)=>{const p=e,{__scopeTooltip:n}=p,r=N(p,["__scopeTooltip"]),o=Js(uu,n),i=Ic(uu,n),s=Xs(n),l=u.useRef(null),a=to(t,l,o.onTriggerChange),c=u.useRef(!1),d=u.useRef(!1),h=u.useCallback(()=>c.current=!1,[]);return u.useEffect(()=>()=>document.removeEventListener("pointerup",h),[h]),x.jsx(aC,R(m({asChild:!0},s),{children:x.jsx(cr.button,R(m({"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute},r),{ref:a,onPointerMove:on(e.onPointerMove,S=>{S.pointerType!=="touch"&&!d.current&&!i.isPointerInTransitRef.current&&(o.onTriggerEnter(),d.current=!0)}),onPointerLeave:on(e.onPointerLeave,()=>{o.onTriggerLeave(),d.current=!1}),onPointerDown:on(e.onPointerDown,()=>{c.current=!0,document.addEventListener("pointerup",h,{once:!0})}),onFocus:on(e.onFocus,()=>{c.current||o.onOpen()}),onBlur:on(e.onBlur,o.onClose),onClick:on(e.onClick,o.onClose)}))}))});Fm.displayName=uu;var xC="TooltipPortal",[eT,EC]=qs(xC,{forceMount:void 0}),qr="TooltipContent",jm=u.forwardRef((e,t)=>{const n=EC(qr,e.__scopeTooltip),l=e,{forceMount:r=n.forceMount,side:o="top"}=l,i=N(l,["forceMount","side"]),s=Js(qr,e.__scopeTooltip);return x.jsx(_t,{present:r||s.open,children:s.disableHoverableContent?x.jsx(Vm,R(m({side:o},i),{ref:t})):x.jsx(kC,R(m({side:o},i),{ref:t}))})}),kC=u.forwardRef((e,t)=>{const n=Js(qr,e.__scopeTooltip),r=Ic(qr,e.__scopeTooltip),o=u.useRef(null),i=to(t,o),[s,l]=u.useState(null),{trigger:a,onClose:c}=n,d=o.current,{onPointerInTransitChange:h}=r,p=u.useCallback(()=>{l(null),h(!1)},[h]),S=u.useCallback((C,g)=>{const w=C.currentTarget,v={x:C.clientX,y:C.clientY},f=TC(v,w.getBoundingClientRect()),y=NC(v,f),E=AC(g.getBoundingClientRect()),P=MC([...y,...E]);l(P),h(!0)},[h]);return u.useEffect(()=>()=>p(),[p]),u.useEffect(()=>{if(a&&d){const C=w=>S(w,d),g=w=>S(w,a);return a.addEventListener("pointerleave",C),d.addEventListener("pointerleave",g),()=>{a.removeEventListener("pointerleave",C),d.removeEventListener("pointerleave",g)}}},[a,d,S,p]),u.useEffect(()=>{if(s){const C=g=>{const w=g.target,v={x:g.clientX,y:g.clientY},f=(a==null?void 0:a.contains(w))||(d==null?void 0:d.contains(w)),y=!bC(v,s);f?p():y&&(p(),c())};return document.addEventListener("pointermove",C),()=>document.removeEventListener("pointermove",C)}},[a,d,s,c,p]),x.jsx(Vm,R(m({},e),{ref:i}))}),[PC,RC]=qs(Zs,{isInside:!1}),Vm=u.forwardRef((e,t)=>{const h=e,{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:i,onPointerDownOutside:s}=h,l=N(h,["__scopeTooltip","children","aria-label","onEscapeKeyDown","onPointerDownOutside"]),a=Js(qr,n),c=Xs(n),{onClose:d}=a;return u.useEffect(()=>(document.addEventListener(au,d),()=>document.removeEventListener(au,d)),[d]),u.useEffect(()=>{if(a.trigger){const p=S=>{const C=S.target;C!=null&&C.contains(a.trigger)&&d()};return window.addEventListener("scroll",p,{capture:!0}),()=>window.removeEventListener("scroll",p,{capture:!0})}},[a.trigger,d]),x.jsx(fm,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:p=>p.preventDefault(),onDismiss:d,children:x.jsxs(uC,R(m(m({"data-state":a.stateAttribute},c),l),{ref:t,style:R(m({},l.style),{"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"}),children:[x.jsx(bc,{children:r}),x.jsx(PC,{scope:n,isInside:!0,children:x.jsx(gC,{id:a.contentId,role:"tooltip",children:o||r})})]}))})});jm.displayName=qr;var zm="TooltipArrow",_C=u.forwardRef((e,t)=>{const s=e,{__scopeTooltip:n}=s,r=N(s,["__scopeTooltip"]),o=Xs(n);return RC(zm,n).isInside?null:x.jsx(cC,R(m(m({},o),r),{ref:t}))});_C.displayName=zm;function TC(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function NC(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function AC(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function bC(e,t){const{x:n,y:r}=e;let o=!1;for(let i=0,s=t.length-1;i<t.length;s=i++){const l=t[i].x,a=t[i].y,c=t[s].x,d=t[s].y;a>r!=d>r&&n<(c-l)*(r-a)/(d-a)+l&&(o=!o)}return o}function MC(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),DC(t)}function DC(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const i=t[t.length-1],s=t[t.length-2];if((i.x-s.x)*(o.y-s.y)>=(i.y-s.y)*(o.x-s.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const i=n[n.length-1],s=n[n.length-2];if((i.x-s.x)*(o.y-s.y)>=(i.y-s.y)*(o.x-s.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var tT=Lm,nT=$m,rT=Fm,oT=jm,Um=u.createContext(void 0),Bm=e=>{const t=u.useContext(Um);if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},iT=({client:e,children:t})=>(u.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),x.jsx(Um.Provider,{value:e,children:t})),Wm=u.createContext(!1),OC=()=>u.useContext(Wm);Wm.Provider;function IC(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var LC=u.createContext(IC()),$C=()=>u.useContext(LC);function Hm(e,t){return typeof e=="function"?e(...t):!!e}function cu(){}var FC=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},jC=e=>{u.useEffect(()=>{e.clearReset()},[e])},VC=({result:e,errorResetBoundary:t,throwOnError:n,query:r,suspense:o})=>e.isError&&!t.isReset()&&!e.isFetching&&r&&(o&&e.data===void 0||Hm(n,[e.error,r])),zC=e=>{const t=e.staleTime;e.suspense&&(e.staleTime=typeof t=="function"?(...n)=>Math.max(t(...n),1e3):Math.max(t!=null?t:1e3,1e3),typeof e.gcTime=="number"&&(e.gcTime=Math.max(e.gcTime,1e3)))},UC=(e,t)=>e.isLoading&&e.isFetching&&!t,BC=(e,t)=>(e==null?void 0:e.suspense)&&t.isPending,Gf=(e,t,n)=>t.fetchOptimistic(e).catch(()=>{n.clearReset()});function WC(e,t,n){var h,p,S,C,g;const r=Bm(),o=OC(),i=$C(),s=r.defaultQueryOptions(e);(p=(h=r.getDefaultOptions().queries)==null?void 0:h._experimental_beforeQuery)==null||p.call(h,s),s._optimisticResults=o?"isRestoring":"optimistic",zC(s),FC(s,i),jC(i);const l=!r.getQueryCache().get(s.queryHash),[a]=u.useState(()=>new t(r,s)),c=a.getOptimisticResult(s),d=!o&&e.subscribed!==!1;if(u.useSyncExternalStore(u.useCallback(w=>{const v=d?a.subscribe(Hp.batchCalls(w)):cu;return a.updateResult(),v},[a,d]),()=>a.getCurrentResult(),()=>a.getCurrentResult()),u.useEffect(()=>{a.setOptions(s)},[s,a]),BC(s,c))throw Gf(s,a,i);if(VC({result:c,errorResetBoundary:i,throwOnError:s.throwOnError,query:r.getQueryCache().get(s.queryHash),suspense:s.suspense}))throw c.error;if((C=(S=r.getDefaultOptions().queries)==null?void 0:S._experimental_afterQuery)==null||C.call(S,s,c),s.experimental_prefetchInRender&&!Hg&&UC(c,o)){const w=l?Gf(s,a,i):(g=r.getQueryCache().get(s.queryHash))==null?void 0:g.promise;w==null||w.catch(cu).finally(()=>{a.updateResult()})}return s.notifyOnChangeProps?c:a.trackResult(c)}function sT(e,t){return WC(e,Kg)}function lT(e,t){const n=Bm(),[r]=u.useState(()=>new Yg(n,e));u.useEffect(()=>{r.setOptions(e)},[r,e]);const o=u.useSyncExternalStore(u.useCallback(s=>r.subscribe(Hp.batchCalls(s)),[r]),()=>r.getCurrentResult(),()=>r.getCurrentResult()),i=u.useCallback((s,l)=>{r.mutate(s,l).catch(cu)},[r]);if(o.error&&Hm(r.options.throwOnError,[o.error]))throw o.error;return R(m({},o),{mutate:i,mutateAsync:o.mutate})}/**
 * React Router v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ti(){return ti=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ti.apply(this,arguments)}const el=u.createContext(null),Km=u.createContext(null),vn=u.createContext(null),tl=u.createContext(null),mn=u.createContext({outlet:null,matches:[],isDataRoute:!1}),Ym=u.createContext(null);function HC(e,t){let{relative:n}=t===void 0?{}:t;no()||We(!1);let{basename:r,navigator:o}=u.useContext(vn),{hash:i,pathname:s,search:l}=nl(e,{relative:n}),a=s;return r!=="/"&&(a=s==="/"?r:Qi([r,s])),o.createHref({pathname:a,search:l,hash:i})}function no(){return u.useContext(tl)!=null}function dr(){return no()||We(!1),u.useContext(tl).location}function Gm(e){u.useContext(vn).static||u.useLayoutEffect(e)}function Lc(){let{isDataRoute:e}=u.useContext(mn);return e?ox():KC()}function KC(){no()||We(!1);let e=u.useContext(el),{basename:t,future:n,navigator:r}=u.useContext(vn),{matches:o}=u.useContext(mn),{pathname:i}=dr(),s=JSON.stringify(Ou(o,n.v7_relativeSplatPath)),l=u.useRef(!1);return Gm(()=>{l.current=!0}),u.useCallback(function(c,d){if(d===void 0&&(d={}),!l.current)return;if(typeof c=="number"){r.go(c);return}let h=Iu(c,JSON.parse(s),i,d.relative==="path");e==null&&t!=="/"&&(h.pathname=h.pathname==="/"?t:Qi([t,h.pathname])),(d.replace?r.replace:r.push)(h,d.state,d)},[t,r,s,i,e])}function aT(){let{matches:e}=u.useContext(mn),t=e[e.length-1];return t?t.params:{}}function nl(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=u.useContext(vn),{matches:o}=u.useContext(mn),{pathname:i}=dr(),s=JSON.stringify(Ou(o,r.v7_relativeSplatPath));return u.useMemo(()=>Iu(e,JSON.parse(s),i,n==="path"),[e,s,i,n])}function YC(e,t){return GC(e,t)}function GC(e,t,n,r){no()||We(!1);let{navigator:o}=u.useContext(vn),{matches:i}=u.useContext(mn),s=i[i.length-1],l=s?s.params:{};s&&s.pathname;let a=s?s.pathnameBase:"/";s&&s.route;let c=dr(),d;if(t){var h;let w=typeof t=="string"?Kp(t):t;a==="/"||(h=w.pathname)!=null&&h.startsWith(a)||We(!1),d=w}else d=c;let p=d.pathname||"/",S=p;if(a!=="/"){let w=a.replace(/^\//,"").split("/");S="/"+p.replace(/^\//,"").split("/").slice(w.length).join("/")}let C=Gg(e,{pathname:S}),g=JC(C&&C.map(w=>Object.assign({},w,{params:Object.assign({},l,w.params),pathname:Qi([a,o.encodeLocation?o.encodeLocation(w.pathname).pathname:w.pathname]),pathnameBase:w.pathnameBase==="/"?a:Qi([a,o.encodeLocation?o.encodeLocation(w.pathnameBase).pathname:w.pathnameBase])})),i,n,r);return t&&g?u.createElement(tl.Provider,{value:{location:ti({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:Yp.Pop}},g):g}function QC(){let e=rx(),t=Qg(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return u.createElement(u.Fragment,null,u.createElement("h2",null,"Unexpected Application Error!"),u.createElement("h3",{style:{fontStyle:"italic"}},t),n?u.createElement("pre",{style:o},n):null,null)}const qC=u.createElement(QC,null);class XC extends u.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?u.createElement(mn.Provider,{value:this.props.routeContext},u.createElement(Ym.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function ZC(e){let{routeContext:t,match:n,children:r}=e,o=u.useContext(el);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),u.createElement(mn.Provider,{value:t},r)}function JC(e,t,n,r){var o;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var i;if(!n)return null;if(n.errors)e=n.matches;else if((i=r)!=null&&i.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let s=e,l=(o=n)==null?void 0:o.errors;if(l!=null){let d=s.findIndex(h=>h.route.id&&(l==null?void 0:l[h.route.id])!==void 0);d>=0||We(!1),s=s.slice(0,Math.min(s.length,d+1))}let a=!1,c=-1;if(n&&r&&r.v7_partialHydration)for(let d=0;d<s.length;d++){let h=s[d];if((h.route.HydrateFallback||h.route.hydrateFallbackElement)&&(c=d),h.route.id){let{loaderData:p,errors:S}=n,C=h.route.loader&&p[h.route.id]===void 0&&(!S||S[h.route.id]===void 0);if(h.route.lazy||C){a=!0,c>=0?s=s.slice(0,c+1):s=[s[0]];break}}}return s.reduceRight((d,h,p)=>{let S,C=!1,g=null,w=null;n&&(S=l&&h.route.id?l[h.route.id]:void 0,g=h.route.errorElement||qC,a&&(c<0&&p===0?(C=!0,w=null):c===p&&(C=!0,w=h.route.hydrateFallbackElement||null)));let v=t.concat(s.slice(0,p+1)),f=()=>{let y;return S?y=g:C?y=w:h.route.Component?y=u.createElement(h.route.Component,null):h.route.element?y=h.route.element:y=d,u.createElement(ZC,{match:h,routeContext:{outlet:d,matches:v,isDataRoute:n!=null},children:y})};return n&&(h.route.ErrorBoundary||h.route.errorElement||p===0)?u.createElement(XC,{location:n.location,revalidation:n.revalidation,component:g,error:S,children:f(),routeContext:{outlet:null,matches:v,isDataRoute:!0}}):f()},null)}var Qm=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Qm||{}),Cs=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Cs||{});function ex(e){let t=u.useContext(el);return t||We(!1),t}function tx(e){let t=u.useContext(Km);return t||We(!1),t}function nx(e){let t=u.useContext(mn);return t||We(!1),t}function qm(e){let t=nx(),n=t.matches[t.matches.length-1];return n.route.id||We(!1),n.route.id}function rx(){var e;let t=u.useContext(Ym),n=tx(Cs.UseRouteError),r=qm(Cs.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function ox(){let{router:e}=ex(Qm.UseNavigateStable),t=qm(Cs.UseNavigateStable),n=u.useRef(!1);return Gm(()=>{n.current=!0}),u.useCallback(function(o,i){i===void 0&&(i={}),n.current&&(typeof o=="number"?e.navigate(o):e.navigate(o,ti({fromRouteId:t},i)))},[e,t])}function uT(e){let{to:t,replace:n,state:r,relative:o}=e;no()||We(!1);let{future:i,static:s}=u.useContext(vn),{matches:l}=u.useContext(mn),{pathname:a}=dr(),c=Lc(),d=Iu(t,Ou(l,i.v7_relativeSplatPath),a,o==="path"),h=JSON.stringify(d);return u.useEffect(()=>c(JSON.parse(h),{replace:n,state:r,relative:o}),[c,h,o,n,r]),null}function ix(e){We(!1)}function sx(e){let{basename:t="/",children:n=null,location:r,navigationType:o=Yp.Pop,navigator:i,static:s=!1,future:l}=e;no()&&We(!1);let a=t.replace(/^\/*/,"/"),c=u.useMemo(()=>({basename:a,navigator:i,static:s,future:ti({v7_relativeSplatPath:!1},l)}),[a,l,i,s]);typeof r=="string"&&(r=Kp(r));let{pathname:d="/",search:h="",hash:p="",state:S=null,key:C="default"}=r,g=u.useMemo(()=>{let w=$o(d,a);return w==null?null:{location:{pathname:w,search:h,hash:p,state:S,key:C},navigationType:o}},[a,d,h,p,S,C,o]);return g==null?null:u.createElement(vn.Provider,{value:c},u.createElement(tl.Provider,{children:n,value:g}))}function cT(e){let{children:t,location:n}=e;return YC(du(t),n)}new Promise(()=>{});function du(e,t){t===void 0&&(t=[]);let n=[];return u.Children.forEach(e,(r,o)=>{if(!u.isValidElement(r))return;let i=[...t,o];if(r.type===u.Fragment){n.push.apply(n,du(r.props.children,i));return}r.type!==ix&&We(!1),!r.props.index||!r.props.children||We(!1);let s={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(s.children=du(r.props.children,i)),n.push(s)}),n}/**
 * React Router DOM v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function xs(){return xs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},xs.apply(this,arguments)}function Xm(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,i;for(i=0;i<r.length;i++)o=r[i],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function lx(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function ax(e,t){return e.button===0&&(!t||t==="_self")&&!lx(e)}function fu(e){return e===void 0&&(e=""),new URLSearchParams(typeof e=="string"||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((t,n)=>{let r=e[n];return t.concat(Array.isArray(r)?r.map(o=>[n,o]):[[n,r]])},[]))}function ux(e,t){let n=fu(e);return t&&t.forEach((r,o)=>{n.has(o)||t.getAll(o).forEach(i=>{n.append(o,i)})}),n}const cx=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],dx=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],fx="6";try{window.__reactRouterVersion=fx}catch(e){}const px=u.createContext({isTransitioning:!1}),hx="startTransition",Qf=ch[hx];function dT(e){let{basename:t,children:n,future:r,window:o}=e,i=u.useRef();i.current==null&&(i.current=qg({window:o,v5Compat:!0}));let s=i.current,[l,a]=u.useState({action:s.action,location:s.location}),{v7_startTransition:c}=r||{},d=u.useCallback(h=>{c&&Qf?Qf(()=>a(h)):a(h)},[a,c]);return u.useLayoutEffect(()=>s.listen(d),[s,d]),u.createElement(sx,{basename:t,children:n,location:l.location,navigationType:l.action,navigator:s,future:r})}const vx=typeof window!="undefined"&&typeof window.document!="undefined"&&typeof window.document.createElement!="undefined",mx=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,yx=u.forwardRef(function(t,n){let{onClick:r,relative:o,reloadDocument:i,replace:s,state:l,target:a,to:c,preventScrollReset:d,viewTransition:h}=t,p=Xm(t,cx),{basename:S}=u.useContext(vn),C,g=!1;if(typeof c=="string"&&mx.test(c)&&(C=c,vx))try{let y=new URL(window.location.href),E=c.startsWith("//")?new URL(y.protocol+c):new URL(c),P=$o(E.pathname,S);E.origin===y.origin&&P!=null?c=P+E.search+E.hash:g=!0}catch(y){}let w=HC(c,{relative:o}),v=wx(c,{replace:s,state:l,target:a,preventScrollReset:d,relative:o,viewTransition:h});function f(y){r&&r(y),y.defaultPrevented||v(y)}return u.createElement("a",xs({},p,{href:C||w,onClick:g||i?r:f,ref:n,target:a}))}),fT=u.forwardRef(function(t,n){let{"aria-current":r="page",caseSensitive:o=!1,className:i="",end:s=!1,style:l,to:a,viewTransition:c,children:d}=t,h=Xm(t,dx),p=nl(a,{relative:h.relative}),S=dr(),C=u.useContext(Km),{navigator:g,basename:w}=u.useContext(vn),v=C!=null&&Sx(p)&&c===!0,f=g.encodeLocation?g.encodeLocation(p).pathname:p.pathname,y=S.pathname,E=C&&C.navigation&&C.navigation.location?C.navigation.location.pathname:null;o||(y=y.toLowerCase(),E=E?E.toLowerCase():null,f=f.toLowerCase()),E&&w&&(E=$o(E,w)||E);const P=f!=="/"&&f.endsWith("/")?f.length-1:f.length;let T=y===f||!s&&y.startsWith(f)&&y.charAt(P)==="/",b=E!=null&&(E===f||!s&&E.startsWith(f)&&E.charAt(f.length)==="/"),M={isActive:T,isPending:b,isTransitioning:v},A=T?r:void 0,I;typeof i=="function"?I=i(M):I=[i,T?"active":null,b?"pending":null,v?"transitioning":null].filter(Boolean).join(" ");let B=typeof l=="function"?l(M):l;return u.createElement(yx,xs({},h,{"aria-current":A,className:I,ref:n,style:B,to:a,viewTransition:c}),typeof d=="function"?d(M):d)});var pu;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(pu||(pu={}));var qf;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(qf||(qf={}));function gx(e){let t=u.useContext(el);return t||We(!1),t}function wx(e,t){let{target:n,replace:r,state:o,preventScrollReset:i,relative:s,viewTransition:l}=t===void 0?{}:t,a=Lc(),c=dr(),d=nl(e,{relative:s});return u.useCallback(h=>{if(ax(h,n)){h.preventDefault();let p=r!==void 0?r:Nd(c)===Nd(d);a(e,{replace:p,state:o,preventScrollReset:i,relative:s,viewTransition:l})}},[c,a,d,r,o,n,e,i,s,l])}function pT(e){let t=u.useRef(fu(e)),n=u.useRef(!1),r=dr(),o=u.useMemo(()=>ux(r.search,n.current?null:t.current),[r.search]),i=Lc(),s=u.useCallback((l,a)=>{const c=fu(typeof l=="function"?l(o):l);n.current=!0,i("?"+c,a)},[i,o]);return[o,s]}function Sx(e,t){t===void 0&&(t={});let n=u.useContext(px);n==null&&We(!1);let{basename:r}=gx(pu.useViewTransitionState),o=nl(e,{relative:t.relative});if(!n.isTransitioning)return!1;let i=$o(n.currentLocation.pathname,r)||n.currentLocation.pathname,s=$o(n.nextLocation.pathname,r)||n.nextLocation.pathname;return Ad(o.pathname,s)!=null||Ad(o.pathname,i)!=null}function Fn(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Xf(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function Cx(...e){return t=>{let n=!1;const r=e.map(o=>{const i=Xf(o,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let o=0;o<r.length;o++){const i=r[o];typeof i=="function"?i():Xf(e[o],null)}}}}function ro(...e){return u.useCallback(Cx(...e),e)}var xx=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],yn=xx.reduce((e,t)=>{const n=u.forwardRef((r,o)=>{const a=r,{asChild:i}=a,s=N(a,["asChild"]),l=i?Oe:t;return typeof window!="undefined"&&(window[Symbol.for("radix-ui")]=!0),x.jsx(l,R(m({},s),{ref:o}))});return n.displayName=`Primitive.${t}`,R(m({},e),{[t]:n})},{});function Ex(e,t){e&&hn.flushSync(()=>e.dispatchEvent(t))}var kx="DismissableLayer",hu="dismissableLayer.update",Px="dismissableLayer.pointerDownOutside",Rx="dismissableLayer.focusOutside",Zf,Zm=u.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Jm=u.forwardRef((e,t)=>{var M;const b=e,{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:s,onDismiss:l}=b,a=N(b,["disableOutsidePointerEvents","onEscapeKeyDown","onPointerDownOutside","onFocusOutside","onInteractOutside","onDismiss"]),c=u.useContext(Zm),[d,h]=u.useState(null),p=(M=d==null?void 0:d.ownerDocument)!=null?M:globalThis==null?void 0:globalThis.document,[,S]=u.useState({}),C=ro(t,A=>h(A)),g=Array.from(c.layers),[w]=[...c.layersWithOutsidePointerEventsDisabled].slice(-1),v=g.indexOf(w),f=d?g.indexOf(d):-1,y=c.layersWithOutsidePointerEventsDisabled.size>0,E=f>=v,P=Nx(A=>{const I=A.target,B=[...c.branches].some(j=>j.contains(I));!E||B||(o==null||o(A),s==null||s(A),A.defaultPrevented||l==null||l())},p),T=Ax(A=>{const I=A.target;[...c.branches].some(j=>j.contains(I))||(i==null||i(A),s==null||s(A),A.defaultPrevented||l==null||l())},p);return Mc(A=>{f===c.layers.size-1&&(r==null||r(A),!A.defaultPrevented&&l&&(A.preventDefault(),l()))},p),u.useEffect(()=>{if(d)return n&&(c.layersWithOutsidePointerEventsDisabled.size===0&&(Zf=p.body.style.pointerEvents,p.body.style.pointerEvents="none"),c.layersWithOutsidePointerEventsDisabled.add(d)),c.layers.add(d),Jf(),()=>{n&&c.layersWithOutsidePointerEventsDisabled.size===1&&(p.body.style.pointerEvents=Zf)}},[d,p,n,c]),u.useEffect(()=>()=>{d&&(c.layers.delete(d),c.layersWithOutsidePointerEventsDisabled.delete(d),Jf())},[d,c]),u.useEffect(()=>{const A=()=>S({});return document.addEventListener(hu,A),()=>document.removeEventListener(hu,A)},[]),x.jsx(yn.div,R(m({},a),{ref:C,style:m({pointerEvents:y?E?"auto":"none":void 0},e.style),onFocusCapture:Fn(e.onFocusCapture,T.onFocusCapture),onBlurCapture:Fn(e.onBlurCapture,T.onBlurCapture),onPointerDownCapture:Fn(e.onPointerDownCapture,P.onPointerDownCapture)}))});Jm.displayName=kx;var _x="DismissableLayerBranch",Tx=u.forwardRef((e,t)=>{const n=u.useContext(Zm),r=u.useRef(null),o=ro(t,r);return u.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),x.jsx(yn.div,R(m({},e),{ref:o}))});Tx.displayName=_x;function Nx(e,t=globalThis==null?void 0:globalThis.document){const n=fe(e),r=u.useRef(!1),o=u.useRef(()=>{});return u.useEffect(()=>{const i=l=>{if(l.target&&!r.current){let a=function(){ey(Px,n,c,{discrete:!0})};const c={originalEvent:l};l.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=a,t.addEventListener("click",o.current,{once:!0})):a()}else t.removeEventListener("click",o.current);r.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Ax(e,t=globalThis==null?void 0:globalThis.document){const n=fe(e),r=u.useRef(!1);return u.useEffect(()=>{const o=i=>{i.target&&!r.current&&ey(Rx,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Jf(){const e=new CustomEvent(hu);document.dispatchEvent(e)}function ey(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Ex(o,i):o.dispatchEvent(i)}var ea="focusScope.autoFocusOnMount",ta="focusScope.autoFocusOnUnmount",ep={bubbles:!1,cancelable:!0},bx="FocusScope",ty=u.forwardRef((e,t)=>{const g=e,{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i}=g,s=N(g,["loop","trapped","onMountAutoFocus","onUnmountAutoFocus"]),[l,a]=u.useState(null),c=fe(o),d=fe(i),h=u.useRef(null),p=ro(t,w=>a(w)),S=u.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;u.useEffect(()=>{if(r){let w=function(E){if(S.paused||!l)return;const P=E.target;l.contains(P)?h.current=P:Cn(h.current,{select:!0})},v=function(E){if(S.paused||!l)return;const P=E.relatedTarget;P!==null&&(l.contains(P)||Cn(h.current,{select:!0}))},f=function(E){if(document.activeElement===document.body)for(const T of E)T.removedNodes.length>0&&Cn(l)};document.addEventListener("focusin",w),document.addEventListener("focusout",v);const y=new MutationObserver(f);return l&&y.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",w),document.removeEventListener("focusout",v),y.disconnect()}}},[r,l,S.paused]),u.useEffect(()=>{if(l){np.add(S);const w=document.activeElement;if(!l.contains(w)){const f=new CustomEvent(ea,ep);l.addEventListener(ea,c),l.dispatchEvent(f),f.defaultPrevented||(Mx($x(ny(l)),{select:!0}),document.activeElement===w&&Cn(l))}return()=>{l.removeEventListener(ea,c),setTimeout(()=>{const f=new CustomEvent(ta,ep);l.addEventListener(ta,d),l.dispatchEvent(f),f.defaultPrevented||Cn(w!=null?w:document.body,{select:!0}),l.removeEventListener(ta,d),np.remove(S)},0)}}},[l,c,d,S]);const C=u.useCallback(w=>{if(!n&&!r||S.paused)return;const v=w.key==="Tab"&&!w.altKey&&!w.ctrlKey&&!w.metaKey,f=document.activeElement;if(v&&f){const y=w.currentTarget,[E,P]=Dx(y);E&&P?!w.shiftKey&&f===P?(w.preventDefault(),n&&Cn(E,{select:!0})):w.shiftKey&&f===E&&(w.preventDefault(),n&&Cn(P,{select:!0})):f===y&&w.preventDefault()}},[n,r,S.paused]);return x.jsx(yn.div,R(m({tabIndex:-1},s),{ref:p,onKeyDown:C}))});ty.displayName=bx;function Mx(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(Cn(r,{select:t}),document.activeElement!==n)return}function Dx(e){const t=ny(e),n=tp(t,e),r=tp(t.reverse(),e);return[n,r]}function ny(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function tp(e,t){for(const n of e)if(!Ox(n,{upTo:t}))return n}function Ox(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Ix(e){return e instanceof HTMLInputElement&&"select"in e}function Cn(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&Ix(e)&&t&&e.select()}}var np=Lx();function Lx(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=rp(e,t),e.unshift(t)},remove(t){var n;e=rp(e,t),(n=e[0])==null||n.resume()}}}function rp(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function $x(e){return e.filter(t=>t.tagName!=="A")}var Fx="Portal",ry=u.forwardRef((e,t)=>{var a;const l=e,{container:n}=l,r=N(l,["container"]),[o,i]=u.useState(!1);xe(()=>i(!0),[]);const s=n||o&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return s?Ac.createPortal(x.jsx(yn.div,R(m({},r),{ref:t})),s):null});ry.displayName=Fx;var na=0;function oy(){u.useEffect(()=>{var t,n;const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",(t=e[0])!=null?t:op()),document.body.insertAdjacentElement("beforeend",(n=e[1])!=null?n:op()),na++,()=>{na===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(r=>r.remove()),na--}},[])}function op(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Do="right-scroll-bar-position",Oo="width-before-scroll-bar",jx="with-scroll-bars-hidden",Vx="--removed-body-scroll-bar-size",iy=Gp(),ra=function(){},rl=u.forwardRef(function(e,t){var n=u.useRef(null),r=u.useState({onScrollCapture:ra,onWheelCapture:ra,onTouchMoveCapture:ra}),o=r[0],i=r[1],s=e.forwardProps,l=e.children,a=e.className,c=e.removeScrollBar,d=e.enabled,h=e.shards,p=e.sideCar,S=e.noIsolation,C=e.inert,g=e.allowPinchZoom,w=e.as,v=w===void 0?"div":w,f=e.gapMode,y=Qp(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),E=p,P=qp([n,t]),T=xt(xt({},y),o);return u.createElement(u.Fragment,null,d&&u.createElement(E,{sideCar:iy,removeScrollBar:c,shards:h,noIsolation:S,inert:C,setCallbacks:i,allowPinchZoom:!!g,lockRef:n,gapMode:f}),s?u.cloneElement(u.Children.only(l),xt(xt({},T),{ref:P})):u.createElement(v,xt({},T,{className:a,ref:P}),l))});rl.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};rl.classNames={fullWidth:Oo,zeroRight:Do};function zx(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Xg();return t&&e.setAttribute("nonce",t),e}function Ux(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function Bx(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var Wx=function(){var e=0,t=null;return{add:function(n){e==0&&(t=zx())&&(Ux(t,n),Bx(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Hx=function(){var e=Wx();return function(t,n){u.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},$c=function(){var e=Hx(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},Kx={left:0,top:0,right:0,gap:0},oa=function(e){return parseInt(e||"",10)||0},Yx=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[oa(n),oa(r),oa(o)]},Gx=function(e){if(e===void 0&&(e="margin"),typeof window=="undefined")return Kx;var t=Yx(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},Qx=$c(),Vr="data-scroll-locked",qx=function(e,t,n,r){var o=e.left,i=e.top,s=e.right,l=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(jx,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(l,"px ").concat(r,`;
  }
  body[`).concat(Vr,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(i,`px;
    padding-right: `).concat(s,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(l,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Do,` {
    right: `).concat(l,"px ").concat(r,`;
  }
  
  .`).concat(Oo,` {
    margin-right: `).concat(l,"px ").concat(r,`;
  }
  
  .`).concat(Do," .").concat(Do,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(Oo," .").concat(Oo,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(Vr,`] {
    `).concat(Vx,": ").concat(l,`px;
  }
`)},ip=function(){var e=parseInt(document.body.getAttribute(Vr)||"0",10);return isFinite(e)?e:0},Xx=function(){u.useEffect(function(){return document.body.setAttribute(Vr,(ip()+1).toString()),function(){var e=ip()-1;e<=0?document.body.removeAttribute(Vr):document.body.setAttribute(Vr,e.toString())}},[])},sy=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;Xx();var i=u.useMemo(function(){return Gx(o)},[o]);return u.createElement(Qx,{styles:qx(i,!t,o,n?"":"!important")})},vu=!1;if(typeof window!="undefined")try{var bi=Object.defineProperty({},"passive",{get:function(){return vu=!0,!0}});window.addEventListener("test",bi,bi),window.removeEventListener("test",bi,bi)}catch(e){vu=!1}var yr=vu?{passive:!1}:!1,Zx=function(e){return e.tagName==="TEXTAREA"},ly=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Zx(e)&&n[t]==="visible")},Jx=function(e){return ly(e,"overflowY")},eE=function(e){return ly(e,"overflowX")},sp=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot!="undefined"&&r instanceof ShadowRoot&&(r=r.host);var o=ay(e,r);if(o){var i=uy(e,r),s=i[1],l=i[2];if(s>l)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},tE=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},nE=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},ay=function(e,t){return e==="v"?Jx(t):eE(t)},uy=function(e,t){return e==="v"?tE(t):nE(t)},rE=function(e,t){return e==="h"&&t==="rtl"?-1:1},oE=function(e,t,n,r,o){var i=rE(e,window.getComputedStyle(t).direction),s=i*r,l=n.target,a=t.contains(l),c=!1,d=s>0,h=0,p=0;do{var S=uy(e,l),C=S[0],g=S[1],w=S[2],v=g-w-i*C;(C||v)&&ay(e,l)&&(h+=v,p+=C),l instanceof ShadowRoot?l=l.host:l=l.parentNode}while(!a&&l!==document.body||a&&(t.contains(l)||t===l));return(d&&(Math.abs(h)<1||!o)||!d&&(Math.abs(p)<1||!o))&&(c=!0),c},Mi=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},lp=function(e){return[e.deltaX,e.deltaY]},ap=function(e){return e&&"current"in e?e.current:e},iE=function(e,t){return e[0]===t[0]&&e[1]===t[1]},sE=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},lE=0,gr=[];function aE(e){var t=u.useRef([]),n=u.useRef([0,0]),r=u.useRef(),o=u.useState(lE++)[0],i=u.useState($c)[0],s=u.useRef(e);u.useEffect(function(){s.current=e},[e]),u.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var g=Xp([e.lockRef.current],(e.shards||[]).map(ap),!0).filter(Boolean);return g.forEach(function(w){return w.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),g.forEach(function(w){return w.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=u.useCallback(function(g,w){if("touches"in g&&g.touches.length===2||g.type==="wheel"&&g.ctrlKey)return!s.current.allowPinchZoom;var v=Mi(g),f=n.current,y="deltaX"in g?g.deltaX:f[0]-v[0],E="deltaY"in g?g.deltaY:f[1]-v[1],P,T=g.target,b=Math.abs(y)>Math.abs(E)?"h":"v";if("touches"in g&&b==="h"&&T.type==="range")return!1;var M=sp(b,T);if(!M)return!0;if(M?P=b:(P=b==="v"?"h":"v",M=sp(b,T)),!M)return!1;if(!r.current&&"changedTouches"in g&&(y||E)&&(r.current=P),!P)return!0;var A=r.current||P;return oE(A,w,g,A==="h"?y:E,!0)},[]),a=u.useCallback(function(g){var w=g;if(!(!gr.length||gr[gr.length-1]!==i)){var v="deltaY"in w?lp(w):Mi(w),f=t.current.filter(function(P){return P.name===w.type&&(P.target===w.target||w.target===P.shadowParent)&&iE(P.delta,v)})[0];if(f&&f.should){w.cancelable&&w.preventDefault();return}if(!f){var y=(s.current.shards||[]).map(ap).filter(Boolean).filter(function(P){return P.contains(w.target)}),E=y.length>0?l(w,y[0]):!s.current.noIsolation;E&&w.cancelable&&w.preventDefault()}}},[]),c=u.useCallback(function(g,w,v,f){var y={name:g,delta:w,target:v,should:f,shadowParent:uE(v)};t.current.push(y),setTimeout(function(){t.current=t.current.filter(function(E){return E!==y})},1)},[]),d=u.useCallback(function(g){n.current=Mi(g),r.current=void 0},[]),h=u.useCallback(function(g){c(g.type,lp(g),g.target,l(g,e.lockRef.current))},[]),p=u.useCallback(function(g){c(g.type,Mi(g),g.target,l(g,e.lockRef.current))},[]);u.useEffect(function(){return gr.push(i),e.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:p}),document.addEventListener("wheel",a,yr),document.addEventListener("touchmove",a,yr),document.addEventListener("touchstart",d,yr),function(){gr=gr.filter(function(g){return g!==i}),document.removeEventListener("wheel",a,yr),document.removeEventListener("touchmove",a,yr),document.removeEventListener("touchstart",d,yr)}},[]);var S=e.removeScrollBar,C=e.inert;return u.createElement(u.Fragment,null,C?u.createElement(i,{styles:sE(o)}):null,S?u.createElement(sy,{gapMode:e.gapMode}):null)}function uE(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const cE=Zp(iy,aE);var cy=u.forwardRef(function(e,t){return u.createElement(rl,xt({},e,{ref:t,sideCar:cE}))});cy.classNames=rl.classNames;var Fc="Dialog",[dy,fy]=Ye(Fc),[dE,Ut]=dy(Fc),py=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:i,modal:s=!0}=e,l=u.useRef(null),a=u.useRef(null),[c=!1,d]=zt({prop:r,defaultProp:o,onChange:i});return x.jsx(dE,{scope:t,triggerRef:l,contentRef:a,contentId:jt(),titleId:jt(),descriptionId:jt(),open:c,onOpenChange:d,onOpenToggle:u.useCallback(()=>d(h=>!h),[d]),modal:s,children:n})};py.displayName=Fc;var hy="DialogTrigger",vy=u.forwardRef((e,t)=>{const s=e,{__scopeDialog:n}=s,r=N(s,["__scopeDialog"]),o=Ut(hy,n),i=ro(t,o.triggerRef);return x.jsx(yn.button,R(m({type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":zc(o.open)},r),{ref:i,onClick:Fn(e.onClick,o.onOpenToggle)}))});vy.displayName=hy;var jc="DialogPortal",[fE,my]=dy(jc,{forceMount:void 0}),yy=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,i=Ut(jc,t);return x.jsx(fE,{scope:t,forceMount:n,children:u.Children.map(r,s=>x.jsx(_t,{present:n||i.open,children:x.jsx(ry,{asChild:!0,container:o,children:s})}))})};yy.displayName=jc;var Es="DialogOverlay",gy=u.forwardRef((e,t)=>{const n=my(Es,e.__scopeDialog),s=e,{forceMount:r=n.forceMount}=s,o=N(s,["forceMount"]),i=Ut(Es,e.__scopeDialog);return i.modal?x.jsx(_t,{present:r||i.open,children:x.jsx(pE,R(m({},o),{ref:t}))}):null});gy.displayName=Es;var pE=u.forwardRef((e,t)=>{const i=e,{__scopeDialog:n}=i,r=N(i,["__scopeDialog"]),o=Ut(Es,n);return x.jsx(cy,{as:Oe,allowPinchZoom:!0,shards:[o.contentRef],children:x.jsx(yn.div,R(m({"data-state":zc(o.open)},r),{ref:t,style:m({pointerEvents:"auto"},r.style)}))})}),sr="DialogContent",wy=u.forwardRef((e,t)=>{const n=my(sr,e.__scopeDialog),s=e,{forceMount:r=n.forceMount}=s,o=N(s,["forceMount"]),i=Ut(sr,e.__scopeDialog);return x.jsx(_t,{present:r||i.open,children:i.modal?x.jsx(hE,R(m({},o),{ref:t})):x.jsx(vE,R(m({},o),{ref:t}))})});wy.displayName=sr;var hE=u.forwardRef((e,t)=>{const n=Ut(sr,e.__scopeDialog),r=u.useRef(null),o=ro(t,n.contentRef,r);return u.useEffect(()=>{const i=r.current;if(i)return Jp(i)},[]),x.jsx(Sy,R(m({},e),{ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:Fn(e.onCloseAutoFocus,i=>{var s;i.preventDefault(),(s=n.triggerRef.current)==null||s.focus()}),onPointerDownOutside:Fn(e.onPointerDownOutside,i=>{const s=i.detail.originalEvent,l=s.button===0&&s.ctrlKey===!0;(s.button===2||l)&&i.preventDefault()}),onFocusOutside:Fn(e.onFocusOutside,i=>i.preventDefault())}))}),vE=u.forwardRef((e,t)=>{const n=Ut(sr,e.__scopeDialog),r=u.useRef(!1),o=u.useRef(!1);return x.jsx(Sy,R(m({},e),{ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:i=>{var s,l;(s=e.onCloseAutoFocus)==null||s.call(e,i),i.defaultPrevented||(r.current||(l=n.triggerRef.current)==null||l.focus(),i.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:i=>{var a,c;(a=e.onInteractOutside)==null||a.call(e,i),i.defaultPrevented||(r.current=!0,i.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const s=i.target;((c=n.triggerRef.current)==null?void 0:c.contains(s))&&i.preventDefault(),i.detail.originalEvent.type==="focusin"&&o.current&&i.preventDefault()}}))}),Sy=u.forwardRef((e,t)=>{const d=e,{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:i}=d,s=N(d,["__scopeDialog","trapFocus","onOpenAutoFocus","onCloseAutoFocus"]),l=Ut(sr,n),a=u.useRef(null),c=ro(t,a);return oy(),x.jsxs(x.Fragment,{children:[x.jsx(ty,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:i,children:x.jsx(Jm,R(m({role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":zc(l.open)},s),{ref:c,onDismiss:()=>l.onOpenChange(!1)}))}),x.jsxs(x.Fragment,{children:[x.jsx(yE,{titleId:l.titleId}),x.jsx(wE,{contentRef:a,descriptionId:l.descriptionId})]})]})}),Vc="DialogTitle",Cy=u.forwardRef((e,t)=>{const i=e,{__scopeDialog:n}=i,r=N(i,["__scopeDialog"]),o=Ut(Vc,n);return x.jsx(yn.h2,R(m({id:o.titleId},r),{ref:t}))});Cy.displayName=Vc;var xy="DialogDescription",Ey=u.forwardRef((e,t)=>{const i=e,{__scopeDialog:n}=i,r=N(i,["__scopeDialog"]),o=Ut(xy,n);return x.jsx(yn.p,R(m({id:o.descriptionId},r),{ref:t}))});Ey.displayName=xy;var ky="DialogClose",Py=u.forwardRef((e,t)=>{const i=e,{__scopeDialog:n}=i,r=N(i,["__scopeDialog"]),o=Ut(ky,n);return x.jsx(yn.button,R(m({type:"button"},r),{ref:t,onClick:Fn(e.onClick,()=>o.onOpenChange(!1))}))});Py.displayName=ky;function zc(e){return e?"open":"closed"}var Ry="DialogTitleWarning",[mE,_y]=I2(Ry,{contentName:sr,titleName:Vc,docsSlug:"dialog"}),yE=({titleId:e})=>{const t=_y(Ry),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return u.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},gE="DialogDescriptionWarning",wE=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${_y(gE).contentName}}.`;return u.useEffect(()=>{var i;const o=(i=e.current)==null?void 0:i.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},SE=py,CE=vy,xE=yy,EE=gy,kE=wy,PE=Cy,RE=Ey,Ty=Py;/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _E=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Ny=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim();/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var TE={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const NE=u.forwardRef((c,a)=>{var d=c,{color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:i,iconNode:s}=d,l=N(d,["color","size","strokeWidth","absoluteStrokeWidth","className","children","iconNode"]);return u.createElement("svg",m(R(m({ref:a},TE),{width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:Ny("lucide",o)}),l),[...s.map(([h,p])=>u.createElement(h,p)),...Array.isArray(i)?i:[i]])});/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const O=(e,t)=>{const n=u.forwardRef((s,i)=>{var l=s,{className:r}=l,o=N(l,["className"]);return u.createElement(NE,m({ref:i,iconNode:t,className:Ny(`lucide-${_E(e)}`,r)},o))});return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hT=O("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vT=O("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mT=O("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yT=O("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gT=O("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wT=O("Bold",[["path",{d:"M6 12h9a4 4 0 0 1 0 8H7a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h7a4 4 0 0 1 0 8",key:"mg9rjx"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ST=O("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const CT=O("BookText",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}],["path",{d:"M8 11h8",key:"vwpz6n"}],["path",{d:"M8 7h6",key:"1f0q6e"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xT=O("Book",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ET=O("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kT=O("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const PT=O("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const RT=O("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _T=O("ChartNoAxesColumn",[["line",{x1:"18",x2:"18",y1:"20",y2:"10",key:"1xfpm4"}],["line",{x1:"12",x2:"12",y1:"20",y2:"4",key:"be30l9"}],["line",{x1:"6",x2:"6",y1:"20",y2:"14",key:"1r4le6"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const TT=O("ChartPie",[["path",{d:"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z",key:"pzmjnu"}],["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const NT=O("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const AT=O("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bT=O("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const MT=O("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const DT=O("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const OT=O("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const IT=O("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const LT=O("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $T=O("CircleHelp",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const FT=O("CircleMinus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jT=O("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const VT=O("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zT=O("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const UT=O("ClipboardCheck",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"m9 14 2 2 4-4",key:"df797q"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const BT=O("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const WT=O("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const HT=O("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const KT=O("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const YT=O("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const GT=O("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const QT=O("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qT=O("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const XT=O("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ZT=O("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const JT=O("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eN=O("GraduationCap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tN=O("Heading1",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"m17 12 3-2v8",key:"1hhhft"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nN=O("Heading2",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"M21 18h-4c0-4 4-3 4-6 0-1.5-2-2.5-4-1",key:"9jr5yi"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rN=O("Heading3",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"M17.5 10.5c1.7-1 3.5 0 3.5 1.5a2 2 0 0 1-2 2",key:"68ncm8"}],["path",{d:"M17 17.5c2 1.5 4 .3 4-1.5a2 2 0 0 0-2-2",key:"1ejuhz"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oN=O("Highlighter",[["path",{d:"m9 11-6 6v3h9l3-3",key:"1a3l36"}],["path",{d:"m22 12-4.6 4.6a2 2 0 0 1-2.8 0l-5.2-5.2a2 2 0 0 1 0-2.8L14 4",key:"14a9rk"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iN=O("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sN=O("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lN=O("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const aN=O("Italic",[["line",{x1:"19",x2:"10",y1:"4",y2:"4",key:"15jd3p"}],["line",{x1:"14",x2:"5",y1:"20",y2:"20",key:"bu0au3"}],["line",{x1:"15",x2:"9",y1:"4",y2:"20",key:"uljnxc"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uN=O("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cN=O("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dN=O("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fN=O("ListOrdered",[["path",{d:"M10 12h11",key:"6m4ad9"}],["path",{d:"M10 18h11",key:"11hvi2"}],["path",{d:"M10 6h11",key:"c7qv1k"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pN=O("List",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hN=O("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vN=O("LockOpen",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 9.9-1",key:"1mm8w8"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mN=O("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yN=O("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gN=O("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wN=O("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const SN=O("Maximize2",[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const CN=O("Medal",[["path",{d:"M7.21 15 2.66 7.14a2 2 0 0 1 .13-2.2L4.4 2.8A2 2 0 0 1 6 2h12a2 2 0 0 1 1.6.8l1.6 2.14a2 2 0 0 1 .14 2.2L16.79 15",key:"143lza"}],["path",{d:"M11 12 5.12 2.2",key:"qhuxz6"}],["path",{d:"m13 12 5.88-9.8",key:"hbye0f"}],["path",{d:"M8 7h8",key:"i86dvs"}],["circle",{cx:"12",cy:"17",r:"5",key:"qbz8iq"}],["path",{d:"M12 18v-2h-.5",key:"fawc4q"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xN=O("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const EN=O("Minimize2",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kN=O("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const PN=O("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const RN=O("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _N=O("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const TN=O("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const NN=O("Quote",[["path",{d:"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"rib7q0"}],["path",{d:"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"1ymkrd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const AN=O("Redo",[["path",{d:"M21 7v6h-6",key:"3ptur4"}],["path",{d:"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7",key:"1kgawr"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bN=O("RefreshCcw",[["path",{d:"M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"14sxne"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16",key:"1hlbsb"}],["path",{d:"M16 16h5v5",key:"ccwih5"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const MN=O("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const DN=O("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ON=O("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const IN=O("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const LN=O("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $N=O("ShieldAlert",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"M12 8v4",key:"1got3b"}],["path",{d:"M12 16h.01",key:"1drbdi"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const FN=O("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jN=O("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const VN=O("Split",[["path",{d:"M16 3h5v5",key:"1806ms"}],["path",{d:"M8 3H3v5",key:"15dfkv"}],["path",{d:"M12 22v-8.3a4 4 0 0 0-1.172-2.872L3 3",key:"1qrqzj"}],["path",{d:"m15 9 6-6",key:"ko1vev"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zN=O("SquareCheckBig",[["path",{d:"M21 10.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.5",key:"1uzm8b"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const UN=O("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const BN=O("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const WN=O("Strikethrough",[["path",{d:"M16 4H9a3 3 0 0 0-2.83 4",key:"43sutm"}],["path",{d:"M14 12a4 4 0 0 1 0 8H6",key:"nlfj13"}],["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const HN=O("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const KN=O("Table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const YN=O("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const GN=O("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2a3.13 3.13 0 0 1 3 3.88Z",key:"emmmcr"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const QN=O("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qN=O("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const XN=O("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ZN=O("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const JN=O("Type",[["polyline",{points:"4 7 4 4 20 4 20 7",key:"1nosan"}],["line",{x1:"9",x2:"15",y1:"20",y2:"20",key:"swin9y"}],["line",{x1:"12",x2:"12",y1:"4",y2:"20",key:"1tx1rr"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eA=O("Underline",[["path",{d:"M6 4v6a6 6 0 0 0 12 0V4",key:"9kb039"}],["line",{x1:"4",x2:"20",y1:"20",y2:"20",key:"nun2al"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tA=O("Undo",[["path",{d:"M3 7v6h6",key:"1v2h90"}],["path",{d:"M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13",key:"1r6uu6"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nA=O("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rA=O("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oA=O("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iA=O("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sA=O("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lA=O("Video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const aA=O("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uA=O("Youtube",[["path",{d:"M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17",key:"1q2vi4"}],["path",{d:"m10 15 5-3-5-3z",key:"1jp15x"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cA=O("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]);function AE(e,t=[]){let n=[];function r(i,s){const l=u.createContext(s),a=n.length;n=[...n,s];function c(h){const v=h,{scope:p,children:S}=v,C=N(v,["scope","children"]),g=(p==null?void 0:p[e][a])||l,w=u.useMemo(()=>C,Object.values(C));return x.jsx(g.Provider,{value:w,children:S})}function d(h,p){const S=(p==null?void 0:p[e][a])||l,C=u.useContext(S);if(C)return C;if(s!==void 0)return s;throw new Error(`\`${h}\` must be used within \`${i}\``)}return c.displayName=i+"Provider",[c,d]}const o=()=>{const i=n.map(s=>u.createContext(s));return function(l){const a=(l==null?void 0:l[e])||i;return u.useMemo(()=>({[`__scope${e}`]:R(m({},l),{[e]:a})}),[l,a])}};return o.scopeName=e,[r,bE(o,...t)]}function bE(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((l,{useScope:a,scopeName:c})=>{const h=a(i)[`__scope${c}`];return m(m({},l),h)},{});return u.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}function ME(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function ol(...e){return t=>e.forEach(n=>ME(n,t))}function Ke(...e){return u.useCallback(ol(...e),e)}var mu=u.forwardRef((e,t)=>{const s=e,{children:n}=s,r=N(s,["children"]),o=u.Children.toArray(n),i=o.find(OE);if(i){const l=i.props.children,a=o.map(c=>c===i?u.Children.count(l)>1?u.Children.only(null):u.isValidElement(l)?l.props.children:null:c);return x.jsx(yu,R(m({},r),{ref:t,children:u.isValidElement(l)?u.cloneElement(l,void 0,a):null}))}return x.jsx(yu,R(m({},r),{ref:t,children:n}))});mu.displayName="Slot";var yu=u.forwardRef((e,t)=>{const o=e,{children:n}=o,r=N(o,["children"]);if(u.isValidElement(n)){const i=LE(n);return u.cloneElement(n,R(m({},IE(r,n.props)),{ref:t?ol(t,i):i}))}return u.Children.count(n)>1?u.Children.only(null):null});yu.displayName="SlotClone";var DE=({children:e})=>x.jsx(x.Fragment,{children:e});function OE(e){return u.isValidElement(e)&&e.type===DE}function IE(e,t){const n=m({},t);for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...l)=>{i(...l),o(...l)}:o&&(n[r]=o):r==="style"?n[r]=m(m({},o),i):r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return m(m({},e),n)}function LE(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function $E(e){const t=e+"CollectionProvider",[n,r]=AE(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=S=>{const{scope:C,children:g}=S,w=$.useRef(null),v=$.useRef(new Map).current;return x.jsx(o,{scope:C,itemMap:v,collectionRef:w,children:g})};s.displayName=t;const l=e+"CollectionSlot",a=$.forwardRef((S,C)=>{const{scope:g,children:w}=S,v=i(l,g),f=Ke(C,v.collectionRef);return x.jsx(mu,{ref:f,children:w})});a.displayName=l;const c=e+"CollectionItemSlot",d="data-radix-collection-item",h=$.forwardRef((S,C)=>{const P=S,{scope:g,children:w}=P,v=N(P,["scope","children"]),f=$.useRef(null),y=Ke(C,f),E=i(c,g);return $.useEffect(()=>(E.itemMap.set(f,m({ref:f},v)),()=>void E.itemMap.delete(f))),x.jsx(mu,{[d]:"",ref:y,children:w})});h.displayName=c;function p(S){const C=i(e+"CollectionConsumer",S);return $.useCallback(()=>{const w=C.collectionRef.current;if(!w)return[];const v=Array.from(w.querySelectorAll(`[${d}]`));return Array.from(C.itemMap.values()).sort((E,P)=>v.indexOf(E.ref.current)-v.indexOf(P.ref.current))},[C.collectionRef,C.itemMap])}return[{Provider:s,Slot:a,ItemSlot:h},p,r]}var FE=u.createContext(void 0);function oo(e){const t=u.useContext(FE);return e||t||"ltr"}var Ay=u.forwardRef((e,t)=>{const s=e,{children:n}=s,r=N(s,["children"]),o=u.Children.toArray(n),i=o.find(VE);if(i){const l=i.props.children,a=o.map(c=>c===i?u.Children.count(l)>1?u.Children.only(null):u.isValidElement(l)?l.props.children:null:c);return x.jsx(gu,R(m({},r),{ref:t,children:u.isValidElement(l)?u.cloneElement(l,void 0,a):null}))}return x.jsx(gu,R(m({},r),{ref:t,children:n}))});Ay.displayName="Slot";var gu=u.forwardRef((e,t)=>{const o=e,{children:n}=o,r=N(o,["children"]);if(u.isValidElement(n)){const i=UE(n);return u.cloneElement(n,R(m({},zE(r,n.props)),{ref:t?ol(t,i):i}))}return u.Children.count(n)>1?u.Children.only(null):null});gu.displayName="SlotClone";var jE=({children:e})=>x.jsx(x.Fragment,{children:e});function VE(e){return u.isValidElement(e)&&e.type===jE}function zE(e,t){const n=m({},t);for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...l)=>{i(...l),o(...l)}:o&&(n[r]=o):r==="style"?n[r]=m(m({},o),i):r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return m(m({},e),n)}function UE(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var BE=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],Ne=BE.reduce((e,t)=>{const n=u.forwardRef((r,o)=>{const a=r,{asChild:i}=a,s=N(a,["asChild"]),l=i?Ay:t;return typeof window!="undefined"&&(window[Symbol.for("radix-ui")]=!0),x.jsx(l,R(m({},s),{ref:o}))});return n.displayName=`Primitive.${t}`,R(m({},e),{[t]:n})},{});function WE(e,t){e&&hn.flushSync(()=>e.dispatchEvent(t))}var HE="DismissableLayer",wu="dismissableLayer.update",KE="dismissableLayer.pointerDownOutside",YE="dismissableLayer.focusOutside",up,by=u.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),My=u.forwardRef((e,t)=>{var M;const b=e,{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:s,onDismiss:l}=b,a=N(b,["disableOutsidePointerEvents","onEscapeKeyDown","onPointerDownOutside","onFocusOutside","onInteractOutside","onDismiss"]),c=u.useContext(by),[d,h]=u.useState(null),p=(M=d==null?void 0:d.ownerDocument)!=null?M:globalThis==null?void 0:globalThis.document,[,S]=u.useState({}),C=Ke(t,A=>h(A)),g=Array.from(c.layers),[w]=[...c.layersWithOutsidePointerEventsDisabled].slice(-1),v=g.indexOf(w),f=d?g.indexOf(d):-1,y=c.layersWithOutsidePointerEventsDisabled.size>0,E=f>=v,P=qE(A=>{const I=A.target,B=[...c.branches].some(j=>j.contains(I));!E||B||(o==null||o(A),s==null||s(A),A.defaultPrevented||l==null||l())},p),T=XE(A=>{const I=A.target;[...c.branches].some(j=>j.contains(I))||(i==null||i(A),s==null||s(A),A.defaultPrevented||l==null||l())},p);return Mc(A=>{f===c.layers.size-1&&(r==null||r(A),!A.defaultPrevented&&l&&(A.preventDefault(),l()))},p),u.useEffect(()=>{if(d)return n&&(c.layersWithOutsidePointerEventsDisabled.size===0&&(up=p.body.style.pointerEvents,p.body.style.pointerEvents="none"),c.layersWithOutsidePointerEventsDisabled.add(d)),c.layers.add(d),cp(),()=>{n&&c.layersWithOutsidePointerEventsDisabled.size===1&&(p.body.style.pointerEvents=up)}},[d,p,n,c]),u.useEffect(()=>()=>{d&&(c.layers.delete(d),c.layersWithOutsidePointerEventsDisabled.delete(d),cp())},[d,c]),u.useEffect(()=>{const A=()=>S({});return document.addEventListener(wu,A),()=>document.removeEventListener(wu,A)},[]),x.jsx(Ne.div,R(m({},a),{ref:C,style:m({pointerEvents:y?E?"auto":"none":void 0},e.style),onFocusCapture:_e(e.onFocusCapture,T.onFocusCapture),onBlurCapture:_e(e.onBlurCapture,T.onBlurCapture),onPointerDownCapture:_e(e.onPointerDownCapture,P.onPointerDownCapture)}))});My.displayName=HE;var GE="DismissableLayerBranch",QE=u.forwardRef((e,t)=>{const n=u.useContext(by),r=u.useRef(null),o=Ke(t,r);return u.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),x.jsx(Ne.div,R(m({},e),{ref:o}))});QE.displayName=GE;function qE(e,t=globalThis==null?void 0:globalThis.document){const n=fe(e),r=u.useRef(!1),o=u.useRef(()=>{});return u.useEffect(()=>{const i=l=>{if(l.target&&!r.current){let a=function(){Dy(KE,n,c,{discrete:!0})};const c={originalEvent:l};l.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=a,t.addEventListener("click",o.current,{once:!0})):a()}else t.removeEventListener("click",o.current);r.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function XE(e,t=globalThis==null?void 0:globalThis.document){const n=fe(e),r=u.useRef(!1);return u.useEffect(()=>{const o=i=>{i.target&&!r.current&&Dy(YE,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function cp(){const e=new CustomEvent(wu);document.dispatchEvent(e)}function Dy(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?WE(o,i):o.dispatchEvent(i)}var ia="focusScope.autoFocusOnMount",sa="focusScope.autoFocusOnUnmount",dp={bubbles:!1,cancelable:!0},ZE="FocusScope",Oy=u.forwardRef((e,t)=>{const g=e,{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i}=g,s=N(g,["loop","trapped","onMountAutoFocus","onUnmountAutoFocus"]),[l,a]=u.useState(null),c=fe(o),d=fe(i),h=u.useRef(null),p=Ke(t,w=>a(w)),S=u.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;u.useEffect(()=>{if(r){let w=function(E){if(S.paused||!l)return;const P=E.target;l.contains(P)?h.current=P:xn(h.current,{select:!0})},v=function(E){if(S.paused||!l)return;const P=E.relatedTarget;P!==null&&(l.contains(P)||xn(h.current,{select:!0}))},f=function(E){if(document.activeElement===document.body)for(const T of E)T.removedNodes.length>0&&xn(l)};document.addEventListener("focusin",w),document.addEventListener("focusout",v);const y=new MutationObserver(f);return l&&y.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",w),document.removeEventListener("focusout",v),y.disconnect()}}},[r,l,S.paused]),u.useEffect(()=>{if(l){pp.add(S);const w=document.activeElement;if(!l.contains(w)){const f=new CustomEvent(ia,dp);l.addEventListener(ia,c),l.dispatchEvent(f),f.defaultPrevented||(JE(ok(Iy(l)),{select:!0}),document.activeElement===w&&xn(l))}return()=>{l.removeEventListener(ia,c),setTimeout(()=>{const f=new CustomEvent(sa,dp);l.addEventListener(sa,d),l.dispatchEvent(f),f.defaultPrevented||xn(w!=null?w:document.body,{select:!0}),l.removeEventListener(sa,d),pp.remove(S)},0)}}},[l,c,d,S]);const C=u.useCallback(w=>{if(!n&&!r||S.paused)return;const v=w.key==="Tab"&&!w.altKey&&!w.ctrlKey&&!w.metaKey,f=document.activeElement;if(v&&f){const y=w.currentTarget,[E,P]=ek(y);E&&P?!w.shiftKey&&f===P?(w.preventDefault(),n&&xn(E,{select:!0})):w.shiftKey&&f===E&&(w.preventDefault(),n&&xn(P,{select:!0})):f===y&&w.preventDefault()}},[n,r,S.paused]);return x.jsx(Ne.div,R(m({tabIndex:-1},s),{ref:p,onKeyDown:C}))});Oy.displayName=ZE;function JE(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(xn(r,{select:t}),document.activeElement!==n)return}function ek(e){const t=Iy(e),n=fp(t,e),r=fp(t.reverse(),e);return[n,r]}function Iy(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function fp(e,t){for(const n of e)if(!tk(n,{upTo:t}))return n}function tk(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function nk(e){return e instanceof HTMLInputElement&&"select"in e}function xn(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&nk(e)&&t&&e.select()}}var pp=rk();function rk(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=hp(e,t),e.unshift(t)},remove(t){var n;e=hp(e,t),(n=e[0])==null||n.resume()}}}function hp(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function ok(e){return e.filter(t=>t.tagName!=="A")}var ik="Arrow",Ly=u.forwardRef((e,t)=>{const s=e,{children:n,width:r=10,height:o=5}=s,i=N(s,["children","width","height"]);return x.jsx(Ne.svg,R(m({},i),{ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:x.jsx("polygon",{points:"0,0 30,0 15,10"})}))});Ly.displayName=ik;var sk=Ly;function lk(e,t=[]){let n=[];function r(i,s){const l=u.createContext(s),a=n.length;n=[...n,s];function c(h){const v=h,{scope:p,children:S}=v,C=N(v,["scope","children"]),g=(p==null?void 0:p[e][a])||l,w=u.useMemo(()=>C,Object.values(C));return x.jsx(g.Provider,{value:w,children:S})}function d(h,p){const S=(p==null?void 0:p[e][a])||l,C=u.useContext(S);if(C)return C;if(s!==void 0)return s;throw new Error(`\`${h}\` must be used within \`${i}\``)}return c.displayName=i+"Provider",[c,d]}const o=()=>{const i=n.map(s=>u.createContext(s));return function(l){const a=(l==null?void 0:l[e])||i;return u.useMemo(()=>({[`__scope${e}`]:R(m({},l),{[e]:a})}),[l,a])}};return o.scopeName=e,[r,ak(o,...t)]}function ak(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((l,{useScope:a,scopeName:c})=>{const h=a(i)[`__scope${c}`];return m(m({},l),h)},{});return u.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var Uc="Popper",[$y,Fy]=lk(Uc),[uk,jy]=$y(Uc),Vy=e=>{const{__scopePopper:t,children:n}=e,[r,o]=u.useState(null);return x.jsx(uk,{scope:t,anchor:r,onAnchorChange:o,children:n})};Vy.displayName=Uc;var zy="PopperAnchor",Uy=u.forwardRef((e,t)=>{const a=e,{__scopePopper:n,virtualRef:r}=a,o=N(a,["__scopePopper","virtualRef"]),i=jy(zy,n),s=u.useRef(null),l=Ke(t,s);return u.useEffect(()=>{i.onAnchorChange((r==null?void 0:r.current)||s.current)}),r?null:x.jsx(Ne.div,R(m({},o),{ref:l}))});Uy.displayName=zy;var Bc="PopperContent",[ck,dk]=$y(Bc),By=u.forwardRef((e,t)=>{var G,ie,Se,ne,te,re,Ae,Ie;const Ze=e,{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:s=0,arrowPadding:l=0,avoidCollisions:a=!0,collisionBoundary:c=[],collisionPadding:d=0,sticky:h="partial",hideWhenDetached:p=!1,updatePositionStrategy:S="optimized",onPlaced:C}=Ze,g=N(Ze,["__scopePopper","side","sideOffset","align","alignOffset","arrowPadding","avoidCollisions","collisionBoundary","collisionPadding","sticky","hideWhenDetached","updatePositionStrategy","onPlaced"]),w=jy(Bc,n),[v,f]=u.useState(null),y=Ke(t,Le=>f(Le)),[E,P]=u.useState(null),T=Qs(E),b=(G=T==null?void 0:T.width)!=null?G:0,M=(ie=T==null?void 0:T.height)!=null?ie:0,A=r+(i!=="center"?"-"+i:""),I=typeof d=="number"?d:m({top:0,right:0,bottom:0,left:0},d),B=Array.isArray(c)?c:[c],j=B.length>0,W={padding:I,boundary:B.filter(pk),altBoundary:j},{refs:Z,floatingStyles:ee,placement:Q,isPositioned:q,middlewareData:Y}=vm({strategy:"fixed",placement:A,whileElementsMounted:(...Le)=>Wp(...Le,{animationFrame:S==="always"}),elements:{reference:w.anchor},middleware:[mm({mainAxis:o+M,alignmentAxis:s}),a&&ym(m({mainAxis:!0,crossAxis:!1,limiter:h==="partial"?gm():void 0},W)),a&&wm(m({},W)),Sm(R(m({},W),{apply:({elements:Le,rects:wt,availableWidth:en,availableHeight:lo})=>{const{width:ci,height:fr}=wt.reference,k=Le.floating.style;k.setProperty("--radix-popper-available-width",`${en}px`),k.setProperty("--radix-popper-available-height",`${lo}px`),k.setProperty("--radix-popper-anchor-width",`${ci}px`),k.setProperty("--radix-popper-anchor-height",`${fr}px`)}})),E&&xm({element:E,padding:l}),hk({arrowWidth:b,arrowHeight:M}),p&&Cm(m({strategy:"referenceHidden"},W))]}),[he,Re]=Ky(Q),Ve=fe(C);xe(()=>{q&&(Ve==null||Ve())},[q,Ve]);const Nt=(Se=Y.arrow)==null?void 0:Se.x,Bt=(ne=Y.arrow)==null?void 0:ne.y,Zt=((te=Y.arrow)==null?void 0:te.centerOffset)!==0,[it,Jt]=u.useState();return xe(()=>{v&&Jt(window.getComputedStyle(v).zIndex)},[v]),x.jsx("div",{ref:Z.setFloating,"data-radix-popper-content-wrapper":"",style:m(R(m({},ee),{transform:q?ee.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:it,"--radix-popper-transform-origin":[(re=Y.transformOrigin)==null?void 0:re.x,(Ae=Y.transformOrigin)==null?void 0:Ae.y].join(" ")}),((Ie=Y.hide)==null?void 0:Ie.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}),dir:e.dir,children:x.jsx(ck,{scope:n,placedSide:he,onArrowChange:P,arrowX:Nt,arrowY:Bt,shouldHideArrow:Zt,children:x.jsx(Ne.div,R(m({"data-side":he,"data-align":Re},g),{ref:y,style:R(m({},g.style),{animation:q?void 0:"none"})}))})})});By.displayName=Bc;var Wy="PopperArrow",fk={top:"bottom",right:"left",bottom:"top",left:"right"},Hy=u.forwardRef(function(t,n){const l=t,{__scopePopper:r}=l,o=N(l,["__scopePopper"]),i=dk(Wy,r),s=fk[i.placedSide];return x.jsx("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[s]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:x.jsx(sk,R(m({},o),{ref:n,style:R(m({},o.style),{display:"block"})}))})});Hy.displayName=Wy;function pk(e){return e!==null}var hk=e=>({name:"transformOrigin",options:e,fn(t){var w,v,f,y,E;const{placement:n,rects:r,middlewareData:o}=t,s=((w=o.arrow)==null?void 0:w.centerOffset)!==0,l=s?0:e.arrowWidth,a=s?0:e.arrowHeight,[c,d]=Ky(n),h={start:"0%",center:"50%",end:"100%"}[d],p=((f=(v=o.arrow)==null?void 0:v.x)!=null?f:0)+l/2,S=((E=(y=o.arrow)==null?void 0:y.y)!=null?E:0)+a/2;let C="",g="";return c==="bottom"?(C=s?h:`${p}px`,g=`${-a}px`):c==="top"?(C=s?h:`${p}px`,g=`${r.floating.height+a}px`):c==="right"?(C=`${-a}px`,g=s?h:`${S}px`):c==="left"&&(C=`${r.floating.width+a}px`,g=s?h:`${S}px`),{data:{x:C,y:g}}}});function Ky(e){const[t,n="center"]=e.split("-");return[t,n]}var vk=Vy,mk=Uy,yk=By,gk=Hy,wk="Portal",Yy=u.forwardRef((e,t)=>{var a;const l=e,{container:n}=l,r=N(l,["container"]),[o,i]=u.useState(!1);xe(()=>i(!0),[]);const s=n||o&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return s?Ac.createPortal(x.jsx(Ne.div,R(m({},r),{ref:t})),s):null});Yy.displayName=wk;var Gy=u.forwardRef((e,t)=>{const s=e,{children:n}=s,r=N(s,["children"]),o=u.Children.toArray(n),i=o.find(Ck);if(i){const l=i.props.children,a=o.map(c=>c===i?u.Children.count(l)>1?u.Children.only(null):u.isValidElement(l)?l.props.children:null:c);return x.jsx(Su,R(m({},r),{ref:t,children:u.isValidElement(l)?u.cloneElement(l,void 0,a):null}))}return x.jsx(Su,R(m({},r),{ref:t,children:n}))});Gy.displayName="Slot";var Su=u.forwardRef((e,t)=>{const o=e,{children:n}=o,r=N(o,["children"]);if(u.isValidElement(n)){const i=Ek(n);return u.cloneElement(n,R(m({},xk(r,n.props)),{ref:t?ol(t,i):i}))}return u.Children.count(n)>1?u.Children.only(null):null});Su.displayName="SlotClone";var Sk=({children:e})=>x.jsx(x.Fragment,{children:e});function Ck(e){return u.isValidElement(e)&&e.type===Sk}function xk(e,t){const n=m({},t);for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...l)=>{i(...l),o(...l)}:o&&(n[r]=o):r==="style"?n[r]=m(m({},o),i):r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return m(m({},e),n)}function Ek(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Wc(e){const t=u.useRef({value:e,previous:e});return u.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var kk="VisuallyHidden",Qy=u.forwardRef((e,t)=>x.jsx(Ne.span,R(m({},e),{ref:t,style:m({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"},e.style)})));Qy.displayName=kk;var qy=Gp(),la=function(){},il=u.forwardRef(function(e,t){var n=u.useRef(null),r=u.useState({onScrollCapture:la,onWheelCapture:la,onTouchMoveCapture:la}),o=r[0],i=r[1],s=e.forwardProps,l=e.children,a=e.className,c=e.removeScrollBar,d=e.enabled,h=e.shards,p=e.sideCar,S=e.noIsolation,C=e.inert,g=e.allowPinchZoom,w=e.as,v=w===void 0?"div":w,f=e.gapMode,y=Qp(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),E=p,P=qp([n,t]),T=xt(xt({},y),o);return u.createElement(u.Fragment,null,d&&u.createElement(E,{sideCar:qy,removeScrollBar:c,shards:h,noIsolation:S,inert:C,setCallbacks:i,allowPinchZoom:!!g,lockRef:n,gapMode:f}),s?u.cloneElement(u.Children.only(l),xt(xt({},T),{ref:P})):u.createElement(v,xt({},T,{className:a,ref:P}),l))});il.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};il.classNames={fullWidth:Oo,zeroRight:Do};var Cu=!1;if(typeof window!="undefined")try{var Di=Object.defineProperty({},"passive",{get:function(){return Cu=!0,!0}});window.addEventListener("test",Di,Di),window.removeEventListener("test",Di,Di)}catch(e){Cu=!1}var wr=Cu?{passive:!1}:!1,Pk=function(e){return e.tagName==="TEXTAREA"},Xy=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Pk(e)&&n[t]==="visible")},Rk=function(e){return Xy(e,"overflowY")},_k=function(e){return Xy(e,"overflowX")},vp=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot!="undefined"&&r instanceof ShadowRoot&&(r=r.host);var o=Zy(e,r);if(o){var i=Jy(e,r),s=i[1],l=i[2];if(s>l)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Tk=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},Nk=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Zy=function(e,t){return e==="v"?Rk(t):_k(t)},Jy=function(e,t){return e==="v"?Tk(t):Nk(t)},Ak=function(e,t){return e==="h"&&t==="rtl"?-1:1},bk=function(e,t,n,r,o){var i=Ak(e,window.getComputedStyle(t).direction),s=i*r,l=n.target,a=t.contains(l),c=!1,d=s>0,h=0,p=0;do{var S=Jy(e,l),C=S[0],g=S[1],w=S[2],v=g-w-i*C;(C||v)&&Zy(e,l)&&(h+=v,p+=C),l instanceof ShadowRoot?l=l.host:l=l.parentNode}while(!a&&l!==document.body||a&&(t.contains(l)||t===l));return(d&&(Math.abs(h)<1||!o)||!d&&(Math.abs(p)<1||!o))&&(c=!0),c},Oi=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},mp=function(e){return[e.deltaX,e.deltaY]},yp=function(e){return e&&"current"in e?e.current:e},Mk=function(e,t){return e[0]===t[0]&&e[1]===t[1]},Dk=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},Ok=0,Sr=[];function Ik(e){var t=u.useRef([]),n=u.useRef([0,0]),r=u.useRef(),o=u.useState(Ok++)[0],i=u.useState($c)[0],s=u.useRef(e);u.useEffect(function(){s.current=e},[e]),u.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var g=Xp([e.lockRef.current],(e.shards||[]).map(yp),!0).filter(Boolean);return g.forEach(function(w){return w.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),g.forEach(function(w){return w.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=u.useCallback(function(g,w){if("touches"in g&&g.touches.length===2||g.type==="wheel"&&g.ctrlKey)return!s.current.allowPinchZoom;var v=Oi(g),f=n.current,y="deltaX"in g?g.deltaX:f[0]-v[0],E="deltaY"in g?g.deltaY:f[1]-v[1],P,T=g.target,b=Math.abs(y)>Math.abs(E)?"h":"v";if("touches"in g&&b==="h"&&T.type==="range")return!1;var M=vp(b,T);if(!M)return!0;if(M?P=b:(P=b==="v"?"h":"v",M=vp(b,T)),!M)return!1;if(!r.current&&"changedTouches"in g&&(y||E)&&(r.current=P),!P)return!0;var A=r.current||P;return bk(A,w,g,A==="h"?y:E,!0)},[]),a=u.useCallback(function(g){var w=g;if(!(!Sr.length||Sr[Sr.length-1]!==i)){var v="deltaY"in w?mp(w):Oi(w),f=t.current.filter(function(P){return P.name===w.type&&(P.target===w.target||w.target===P.shadowParent)&&Mk(P.delta,v)})[0];if(f&&f.should){w.cancelable&&w.preventDefault();return}if(!f){var y=(s.current.shards||[]).map(yp).filter(Boolean).filter(function(P){return P.contains(w.target)}),E=y.length>0?l(w,y[0]):!s.current.noIsolation;E&&w.cancelable&&w.preventDefault()}}},[]),c=u.useCallback(function(g,w,v,f){var y={name:g,delta:w,target:v,should:f,shadowParent:Lk(v)};t.current.push(y),setTimeout(function(){t.current=t.current.filter(function(E){return E!==y})},1)},[]),d=u.useCallback(function(g){n.current=Oi(g),r.current=void 0},[]),h=u.useCallback(function(g){c(g.type,mp(g),g.target,l(g,e.lockRef.current))},[]),p=u.useCallback(function(g){c(g.type,Oi(g),g.target,l(g,e.lockRef.current))},[]);u.useEffect(function(){return Sr.push(i),e.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:p}),document.addEventListener("wheel",a,wr),document.addEventListener("touchmove",a,wr),document.addEventListener("touchstart",d,wr),function(){Sr=Sr.filter(function(g){return g!==i}),document.removeEventListener("wheel",a,wr),document.removeEventListener("touchmove",a,wr),document.removeEventListener("touchstart",d,wr)}},[]);var S=e.removeScrollBar,C=e.inert;return u.createElement(u.Fragment,null,C?u.createElement(i,{styles:Dk(o)}):null,S?u.createElement(sy,{gapMode:e.gapMode}):null)}function Lk(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const $k=Zp(qy,Ik);var e1=u.forwardRef(function(e,t){return u.createElement(il,xt({},e,{ref:t,sideCar:$k}))});e1.classNames=il.classNames;var Fk=[" ","Enter","ArrowUp","ArrowDown"],jk=[" ","Enter"],li="Select",[sl,ll,Vk]=$E(li),[io,dA]=Ye(li,[Vk,Fy]),al=Fy(),[zk,Wn]=io(li),[Uk,Bk]=io(li),t1=e=>{const{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:i,value:s,defaultValue:l,onValueChange:a,dir:c,name:d,autoComplete:h,disabled:p,required:S,form:C}=e,g=al(t),[w,v]=u.useState(null),[f,y]=u.useState(null),[E,P]=u.useState(!1),T=oo(c),[b=!1,M]=zt({prop:r,defaultProp:o,onChange:i}),[A,I]=zt({prop:s,defaultProp:l,onChange:a}),B=u.useRef(null),j=w?C||!!w.closest("form"):!0,[W,Z]=u.useState(new Set),ee=Array.from(W).map(Q=>Q.props.value).join(";");return x.jsx(vk,R(m({},g),{children:x.jsxs(zk,{required:S,scope:t,trigger:w,onTriggerChange:v,valueNode:f,onValueNodeChange:y,valueNodeHasChildren:E,onValueNodeHasChildrenChange:P,contentId:jt(),value:A,onValueChange:I,open:b,onOpenChange:M,dir:T,triggerPointerDownPosRef:B,disabled:p,children:[x.jsx(sl.Provider,{scope:t,children:x.jsx(Uk,{scope:e.__scopeSelect,onNativeOptionAdd:u.useCallback(Q=>{Z(q=>new Set(q).add(Q))},[]),onNativeOptionRemove:u.useCallback(Q=>{Z(q=>{const Y=new Set(q);return Y.delete(Q),Y})},[]),children:n})}),j?x.jsxs(R1,{"aria-hidden":!0,required:S,tabIndex:-1,name:d,autoComplete:h,value:A,onChange:Q=>I(Q.target.value),disabled:p,form:C,children:[A===void 0?x.jsx("option",{value:""}):null,Array.from(W)]},ee):null]})}))};t1.displayName=li;var n1="SelectTrigger",r1=u.forwardRef((e,t)=>{const g=e,{__scopeSelect:n,disabled:r=!1}=g,o=N(g,["__scopeSelect","disabled"]),i=al(n),s=Wn(n1,n),l=s.disabled||r,a=Ke(t,s.onTriggerChange),c=ll(n),d=u.useRef("touch"),[h,p,S]=_1(w=>{const v=c().filter(E=>!E.disabled),f=v.find(E=>E.value===s.value),y=T1(v,w,f);y!==void 0&&s.onValueChange(y.value)}),C=w=>{l||(s.onOpenChange(!0),S()),w&&(s.triggerPointerDownPosRef.current={x:Math.round(w.pageX),y:Math.round(w.pageY)})};return x.jsx(mk,R(m({asChild:!0},i),{children:x.jsx(Ne.button,R(m({type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:l,"data-disabled":l?"":void 0,"data-placeholder":P1(s.value)?"":void 0},o),{ref:a,onClick:_e(o.onClick,w=>{w.currentTarget.focus(),d.current!=="mouse"&&C(w)}),onPointerDown:_e(o.onPointerDown,w=>{d.current=w.pointerType;const v=w.target;v.hasPointerCapture(w.pointerId)&&v.releasePointerCapture(w.pointerId),w.button===0&&w.ctrlKey===!1&&w.pointerType==="mouse"&&(C(w),w.preventDefault())}),onKeyDown:_e(o.onKeyDown,w=>{const v=h.current!=="";!(w.ctrlKey||w.altKey||w.metaKey)&&w.key.length===1&&p(w.key),!(v&&w.key===" ")&&Fk.includes(w.key)&&(C(),w.preventDefault())})}))}))});r1.displayName=n1;var o1="SelectValue",i1=u.forwardRef((e,t)=>{const p=e,{__scopeSelect:n,className:r,style:o,children:i,placeholder:s=""}=p,l=N(p,["__scopeSelect","className","style","children","placeholder"]),a=Wn(o1,n),{onValueNodeHasChildrenChange:c}=a,d=i!==void 0,h=Ke(t,a.onValueNodeChange);return xe(()=>{c(d)},[c,d]),x.jsx(Ne.span,R(m({},l),{ref:h,style:{pointerEvents:"none"},children:P1(a.value)?x.jsx(x.Fragment,{children:s}):i}))});i1.displayName=o1;var Wk="SelectIcon",s1=u.forwardRef((e,t)=>{const i=e,{__scopeSelect:n,children:r}=i,o=N(i,["__scopeSelect","children"]);return x.jsx(Ne.span,R(m({"aria-hidden":!0},o),{ref:t,children:r||"▼"}))});s1.displayName=Wk;var Hk="SelectPortal",l1=e=>x.jsx(Yy,m({asChild:!0},e));l1.displayName=Hk;var lr="SelectContent",a1=u.forwardRef((e,t)=>{const n=Wn(lr,e.__scopeSelect),[r,o]=u.useState();if(xe(()=>{o(new DocumentFragment)},[]),!n.open){const i=r;return i?hn.createPortal(x.jsx(u1,{scope:e.__scopeSelect,children:x.jsx(sl.Slot,{scope:e.__scopeSelect,children:x.jsx("div",{children:e.children})})}),i):null}return x.jsx(c1,R(m({},e),{ref:t}))});a1.displayName=lr;var bt=10,[u1,Hn]=io(lr),Kk="SelectContentImpl",c1=u.forwardRef((e,t)=>{const Ze=e,{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:s,side:l,sideOffset:a,align:c,alignOffset:d,arrowPadding:h,collisionBoundary:p,collisionPadding:S,sticky:C,hideWhenDetached:g,avoidCollisions:w}=Ze,v=N(Ze,["__scopeSelect","position","onCloseAutoFocus","onEscapeKeyDown","onPointerDownOutside","side","sideOffset","align","alignOffset","arrowPadding","collisionBoundary","collisionPadding","sticky","hideWhenDetached","avoidCollisions"]),f=Wn(lr,n),[y,E]=u.useState(null),[P,T]=u.useState(null),b=Ke(t,G=>E(G)),[M,A]=u.useState(null),[I,B]=u.useState(null),j=ll(n),[W,Z]=u.useState(!1),ee=u.useRef(!1);u.useEffect(()=>{if(y)return Jp(y)},[y]),oy();const Q=u.useCallback(G=>{const[ie,...Se]=j().map(re=>re.ref.current),[ne]=Se.slice(-1),te=document.activeElement;for(const re of G)if(re===te||(re==null||re.scrollIntoView({block:"nearest"}),re===ie&&P&&(P.scrollTop=0),re===ne&&P&&(P.scrollTop=P.scrollHeight),re==null||re.focus(),document.activeElement!==te))return},[j,P]),q=u.useCallback(()=>Q([M,y]),[Q,M,y]);u.useEffect(()=>{W&&q()},[W,q]);const{onOpenChange:Y,triggerPointerDownPosRef:he}=f;u.useEffect(()=>{if(y){let G={x:0,y:0};const ie=ne=>{var te,re,Ae,Ie;G={x:Math.abs(Math.round(ne.pageX)-((re=(te=he.current)==null?void 0:te.x)!=null?re:0)),y:Math.abs(Math.round(ne.pageY)-((Ie=(Ae=he.current)==null?void 0:Ae.y)!=null?Ie:0))}},Se=ne=>{G.x<=10&&G.y<=10?ne.preventDefault():y.contains(ne.target)||Y(!1),document.removeEventListener("pointermove",ie),he.current=null};return he.current!==null&&(document.addEventListener("pointermove",ie),document.addEventListener("pointerup",Se,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",ie),document.removeEventListener("pointerup",Se,{capture:!0})}}},[y,Y,he]),u.useEffect(()=>{const G=()=>Y(!1);return window.addEventListener("blur",G),window.addEventListener("resize",G),()=>{window.removeEventListener("blur",G),window.removeEventListener("resize",G)}},[Y]);const[Re,Ve]=_1(G=>{const ie=j().filter(te=>!te.disabled),Se=ie.find(te=>te.ref.current===document.activeElement),ne=T1(ie,G,Se);ne&&setTimeout(()=>ne.ref.current.focus())}),Nt=u.useCallback((G,ie,Se)=>{const ne=!ee.current&&!Se;(f.value!==void 0&&f.value===ie||ne)&&(A(G),ne&&(ee.current=!0))},[f.value]),Bt=u.useCallback(()=>y==null?void 0:y.focus(),[y]),Zt=u.useCallback((G,ie,Se)=>{const ne=!ee.current&&!Se;(f.value!==void 0&&f.value===ie||ne)&&B(G)},[f.value]),it=r==="popper"?xu:d1,Jt=it===xu?{side:l,sideOffset:a,align:c,alignOffset:d,arrowPadding:h,collisionBoundary:p,collisionPadding:S,sticky:C,hideWhenDetached:g,avoidCollisions:w}:{};return x.jsx(u1,{scope:n,content:y,viewport:P,onViewportChange:T,itemRefCallback:Nt,selectedItem:M,onItemLeave:Bt,itemTextRefCallback:Zt,focusSelectedItem:q,selectedItemText:I,position:r,isPositioned:W,searchRef:Re,children:x.jsx(e1,{as:Gy,allowPinchZoom:!0,children:x.jsx(Oy,{asChild:!0,trapped:f.open,onMountAutoFocus:G=>{G.preventDefault()},onUnmountAutoFocus:_e(o,G=>{var ie;(ie=f.trigger)==null||ie.focus({preventScroll:!0}),G.preventDefault()}),children:x.jsx(My,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:G=>G.preventDefault(),onDismiss:()=>f.onOpenChange(!1),children:x.jsx(it,R(m(m({role:"listbox",id:f.contentId,"data-state":f.open?"open":"closed",dir:f.dir,onContextMenu:G=>G.preventDefault()},v),Jt),{onPlaced:()=>Z(!0),ref:b,style:m({display:"flex",flexDirection:"column",outline:"none"},v.style),onKeyDown:_e(v.onKeyDown,G=>{const ie=G.ctrlKey||G.altKey||G.metaKey;if(G.key==="Tab"&&G.preventDefault(),!ie&&G.key.length===1&&Ve(G.key),["ArrowUp","ArrowDown","Home","End"].includes(G.key)){let ne=j().filter(te=>!te.disabled).map(te=>te.ref.current);if(["ArrowUp","End"].includes(G.key)&&(ne=ne.slice().reverse()),["ArrowUp","ArrowDown"].includes(G.key)){const te=G.target,re=ne.indexOf(te);ne=ne.slice(re+1)}setTimeout(()=>Q(ne)),G.preventDefault()}})}))})})})})});c1.displayName=Kk;var Yk="SelectItemAlignedPosition",d1=u.forwardRef((e,t)=>{const b=e,{__scopeSelect:n,onPlaced:r}=b,o=N(b,["__scopeSelect","onPlaced"]),i=Wn(lr,n),s=Hn(lr,n),[l,a]=u.useState(null),[c,d]=u.useState(null),h=Ke(t,M=>d(M)),p=ll(n),S=u.useRef(!1),C=u.useRef(!0),{viewport:g,selectedItem:w,selectedItemText:v,focusSelectedItem:f}=s,y=u.useCallback(()=>{if(i.trigger&&i.valueNode&&l&&c&&g&&w&&v){const M=i.trigger.getBoundingClientRect(),A=c.getBoundingClientRect(),I=i.valueNode.getBoundingClientRect(),B=v.getBoundingClientRect();if(i.dir!=="rtl"){const te=B.left-A.left,re=I.left-te,Ae=M.left-re,Ie=M.width+Ae,Le=Math.max(Ie,A.width),wt=window.innerWidth-bt,en=pa(re,[bt,Math.max(bt,wt-Le)]);l.style.minWidth=Ie+"px",l.style.left=en+"px"}else{const te=A.right-B.right,re=window.innerWidth-I.right-te,Ae=window.innerWidth-M.right-re,Ie=M.width+Ae,Le=Math.max(Ie,A.width),wt=window.innerWidth-bt,en=pa(re,[bt,Math.max(bt,wt-Le)]);l.style.minWidth=Ie+"px",l.style.right=en+"px"}const j=p(),W=window.innerHeight-bt*2,Z=g.scrollHeight,ee=window.getComputedStyle(c),Q=parseInt(ee.borderTopWidth,10),q=parseInt(ee.paddingTop,10),Y=parseInt(ee.borderBottomWidth,10),he=parseInt(ee.paddingBottom,10),Re=Q+q+Z+he+Y,Ve=Math.min(w.offsetHeight*5,Re),Nt=window.getComputedStyle(g),Bt=parseInt(Nt.paddingTop,10),Zt=parseInt(Nt.paddingBottom,10),it=M.top+M.height/2-bt,Jt=W-it,Ze=w.offsetHeight/2,G=w.offsetTop+Ze,ie=Q+q+G,Se=Re-ie;if(ie<=it){const te=j.length>0&&w===j[j.length-1].ref.current;l.style.bottom="0px";const re=c.clientHeight-g.offsetTop-g.offsetHeight,Ae=Math.max(Jt,Ze+(te?Zt:0)+re+Y),Ie=ie+Ae;l.style.height=Ie+"px"}else{const te=j.length>0&&w===j[0].ref.current;l.style.top="0px";const Ae=Math.max(it,Q+g.offsetTop+(te?Bt:0)+Ze)+Se;l.style.height=Ae+"px",g.scrollTop=ie-it+g.offsetTop}l.style.margin=`${bt}px 0`,l.style.minHeight=Ve+"px",l.style.maxHeight=W+"px",r==null||r(),requestAnimationFrame(()=>S.current=!0)}},[p,i.trigger,i.valueNode,l,c,g,w,v,i.dir,r]);xe(()=>y(),[y]);const[E,P]=u.useState();xe(()=>{c&&P(window.getComputedStyle(c).zIndex)},[c]);const T=u.useCallback(M=>{M&&C.current===!0&&(y(),f==null||f(),C.current=!1)},[y,f]);return x.jsx(Qk,{scope:n,contentWrapper:l,shouldExpandOnScrollRef:S,onScrollButtonChange:T,children:x.jsx("div",{ref:a,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:E},children:x.jsx(Ne.div,R(m({},o),{ref:h,style:m({boxSizing:"border-box",maxHeight:"100%"},o.style)}))})})});d1.displayName=Yk;var Gk="SelectPopperPosition",xu=u.forwardRef((e,t)=>{const l=e,{__scopeSelect:n,align:r="start",collisionPadding:o=bt}=l,i=N(l,["__scopeSelect","align","collisionPadding"]),s=al(n);return x.jsx(yk,R(m(m({},s),i),{ref:t,align:r,collisionPadding:o,style:R(m({boxSizing:"border-box"},i.style),{"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"})}))});xu.displayName=Gk;var[Qk,Hc]=io(lr,{}),Eu="SelectViewport",f1=u.forwardRef((e,t)=>{const c=e,{__scopeSelect:n,nonce:r}=c,o=N(c,["__scopeSelect","nonce"]),i=Hn(Eu,n),s=Hc(Eu,n),l=Ke(t,i.onViewportChange),a=u.useRef(0);return x.jsxs(x.Fragment,{children:[x.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),x.jsx(sl.Slot,{scope:n,children:x.jsx(Ne.div,R(m({"data-radix-select-viewport":"",role:"presentation"},o),{ref:l,style:m({position:"relative",flex:1,overflow:"hidden auto"},o.style),onScroll:_e(o.onScroll,d=>{const h=d.currentTarget,{contentWrapper:p,shouldExpandOnScrollRef:S}=s;if(S!=null&&S.current&&p){const C=Math.abs(a.current-h.scrollTop);if(C>0){const g=window.innerHeight-bt*2,w=parseFloat(p.style.minHeight),v=parseFloat(p.style.height),f=Math.max(w,v);if(f<g){const y=f+C,E=Math.min(g,y),P=y-E;p.style.height=E+"px",p.style.bottom==="0px"&&(h.scrollTop=P>0?P:0,p.style.justifyContent="flex-end")}}}a.current=h.scrollTop})}))})]})});f1.displayName=Eu;var p1="SelectGroup",[qk,Xk]=io(p1),Zk=u.forwardRef((e,t)=>{const i=e,{__scopeSelect:n}=i,r=N(i,["__scopeSelect"]),o=jt();return x.jsx(qk,{scope:n,id:o,children:x.jsx(Ne.div,R(m({role:"group","aria-labelledby":o},r),{ref:t}))})});Zk.displayName=p1;var h1="SelectLabel",v1=u.forwardRef((e,t)=>{const i=e,{__scopeSelect:n}=i,r=N(i,["__scopeSelect"]),o=Xk(h1,n);return x.jsx(Ne.div,R(m({id:o.id},r),{ref:t}))});v1.displayName=h1;var ks="SelectItem",[Jk,m1]=io(ks),y1=u.forwardRef((e,t)=>{const f=e,{__scopeSelect:n,value:r,disabled:o=!1,textValue:i}=f,s=N(f,["__scopeSelect","value","disabled","textValue"]),l=Wn(ks,n),a=Hn(ks,n),c=l.value===r,[d,h]=u.useState(i!=null?i:""),[p,S]=u.useState(!1),C=Ke(t,y=>{var E;return(E=a.itemRefCallback)==null?void 0:E.call(a,y,r,o)}),g=jt(),w=u.useRef("touch"),v=()=>{o||(l.onValueChange(r),l.onOpenChange(!1))};if(r==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return x.jsx(Jk,{scope:n,value:r,disabled:o,textId:g,isSelected:c,onItemTextChange:u.useCallback(y=>{h(E=>{var P;return E||((P=y==null?void 0:y.textContent)!=null?P:"").trim()})},[]),children:x.jsx(sl.ItemSlot,{scope:n,value:r,disabled:o,textValue:d,children:x.jsx(Ne.div,R(m({role:"option","aria-labelledby":g,"data-highlighted":p?"":void 0,"aria-selected":c&&p,"data-state":c?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1},s),{ref:C,onFocus:_e(s.onFocus,()=>S(!0)),onBlur:_e(s.onBlur,()=>S(!1)),onClick:_e(s.onClick,()=>{w.current!=="mouse"&&v()}),onPointerUp:_e(s.onPointerUp,()=>{w.current==="mouse"&&v()}),onPointerDown:_e(s.onPointerDown,y=>{w.current=y.pointerType}),onPointerMove:_e(s.onPointerMove,y=>{var E;w.current=y.pointerType,o?(E=a.onItemLeave)==null||E.call(a):w.current==="mouse"&&y.currentTarget.focus({preventScroll:!0})}),onPointerLeave:_e(s.onPointerLeave,y=>{var E;y.currentTarget===document.activeElement&&((E=a.onItemLeave)==null||E.call(a))}),onKeyDown:_e(s.onKeyDown,y=>{var P;((P=a.searchRef)==null?void 0:P.current)!==""&&y.key===" "||(jk.includes(y.key)&&v(),y.key===" "&&y.preventDefault())})}))})})});y1.displayName=ks;var Eo="SelectItemText",g1=u.forwardRef((e,t)=>{const v=e,{__scopeSelect:n,className:r,style:o}=v,i=N(v,["__scopeSelect","className","style"]),s=Wn(Eo,n),l=Hn(Eo,n),a=m1(Eo,n),c=Bk(Eo,n),[d,h]=u.useState(null),p=Ke(t,f=>h(f),a.onItemTextChange,f=>{var y;return(y=l.itemTextRefCallback)==null?void 0:y.call(l,f,a.value,a.disabled)}),S=d==null?void 0:d.textContent,C=u.useMemo(()=>x.jsx("option",{value:a.value,disabled:a.disabled,children:S},a.value),[a.disabled,a.value,S]),{onNativeOptionAdd:g,onNativeOptionRemove:w}=c;return xe(()=>(g(C),()=>w(C)),[g,w,C]),x.jsxs(x.Fragment,{children:[x.jsx(Ne.span,R(m({id:a.textId},i),{ref:p})),a.isSelected&&s.valueNode&&!s.valueNodeHasChildren?hn.createPortal(i.children,s.valueNode):null]})});g1.displayName=Eo;var w1="SelectItemIndicator",S1=u.forwardRef((e,t)=>{const i=e,{__scopeSelect:n}=i,r=N(i,["__scopeSelect"]);return m1(w1,n).isSelected?x.jsx(Ne.span,R(m({"aria-hidden":!0},r),{ref:t})):null});S1.displayName=w1;var ku="SelectScrollUpButton",C1=u.forwardRef((e,t)=>{const n=Hn(ku,e.__scopeSelect),r=Hc(ku,e.__scopeSelect),[o,i]=u.useState(!1),s=Ke(t,r.onScrollButtonChange);return xe(()=>{if(n.viewport&&n.isPositioned){let l=function(){const c=a.scrollTop>0;i(c)};const a=n.viewport;return l(),a.addEventListener("scroll",l),()=>a.removeEventListener("scroll",l)}},[n.viewport,n.isPositioned]),o?x.jsx(E1,R(m({},e),{ref:s,onAutoScroll:()=>{const{viewport:l,selectedItem:a}=n;l&&a&&(l.scrollTop=l.scrollTop-a.offsetHeight)}})):null});C1.displayName=ku;var Pu="SelectScrollDownButton",x1=u.forwardRef((e,t)=>{const n=Hn(Pu,e.__scopeSelect),r=Hc(Pu,e.__scopeSelect),[o,i]=u.useState(!1),s=Ke(t,r.onScrollButtonChange);return xe(()=>{if(n.viewport&&n.isPositioned){let l=function(){const c=a.scrollHeight-a.clientHeight,d=Math.ceil(a.scrollTop)<c;i(d)};const a=n.viewport;return l(),a.addEventListener("scroll",l),()=>a.removeEventListener("scroll",l)}},[n.viewport,n.isPositioned]),o?x.jsx(E1,R(m({},e),{ref:s,onAutoScroll:()=>{const{viewport:l,selectedItem:a}=n;l&&a&&(l.scrollTop=l.scrollTop+a.offsetHeight)}})):null});x1.displayName=Pu;var E1=u.forwardRef((e,t)=>{const c=e,{__scopeSelect:n,onAutoScroll:r}=c,o=N(c,["__scopeSelect","onAutoScroll"]),i=Hn("SelectScrollButton",n),s=u.useRef(null),l=ll(n),a=u.useCallback(()=>{s.current!==null&&(window.clearInterval(s.current),s.current=null)},[]);return u.useEffect(()=>()=>a(),[a]),xe(()=>{var h;const d=l().find(p=>p.ref.current===document.activeElement);(h=d==null?void 0:d.ref.current)==null||h.scrollIntoView({block:"nearest"})},[l]),x.jsx(Ne.div,R(m({"aria-hidden":!0},o),{ref:t,style:m({flexShrink:0},o.style),onPointerDown:_e(o.onPointerDown,()=>{s.current===null&&(s.current=window.setInterval(r,50))}),onPointerMove:_e(o.onPointerMove,()=>{var d;(d=i.onItemLeave)==null||d.call(i),s.current===null&&(s.current=window.setInterval(r,50))}),onPointerLeave:_e(o.onPointerLeave,()=>{a()})}))}),eP="SelectSeparator",k1=u.forwardRef((e,t)=>{const o=e,{__scopeSelect:n}=o,r=N(o,["__scopeSelect"]);return x.jsx(Ne.div,R(m({"aria-hidden":!0},r),{ref:t}))});k1.displayName=eP;var Ru="SelectArrow",tP=u.forwardRef((e,t)=>{const l=e,{__scopeSelect:n}=l,r=N(l,["__scopeSelect"]),o=al(n),i=Wn(Ru,n),s=Hn(Ru,n);return i.open&&s.position==="popper"?x.jsx(gk,R(m(m({},o),r),{ref:t})):null});tP.displayName=Ru;function P1(e){return e===""||e===void 0}var R1=u.forwardRef((e,t)=>{const l=e,{value:n}=l,r=N(l,["value"]),o=u.useRef(null),i=Ke(t,o),s=Wc(n);return u.useEffect(()=>{const a=o.current,c=window.HTMLSelectElement.prototype,h=Object.getOwnPropertyDescriptor(c,"value").set;if(s!==n&&h){const p=new Event("change",{bubbles:!0});h.call(a,n),a.dispatchEvent(p)}},[s,n]),x.jsx(Qy,{asChild:!0,children:x.jsx("select",R(m({},r),{ref:i,defaultValue:n}))})});R1.displayName="BubbleSelect";function _1(e){const t=fe(e),n=u.useRef(""),r=u.useRef(0),o=u.useCallback(s=>{const l=n.current+s;t(l),function a(c){n.current=c,window.clearTimeout(r.current),c!==""&&(r.current=window.setTimeout(()=>a(""),1e3))}(l)},[t]),i=u.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return u.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,i]}function T1(e,t,n){const o=t.length>1&&Array.from(t).every(c=>c===t[0])?t[0]:t,i=n?e.indexOf(n):-1;let s=nP(e,Math.max(i,0));o.length===1&&(s=s.filter(c=>c!==n));const a=s.find(c=>c.textValue.toLowerCase().startsWith(o.toLowerCase()));return a!==n?a:void 0}function nP(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var fA=t1,pA=r1,hA=i1,vA=s1,mA=l1,yA=a1,gA=f1,wA=v1,SA=y1,CA=g1,xA=S1,EA=C1,kA=x1,PA=k1;function rP(o,r){var i=o,{title:e,titleId:t}=i,n=N(i,["title","titleId"]);return u.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},n),e?u.createElement("title",{id:t},e):null,u.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))}const RA=u.forwardRef(rP);function oP(o,r){var i=o,{title:e,titleId:t}=i,n=N(i,["title","titleId"]);return u.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},n),e?u.createElement("title",{id:t},e):null,u.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 12h16.5m-16.5 3.75h16.5M3.75 19.5h16.5M5.625 4.5h12.75a1.875 1.875 0 0 1 0 3.75H5.625a1.875 1.875 0 0 1 0-3.75Z"}))}const _A=u.forwardRef(oP);function iP(o,r){var i=o,{title:e,titleId:t}=i,n=N(i,["title","titleId"]);return u.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},n),e?u.createElement("title",{id:t},e):null,u.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.288 15.038a5.25 5.25 0 0 1 7.424 0M5.106 11.856c3.807-3.808 9.98-3.808 13.788 0M1.924 8.674c5.565-5.565 14.587-5.565 20.152 0M12.53 18.22l-.53.53-.53-.53a.75.75 0 0 1 1.06 0Z"}))}const TA=u.forwardRef(iP);var sP=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],ai=sP.reduce((e,t)=>{const n=u.forwardRef((r,o)=>{const a=r,{asChild:i}=a,s=N(a,["asChild"]),l=i?Oe:t;return typeof window!="undefined"&&(window[Symbol.for("radix-ui")]=!0),x.jsx(l,R(m({},s),{ref:o}))});return n.displayName=`Primitive.${t}`,R(m({},e),{[t]:n})},{});function gp(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function lP(...e){return t=>{let n=!1;const r=e.map(o=>{const i=gp(o,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let o=0;o<r.length;o++){const i=r[o];typeof i=="function"?i():gp(e[o],null)}}}}function so(...e){return u.useCallback(lP(...e),e)}function er(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function aP(e,t){return u.useReducer((n,r)=>{const o=t[n][r];return o!=null?o:n},e)}var Kc="ScrollArea",[N1,NA]=Ye(Kc),[uP,Tt]=N1(Kc),A1=u.forwardRef((e,t)=>{const B=e,{__scopeScrollArea:n,type:r="hover",dir:o,scrollHideDelay:i=600}=B,s=N(B,["__scopeScrollArea","type","dir","scrollHideDelay"]),[l,a]=u.useState(null),[c,d]=u.useState(null),[h,p]=u.useState(null),[S,C]=u.useState(null),[g,w]=u.useState(null),[v,f]=u.useState(0),[y,E]=u.useState(0),[P,T]=u.useState(!1),[b,M]=u.useState(!1),A=so(t,j=>a(j)),I=oo(o);return x.jsx(uP,{scope:n,type:r,dir:I,scrollHideDelay:i,scrollArea:l,viewport:c,onViewportChange:d,content:h,onContentChange:p,scrollbarX:S,onScrollbarXChange:C,scrollbarXEnabled:P,onScrollbarXEnabledChange:T,scrollbarY:g,onScrollbarYChange:w,scrollbarYEnabled:b,onScrollbarYEnabledChange:M,onCornerWidthChange:f,onCornerHeightChange:E,children:x.jsx(ai.div,R(m({dir:I},s),{ref:A,style:m({position:"relative","--radix-scroll-area-corner-width":v+"px","--radix-scroll-area-corner-height":y+"px"},e.style)}))})});A1.displayName=Kc;var b1="ScrollAreaViewport",M1=u.forwardRef((e,t)=>{const c=e,{__scopeScrollArea:n,children:r,nonce:o}=c,i=N(c,["__scopeScrollArea","children","nonce"]),s=Tt(b1,n),l=u.useRef(null),a=so(t,l,s.onViewportChange);return x.jsxs(x.Fragment,{children:[x.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),x.jsx(ai.div,R(m({"data-radix-scroll-area-viewport":""},i),{ref:a,style:m({overflowX:s.scrollbarXEnabled?"scroll":"hidden",overflowY:s.scrollbarYEnabled?"scroll":"hidden"},e.style),children:x.jsx("div",{ref:s.onContentChange,style:{minWidth:"100%",display:"table"},children:r})}))]})});M1.displayName=b1;var Xt="ScrollAreaScrollbar",cP=u.forwardRef((e,t)=>{const a=e,{forceMount:n}=a,r=N(a,["forceMount"]),o=Tt(Xt,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:s}=o,l=e.orientation==="horizontal";return u.useEffect(()=>(l?i(!0):s(!0),()=>{l?i(!1):s(!1)}),[l,i,s]),o.type==="hover"?x.jsx(dP,R(m({},r),{ref:t,forceMount:n})):o.type==="scroll"?x.jsx(fP,R(m({},r),{ref:t,forceMount:n})):o.type==="auto"?x.jsx(D1,R(m({},r),{ref:t,forceMount:n})):o.type==="always"?x.jsx(Yc,R(m({},r),{ref:t})):null});cP.displayName=Xt;var dP=u.forwardRef((e,t)=>{const l=e,{forceMount:n}=l,r=N(l,["forceMount"]),o=Tt(Xt,e.__scopeScrollArea),[i,s]=u.useState(!1);return u.useEffect(()=>{const a=o.scrollArea;let c=0;if(a){const d=()=>{window.clearTimeout(c),s(!0)},h=()=>{c=window.setTimeout(()=>s(!1),o.scrollHideDelay)};return a.addEventListener("pointerenter",d),a.addEventListener("pointerleave",h),()=>{window.clearTimeout(c),a.removeEventListener("pointerenter",d),a.removeEventListener("pointerleave",h)}}},[o.scrollArea,o.scrollHideDelay]),x.jsx(_t,{present:n||i,children:x.jsx(D1,R(m({"data-state":i?"visible":"hidden"},r),{ref:t}))})}),fP=u.forwardRef((e,t)=>{const c=e,{forceMount:n}=c,r=N(c,["forceMount"]),o=Tt(Xt,e.__scopeScrollArea),i=e.orientation==="horizontal",s=cl(()=>a("SCROLL_END"),100),[l,a]=aP("hidden",{hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}});return u.useEffect(()=>{if(l==="idle"){const d=window.setTimeout(()=>a("HIDE"),o.scrollHideDelay);return()=>window.clearTimeout(d)}},[l,o.scrollHideDelay,a]),u.useEffect(()=>{const d=o.viewport,h=i?"scrollLeft":"scrollTop";if(d){let p=d[h];const S=()=>{const C=d[h];p!==C&&(a("SCROLL"),s()),p=C};return d.addEventListener("scroll",S),()=>d.removeEventListener("scroll",S)}},[o.viewport,i,a,s]),x.jsx(_t,{present:n||l!=="hidden",children:x.jsx(Yc,R(m({"data-state":l==="hidden"?"hidden":"visible"},r),{ref:t,onPointerEnter:er(e.onPointerEnter,()=>a("POINTER_ENTER")),onPointerLeave:er(e.onPointerLeave,()=>a("POINTER_LEAVE"))}))})}),D1=u.forwardRef((e,t)=>{const n=Tt(Xt,e.__scopeScrollArea),c=e,{forceMount:r}=c,o=N(c,["forceMount"]),[i,s]=u.useState(!1),l=e.orientation==="horizontal",a=cl(()=>{if(n.viewport){const d=n.viewport.offsetWidth<n.viewport.scrollWidth,h=n.viewport.offsetHeight<n.viewport.scrollHeight;s(l?d:h)}},10);return Xr(n.viewport,a),Xr(n.content,a),x.jsx(_t,{present:r||i,children:x.jsx(Yc,R(m({"data-state":i?"visible":"hidden"},o),{ref:t}))})}),Yc=u.forwardRef((e,t)=>{const p=e,{orientation:n="vertical"}=p,r=N(p,["orientation"]),o=Tt(Xt,e.__scopeScrollArea),i=u.useRef(null),s=u.useRef(0),[l,a]=u.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),c=$1(l.viewport,l.content),d=R(m({},r),{sizes:l,onSizesChange:a,hasThumb:c>0&&c<1,onThumbChange:S=>i.current=S,onThumbPointerUp:()=>s.current=0,onThumbPointerDown:S=>s.current=S});function h(S,C){return wP(S,s.current,l,C)}return n==="horizontal"?x.jsx(pP,R(m({},d),{ref:t,onThumbPositionChange:()=>{if(o.viewport&&i.current){const S=o.viewport.scrollLeft,C=wp(S,l,o.dir);i.current.style.transform=`translate3d(${C}px, 0, 0)`}},onWheelScroll:S=>{o.viewport&&(o.viewport.scrollLeft=S)},onDragScroll:S=>{o.viewport&&(o.viewport.scrollLeft=h(S,o.dir))}})):n==="vertical"?x.jsx(hP,R(m({},d),{ref:t,onThumbPositionChange:()=>{if(o.viewport&&i.current){const S=o.viewport.scrollTop,C=wp(S,l);i.current.style.transform=`translate3d(0, ${C}px, 0)`}},onWheelScroll:S=>{o.viewport&&(o.viewport.scrollTop=S)},onDragScroll:S=>{o.viewport&&(o.viewport.scrollTop=h(S))}})):null}),pP=u.forwardRef((e,t)=>{const d=e,{sizes:n,onSizesChange:r}=d,o=N(d,["sizes","onSizesChange"]),i=Tt(Xt,e.__scopeScrollArea),[s,l]=u.useState(),a=u.useRef(null),c=so(t,a,i.onScrollbarXChange);return u.useEffect(()=>{a.current&&l(getComputedStyle(a.current))},[a]),x.jsx(I1,R(m({"data-orientation":"horizontal"},o),{ref:c,sizes:n,style:m({bottom:0,left:i.dir==="rtl"?"var(--radix-scroll-area-corner-width)":0,right:i.dir==="ltr"?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":ul(n)+"px"},e.style),onThumbPointerDown:h=>e.onThumbPointerDown(h.x),onDragScroll:h=>e.onDragScroll(h.x),onWheelScroll:(h,p)=>{if(i.viewport){const S=i.viewport.scrollLeft+h.deltaX;e.onWheelScroll(S),j1(S,p)&&h.preventDefault()}},onResize:()=>{a.current&&i.viewport&&s&&r({content:i.viewport.scrollWidth,viewport:i.viewport.offsetWidth,scrollbar:{size:a.current.clientWidth,paddingStart:Rs(s.paddingLeft),paddingEnd:Rs(s.paddingRight)}})}}))}),hP=u.forwardRef((e,t)=>{const d=e,{sizes:n,onSizesChange:r}=d,o=N(d,["sizes","onSizesChange"]),i=Tt(Xt,e.__scopeScrollArea),[s,l]=u.useState(),a=u.useRef(null),c=so(t,a,i.onScrollbarYChange);return u.useEffect(()=>{a.current&&l(getComputedStyle(a.current))},[a]),x.jsx(I1,R(m({"data-orientation":"vertical"},o),{ref:c,sizes:n,style:m({top:0,right:i.dir==="ltr"?0:void 0,left:i.dir==="rtl"?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":ul(n)+"px"},e.style),onThumbPointerDown:h=>e.onThumbPointerDown(h.y),onDragScroll:h=>e.onDragScroll(h.y),onWheelScroll:(h,p)=>{if(i.viewport){const S=i.viewport.scrollTop+h.deltaY;e.onWheelScroll(S),j1(S,p)&&h.preventDefault()}},onResize:()=>{a.current&&i.viewport&&s&&r({content:i.viewport.scrollHeight,viewport:i.viewport.offsetHeight,scrollbar:{size:a.current.clientHeight,paddingStart:Rs(s.paddingTop),paddingEnd:Rs(s.paddingBottom)}})}}))}),[vP,O1]=N1(Xt),I1=u.forwardRef((e,t)=>{const A=e,{__scopeScrollArea:n,sizes:r,hasThumb:o,onThumbChange:i,onThumbPointerUp:s,onThumbPointerDown:l,onThumbPositionChange:a,onDragScroll:c,onWheelScroll:d,onResize:h}=A,p=N(A,["__scopeScrollArea","sizes","hasThumb","onThumbChange","onThumbPointerUp","onThumbPointerDown","onThumbPositionChange","onDragScroll","onWheelScroll","onResize"]),S=Tt(Xt,n),[C,g]=u.useState(null),w=so(t,I=>g(I)),v=u.useRef(null),f=u.useRef(""),y=S.viewport,E=r.content-r.viewport,P=fe(d),T=fe(a),b=cl(h,10);function M(I){if(v.current){const B=I.clientX-v.current.left,j=I.clientY-v.current.top;c({x:B,y:j})}}return u.useEffect(()=>{const I=B=>{const j=B.target;(C==null?void 0:C.contains(j))&&P(B,E)};return document.addEventListener("wheel",I,{passive:!1}),()=>document.removeEventListener("wheel",I,{passive:!1})},[y,C,E,P]),u.useEffect(T,[r,T]),Xr(C,b),Xr(S.content,b),x.jsx(vP,{scope:n,scrollbar:C,hasThumb:o,onThumbChange:fe(i),onThumbPointerUp:fe(s),onThumbPositionChange:T,onThumbPointerDown:fe(l),children:x.jsx(ai.div,R(m({},p),{ref:w,style:m({position:"absolute"},p.style),onPointerDown:er(e.onPointerDown,I=>{I.button===0&&(I.target.setPointerCapture(I.pointerId),v.current=C.getBoundingClientRect(),f.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",S.viewport&&(S.viewport.style.scrollBehavior="auto"),M(I))}),onPointerMove:er(e.onPointerMove,M),onPointerUp:er(e.onPointerUp,I=>{const B=I.target;B.hasPointerCapture(I.pointerId)&&B.releasePointerCapture(I.pointerId),document.body.style.webkitUserSelect=f.current,S.viewport&&(S.viewport.style.scrollBehavior=""),v.current=null})}))})}),Ps="ScrollAreaThumb",mP=u.forwardRef((e,t)=>{const i=e,{forceMount:n}=i,r=N(i,["forceMount"]),o=O1(Ps,e.__scopeScrollArea);return x.jsx(_t,{present:n||o.hasThumb,children:x.jsx(yP,m({ref:t},r))})}),yP=u.forwardRef((e,t)=>{const h=e,{__scopeScrollArea:n,style:r}=h,o=N(h,["__scopeScrollArea","style"]),i=Tt(Ps,n),s=O1(Ps,n),{onThumbPositionChange:l}=s,a=so(t,p=>s.onThumbChange(p)),c=u.useRef(void 0),d=cl(()=>{c.current&&(c.current(),c.current=void 0)},100);return u.useEffect(()=>{const p=i.viewport;if(p){const S=()=>{if(d(),!c.current){const C=SP(p,l);c.current=C,l()}};return l(),p.addEventListener("scroll",S),()=>p.removeEventListener("scroll",S)}},[i.viewport,d,l]),x.jsx(ai.div,R(m({"data-state":s.hasThumb?"visible":"hidden"},o),{ref:a,style:m({width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)"},r),onPointerDownCapture:er(e.onPointerDownCapture,p=>{const C=p.target.getBoundingClientRect(),g=p.clientX-C.left,w=p.clientY-C.top;s.onThumbPointerDown({x:g,y:w})}),onPointerUp:er(e.onPointerUp,s.onThumbPointerUp)}))});mP.displayName=Ps;var Gc="ScrollAreaCorner",L1=u.forwardRef((e,t)=>{const n=Tt(Gc,e.__scopeScrollArea),r=!!(n.scrollbarX&&n.scrollbarY);return n.type!=="scroll"&&r?x.jsx(gP,R(m({},e),{ref:t})):null});L1.displayName=Gc;var gP=u.forwardRef((e,t)=>{const d=e,{__scopeScrollArea:n}=d,r=N(d,["__scopeScrollArea"]),o=Tt(Gc,n),[i,s]=u.useState(0),[l,a]=u.useState(0),c=!!(i&&l);return Xr(o.scrollbarX,()=>{var p;const h=((p=o.scrollbarX)==null?void 0:p.offsetHeight)||0;o.onCornerHeightChange(h),a(h)}),Xr(o.scrollbarY,()=>{var p;const h=((p=o.scrollbarY)==null?void 0:p.offsetWidth)||0;o.onCornerWidthChange(h),s(h)}),c?x.jsx(ai.div,R(m({},r),{ref:t,style:m({width:i,height:l,position:"absolute",right:o.dir==="ltr"?0:void 0,left:o.dir==="rtl"?0:void 0,bottom:0},e.style)})):null});function Rs(e){return e?parseInt(e,10):0}function $1(e,t){const n=e/t;return isNaN(n)?0:n}function ul(e){const t=$1(e.viewport,e.content),n=e.scrollbar.paddingStart+e.scrollbar.paddingEnd,r=(e.scrollbar.size-n)*t;return Math.max(r,18)}function wP(e,t,n,r="ltr"){const o=ul(n),i=o/2,s=t||i,l=o-s,a=n.scrollbar.paddingStart+s,c=n.scrollbar.size-n.scrollbar.paddingEnd-l,d=n.content-n.viewport,h=r==="ltr"?[0,d]:[d*-1,0];return F1([a,c],h)(e)}function wp(e,t,n="ltr"){const r=ul(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,i=t.scrollbar.size-o,s=t.content-t.viewport,l=i-r,a=n==="ltr"?[0,s]:[s*-1,0],c=pa(e,a);return F1([0,s],[0,l])(c)}function F1(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];const r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}function j1(e,t){return e>0&&e<t}var SP=(e,t=()=>{})=>{let n={left:e.scrollLeft,top:e.scrollTop},r=0;return function o(){const i={left:e.scrollLeft,top:e.scrollTop},s=n.left!==i.left,l=n.top!==i.top;(s||l)&&t(),n=i,r=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(r)};function cl(e,t){const n=fe(e),r=u.useRef(0);return u.useEffect(()=>()=>window.clearTimeout(r.current),[]),u.useCallback(()=>{window.clearTimeout(r.current),r.current=window.setTimeout(n,t)},[n,t])}function Xr(e,t){const n=fe(t);xe(()=>{let r=0;if(e){const o=new ResizeObserver(()=>{cancelAnimationFrame(r),r=window.requestAnimationFrame(n)});return o.observe(e),()=>{window.cancelAnimationFrame(r),o.unobserve(e)}}},[e,n])}var AA=A1,bA=M1,MA=L1;function DA(e){const t=m({},e);Zg(t,"react"),Jg(t)}var V1={exports:{}},ae={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var je=typeof Symbol=="function"&&Symbol.for,Qc=je?Symbol.for("react.element"):60103,qc=je?Symbol.for("react.portal"):60106,dl=je?Symbol.for("react.fragment"):60107,fl=je?Symbol.for("react.strict_mode"):60108,pl=je?Symbol.for("react.profiler"):60114,hl=je?Symbol.for("react.provider"):60109,vl=je?Symbol.for("react.context"):60110,Xc=je?Symbol.for("react.async_mode"):60111,ml=je?Symbol.for("react.concurrent_mode"):60111,yl=je?Symbol.for("react.forward_ref"):60112,gl=je?Symbol.for("react.suspense"):60113,CP=je?Symbol.for("react.suspense_list"):60120,wl=je?Symbol.for("react.memo"):60115,Sl=je?Symbol.for("react.lazy"):60116,xP=je?Symbol.for("react.block"):60121,EP=je?Symbol.for("react.fundamental"):60117,kP=je?Symbol.for("react.responder"):60118,PP=je?Symbol.for("react.scope"):60119;function gt(e){if(typeof e=="object"&&e!==null){var t=e.$$typeof;switch(t){case Qc:switch(e=e.type,e){case Xc:case ml:case dl:case pl:case fl:case gl:return e;default:switch(e=e&&e.$$typeof,e){case vl:case yl:case Sl:case wl:case hl:return e;default:return t}}case qc:return t}}}function z1(e){return gt(e)===ml}ae.AsyncMode=Xc;ae.ConcurrentMode=ml;ae.ContextConsumer=vl;ae.ContextProvider=hl;ae.Element=Qc;ae.ForwardRef=yl;ae.Fragment=dl;ae.Lazy=Sl;ae.Memo=wl;ae.Portal=qc;ae.Profiler=pl;ae.StrictMode=fl;ae.Suspense=gl;ae.isAsyncMode=function(e){return z1(e)||gt(e)===Xc};ae.isConcurrentMode=z1;ae.isContextConsumer=function(e){return gt(e)===vl};ae.isContextProvider=function(e){return gt(e)===hl};ae.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===Qc};ae.isForwardRef=function(e){return gt(e)===yl};ae.isFragment=function(e){return gt(e)===dl};ae.isLazy=function(e){return gt(e)===Sl};ae.isMemo=function(e){return gt(e)===wl};ae.isPortal=function(e){return gt(e)===qc};ae.isProfiler=function(e){return gt(e)===pl};ae.isStrictMode=function(e){return gt(e)===fl};ae.isSuspense=function(e){return gt(e)===gl};ae.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===dl||e===ml||e===pl||e===fl||e===gl||e===CP||typeof e=="object"&&e!==null&&(e.$$typeof===Sl||e.$$typeof===wl||e.$$typeof===hl||e.$$typeof===vl||e.$$typeof===yl||e.$$typeof===EP||e.$$typeof===kP||e.$$typeof===PP||e.$$typeof===xP)};ae.typeOf=gt;V1.exports=ae;var RP=V1.exports,U1=RP,_P={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},TP={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},B1={};B1[U1.ForwardRef]=_P;B1[U1.Memo]=TP;const NP=typeof __SENTRY_DEBUG__=="undefined"||__SENTRY_DEBUG__;function AP(e){const t=e.match(/^([^.]+)/);return t!==null&&parseInt(t[0])>=17}const Sp={componentStack:null,error:null,eventId:null};function bP(e,t){const n=new WeakMap;function r(o,i){if(!n.has(o)){if(o.cause)return n.set(o,!0),r(o.cause,i);o.cause=i}}r(e,t)}class W1 extends u.Component{constructor(t){super(t),W1.prototype.__init.call(this),this.state=Sp,this._openFallbackReportDialog=!0;const n=ew();n&&n.on&&t.showDialog&&(this._openFallbackReportDialog=!1,n.on("afterSendEvent",r=>{!r.type&&r.event_id===this._lastEventId&&bd(R(m({},t.dialogOptions),{eventId:this._lastEventId}))}))}componentDidCatch(t,{componentStack:n}){const{beforeCapture:r,onError:o,showDialog:i,dialogOptions:s}=this.props;tw(l=>{if(AP(u.version)&&rw(t)){const c=new Error(t.message);c.name=`React ErrorBoundary ${t.name}`,c.stack=n,bP(t,c)}r&&r(l,t,n);const a=ow(t,{captureContext:{contexts:{react:{componentStack:n}}},mechanism:{handled:!!this.props.fallback}});o&&o(t,n,a),i&&(this._lastEventId=a,this._openFallbackReportDialog&&bd(R(m({},s),{eventId:a}))),this.setState({error:t,componentStack:n,eventId:a})})}componentDidMount(){const{onMount:t}=this.props;t&&t()}componentWillUnmount(){const{error:t,componentStack:n,eventId:r}=this.state,{onUnmount:o}=this.props;o&&o(t,n,r)}__init(){this.resetErrorBoundary=()=>{const{onReset:t}=this.props,{error:n,componentStack:r,eventId:o}=this.state;t&&t(n,r,o),this.setState(Sp)}}render(){const{fallback:t,children:n}=this.props,r=this.state;if(r.error){let o;return typeof t=="function"?o=t({error:r.error,componentStack:r.componentStack,resetError:this.resetErrorBoundary,eventId:r.eventId}):o=t,u.isValidElement(o)?o:(t&&NP&&nw.warn("fallback did not produce a valid ReactElement"),null)}return typeof n=="function"?n():n}}var MP=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],Zc=MP.reduce((e,t)=>{const n=u.forwardRef((r,o)=>{const a=r,{asChild:i}=a,s=N(a,["asChild"]),l=i?Oe:t;return typeof window!="undefined"&&(window[Symbol.for("radix-ui")]=!0),x.jsx(l,R(m({},s),{ref:o}))});return n.displayName=`Primitive.${t}`,R(m({},e),{[t]:n})},{}),Jc="Avatar",[DP,OA]=Ye(Jc),[OP,H1]=DP(Jc),K1=u.forwardRef((e,t)=>{const s=e,{__scopeAvatar:n}=s,r=N(s,["__scopeAvatar"]),[o,i]=u.useState("idle");return x.jsx(OP,{scope:n,imageLoadingStatus:o,onImageLoadingStatusChange:i,children:x.jsx(Zc.span,R(m({},r),{ref:t}))})});K1.displayName=Jc;var Y1="AvatarImage",G1=u.forwardRef((e,t)=>{const c=e,{__scopeAvatar:n,src:r,onLoadingStatusChange:o=()=>{}}=c,i=N(c,["__scopeAvatar","src","onLoadingStatusChange"]),s=H1(Y1,n),l=IP(r,i.referrerPolicy),a=fe(d=>{o(d),s.onImageLoadingStatusChange(d)});return xe(()=>{l!=="idle"&&a(l)},[l,a]),l==="loaded"?x.jsx(Zc.img,R(m({},i),{ref:t,src:r})):null});G1.displayName=Y1;var Q1="AvatarFallback",q1=u.forwardRef((e,t)=>{const a=e,{__scopeAvatar:n,delayMs:r}=a,o=N(a,["__scopeAvatar","delayMs"]),i=H1(Q1,n),[s,l]=u.useState(r===void 0);return u.useEffect(()=>{if(r!==void 0){const c=window.setTimeout(()=>l(!0),r);return()=>window.clearTimeout(c)}},[r]),s&&i.imageLoadingStatus!=="loaded"?x.jsx(Zc.span,R(m({},o),{ref:t})):null});q1.displayName=Q1;function IP(e,t){const[n,r]=u.useState("idle");return xe(()=>{if(!e){r("error");return}let o=!0;const i=new window.Image,s=l=>()=>{o&&r(l)};return r("loading"),i.onload=s("loaded"),i.onerror=s("error"),i.src=e,t&&(i.referrerPolicy=t),()=>{o=!1}},[e,t]),n}var IA=K1,LA=G1,$A=q1,LP=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],X1=LP.reduce((e,t)=>{const n=u.forwardRef((r,o)=>{const a=r,{asChild:i}=a,s=N(a,["asChild"]),l=i?Oe:t;return typeof window!="undefined"&&(window[Symbol.for("radix-ui")]=!0),x.jsx(l,R(m({},s),{ref:o}))});return n.displayName=`Primitive.${t}`,R(m({},e),{[t]:n})},{}),ed="Progress",td=100,[$P,FA]=Ye(ed),[FP,jP]=$P(ed),Z1=u.forwardRef((e,t)=>{const d=e,{__scopeProgress:n,value:r=null,max:o,getValueLabel:i=VP}=d,s=N(d,["__scopeProgress","value","max","getValueLabel"]);(o||o===0)&&!Cp(o)&&console.error(zP(`${o}`,"Progress"));const l=Cp(o)?o:td;r!==null&&!xp(r,l)&&console.error(UP(`${r}`,"Progress"));const a=xp(r,l)?r:null,c=_s(a)?i(a,l):void 0;return x.jsx(FP,{scope:n,value:a,max:l,children:x.jsx(X1.div,R(m({"aria-valuemax":l,"aria-valuemin":0,"aria-valuenow":_s(a)?a:void 0,"aria-valuetext":c,role:"progressbar","data-state":t0(a,l),"data-value":a!=null?a:void 0,"data-max":l},s),{ref:t}))})});Z1.displayName=ed;var J1="ProgressIndicator",e0=u.forwardRef((e,t)=>{var s;const i=e,{__scopeProgress:n}=i,r=N(i,["__scopeProgress"]),o=jP(J1,n);return x.jsx(X1.div,R(m({"data-state":t0(o.value,o.max),"data-value":(s=o.value)!=null?s:void 0,"data-max":o.max},r),{ref:t}))});e0.displayName=J1;function VP(e,t){return`${Math.round(e/t*100)}%`}function t0(e,t){return e==null?"indeterminate":e===t?"complete":"loading"}function _s(e){return typeof e=="number"}function Cp(e){return _s(e)&&!isNaN(e)&&e>0}function xp(e,t){return _s(e)&&!isNaN(e)&&e<=t&&e>=0}function zP(e,t){return`Invalid prop \`max\` of value \`${e}\` supplied to \`${t}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${td}\`.`}function UP(e,t){return`Invalid prop \`value\` of value \`${e}\` supplied to \`${t}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${td} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var jA=Z1,VA=e0;function BP(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Ep(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function WP(...e){return t=>{let n=!1;const r=e.map(o=>{const i=Ep(o,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let o=0;o<r.length;o++){const i=r[o];typeof i=="function"?i():Ep(e[o],null)}}}}function HP(...e){return u.useCallback(WP(...e),e)}var KP=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],n0=KP.reduce((e,t)=>{const n=u.forwardRef((r,o)=>{const a=r,{asChild:i}=a,s=N(a,["asChild"]),l=i?Oe:t;return typeof window!="undefined"&&(window[Symbol.for("radix-ui")]=!0),x.jsx(l,R(m({},s),{ref:o}))});return n.displayName=`Primitive.${t}`,R(m({},e),{[t]:n})},{}),nd="Switch",[YP,zA]=Ye(nd),[GP,QP]=YP(nd),r0=u.forwardRef((e,t)=>{const y=e,{__scopeSwitch:n,name:r,checked:o,defaultChecked:i,required:s,disabled:l,value:a="on",onCheckedChange:c,form:d}=y,h=N(y,["__scopeSwitch","name","checked","defaultChecked","required","disabled","value","onCheckedChange","form"]),[p,S]=u.useState(null),C=HP(t,E=>S(E)),g=u.useRef(!1),w=p?d||!!p.closest("form"):!0,[v=!1,f]=zt({prop:o,defaultProp:i,onChange:c});return x.jsxs(GP,{scope:n,checked:v,disabled:l,children:[x.jsx(n0.button,R(m({type:"button",role:"switch","aria-checked":v,"aria-required":s,"data-state":s0(v),"data-disabled":l?"":void 0,disabled:l,value:a},h),{ref:C,onClick:BP(e.onClick,E=>{f(P=>!P),w&&(g.current=E.isPropagationStopped(),g.current||E.stopPropagation())})})),w&&x.jsx(qP,{control:p,bubbles:!g.current,name:r,value:a,checked:v,required:s,disabled:l,form:d,style:{transform:"translateX(-100%)"}})]})});r0.displayName=nd;var o0="SwitchThumb",i0=u.forwardRef((e,t)=>{const i=e,{__scopeSwitch:n}=i,r=N(i,["__scopeSwitch"]),o=QP(o0,n);return x.jsx(n0.span,R(m({"data-state":s0(o.checked),"data-disabled":o.disabled?"":void 0},r),{ref:t}))});i0.displayName=o0;var qP=e=>{const a=e,{control:t,checked:n,bubbles:r=!0}=a,o=N(a,["control","checked","bubbles"]),i=u.useRef(null),s=Wc(n),l=Qs(t);return u.useEffect(()=>{const c=i.current,d=window.HTMLInputElement.prototype,p=Object.getOwnPropertyDescriptor(d,"checked").set;if(s!==n&&p){const S=new Event("click",{bubbles:r});p.call(c,n),c.dispatchEvent(S)}},[s,n,r]),x.jsx("input",R(m({type:"checkbox","aria-hidden":!0,defaultChecked:n},o),{tabIndex:-1,ref:i,style:R(m(m({},e.style),l),{position:"absolute",pointerEvents:"none",opacity:0,margin:0})}))};function s0(e){return e?"checked":"unchecked"}var UA=r0,BA=i0,XP=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],ZP=XP.reduce((e,t)=>{const n=u.forwardRef((r,o)=>{const a=r,{asChild:i}=a,s=N(a,["asChild"]),l=i?Oe:t;return typeof window!="undefined"&&(window[Symbol.for("radix-ui")]=!0),x.jsx(l,R(m({},s),{ref:o}))});return n.displayName=`Primitive.${t}`,R(m({},e),{[t]:n})},{}),JP="Label",l0=u.forwardRef((e,t)=>x.jsx(ZP.label,R(m({},e),{ref:t,onMouseDown:n=>{var o;n.target.closest("button, input, select, textarea")||((o=e.onMouseDown)==null||o.call(e,n),!n.defaultPrevented&&n.detail>1&&n.preventDefault())}})));l0.displayName=JP;var WA=l0;function kp(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function eR(...e){return t=>{let n=!1;const r=e.map(o=>{const i=kp(o,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let o=0;o<r.length;o++){const i=r[o];typeof i=="function"?i():kp(e[o],null)}}}}function a0(...e){return u.useCallback(eR(...e),e)}function tR(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}var u0="AlertDialog",[nR,HA]=Ye(u0,[fy]),gn=fy(),c0=e=>{const o=e,{__scopeAlertDialog:t}=o,n=N(o,["__scopeAlertDialog"]),r=gn(t);return x.jsx(SE,R(m(m({},r),n),{modal:!0}))};c0.displayName=u0;var rR="AlertDialogTrigger",d0=u.forwardRef((e,t)=>{const i=e,{__scopeAlertDialog:n}=i,r=N(i,["__scopeAlertDialog"]),o=gn(n);return x.jsx(CE,R(m(m({},o),r),{ref:t}))});d0.displayName=rR;var oR="AlertDialogPortal",f0=e=>{const o=e,{__scopeAlertDialog:t}=o,n=N(o,["__scopeAlertDialog"]),r=gn(t);return x.jsx(xE,m(m({},r),n))};f0.displayName=oR;var iR="AlertDialogOverlay",p0=u.forwardRef((e,t)=>{const i=e,{__scopeAlertDialog:n}=i,r=N(i,["__scopeAlertDialog"]),o=gn(n);return x.jsx(EE,R(m(m({},o),r),{ref:t}))});p0.displayName=iR;var zr="AlertDialogContent",[sR,lR]=nR(zr),h0=u.forwardRef((e,t)=>{const c=e,{__scopeAlertDialog:n,children:r}=c,o=N(c,["__scopeAlertDialog","children"]),i=gn(n),s=u.useRef(null),l=a0(t,s),a=u.useRef(null);return x.jsx(mE,{contentName:zr,titleName:v0,docsSlug:"alert-dialog",children:x.jsx(sR,{scope:n,cancelRef:a,children:x.jsxs(kE,R(m(m({role:"alertdialog"},i),o),{ref:l,onOpenAutoFocus:tR(o.onOpenAutoFocus,d=>{var h;d.preventDefault(),(h=a.current)==null||h.focus({preventScroll:!0})}),onPointerDownOutside:d=>d.preventDefault(),onInteractOutside:d=>d.preventDefault(),children:[x.jsx(bc,{children:r}),x.jsx(uR,{contentRef:s})]}))})})});h0.displayName=zr;var v0="AlertDialogTitle",m0=u.forwardRef((e,t)=>{const i=e,{__scopeAlertDialog:n}=i,r=N(i,["__scopeAlertDialog"]),o=gn(n);return x.jsx(PE,R(m(m({},o),r),{ref:t}))});m0.displayName=v0;var y0="AlertDialogDescription",g0=u.forwardRef((e,t)=>{const i=e,{__scopeAlertDialog:n}=i,r=N(i,["__scopeAlertDialog"]),o=gn(n);return x.jsx(RE,R(m(m({},o),r),{ref:t}))});g0.displayName=y0;var aR="AlertDialogAction",w0=u.forwardRef((e,t)=>{const i=e,{__scopeAlertDialog:n}=i,r=N(i,["__scopeAlertDialog"]),o=gn(n);return x.jsx(Ty,R(m(m({},o),r),{ref:t}))});w0.displayName=aR;var S0="AlertDialogCancel",C0=u.forwardRef((e,t)=>{const l=e,{__scopeAlertDialog:n}=l,r=N(l,["__scopeAlertDialog"]),{cancelRef:o}=lR(S0,n),i=gn(n),s=a0(t,o);return x.jsx(Ty,R(m(m({},i),r),{ref:s}))});C0.displayName=S0;var uR=({contentRef:e})=>{const t=`\`${zr}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${zr}\` by passing a \`${y0}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${zr}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return u.useEffect(()=>{var r;document.getElementById((r=e.current)==null?void 0:r.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null},KA=c0,YA=d0,GA=f0,QA=p0,qA=h0,XA=w0,ZA=C0,JA=m0,eb=g0;function Pp(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function cR(...e){return t=>{let n=!1;const r=e.map(o=>{const i=Pp(o,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let o=0;o<r.length;o++){const i=r[o];typeof i=="function"?i():Pp(e[o],null)}}}}function _u(...e){return u.useCallback(cR(...e),e)}function dR(e){const t=e+"CollectionProvider",[n,r]=Ye(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=S=>{const{scope:C,children:g}=S,w=$.useRef(null),v=$.useRef(new Map).current;return x.jsx(o,{scope:C,itemMap:v,collectionRef:w,children:g})};s.displayName=t;const l=e+"CollectionSlot",a=$.forwardRef((S,C)=>{const{scope:g,children:w}=S,v=i(l,g),f=_u(C,v.collectionRef);return x.jsx(Oe,{ref:f,children:w})});a.displayName=l;const c=e+"CollectionItemSlot",d="data-radix-collection-item",h=$.forwardRef((S,C)=>{const P=S,{scope:g,children:w}=P,v=N(P,["scope","children"]),f=$.useRef(null),y=_u(C,f),E=i(c,g);return $.useEffect(()=>(E.itemMap.set(f,m({ref:f},v)),()=>void E.itemMap.delete(f))),x.jsx(Oe,{[d]:"",ref:y,children:w})});h.displayName=c;function p(S){const C=i(e+"CollectionConsumer",S);return $.useCallback(()=>{const w=C.collectionRef.current;if(!w)return[];const v=Array.from(w.querySelectorAll(`[${d}]`));return Array.from(C.itemMap.values()).sort((E,P)=>v.indexOf(E.ref.current)-v.indexOf(P.ref.current))},[C.collectionRef,C.itemMap])}return[{Provider:s,Slot:a,ItemSlot:h},p,r]}function fR(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}var pR=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],x0=pR.reduce((e,t)=>{const n=u.forwardRef((r,o)=>{const a=r,{asChild:i}=a,s=N(a,["asChild"]),l=i?Oe:t;return typeof window!="undefined"&&(window[Symbol.for("radix-ui")]=!0),x.jsx(l,R(m({},s),{ref:o}))});return n.displayName=`Primitive.${t}`,R(m({},e),{[t]:n})},{});function hR(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Rp(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function vR(...e){return t=>{let n=!1;const r=e.map(o=>{const i=Rp(o,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let o=0;o<r.length;o++){const i=r[o];typeof i=="function"?i():Rp(e[o],null)}}}}function mR(...e){return u.useCallback(vR(...e),e)}var yR=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],rd=yR.reduce((e,t)=>{const n=u.forwardRef((r,o)=>{const a=r,{asChild:i}=a,s=N(a,["asChild"]),l=i?Oe:t;return typeof window!="undefined"&&(window[Symbol.for("radix-ui")]=!0),x.jsx(l,R(m({},s),{ref:o}))});return n.displayName=`Primitive.${t}`,R(m({},e),{[t]:n})},{}),od="Collapsible",[gR,E0]=Ye(od),[wR,id]=gR(od),k0=u.forwardRef((e,t)=>{const d=e,{__scopeCollapsible:n,open:r,defaultOpen:o,disabled:i,onOpenChange:s}=d,l=N(d,["__scopeCollapsible","open","defaultOpen","disabled","onOpenChange"]),[a=!1,c]=zt({prop:r,defaultProp:o,onChange:s});return x.jsx(wR,{scope:n,disabled:i,contentId:jt(),open:a,onOpenToggle:u.useCallback(()=>c(h=>!h),[c]),children:x.jsx(rd.div,R(m({"data-state":ld(a),"data-disabled":i?"":void 0},l),{ref:t}))})});k0.displayName=od;var P0="CollapsibleTrigger",R0=u.forwardRef((e,t)=>{const i=e,{__scopeCollapsible:n}=i,r=N(i,["__scopeCollapsible"]),o=id(P0,n);return x.jsx(rd.button,R(m({type:"button","aria-controls":o.contentId,"aria-expanded":o.open||!1,"data-state":ld(o.open),"data-disabled":o.disabled?"":void 0,disabled:o.disabled},r),{ref:t,onClick:hR(e.onClick,o.onOpenToggle)}))});R0.displayName=P0;var sd="CollapsibleContent",_0=u.forwardRef((e,t)=>{const i=e,{forceMount:n}=i,r=N(i,["forceMount"]),o=id(sd,e.__scopeCollapsible);return x.jsx(_t,{present:n||o.open,children:({present:s})=>x.jsx(SR,R(m({},r),{ref:t,present:s}))})});_0.displayName=sd;var SR=u.forwardRef((e,t)=>{const f=e,{__scopeCollapsible:n,present:r,children:o}=f,i=N(f,["__scopeCollapsible","present","children"]),s=id(sd,n),[l,a]=u.useState(r),c=u.useRef(null),d=mR(t,c),h=u.useRef(0),p=h.current,S=u.useRef(0),C=S.current,g=s.open||l,w=u.useRef(g),v=u.useRef(void 0);return u.useEffect(()=>{const y=requestAnimationFrame(()=>w.current=!1);return()=>cancelAnimationFrame(y)},[]),xe(()=>{const y=c.current;if(y){v.current=v.current||{transitionDuration:y.style.transitionDuration,animationName:y.style.animationName},y.style.transitionDuration="0s",y.style.animationName="none";const E=y.getBoundingClientRect();h.current=E.height,S.current=E.width,w.current||(y.style.transitionDuration=v.current.transitionDuration,y.style.animationName=v.current.animationName),a(r)}},[s.open,r]),x.jsx(rd.div,R(m({"data-state":ld(s.open),"data-disabled":s.disabled?"":void 0,id:s.contentId,hidden:!g},i),{ref:d,style:m({"--radix-collapsible-content-height":p?`${p}px`:void 0,"--radix-collapsible-content-width":C?`${C}px`:void 0},e.style),children:g&&o}))});function ld(e){return e?"open":"closed"}var CR=k0,xR=R0,ER=_0,wn="Accordion",kR=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[ad,PR,RR]=dR(wn),[Cl,tb]=Ye(wn,[RR,E0]),ud=E0(),T0=$.forwardRef((e,t)=>{const s=e,{type:n}=s,r=N(s,["type"]),o=r,i=r;return x.jsx(ad.Provider,{scope:e.__scopeAccordion,children:n==="multiple"?x.jsx(AR,R(m({},i),{ref:t})):x.jsx(NR,R(m({},o),{ref:t}))})});T0.displayName=wn;var[N0,_R]=Cl(wn),[A0,TR]=Cl(wn,{collapsible:!1}),NR=$.forwardRef((e,t)=>{const c=e,{value:n,defaultValue:r,onValueChange:o=()=>{},collapsible:i=!1}=c,s=N(c,["value","defaultValue","onValueChange","collapsible"]),[l,a]=zt({prop:n,defaultProp:r,onChange:o});return x.jsx(N0,{scope:e.__scopeAccordion,value:l?[l]:[],onItemOpen:a,onItemClose:$.useCallback(()=>i&&a(""),[i,a]),children:x.jsx(A0,{scope:e.__scopeAccordion,collapsible:i,children:x.jsx(b0,R(m({},s),{ref:t}))})})}),AR=$.forwardRef((e,t)=>{const d=e,{value:n,defaultValue:r,onValueChange:o=()=>{}}=d,i=N(d,["value","defaultValue","onValueChange"]),[s=[],l]=zt({prop:n,defaultProp:r,onChange:o}),a=$.useCallback(h=>l((p=[])=>[...p,h]),[l]),c=$.useCallback(h=>l((p=[])=>p.filter(S=>S!==h)),[l]);return x.jsx(N0,{scope:e.__scopeAccordion,value:s,onItemOpen:a,onItemClose:c,children:x.jsx(A0,{scope:e.__scopeAccordion,collapsible:!0,children:x.jsx(b0,R(m({},i),{ref:t}))})})}),[bR,xl]=Cl(wn),b0=$.forwardRef((e,t)=>{const S=e,{__scopeAccordion:n,disabled:r,dir:o,orientation:i="vertical"}=S,s=N(S,["__scopeAccordion","disabled","dir","orientation"]),l=$.useRef(null),a=_u(l,t),c=PR(n),h=oo(o)==="ltr",p=fR(e.onKeyDown,C=>{var A;if(!kR.includes(C.key))return;const g=C.target,w=c().filter(I=>{var B;return!((B=I.ref.current)!=null&&B.disabled)}),v=w.findIndex(I=>I.ref.current===g),f=w.length;if(v===-1)return;C.preventDefault();let y=v;const E=0,P=f-1,T=()=>{y=v+1,y>P&&(y=E)},b=()=>{y=v-1,y<E&&(y=P)};switch(C.key){case"Home":y=E;break;case"End":y=P;break;case"ArrowRight":i==="horizontal"&&(h?T():b());break;case"ArrowDown":i==="vertical"&&T();break;case"ArrowLeft":i==="horizontal"&&(h?b():T());break;case"ArrowUp":i==="vertical"&&b();break}const M=y%f;(A=w[M].ref.current)==null||A.focus()});return x.jsx(bR,{scope:n,disabled:r,direction:o,orientation:i,children:x.jsx(ad.Slot,{scope:n,children:x.jsx(x0.div,R(m({},s),{"data-orientation":i,ref:a,onKeyDown:r?void 0:p}))})})}),Ts="AccordionItem",[MR,cd]=Cl(Ts),M0=$.forwardRef((e,t)=>{const h=e,{__scopeAccordion:n,value:r}=h,o=N(h,["__scopeAccordion","value"]),i=xl(Ts,n),s=_R(Ts,n),l=ud(n),a=jt(),c=r&&s.value.includes(r)||!1,d=i.disabled||e.disabled;return x.jsx(MR,{scope:n,open:c,disabled:d,triggerId:a,children:x.jsx(CR,R(m(m({"data-orientation":i.orientation,"data-state":F0(c)},l),o),{ref:t,disabled:d,open:c,onOpenChange:p=>{p?s.onItemOpen(r):s.onItemClose(r)}}))})});M0.displayName=Ts;var D0="AccordionHeader",O0=$.forwardRef((e,t)=>{const s=e,{__scopeAccordion:n}=s,r=N(s,["__scopeAccordion"]),o=xl(wn,n),i=cd(D0,n);return x.jsx(x0.h3,R(m({"data-orientation":o.orientation,"data-state":F0(i.open),"data-disabled":i.disabled?"":void 0},r),{ref:t}))});O0.displayName=D0;var Tu="AccordionTrigger",I0=$.forwardRef((e,t)=>{const a=e,{__scopeAccordion:n}=a,r=N(a,["__scopeAccordion"]),o=xl(wn,n),i=cd(Tu,n),s=TR(Tu,n),l=ud(n);return x.jsx(ad.ItemSlot,{scope:n,children:x.jsx(xR,R(m(m({"aria-disabled":i.open&&!s.collapsible||void 0,"data-orientation":o.orientation,id:i.triggerId},l),r),{ref:t}))})});I0.displayName=Tu;var L0="AccordionContent",$0=$.forwardRef((e,t)=>{const l=e,{__scopeAccordion:n}=l,r=N(l,["__scopeAccordion"]),o=xl(wn,n),i=cd(L0,n),s=ud(n);return x.jsx(ER,R(m(m({role:"region","aria-labelledby":i.triggerId,"data-orientation":o.orientation},s),r),{ref:t,style:m({"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)"},e.style)}))});$0.displayName=L0;function F0(e){return e?"open":"closed"}var nb=T0,rb=M0,ob=O0,ib=I0,sb=$0;function Nu(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function _p(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function DR(...e){return t=>{let n=!1;const r=e.map(o=>{const i=_p(o,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let o=0;o<r.length;o++){const i=r[o];typeof i=="function"?i():_p(e[o],null)}}}}function j0(...e){return u.useCallback(DR(...e),e)}var OR=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],dd=OR.reduce((e,t)=>{const n=u.forwardRef((r,o)=>{const a=r,{asChild:i}=a,s=N(a,["asChild"]),l=i?Oe:t;return typeof window!="undefined"&&(window[Symbol.for("radix-ui")]=!0),x.jsx(l,R(m({},s),{ref:o}))});return n.displayName=`Primitive.${t}`,R(m({},e),{[t]:n})},{});function Ur(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Tp(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function IR(...e){return t=>{let n=!1;const r=e.map(o=>{const i=Tp(o,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let o=0;o<r.length;o++){const i=r[o];typeof i=="function"?i():Tp(e[o],null)}}}}function Au(...e){return u.useCallback(IR(...e),e)}function LR(e){const t=e+"CollectionProvider",[n,r]=Ye(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=S=>{const{scope:C,children:g}=S,w=$.useRef(null),v=$.useRef(new Map).current;return x.jsx(o,{scope:C,itemMap:v,collectionRef:w,children:g})};s.displayName=t;const l=e+"CollectionSlot",a=$.forwardRef((S,C)=>{const{scope:g,children:w}=S,v=i(l,g),f=Au(C,v.collectionRef);return x.jsx(Oe,{ref:f,children:w})});a.displayName=l;const c=e+"CollectionItemSlot",d="data-radix-collection-item",h=$.forwardRef((S,C)=>{const P=S,{scope:g,children:w}=P,v=N(P,["scope","children"]),f=$.useRef(null),y=Au(C,f),E=i(c,g);return $.useEffect(()=>(E.itemMap.set(f,m({ref:f},v)),()=>void E.itemMap.delete(f))),x.jsx(Oe,{[d]:"",ref:y,children:w})});h.displayName=c;function p(S){const C=i(e+"CollectionConsumer",S);return $.useCallback(()=>{const w=C.collectionRef.current;if(!w)return[];const v=Array.from(w.querySelectorAll(`[${d}]`));return Array.from(C.itemMap.values()).sort((E,P)=>v.indexOf(E.ref.current)-v.indexOf(P.ref.current))},[C.collectionRef,C.itemMap])}return[{Provider:s,Slot:a,ItemSlot:h},p,r]}var $R=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],V0=$R.reduce((e,t)=>{const n=u.forwardRef((r,o)=>{const a=r,{asChild:i}=a,s=N(a,["asChild"]),l=i?Oe:t;return typeof window!="undefined"&&(window[Symbol.for("radix-ui")]=!0),x.jsx(l,R(m({},s),{ref:o}))});return n.displayName=`Primitive.${t}`,R(m({},e),{[t]:n})},{}),aa="rovingFocusGroup.onEntryFocus",FR={bubbles:!1,cancelable:!0},El="RovingFocusGroup",[bu,z0,jR]=LR(El),[VR,kl]=Ye(El,[jR]),[zR,UR]=VR(El),U0=u.forwardRef((e,t)=>x.jsx(bu.Provider,{scope:e.__scopeRovingFocusGroup,children:x.jsx(bu.Slot,{scope:e.__scopeRovingFocusGroup,children:x.jsx(BR,R(m({},e),{ref:t}))})}));U0.displayName=El;var BR=u.forwardRef((e,t)=>{const M=e,{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:i,currentTabStopId:s,defaultCurrentTabStopId:l,onCurrentTabStopIdChange:a,onEntryFocus:c,preventScrollOnEntryFocus:d=!1}=M,h=N(M,["__scopeRovingFocusGroup","orientation","loop","dir","currentTabStopId","defaultCurrentTabStopId","onCurrentTabStopIdChange","onEntryFocus","preventScrollOnEntryFocus"]),p=u.useRef(null),S=Au(t,p),C=oo(i),[g=null,w]=zt({prop:s,defaultProp:l,onChange:a}),[v,f]=u.useState(!1),y=fe(c),E=z0(n),P=u.useRef(!1),[T,b]=u.useState(0);return u.useEffect(()=>{const A=p.current;if(A)return A.addEventListener(aa,y),()=>A.removeEventListener(aa,y)},[y]),x.jsx(zR,{scope:n,orientation:r,dir:C,loop:o,currentTabStopId:g,onItemFocus:u.useCallback(A=>w(A),[w]),onItemShiftTab:u.useCallback(()=>f(!0),[]),onFocusableItemAdd:u.useCallback(()=>b(A=>A+1),[]),onFocusableItemRemove:u.useCallback(()=>b(A=>A-1),[]),children:x.jsx(V0.div,R(m({tabIndex:v||T===0?-1:0,"data-orientation":r},h),{ref:S,style:m({outline:"none"},e.style),onMouseDown:Ur(e.onMouseDown,()=>{P.current=!0}),onFocus:Ur(e.onFocus,A=>{const I=!P.current;if(A.target===A.currentTarget&&I&&!v){const B=new CustomEvent(aa,FR);if(A.currentTarget.dispatchEvent(B),!B.defaultPrevented){const j=E().filter(q=>q.focusable),W=j.find(q=>q.active),Z=j.find(q=>q.id===g),Q=[W,Z,...j].filter(Boolean).map(q=>q.ref.current);H0(Q,d)}}P.current=!1}),onBlur:Ur(e.onBlur,()=>f(!1))}))})}),B0="RovingFocusGroupItem",W0=u.forwardRef((e,t)=>{const C=e,{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:i}=C,s=N(C,["__scopeRovingFocusGroup","focusable","active","tabStopId"]),l=jt(),a=i||l,c=UR(B0,n),d=c.currentTabStopId===a,h=z0(n),{onFocusableItemAdd:p,onFocusableItemRemove:S}=c;return u.useEffect(()=>{if(r)return p(),()=>S()},[r,p,S]),x.jsx(bu.ItemSlot,{scope:n,id:a,focusable:r,active:o,children:x.jsx(V0.span,R(m({tabIndex:d?0:-1,"data-orientation":c.orientation},s),{ref:t,onMouseDown:Ur(e.onMouseDown,g=>{r?c.onItemFocus(a):g.preventDefault()}),onFocus:Ur(e.onFocus,()=>c.onItemFocus(a)),onKeyDown:Ur(e.onKeyDown,g=>{if(g.key==="Tab"&&g.shiftKey){c.onItemShiftTab();return}if(g.target!==g.currentTarget)return;const w=KR(g,c.orientation,c.dir);if(w!==void 0){if(g.metaKey||g.ctrlKey||g.altKey||g.shiftKey)return;g.preventDefault();let f=h().filter(y=>y.focusable).map(y=>y.ref.current);if(w==="last")f.reverse();else if(w==="prev"||w==="next"){w==="prev"&&f.reverse();const y=f.indexOf(g.currentTarget);f=c.loop?YR(f,y+1):f.slice(y+1)}setTimeout(()=>H0(f))}})}))})});W0.displayName=B0;var WR={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function HR(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function KR(e,t,n){const r=HR(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return WR[r]}function H0(e,t=!1){const n=document.activeElement;for(const r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function YR(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var K0=U0,Y0=W0,fd="Radio",[GR,G0]=Ye(fd),[QR,qR]=GR(fd),Q0=u.forwardRef((e,t)=>{const w=e,{__scopeRadio:n,name:r,checked:o=!1,required:i,disabled:s,value:l="on",onCheck:a,form:c}=w,d=N(w,["__scopeRadio","name","checked","required","disabled","value","onCheck","form"]),[h,p]=u.useState(null),S=j0(t,v=>p(v)),C=u.useRef(!1),g=h?c||!!h.closest("form"):!0;return x.jsxs(QR,{scope:n,checked:o,disabled:s,children:[x.jsx(dd.button,R(m({type:"button",role:"radio","aria-checked":o,"data-state":Z0(o),"data-disabled":s?"":void 0,disabled:s,value:l},d),{ref:S,onClick:Nu(e.onClick,v=>{o||a==null||a(),g&&(C.current=v.isPropagationStopped(),C.current||v.stopPropagation())})})),g&&x.jsx(XR,{control:h,bubbles:!C.current,name:r,value:l,checked:o,required:i,disabled:s,form:c,style:{transform:"translateX(-100%)"}})]})});Q0.displayName=fd;var q0="RadioIndicator",X0=u.forwardRef((e,t)=>{const s=e,{__scopeRadio:n,forceMount:r}=s,o=N(s,["__scopeRadio","forceMount"]),i=qR(q0,n);return x.jsx(_t,{present:r||i.checked,children:x.jsx(dd.span,R(m({"data-state":Z0(i.checked),"data-disabled":i.disabled?"":void 0},o),{ref:t}))})});X0.displayName=q0;var XR=e=>{const a=e,{control:t,checked:n,bubbles:r=!0}=a,o=N(a,["control","checked","bubbles"]),i=u.useRef(null),s=Wc(n),l=Qs(t);return u.useEffect(()=>{const c=i.current,d=window.HTMLInputElement.prototype,p=Object.getOwnPropertyDescriptor(d,"checked").set;if(s!==n&&p){const S=new Event("click",{bubbles:r});p.call(c,n),c.dispatchEvent(S)}},[s,n,r]),x.jsx("input",R(m({type:"radio","aria-hidden":!0,defaultChecked:n},o),{tabIndex:-1,ref:i,style:R(m(m({},e.style),l),{position:"absolute",pointerEvents:"none",opacity:0,margin:0})}))};function Z0(e){return e?"checked":"unchecked"}var ZR=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],pd="RadioGroup",[JR,lb]=Ye(pd,[kl,G0]),J0=kl(),eg=G0(),[e_,t_]=JR(pd),tg=u.forwardRef((e,t)=>{const v=e,{__scopeRadioGroup:n,name:r,defaultValue:o,value:i,required:s=!1,disabled:l=!1,orientation:a,dir:c,loop:d=!0,onValueChange:h}=v,p=N(v,["__scopeRadioGroup","name","defaultValue","value","required","disabled","orientation","dir","loop","onValueChange"]),S=J0(n),C=oo(c),[g,w]=zt({prop:i,defaultProp:o,onChange:h});return x.jsx(e_,{scope:n,name:r,required:s,disabled:l,value:g,onValueChange:w,children:x.jsx(K0,R(m({asChild:!0},S),{orientation:a,dir:C,loop:d,children:x.jsx(dd.div,R(m({role:"radiogroup","aria-required":s,"aria-orientation":a,"data-disabled":l?"":void 0,dir:C},p),{ref:t}))}))})});tg.displayName=pd;var ng="RadioGroupItem",rg=u.forwardRef((e,t)=>{const S=e,{__scopeRadioGroup:n,disabled:r}=S,o=N(S,["__scopeRadioGroup","disabled"]),i=t_(ng,n),s=i.disabled||r,l=J0(n),a=eg(n),c=u.useRef(null),d=j0(t,c),h=i.value===o.value,p=u.useRef(!1);return u.useEffect(()=>{const C=w=>{ZR.includes(w.key)&&(p.current=!0)},g=()=>p.current=!1;return document.addEventListener("keydown",C),document.addEventListener("keyup",g),()=>{document.removeEventListener("keydown",C),document.removeEventListener("keyup",g)}},[]),x.jsx(Y0,R(m({asChild:!0},l),{focusable:!s,active:h,children:x.jsx(Q0,R(m(m({disabled:s,required:i.required,checked:h},a),o),{name:i.name,ref:d,onCheck:()=>i.onValueChange(o.value),onKeyDown:Nu(C=>{C.key==="Enter"&&C.preventDefault()}),onFocus:Nu(o.onFocus,()=>{var C;p.current&&((C=c.current)==null||C.click())})}))}))});rg.displayName=ng;var n_="RadioGroupIndicator",og=u.forwardRef((e,t)=>{const i=e,{__scopeRadioGroup:n}=i,r=N(i,["__scopeRadioGroup"]),o=eg(n);return x.jsx(X0,R(m(m({},o),r),{ref:t}))});og.displayName=n_;var ab=tg,ub=rg,cb=og;function ua(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}var r_=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],Pl=r_.reduce((e,t)=>{const n=u.forwardRef((r,o)=>{const a=r,{asChild:i}=a,s=N(a,["asChild"]),l=i?Oe:t;return typeof window!="undefined"&&(window[Symbol.for("radix-ui")]=!0),x.jsx(l,R(m({},s),{ref:o}))});return n.displayName=`Primitive.${t}`,R(m({},e),{[t]:n})},{}),hd="Tabs",[o_,db]=Ye(hd,[kl]),ig=kl(),[i_,vd]=o_(hd),sg=u.forwardRef((e,t)=>{const S=e,{__scopeTabs:n,value:r,onValueChange:o,defaultValue:i,orientation:s="horizontal",dir:l,activationMode:a="automatic"}=S,c=N(S,["__scopeTabs","value","onValueChange","defaultValue","orientation","dir","activationMode"]),d=oo(l),[h,p]=zt({prop:r,onChange:o,defaultProp:i});return x.jsx(i_,{scope:n,baseId:jt(),value:h,onValueChange:p,orientation:s,dir:d,activationMode:a,children:x.jsx(Pl.div,R(m({dir:d,"data-orientation":s},c),{ref:t}))})});sg.displayName=hd;var lg="TabsList",ag=u.forwardRef((e,t)=>{const l=e,{__scopeTabs:n,loop:r=!0}=l,o=N(l,["__scopeTabs","loop"]),i=vd(lg,n),s=ig(n);return x.jsx(K0,R(m({asChild:!0},s),{orientation:i.orientation,dir:i.dir,loop:r,children:x.jsx(Pl.div,R(m({role:"tablist","aria-orientation":i.orientation},o),{ref:t}))}))});ag.displayName=lg;var ug="TabsTrigger",cg=u.forwardRef((e,t)=>{const h=e,{__scopeTabs:n,value:r,disabled:o=!1}=h,i=N(h,["__scopeTabs","value","disabled"]),s=vd(ug,n),l=ig(n),a=pg(s.baseId,r),c=hg(s.baseId,r),d=r===s.value;return x.jsx(Y0,R(m({asChild:!0},l),{focusable:!o,active:d,children:x.jsx(Pl.button,R(m({type:"button",role:"tab","aria-selected":d,"aria-controls":c,"data-state":d?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:a},i),{ref:t,onMouseDown:ua(e.onMouseDown,p=>{!o&&p.button===0&&p.ctrlKey===!1?s.onValueChange(r):p.preventDefault()}),onKeyDown:ua(e.onKeyDown,p=>{[" ","Enter"].includes(p.key)&&s.onValueChange(r)}),onFocus:ua(e.onFocus,()=>{const p=s.activationMode!=="manual";!d&&!o&&p&&s.onValueChange(r)})}))}))});cg.displayName=ug;var dg="TabsContent",fg=u.forwardRef((e,t)=>{const p=e,{__scopeTabs:n,value:r,forceMount:o,children:i}=p,s=N(p,["__scopeTabs","value","forceMount","children"]),l=vd(dg,n),a=pg(l.baseId,r),c=hg(l.baseId,r),d=r===l.value,h=u.useRef(d);return u.useEffect(()=>{const S=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(S)},[]),x.jsx(_t,{present:o||d,children:({present:S})=>x.jsx(Pl.div,R(m({"data-state":d?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":a,hidden:!S,id:c,tabIndex:0},s),{ref:t,style:R(m({},e.style),{animationDuration:h.current?"0s":void 0}),children:S&&i}))})});fg.displayName=dg;function pg(e,t){return`${e}-trigger-${t}`}function hg(e,t){return`${e}-content-${t}`}var fb=sg,pb=ag,hb=cg,vb=fg,ui=e=>e.type==="checkbox",Xn=e=>e instanceof Date,tt=e=>e==null;const vg=e=>typeof e=="object";var Pe=e=>!tt(e)&&!Array.isArray(e)&&vg(e)&&!Xn(e),mg=e=>Pe(e)&&e.target?ui(e.target)?e.target.checked:e.target.value:e,s_=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,yg=(e,t)=>e.has(s_(t)),l_=e=>{const t=e.constructor&&e.constructor.prototype;return Pe(t)&&t.hasOwnProperty("isPrototypeOf")},md=typeof window!="undefined"&&typeof window.HTMLElement!="undefined"&&typeof document!="undefined";function ze(e){let t;const n=Array.isArray(e),r=typeof FileList!="undefined"?e instanceof FileList:!1;if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else if(!(md&&(e instanceof Blob||r))&&(n||Pe(e)))if(t=n?[]:{},!n&&!l_(e))t=e;else for(const o in e)e.hasOwnProperty(o)&&(t[o]=ze(e[o]));else return e;return t}var Rl=e=>Array.isArray(e)?e.filter(Boolean):[],Ee=e=>e===void 0,V=(e,t,n)=>{if(!t||!Pe(e))return n;const r=Rl(t.split(/[,[\].]+?/)).reduce((o,i)=>tt(o)?o:o[i],e);return Ee(r)||r===e?Ee(e[t])?n:e[t]:r},ft=e=>typeof e=="boolean",yd=e=>/^\w*$/.test(e),gg=e=>Rl(e.replace(/["|']|\]/g,"").split(/\.|\[/)),ue=(e,t,n)=>{let r=-1;const o=yd(t)?[t]:gg(t),i=o.length,s=i-1;for(;++r<i;){const l=o[r];let a=n;if(r!==s){const c=e[l];a=Pe(c)||Array.isArray(c)?c:isNaN(+o[r+1])?{}:[]}if(l==="__proto__"||l==="constructor"||l==="prototype")return;e[l]=a,e=e[l]}};const Ns={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},It={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},tn={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},wg=$.createContext(null),gd=()=>$.useContext(wg),mb=e=>{const r=e,{children:t}=r,n=N(r,["children"]);return $.createElement(wg.Provider,{value:n},t)};var Sg=(e,t,n,r=!0)=>{const o={defaultValues:t._defaultValues};for(const i in e)Object.defineProperty(o,i,{get:()=>{const s=i;return t._proxyFormState[s]!==It.all&&(t._proxyFormState[s]=!r||It.all),n&&(n[s]=!0),e[s]}});return o};function a_(e){const t=gd(),{control:n=t.control,disabled:r,name:o,exact:i}=e||{},[s,l]=$.useState(n._formState),a=$.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1}),c=$.useRef(o);return c.current=o,$.useEffect(()=>n._subscribe({name:c.current,formState:a.current,exact:i,callback:d=>{!r&&l(m(m({},n._formState),d))}}),[n,r,i]),$.useEffect(()=>{a.current.isValid&&n._setValid(!0)},[n]),$.useMemo(()=>Sg(s,n,a.current,!1),[s,n])}var Gt=e=>typeof e=="string",Cg=(e,t,n,r,o)=>Gt(e)?(r&&t.watch.add(e),V(n,e,o)):Array.isArray(e)?e.map(i=>(r&&t.watch.add(i),V(n,i))):(r&&(t.watchAll=!0),n);function u_(e){const t=gd(),{control:n=t.control,name:r,defaultValue:o,disabled:i,exact:s}=e||{},l=$.useRef(r),a=$.useRef(o);l.current=r,$.useEffect(()=>n._subscribe({name:l.current,formState:{values:!0},exact:s,callback:h=>!i&&d(Cg(l.current,n._names,h.values||n._formValues,!1,a.current))}),[n,i,s]);const[c,d]=$.useState(n._getWatch(r,o));return $.useEffect(()=>n._removeUnmounted()),c}function c_(e){const t=gd(),{name:n,disabled:r,control:o=t.control,shouldUnregister:i}=e,s=yg(o._names.array,n),l=u_({control:o,name:n,defaultValue:V(o._formValues,n,V(o._defaultValues,n,e.defaultValue)),exact:!0}),a=a_({control:o,name:n,exact:!0}),c=$.useRef(e),d=$.useRef(o.register(n,m(R(m({},e.rules),{value:l}),ft(e.disabled)?{disabled:e.disabled}:{}))),h=$.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!V(a.errors,n)},isDirty:{enumerable:!0,get:()=>!!V(a.dirtyFields,n)},isTouched:{enumerable:!0,get:()=>!!V(a.touchedFields,n)},isValidating:{enumerable:!0,get:()=>!!V(a.validatingFields,n)},error:{enumerable:!0,get:()=>V(a.errors,n)}}),[a,n]),p=$.useCallback(w=>d.current.onChange({target:{value:mg(w),name:n},type:Ns.CHANGE}),[n]),S=$.useCallback(()=>d.current.onBlur({target:{value:V(o._formValues,n),name:n},type:Ns.BLUR}),[n,o._formValues]),C=$.useCallback(w=>{const v=V(o._fields,n);v&&w&&(v._f.ref={focus:()=>w.focus(),select:()=>w.select(),setCustomValidity:f=>w.setCustomValidity(f),reportValidity:()=>w.reportValidity()})},[o._fields,n]),g=$.useMemo(()=>R(m({name:n,value:l},ft(r)||a.disabled?{disabled:a.disabled||r}:{}),{onChange:p,onBlur:S,ref:C}),[n,r,a.disabled,p,S,C,l]);return $.useEffect(()=>{const w=o._options.shouldUnregister||i;o.register(n,m(m({},c.current.rules),ft(c.current.disabled)?{disabled:c.current.disabled}:{}));const v=(f,y)=>{const E=V(o._fields,f);E&&E._f&&(E._f.mount=y)};if(v(n,!0),w){const f=ze(V(o._options.defaultValues,n));ue(o._defaultValues,n,f),Ee(V(o._formValues,n))&&ue(o._formValues,n,f)}return!s&&o.register(n),()=>{(s?w&&!o._state.action:w)?o.unregister(n):v(n,!1)}},[n,o,s,i]),$.useEffect(()=>{o._setDisabledField({disabled:r,name:n})},[r,n,o]),$.useMemo(()=>({field:g,formState:a,fieldState:h}),[g,a,h])}const yb=e=>e.render(c_(e));var d_=(e,t,n,r,o)=>t?R(m({},n[e]),{types:R(m({},n[e]&&n[e].types?n[e].types:{}),{[r]:o||!0})}):{},Io=e=>Array.isArray(e)?e:[e],Np=()=>{let e=[];return{get observers(){return e},next:o=>{for(const i of e)i.next&&i.next(o)},subscribe:o=>(e.push(o),{unsubscribe:()=>{e=e.filter(i=>i!==o)}}),unsubscribe:()=>{e=[]}}},Mu=e=>tt(e)||!vg(e);function _n(e,t){if(Mu(e)||Mu(t))return e===t;if(Xn(e)&&Xn(t))return e.getTime()===t.getTime();const n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(const o of n){const i=e[o];if(!r.includes(o))return!1;if(o!=="ref"){const s=t[o];if(Xn(i)&&Xn(s)||Pe(i)&&Pe(s)||Array.isArray(i)&&Array.isArray(s)?!_n(i,s):i!==s)return!1}}return!0}var Je=e=>Pe(e)&&!Object.keys(e).length,wd=e=>e.type==="file",Lt=e=>typeof e=="function",As=e=>{if(!md)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},xg=e=>e.type==="select-multiple",Sd=e=>e.type==="radio",f_=e=>Sd(e)||ui(e),ca=e=>As(e)&&e.isConnected;function p_(e,t){const n=t.slice(0,-1).length;let r=0;for(;r<n;)e=Ee(e)?r++:e[t[r++]];return e}function h_(e){for(const t in e)if(e.hasOwnProperty(t)&&!Ee(e[t]))return!1;return!0}function be(e,t){const n=Array.isArray(t)?t:yd(t)?[t]:gg(t),r=n.length===1?e:p_(e,n),o=n.length-1,i=n[o];return r&&delete r[i],o!==0&&(Pe(r)&&Je(r)||Array.isArray(r)&&h_(r))&&be(e,n.slice(0,-1)),e}var Eg=e=>{for(const t in e)if(Lt(e[t]))return!0;return!1};function bs(e,t={}){const n=Array.isArray(e);if(Pe(e)||n)for(const r in e)Array.isArray(e[r])||Pe(e[r])&&!Eg(e[r])?(t[r]=Array.isArray(e[r])?[]:{},bs(e[r],t[r])):tt(e[r])||(t[r]=!0);return t}function kg(e,t,n){const r=Array.isArray(e);if(Pe(e)||r)for(const o in e)Array.isArray(e[o])||Pe(e[o])&&!Eg(e[o])?Ee(t)||Mu(n[o])?n[o]=Array.isArray(e[o])?bs(e[o],[]):m({},bs(e[o])):kg(e[o],tt(t)?{}:t[o],n[o]):n[o]=!_n(e[o],t[o]);return n}var yo=(e,t)=>kg(e,t,bs(t));const Ap={value:!1,isValid:!1},bp={value:!0,isValid:!0};var Pg=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter(n=>n&&n.checked&&!n.disabled).map(n=>n.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!Ee(e[0].attributes.value)?Ee(e[0].value)||e[0].value===""?bp:{value:e[0].value,isValid:!0}:bp:Ap}return Ap},Rg=(e,{valueAsNumber:t,valueAsDate:n,setValueAs:r})=>Ee(e)?e:t?e===""?NaN:e&&+e:n&&Gt(e)?new Date(e):r?r(e):e;const Mp={isValid:!1,value:null};var _g=e=>Array.isArray(e)?e.reduce((t,n)=>n&&n.checked&&!n.disabled?{isValid:!0,value:n.value}:t,Mp):Mp;function Dp(e){const t=e.ref;return wd(t)?t.files:Sd(t)?_g(e.refs).value:xg(t)?[...t.selectedOptions].map(({value:n})=>n):ui(t)?Pg(e.refs).value:Rg(Ee(t.value)?e.ref.value:t.value,e)}var v_=(e,t,n,r)=>{const o={};for(const i of e){const s=V(t,i);s&&ue(o,i,s._f)}return{criteriaMode:n,names:[...e],fields:o,shouldUseNativeValidation:r}},Ms=e=>e instanceof RegExp,go=e=>Ee(e)?e:Ms(e)?e.source:Pe(e)?Ms(e.value)?e.value.source:e.value:e,Op=e=>({isOnSubmit:!e||e===It.onSubmit,isOnBlur:e===It.onBlur,isOnChange:e===It.onChange,isOnAll:e===It.all,isOnTouch:e===It.onTouched});const Ip="AsyncFunction";var m_=e=>!!e&&!!e.validate&&!!(Lt(e.validate)&&e.validate.constructor.name===Ip||Pe(e.validate)&&Object.values(e.validate).find(t=>t.constructor.name===Ip)),y_=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),Lp=(e,t,n)=>!n&&(t.watchAll||t.watch.has(e)||[...t.watch].some(r=>e.startsWith(r)&&/^\.\w+/.test(e.slice(r.length))));const Lo=(e,t,n,r)=>{for(const i of n||Object.keys(e)){const s=V(e,i);if(s){const o=s,{_f:l}=o,a=N(o,["_f"]);if(l){if(l.refs&&l.refs[0]&&t(l.refs[0],i)&&!r)return!0;if(l.ref&&t(l.ref,l.name)&&!r)return!0;if(Lo(a,t))break}else if(Pe(a)&&Lo(a,t))break}}};function $p(e,t,n){const r=V(e,n);if(r||yd(n))return{error:r,name:n};const o=n.split(".");for(;o.length;){const i=o.join("."),s=V(t,i),l=V(e,i);if(s&&!Array.isArray(s)&&n!==i)return{name:n};if(l&&l.type)return{name:i,error:l};o.pop()}return{name:n}}var g_=(e,t,n,r)=>{n(e);const s=e,{name:o}=s,i=N(s,["name"]);return Je(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find(l=>t[l]===(!r||It.all))},w_=(e,t,n)=>!e||!t||e===t||Io(e).some(r=>r&&(n?r===t:r.startsWith(t)||t.startsWith(r))),S_=(e,t,n,r,o)=>o.isOnAll?!1:!n&&o.isOnTouch?!(t||e):(n?r.isOnBlur:o.isOnBlur)?!e:(n?r.isOnChange:o.isOnChange)?e:!0,C_=(e,t)=>!Rl(V(e,t)).length&&be(e,t),x_=(e,t,n)=>{const r=Io(V(e,n));return ue(r,"root",t[n]),ue(e,n,r),e},Gi=e=>Gt(e);function Fp(e,t,n="validate"){if(Gi(e)||Array.isArray(e)&&e.every(Gi)||ft(e)&&!e)return{type:n,message:Gi(e)?e:"",ref:t}}var Cr=e=>Pe(e)&&!Ms(e)?e:{value:e,message:""},jp=(e,t,n,r,o,i)=>Wt(void 0,null,function*(){const{ref:s,refs:l,required:a,maxLength:c,minLength:d,min:h,max:p,pattern:S,validate:C,name:g,valueAsNumber:w,mount:v}=e._f,f=V(n,g);if(!v||t.has(g))return{};const y=l?l[0]:s,E=j=>{o&&y.reportValidity&&(y.setCustomValidity(ft(j)?"":j||""),y.reportValidity())},P={},T=Sd(s),b=ui(s),M=T||b,A=(w||wd(s))&&Ee(s.value)&&Ee(f)||As(s)&&s.value===""||f===""||Array.isArray(f)&&!f.length,I=d_.bind(null,g,r,P),B=(j,W,Z,ee=tn.maxLength,Q=tn.minLength)=>{const q=j?W:Z;P[g]=m({type:j?ee:Q,message:q,ref:s},I(j?ee:Q,q))};if(i?!Array.isArray(f)||!f.length:a&&(!M&&(A||tt(f))||ft(f)&&!f||b&&!Pg(l).isValid||T&&!_g(l).isValid)){const{value:j,message:W}=Gi(a)?{value:!!a,message:a}:Cr(a);if(j&&(P[g]=m({type:tn.required,message:W,ref:y},I(tn.required,W)),!r))return E(W),P}if(!A&&(!tt(h)||!tt(p))){let j,W;const Z=Cr(p),ee=Cr(h);if(!tt(f)&&!isNaN(f)){const Q=s.valueAsNumber||f&&+f;tt(Z.value)||(j=Q>Z.value),tt(ee.value)||(W=Q<ee.value)}else{const Q=s.valueAsDate||new Date(f),q=Re=>new Date(new Date().toDateString()+" "+Re),Y=s.type=="time",he=s.type=="week";Gt(Z.value)&&f&&(j=Y?q(f)>q(Z.value):he?f>Z.value:Q>new Date(Z.value)),Gt(ee.value)&&f&&(W=Y?q(f)<q(ee.value):he?f<ee.value:Q<new Date(ee.value))}if((j||W)&&(B(!!j,Z.message,ee.message,tn.max,tn.min),!r))return E(P[g].message),P}if((c||d)&&!A&&(Gt(f)||i&&Array.isArray(f))){const j=Cr(c),W=Cr(d),Z=!tt(j.value)&&f.length>+j.value,ee=!tt(W.value)&&f.length<+W.value;if((Z||ee)&&(B(Z,j.message,W.message),!r))return E(P[g].message),P}if(S&&!A&&Gt(f)){const{value:j,message:W}=Cr(S);if(Ms(j)&&!f.match(j)&&(P[g]=m({type:tn.pattern,message:W,ref:s},I(tn.pattern,W)),!r))return E(W),P}if(C){if(Lt(C)){const j=yield C(f,n),W=Fp(j,y);if(W&&(P[g]=m(m({},W),I(tn.validate,W.message)),!r))return E(W.message),P}else if(Pe(C)){let j={};for(const W in C){if(!Je(j)&&!r)break;const Z=Fp(yield C[W](f,n),y,W);Z&&(j=m(m({},Z),I(W,Z.message)),E(Z.message),r&&(P[g]=j))}if(!Je(j)&&(P[g]=m({ref:y},j),!r))return P}}return E(!0),P});const E_={mode:It.onSubmit,reValidateMode:It.onChange,shouldFocusError:!0};function k_(e={}){let t=m(m({},E_),e),n={submitCount:0,isDirty:!1,isLoading:Lt(t.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1};const r={};let o=Pe(t.defaultValues)||Pe(t.values)?ze(t.values||t.defaultValues)||{}:{},i=t.shouldUnregister?{}:ze(o),s={action:!1,mount:!1,watch:!1},l={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},a,c=0;const d={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let h=m({},d);const p={array:Np(),state:Np()},S=Op(t.mode),C=Op(t.reValidateMode),g=t.criteriaMode===It.all,w=k=>_=>{clearTimeout(c),c=setTimeout(k,_)},v=k=>Wt(this,null,function*(){if(!t.disabled&&(d.isValid||h.isValid||k)){const _=t.resolver?Je((yield A()).errors):yield B(r,!0);_!==n.isValid&&p.state.next({isValid:_})}}),f=(k,_)=>{!t.disabled&&(d.isValidating||d.validatingFields||h.isValidating||h.validatingFields)&&((k||Array.from(l.mount)).forEach(D=>{D&&(_?ue(n.validatingFields,D,_):be(n.validatingFields,D))}),p.state.next({validatingFields:n.validatingFields,isValidating:!Je(n.validatingFields)}))},y=(k,_=[],D,K,z=!0,F=!0)=>{if(K&&D&&!t.disabled){if(s.action=!0,F&&Array.isArray(V(r,k))){const H=D(V(r,k),K.argA,K.argB);z&&ue(r,k,H)}if(F&&Array.isArray(V(n.errors,k))){const H=D(V(n.errors,k),K.argA,K.argB);z&&ue(n.errors,k,H),C_(n.errors,k)}if((d.touchedFields||h.touchedFields)&&F&&Array.isArray(V(n.touchedFields,k))){const H=D(V(n.touchedFields,k),K.argA,K.argB);z&&ue(n.touchedFields,k,H)}(d.dirtyFields||h.dirtyFields)&&(n.dirtyFields=yo(o,i)),p.state.next({name:k,isDirty:W(k,_),dirtyFields:n.dirtyFields,errors:n.errors,isValid:n.isValid})}else ue(i,k,_)},E=(k,_)=>{ue(n.errors,k,_),p.state.next({errors:n.errors})},P=k=>{n.errors=k,p.state.next({errors:n.errors,isValid:!1})},T=(k,_,D,K)=>{const z=V(r,k);if(z){const F=V(i,k,Ee(D)?V(o,k):D);Ee(F)||K&&K.defaultChecked||_?ue(i,k,_?F:Dp(z._f)):Q(k,F),s.mount&&v()}},b=(k,_,D,K,z)=>{let F=!1,H=!1;const X={name:k};if(!t.disabled){if(!D||K){(d.isDirty||h.isDirty)&&(H=n.isDirty,n.isDirty=X.isDirty=W(),F=H!==X.isDirty);const se=_n(V(o,k),_);H=!!V(n.dirtyFields,k),se?be(n.dirtyFields,k):ue(n.dirtyFields,k,!0),X.dirtyFields=n.dirtyFields,F=F||(d.dirtyFields||h.dirtyFields)&&H!==!se}if(D){const se=V(n.touchedFields,k);se||(ue(n.touchedFields,k,D),X.touchedFields=n.touchedFields,F=F||(d.touchedFields||h.touchedFields)&&se!==D)}F&&z&&p.state.next(X)}return F?X:{}},M=(k,_,D,K)=>{const z=V(n.errors,k),F=(d.isValid||h.isValid)&&ft(_)&&n.isValid!==_;if(t.delayError&&D?(a=w(()=>E(k,D)),a(t.delayError)):(clearTimeout(c),a=null,D?ue(n.errors,k,D):be(n.errors,k)),(D?!_n(z,D):z)||!Je(K)||F){const H=R(m(m({},K),F&&ft(_)?{isValid:_}:{}),{errors:n.errors,name:k});n=m(m({},n),H),p.state.next(H)}},A=k=>Wt(this,null,function*(){f(k,!0);const _=yield t.resolver(i,t.context,v_(k||l.mount,r,t.criteriaMode,t.shouldUseNativeValidation));return f(k),_}),I=k=>Wt(this,null,function*(){const{errors:_}=yield A(k);if(k)for(const D of k){const K=V(_,D);K?ue(n.errors,D,K):be(n.errors,D)}else n.errors=_;return _}),B=(K,z,...F)=>Wt(this,[K,z,...F],function*(k,_,D={valid:!0}){for(const X in k){const se=k[X];if(se){const H=se,{_f:me}=H,di=N(H,["_f"]);if(me){const pr=l.array.has(me.name),hr=se._f&&m_(se._f);hr&&d.validatingFields&&f([X],!0);const vr=yield jp(se,l.disabled,i,g,t.shouldUseNativeValidation&&!_,pr);if(hr&&d.validatingFields&&f([X]),vr[me.name]&&(D.valid=!1,_))break;!_&&(V(vr,me.name)?pr?x_(n.errors,vr,me.name):ue(n.errors,me.name,vr[me.name]):be(n.errors,me.name))}!Je(di)&&(yield B(di,_,D))}}return D.valid}),j=()=>{for(const k of l.unMount){const _=V(r,k);_&&(_._f.refs?_._f.refs.every(D=>!ca(D)):!ca(_._f.ref))&&ie(k)}l.unMount=new Set},W=(k,_)=>!t.disabled&&(k&&_&&ue(i,k,_),!_n(Nt(),o)),Z=(k,_,D)=>Cg(k,l,m({},s.mount?i:Ee(_)?o:Gt(k)?{[k]:_}:_),D,_),ee=k=>Rl(V(s.mount?i:o,k,t.shouldUnregister?V(o,k,[]):[])),Q=(k,_,D={})=>{const K=V(r,k);let z=_;if(K){const F=K._f;F&&(!F.disabled&&ue(i,k,Rg(_,F)),z=As(F.ref)&&tt(_)?"":_,xg(F.ref)?[...F.ref.options].forEach(H=>H.selected=z.includes(H.value)):F.refs?ui(F.ref)?F.refs.length>1?F.refs.forEach(H=>(!H.defaultChecked||!H.disabled)&&(H.checked=Array.isArray(z)?!!z.find(X=>X===H.value):z===H.value)):F.refs[0]&&(F.refs[0].checked=!!z):F.refs.forEach(H=>H.checked=H.value===z):wd(F.ref)?F.ref.value="":(F.ref.value=z,F.ref.type||p.state.next({name:k,values:ze(i)})))}(D.shouldDirty||D.shouldTouch)&&b(k,z,D.shouldTouch,D.shouldDirty,!0),D.shouldValidate&&Ve(k)},q=(k,_,D)=>{for(const K in _){const z=_[K],F=`${k}.${K}`,H=V(r,F);(l.array.has(k)||Pe(z)||H&&!H._f)&&!Xn(z)?q(F,z,D):Q(F,z,D)}},Y=(k,_,D={})=>{const K=V(r,k),z=l.array.has(k),F=ze(_);ue(i,k,F),z?(p.array.next({name:k,values:ze(i)}),(d.isDirty||d.dirtyFields||h.isDirty||h.dirtyFields)&&D.shouldDirty&&p.state.next({name:k,dirtyFields:yo(o,i),isDirty:W(k,F)})):K&&!K._f&&!tt(F)?q(k,F,D):Q(k,F,D),Lp(k,l)&&p.state.next(m({},n)),p.state.next({name:s.mount?k:void 0,values:ze(i)})},he=k=>Wt(this,null,function*(){s.mount=!0;const _=k.target;let D=_.name,K=!0;const z=V(r,D),F=H=>{K=Number.isNaN(H)||Xn(H)&&isNaN(H.getTime())||_n(H,V(i,D,H))};if(z){let H,X;const se=_.type?Dp(z._f):mg(k),me=k.type===Ns.BLUR||k.type===Ns.FOCUS_OUT,di=!y_(z._f)&&!t.resolver&&!V(n.errors,D)&&!z._f.deps||S_(me,V(n.touchedFields,D),n.isSubmitted,C,S),pr=Lp(D,l,me);ue(i,D,se),me?(z._f.onBlur&&z._f.onBlur(k),a&&a(0)):z._f.onChange&&z._f.onChange(k);const hr=b(D,se,me),vr=!Je(hr)||pr;if(!me&&p.state.next({name:D,type:k.type,values:ze(i)}),di)return(d.isValid||h.isValid)&&(t.mode==="onBlur"?me&&v():me||v()),vr&&p.state.next(m({name:D},pr?{}:hr));if(!me&&pr&&p.state.next(m({},n)),t.resolver){const{errors:Ed}=yield A([D]);if(F(se),K){const Dg=$p(n.errors,r,D),kd=$p(Ed,r,Dg.name||D);H=kd.error,D=kd.name,X=Je(Ed)}}else f([D],!0),H=(yield jp(z,l.disabled,i,g,t.shouldUseNativeValidation))[D],f([D]),F(se),K&&(H?X=!1:(d.isValid||h.isValid)&&(X=yield B(r,!0)));K&&(z._f.deps&&Ve(z._f.deps),M(D,X,H,hr))}}),Re=(k,_)=>{if(V(n.errors,_)&&k.focus)return k.focus(),1},Ve=(D,...K)=>Wt(this,[D,...K],function*(k,_={}){let z,F;const H=Io(k);if(t.resolver){const X=yield I(Ee(k)?k:H);z=Je(X),F=k?!H.some(se=>V(X,se)):z}else k?(F=(yield Promise.all(H.map(X=>Wt(this,null,function*(){const se=V(r,X);return yield B(se&&se._f?{[X]:se}:se)})))).every(Boolean),!(!F&&!n.isValid)&&v()):F=z=yield B(r);return p.state.next(R(m(m({},!Gt(k)||(d.isValid||h.isValid)&&z!==n.isValid?{}:{name:k}),t.resolver||!k?{isValid:z}:{}),{errors:n.errors})),_.shouldFocus&&!F&&Lo(r,Re,k?H:l.mount),F}),Nt=k=>{const _=m({},s.mount?i:o);return Ee(k)?_:Gt(k)?V(_,k):k.map(D=>V(_,D))},Bt=(k,_)=>({invalid:!!V((_||n).errors,k),isDirty:!!V((_||n).dirtyFields,k),error:V((_||n).errors,k),isValidating:!!V(n.validatingFields,k),isTouched:!!V((_||n).touchedFields,k)}),Zt=k=>{k&&Io(k).forEach(_=>be(n.errors,_)),p.state.next({errors:k?n.errors:{}})},it=(k,_,D)=>{const K=(V(r,k,{_f:{}})._f||{}).ref,me=V(n.errors,k)||{},{ref:F,message:H,type:X}=me,se=N(me,["ref","message","type"]);ue(n.errors,k,R(m(m({},se),_),{ref:K})),p.state.next({name:k,errors:n.errors,isValid:!1}),D&&D.shouldFocus&&K&&K.focus&&K.focus()},Jt=(k,_)=>Lt(k)?p.state.subscribe({next:D=>k(Z(void 0,_),D)}):Z(k,_,!0),Ze=k=>p.state.subscribe({next:_=>{w_(k.name,_.name,k.exact)&&g_(_,k.formState||d,lo,k.reRenderRoot)&&k.callback(m(m({values:m({},i)},n),_))}}).unsubscribe,G=k=>(s.mount=!0,h=m(m({},h),k.formState),Ze(R(m({},k),{formState:h}))),ie=(k,_={})=>{for(const D of k?Io(k):l.mount)l.mount.delete(D),l.array.delete(D),_.keepValue||(be(r,D),be(i,D)),!_.keepError&&be(n.errors,D),!_.keepDirty&&be(n.dirtyFields,D),!_.keepTouched&&be(n.touchedFields,D),!_.keepIsValidating&&be(n.validatingFields,D),!t.shouldUnregister&&!_.keepDefaultValue&&be(o,D);p.state.next({values:ze(i)}),p.state.next(m(m({},n),_.keepDirty?{isDirty:W()}:{})),!_.keepIsValid&&v()},Se=({disabled:k,name:_})=>{(ft(k)&&s.mount||k||l.disabled.has(_))&&(k?l.disabled.add(_):l.disabled.delete(_))},ne=(k,_={})=>{let D=V(r,k);const K=ft(_.disabled)||ft(t.disabled);return ue(r,k,R(m({},D||{}),{_f:m(R(m({},D&&D._f?D._f:{ref:{name:k}}),{name:k,mount:!0}),_)})),l.mount.add(k),D?Se({disabled:ft(_.disabled)?_.disabled:t.disabled,name:k}):T(k,!0,_.value),R(m(m({},K?{disabled:_.disabled||t.disabled}:{}),t.progressive?{required:!!_.required,min:go(_.min),max:go(_.max),minLength:go(_.minLength),maxLength:go(_.maxLength),pattern:go(_.pattern)}:{}),{name:k,onChange:he,onBlur:he,ref:z=>{if(z){ne(k,_),D=V(r,k);const F=Ee(z.value)&&z.querySelectorAll&&z.querySelectorAll("input,select,textarea")[0]||z,H=f_(F),X=D._f.refs||[];if(H?X.find(se=>se===F):F===D._f.ref)return;ue(r,k,{_f:m(m({},D._f),H?{refs:[...X.filter(ca),F,...Array.isArray(V(o,k))?[{}]:[]],ref:{type:F.type,name:k}}:{ref:F})}),T(k,!1,void 0,F)}else D=V(r,k,{}),D._f&&(D._f.mount=!1),(t.shouldUnregister||_.shouldUnregister)&&!(yg(l.array,k)&&s.action)&&l.unMount.add(k)}})},te=()=>t.shouldFocusError&&Lo(r,Re,l.mount),re=k=>{ft(k)&&(p.state.next({disabled:k}),Lo(r,(_,D)=>{const K=V(r,D);K&&(_.disabled=K._f.disabled||k,Array.isArray(K._f.refs)&&K._f.refs.forEach(z=>{z.disabled=K._f.disabled||k}))},0,!1))},Ae=(k,_)=>D=>Wt(this,null,function*(){let K;D&&(D.preventDefault&&D.preventDefault(),D.persist&&D.persist());let z=ze(i);if(p.state.next({isSubmitting:!0}),t.resolver){const{errors:F,values:H}=yield A();n.errors=F,z=H}else yield B(r);if(l.disabled.size)for(const F of l.disabled)ue(z,F,void 0);if(be(n.errors,"root"),Je(n.errors)){p.state.next({errors:{}});try{yield k(z,D)}catch(F){K=F}}else _&&(yield _(m({},n.errors),D)),te(),setTimeout(te);if(p.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:Je(n.errors)&&!K,submitCount:n.submitCount+1,errors:n.errors}),K)throw K}),Ie=(k,_={})=>{V(r,k)&&(Ee(_.defaultValue)?Y(k,ze(V(o,k))):(Y(k,_.defaultValue),ue(o,k,ze(_.defaultValue))),_.keepTouched||be(n.touchedFields,k),_.keepDirty||(be(n.dirtyFields,k),n.isDirty=_.defaultValue?W(k,ze(V(o,k))):W()),_.keepError||(be(n.errors,k),d.isValid&&v()),p.state.next(m({},n)))},Le=(k,_={})=>{const D=k?ze(k):o,K=ze(D),z=Je(k),F=z?o:K;if(_.keepDefaultValues||(o=D),!_.keepValues){if(_.keepDirtyValues){const H=new Set([...l.mount,...Object.keys(yo(o,i))]);for(const X of Array.from(H))V(n.dirtyFields,X)?ue(F,X,V(i,X)):Y(X,V(F,X))}else{if(md&&Ee(k))for(const H of l.mount){const X=V(r,H);if(X&&X._f){const se=Array.isArray(X._f.refs)?X._f.refs[0]:X._f.ref;if(As(se)){const me=se.closest("form");if(me){me.reset();break}}}}for(const H of l.mount)Y(H,V(F,H))}i=ze(F),p.array.next({values:m({},F)}),p.state.next({values:m({},F)})}l={mount:_.keepDirtyValues?l.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},s.mount=!d.isValid||!!_.keepIsValid||!!_.keepDirtyValues,s.watch=!!t.shouldUnregister,p.state.next({submitCount:_.keepSubmitCount?n.submitCount:0,isDirty:z?!1:_.keepDirty?n.isDirty:!!(_.keepDefaultValues&&!_n(k,o)),isSubmitted:_.keepIsSubmitted?n.isSubmitted:!1,dirtyFields:z?{}:_.keepDirtyValues?_.keepDefaultValues&&i?yo(o,i):n.dirtyFields:_.keepDefaultValues&&k?yo(o,k):_.keepDirty?n.dirtyFields:{},touchedFields:_.keepTouched?n.touchedFields:{},errors:_.keepErrors?n.errors:{},isSubmitSuccessful:_.keepIsSubmitSuccessful?n.isSubmitSuccessful:!1,isSubmitting:!1})},wt=(k,_)=>Le(Lt(k)?k(i):k,_),en=(k,_={})=>{const D=V(r,k),K=D&&D._f;if(K){const z=K.refs?K.refs[0]:K.ref;z.focus&&(z.focus(),_.shouldSelect&&Lt(z.select)&&z.select())}},lo=k=>{n=m(m({},n),k)},fr={control:{register:ne,unregister:ie,getFieldState:Bt,handleSubmit:Ae,setError:it,_subscribe:Ze,_runSchema:A,_getWatch:Z,_getDirty:W,_setValid:v,_setFieldArray:y,_setDisabledField:Se,_setErrors:P,_getFieldArray:ee,_reset:Le,_resetDefaultValues:()=>Lt(t.defaultValues)&&t.defaultValues().then(k=>{wt(k,t.resetOptions),p.state.next({isLoading:!1})}),_removeUnmounted:j,_disableForm:re,_subjects:p,_proxyFormState:d,get _fields(){return r},get _formValues(){return i},get _state(){return s},set _state(k){s=k},get _defaultValues(){return o},get _names(){return l},set _names(k){l=k},get _formState(){return n},get _options(){return t},set _options(k){t=m(m({},t),k)}},subscribe:G,trigger:Ve,register:ne,handleSubmit:Ae,watch:Jt,setValue:Y,getValues:Nt,reset:wt,resetField:Ie,clearErrors:Zt,unregister:ie,setError:it,setFocus:en,getFieldState:Bt};return R(m({},fr),{formControl:fr})}function gb(e={}){const t=$.useRef(void 0),n=$.useRef(void 0),[r,o]=$.useState({isDirty:!1,isValidating:!1,isLoading:Lt(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,defaultValues:Lt(e.defaultValues)?void 0:e.defaultValues});t.current||(t.current=R(m({},e.formControl?e.formControl:k_(e)),{formState:r}),e.formControl&&e.defaultValues&&!Lt(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions));const i=t.current.control;return i._options=e,$.useLayoutEffect(()=>i._subscribe({formState:i._proxyFormState,callback:()=>o(m({},i._formState)),reRenderRoot:!0}),[i]),$.useEffect(()=>i._disableForm(e.disabled),[i,e.disabled]),$.useEffect(()=>{if(i._proxyFormState.isDirty){const s=i._getDirty();s!==r.isDirty&&i._subjects.state.next({isDirty:s})}},[i,r.isDirty]),$.useEffect(()=>{e.values&&!_n(e.values,n.current)?(i._reset(e.values,i._options.resetOptions),n.current=e.values,o(s=>m({},s))):i._resetDefaultValues()},[e.values,i]),$.useEffect(()=>{e.errors&&!Je(e.errors)&&i._setErrors(e.errors)},[e.errors,i]),$.useEffect(()=>{i._state.mount||(i._setValid(),i._state.mount=!0),i._state.watch&&(i._state.watch=!1,i._subjects.state.next(m({},i._formState))),i._removeUnmounted()}),$.useEffect(()=>{e.shouldUnregister&&i._subjects.state.next({values:i._getWatch()})},[e.shouldUnregister,i]),t.current.formState=Sg(r,i),t.current}function P_(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Tg={exports:{}},da={};/**
 * @license React
 * use-sync-external-store-shim.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vp;function R_(){if(Vp)return da;Vp=1;var e=$;function t(h,p){return h===p&&(h!==0||1/h===1/p)||h!==h&&p!==p}var n=typeof Object.is=="function"?Object.is:t,r=e.useState,o=e.useEffect,i=e.useLayoutEffect,s=e.useDebugValue;function l(h,p){var S=p(),C=r({inst:{value:S,getSnapshot:p}}),g=C[0].inst,w=C[1];return i(function(){g.value=S,g.getSnapshot=p,a(g)&&w({inst:g})},[h,S,p]),o(function(){return a(g)&&w({inst:g}),h(function(){a(g)&&w({inst:g})})},[h]),s(S),S}function a(h){var p=h.getSnapshot;h=h.value;try{var S=p();return!n(h,S)}catch(C){return!0}}function c(h,p){return p()}var d=typeof window=="undefined"||typeof window.document=="undefined"||typeof window.document.createElement=="undefined"?c:l;return da.useSyncExternalStore=e.useSyncExternalStore!==void 0?e.useSyncExternalStore:d,da}Tg.exports=R_();var Cd=Tg.exports;const __=(...e)=>t=>{e.forEach(n=>{typeof n=="function"?n(t):n&&(n.current=t)})},T_=({contentComponent:e})=>{const t=Cd.useSyncExternalStore(e.subscribe,e.getSnapshot,e.getServerSnapshot);return $.createElement($.Fragment,null,Object.values(t))};function N_(){const e=new Set;let t={};return{subscribe(n){return e.add(n),()=>{e.delete(n)}},getSnapshot(){return t},getServerSnapshot(){return t},setRenderer(n,r){t=R(m({},t),{[n]:Ac.createPortal(r.reactElement,r.element,n)}),e.forEach(o=>o())},removeRenderer(n){const r=m({},t);delete r[n],t=r,e.forEach(o=>o())}}}class A_ extends $.Component{constructor(t){var n;super(t),this.editorContentRef=$.createRef(),this.initialized=!1,this.state={hasContentComponentInitialized:!!(!((n=t.editor)===null||n===void 0)&&n.contentComponent)}}componentDidMount(){this.init()}componentDidUpdate(){this.init()}init(){const t=this.props.editor;if(t&&!t.isDestroyed&&t.options.element){if(t.contentComponent)return;const n=this.editorContentRef.current;n.append(...t.options.element.childNodes),t.setOptions({element:n}),t.contentComponent=N_(),this.state.hasContentComponentInitialized||(this.unsubscribeToContentComponent=t.contentComponent.subscribe(()=>{this.setState(r=>r.hasContentComponentInitialized?r:{hasContentComponentInitialized:!0}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent()})),t.createNodeViews(),this.initialized=!0}}componentWillUnmount(){const t=this.props.editor;if(!t||(this.initialized=!1,t.isDestroyed||t.view.setProps({nodeViews:{}}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent(),t.contentComponent=null,!t.options.element.firstChild))return;const n=document.createElement("div");n.append(...t.options.element.childNodes),t.setOptions({element:n})}render(){const o=this.props,{editor:t,innerRef:n}=o,r=N(o,["editor","innerRef"]);return $.createElement($.Fragment,null,$.createElement("div",m({ref:__(n,this.editorContentRef)},r)),(t==null?void 0:t.contentComponent)&&$.createElement(T_,{contentComponent:t.contentComponent}))}}const b_=u.forwardRef((e,t)=>{const n=$.useMemo(()=>Math.floor(Math.random()*4294967295).toString(),[e.editor]);return $.createElement(A_,m({key:n,innerRef:t},e))}),wb=$.memo(b_);var M_=function e(t,n){if(t===n)return!0;if(t&&n&&typeof t=="object"&&typeof n=="object"){if(t.constructor!==n.constructor)return!1;var r,o,i;if(Array.isArray(t)){if(r=t.length,r!=n.length)return!1;for(o=r;o--!==0;)if(!e(t[o],n[o]))return!1;return!0}if(t instanceof Map&&n instanceof Map){if(t.size!==n.size)return!1;for(o of t.entries())if(!n.has(o[0]))return!1;for(o of t.entries())if(!e(o[1],n.get(o[0])))return!1;return!0}if(t instanceof Set&&n instanceof Set){if(t.size!==n.size)return!1;for(o of t.entries())if(!n.has(o[0]))return!1;return!0}if(ArrayBuffer.isView(t)&&ArrayBuffer.isView(n)){if(r=t.length,r!=n.length)return!1;for(o=r;o--!==0;)if(t[o]!==n[o])return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if(i=Object.keys(t),r=i.length,r!==Object.keys(n).length)return!1;for(o=r;o--!==0;)if(!Object.prototype.hasOwnProperty.call(n,i[o]))return!1;for(o=r;o--!==0;){var s=i[o];if(!(s==="_owner"&&t.$$typeof)&&!e(t[s],n[s]))return!1}return!0}return t!==t&&n!==n},D_=P_(M_),Ng={exports:{}},fa={};/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var zp;function O_(){if(zp)return fa;zp=1;var e=$,t=Cd;function n(c,d){return c===d&&(c!==0||1/c===1/d)||c!==c&&d!==d}var r=typeof Object.is=="function"?Object.is:n,o=t.useSyncExternalStore,i=e.useRef,s=e.useEffect,l=e.useMemo,a=e.useDebugValue;return fa.useSyncExternalStoreWithSelector=function(c,d,h,p,S){var C=i(null);if(C.current===null){var g={hasValue:!1,value:null};C.current=g}else g=C.current;C=l(function(){function v(T){if(!f){if(f=!0,y=T,T=p(T),S!==void 0&&g.hasValue){var b=g.value;if(S(b,T))return E=b}return E=T}if(b=E,r(y,T))return b;var M=p(T);return S!==void 0&&S(b,M)?b:(y=T,E=M)}var f=!1,y,E,P=h===void 0?null:h;return[function(){return v(d())},P===null?void 0:function(){return v(P())}]},[d,h,p,S]);var w=o(c,C[0],C[1]);return s(function(){g.hasValue=!0,g.value=w},[w]),a(w),w},fa}Ng.exports=O_();var I_=Ng.exports;const L_=typeof window!="undefined"?u.useLayoutEffect:u.useEffect;class $_{constructor(t){this.transactionNumber=0,this.lastTransactionNumber=0,this.subscribers=new Set,this.editor=t,this.lastSnapshot={editor:t,transactionNumber:0},this.getSnapshot=this.getSnapshot.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.watch=this.watch.bind(this),this.subscribe=this.subscribe.bind(this)}getSnapshot(){return this.transactionNumber===this.lastTransactionNumber?this.lastSnapshot:(this.lastTransactionNumber=this.transactionNumber,this.lastSnapshot={editor:this.editor,transactionNumber:this.transactionNumber},this.lastSnapshot)}getServerSnapshot(){return{editor:null,transactionNumber:0}}subscribe(t){return this.subscribers.add(t),()=>{this.subscribers.delete(t)}}watch(t){if(this.editor=t,this.editor){const n=()=>{this.transactionNumber+=1,this.subscribers.forEach(o=>o())},r=this.editor;return r.on("transaction",n),()=>{r.off("transaction",n)}}}}function F_(e){var t;const[n]=u.useState(()=>new $_(e.editor)),r=I_.useSyncExternalStoreWithSelector(n.subscribe,n.getSnapshot,n.getServerSnapshot,e.selector,(t=e.equalityFn)!==null&&t!==void 0?t:D_);return L_(()=>n.watch(e.editor),[e.editor,n]),u.useDebugValue(r),r}const j_=!1,Du=typeof window=="undefined",V_=Du||!!(typeof window!="undefined"&&window.next);class xd{constructor(t){this.editor=null,this.subscriptions=new Set,this.isComponentMounted=!1,this.previousDeps=null,this.instanceId="",this.options=t,this.subscriptions=new Set,this.setEditor(this.getInitialEditor()),this.scheduleDestroy(),this.getEditor=this.getEditor.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.subscribe=this.subscribe.bind(this),this.refreshEditorInstance=this.refreshEditorInstance.bind(this),this.scheduleDestroy=this.scheduleDestroy.bind(this),this.onRender=this.onRender.bind(this),this.createEditor=this.createEditor.bind(this)}setEditor(t){this.editor=t,this.instanceId=Math.random().toString(36).slice(2,9),this.subscriptions.forEach(n=>n())}getInitialEditor(){if(this.options.current.immediatelyRender===void 0)return Du||V_?null:this.createEditor();if(this.options.current.immediatelyRender&&Du&&j_)throw new Error("Tiptap Error: SSR has been detected, and `immediatelyRender` has been set to `true` this is an unsupported configuration that may result in errors, explicitly set `immediatelyRender` to `false` to avoid hydration mismatches.");return this.options.current.immediatelyRender?this.createEditor():null}createEditor(){const t=R(m({},this.options.current),{onBeforeCreate:(...r)=>{var o,i;return(i=(o=this.options.current).onBeforeCreate)===null||i===void 0?void 0:i.call(o,...r)},onBlur:(...r)=>{var o,i;return(i=(o=this.options.current).onBlur)===null||i===void 0?void 0:i.call(o,...r)},onCreate:(...r)=>{var o,i;return(i=(o=this.options.current).onCreate)===null||i===void 0?void 0:i.call(o,...r)},onDestroy:(...r)=>{var o,i;return(i=(o=this.options.current).onDestroy)===null||i===void 0?void 0:i.call(o,...r)},onFocus:(...r)=>{var o,i;return(i=(o=this.options.current).onFocus)===null||i===void 0?void 0:i.call(o,...r)},onSelectionUpdate:(...r)=>{var o,i;return(i=(o=this.options.current).onSelectionUpdate)===null||i===void 0?void 0:i.call(o,...r)},onTransaction:(...r)=>{var o,i;return(i=(o=this.options.current).onTransaction)===null||i===void 0?void 0:i.call(o,...r)},onUpdate:(...r)=>{var o,i;return(i=(o=this.options.current).onUpdate)===null||i===void 0?void 0:i.call(o,...r)},onContentError:(...r)=>{var o,i;return(i=(o=this.options.current).onContentError)===null||i===void 0?void 0:i.call(o,...r)},onDrop:(...r)=>{var o,i;return(i=(o=this.options.current).onDrop)===null||i===void 0?void 0:i.call(o,...r)},onPaste:(...r)=>{var o,i;return(i=(o=this.options.current).onPaste)===null||i===void 0?void 0:i.call(o,...r)}});return new iw(t)}getEditor(){return this.editor}getServerSnapshot(){return null}subscribe(t){return this.subscriptions.add(t),()=>{this.subscriptions.delete(t)}}static compareOptions(t,n){return Object.keys(t).every(r=>["onCreate","onBeforeCreate","onDestroy","onUpdate","onTransaction","onFocus","onBlur","onSelectionUpdate","onContentError","onDrop","onPaste"].includes(r)?!0:r==="extensions"&&t.extensions&&n.extensions?t.extensions.length!==n.extensions.length?!1:t.extensions.every((o,i)=>{var s;return o===((s=n.extensions)===null||s===void 0?void 0:s[i])}):t[r]===n[r])}onRender(t){return()=>(this.isComponentMounted=!0,clearTimeout(this.scheduledDestructionTimeout),this.editor&&!this.editor.isDestroyed&&t.length===0?xd.compareOptions(this.options.current,this.editor.options)||this.editor.setOptions(R(m({},this.options.current),{editable:this.editor.isEditable})):this.refreshEditorInstance(t),()=>{this.isComponentMounted=!1,this.scheduleDestroy()})}refreshEditorInstance(t){if(this.editor&&!this.editor.isDestroyed){if(this.previousDeps===null){this.previousDeps=t;return}if(this.previousDeps.length===t.length&&this.previousDeps.every((r,o)=>r===t[o]))return}this.editor&&!this.editor.isDestroyed&&this.editor.destroy(),this.setEditor(this.createEditor()),this.previousDeps=t}scheduleDestroy(){const t=this.instanceId,n=this.editor;this.scheduledDestructionTimeout=setTimeout(()=>{if(this.isComponentMounted&&this.instanceId===t){n&&n.setOptions(this.options.current);return}n&&!n.isDestroyed&&(n.destroy(),this.instanceId===t&&this.setEditor(null))},1)}}function Sb(e={},t=[]){const n=u.useRef(e);n.current=e;const[r]=u.useState(()=>new xd(n)),o=Cd.useSyncExternalStore(r.subscribe,r.getEditor,r.getServerSnapshot);return u.useDebugValue(o),u.useEffect(r.onRender(t)),F_({editor:o,selector:({transactionNumber:i})=>e.shouldRerenderOnTransaction===!1?null:e.immediatelyRender&&i===0?0:i+1}),o}const z_=u.createContext({editor:null});z_.Consumer;const Ag=u.createContext({onDragStart:void 0}),bg=()=>u.useContext(Ag),Cb=e=>{const t=e.as||"div",{nodeViewContentRef:n}=bg();return $.createElement(t,R(m({},e),{ref:n,"data-node-view-content":"",style:m({whiteSpace:"pre-wrap"},e.style)}))},xb=$.forwardRef((e,t)=>{const{onDragStart:n}=bg(),r=e.as||"div";return $.createElement(r,R(m({},e),{ref:t,"data-node-view-wrapper":"",onDragStart:n,style:m({whiteSpace:"normal"},e.style)}))});function U_(e){return!!(typeof e=="function"&&e.prototype&&e.prototype.isReactComponent)}function B_(e){var t;return typeof e=="object"&&((t=e.$$typeof)===null||t===void 0?void 0:t.toString())==="Symbol(react.forward_ref)"}class W_{constructor(t,{editor:n,props:r={},as:o="div",className:i=""}){this.ref=null,this.id=Math.floor(Math.random()*4294967295).toString(),this.component=t,this.editor=n,this.props=r,this.element=document.createElement(o),this.element.classList.add("react-renderer"),i&&this.element.classList.add(...i.split(" ")),this.editor.isInitialized?hn.flushSync(()=>{this.render()}):this.render()}render(){var t;const n=this.component,r=this.props,o=this.editor;(U_(n)||B_(n))&&(r.ref=i=>{this.ref=i}),this.reactElement=$.createElement(n,m({},r)),(t=o==null?void 0:o.contentComponent)===null||t===void 0||t.setRenderer(this.id,this)}updateProps(t={}){this.props=m(m({},this.props),t),this.render()}destroy(){var t;const n=this.editor;(t=n==null?void 0:n.contentComponent)===null||t===void 0||t.removeRenderer(this.id)}updateAttributes(t){Object.keys(t).forEach(n=>{this.element.setAttribute(n,t[n])})}}class H_ extends sw{mount(){const t={editor:this.editor,node:this.node,decorations:this.decorations,innerDecorations:this.innerDecorations,view:this.view,selected:!1,extension:this.extension,HTMLAttributes:this.HTMLAttributes,getPos:()=>this.getPos(),updateAttributes:(c={})=>this.updateAttributes(c),deleteNode:()=>this.deleteNode()};if(!this.component.displayName){const c=d=>d.charAt(0).toUpperCase()+d.substring(1);this.component.displayName=c(this.extension.name)}const o={onDragStart:this.onDragStart.bind(this),nodeViewContentRef:c=>{c&&this.contentDOMElement&&c.firstChild!==this.contentDOMElement&&c.appendChild(this.contentDOMElement)}},i=this.component,s=$.memo(c=>$.createElement(Ag.Provider,{value:o},$.createElement(i,c)));s.displayName="ReactNodeView",this.node.isLeaf?this.contentDOMElement=null:this.options.contentDOMElementTag?this.contentDOMElement=document.createElement(this.options.contentDOMElementTag):this.contentDOMElement=document.createElement(this.node.isInline?"span":"div"),this.contentDOMElement&&(this.contentDOMElement.dataset.nodeViewContentReact="",this.contentDOMElement.style.whiteSpace="inherit");let l=this.node.isInline?"span":"div";this.options.as&&(l=this.options.as);const{className:a=""}=this.options;this.handleSelectionUpdate=this.handleSelectionUpdate.bind(this),this.renderer=new W_(s,{editor:this.editor,props:t,as:l,className:`node-${this.node.type.name} ${a}`.trim()}),this.editor.on("selectionUpdate",this.handleSelectionUpdate),this.updateElementAttributes()}get dom(){var t;if(this.renderer.element.firstElementChild&&!(!((t=this.renderer.element.firstElementChild)===null||t===void 0)&&t.hasAttribute("data-node-view-wrapper")))throw Error("Please use the NodeViewWrapper component for your node view.");return this.renderer.element}get contentDOM(){return this.node.isLeaf?null:this.contentDOMElement}handleSelectionUpdate(){const{from:t,to:n}=this.editor.state.selection,r=this.getPos();if(typeof r=="number")if(t<=r&&n>=r+this.node.nodeSize){if(this.renderer.props.selected)return;this.selectNode()}else{if(!this.renderer.props.selected)return;this.deselectNode()}}update(t,n,r){const o=i=>{this.renderer.updateProps(i),typeof this.options.attrs=="function"&&this.updateElementAttributes()};if(t.type!==this.node.type)return!1;if(typeof this.options.update=="function"){const i=this.node,s=this.decorations,l=this.innerDecorations;return this.node=t,this.decorations=n,this.innerDecorations=r,this.options.update({oldNode:i,oldDecorations:s,newNode:t,newDecorations:n,oldInnerDecorations:l,innerDecorations:r,updateProps:()=>o({node:t,decorations:n,innerDecorations:r})})}return t===this.node&&this.decorations===n&&this.innerDecorations===r||(this.node=t,this.decorations=n,this.innerDecorations=r,o({node:t,decorations:n,innerDecorations:r})),!0}selectNode(){this.renderer.updateProps({selected:!0}),this.renderer.element.classList.add("ProseMirror-selectednode")}deselectNode(){this.renderer.updateProps({selected:!1}),this.renderer.element.classList.remove("ProseMirror-selectednode")}destroy(){this.renderer.destroy(),this.editor.off("selectionUpdate",this.handleSelectionUpdate),this.contentDOMElement=null}updateElementAttributes(){if(this.options.attrs){let t={};if(typeof this.options.attrs=="function"){const n=this.editor.extensionManager.attributes,r=lw(this.node,n);t=this.options.attrs({node:this.node,HTMLAttributes:r})}else t=this.options.attrs;this.renderer.updateAttributes(t)}}}function Eb(e,t){return n=>n.editor.contentComponent?new H_(e,n,t):{}}var K_=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],Y_=K_.reduce((e,t)=>{const n=u.forwardRef((r,o)=>{const a=r,{asChild:i}=a,s=N(a,["asChild"]),l=i?Oe:t;return typeof window!="undefined"&&(window[Symbol.for("radix-ui")]=!0),x.jsx(l,R(m({},s),{ref:o}))});return n.displayName=`Primitive.${t}`,R(m({},e),{[t]:n})},{}),G_="Separator",Up="horizontal",Q_=["horizontal","vertical"],Mg=u.forwardRef((e,t)=>{const a=e,{decorative:n,orientation:r=Up}=a,o=N(a,["decorative","orientation"]),i=q_(r)?r:Up,l=n?{role:"none"}:{"aria-orientation":i==="vertical"?i:void 0,role:"separator"};return x.jsx(Y_.div,R(m(m({"data-orientation":i},l),o),{ref:t}))});Mg.displayName=G_;function q_(e){return Q_.includes(e)}var kb=Mg;export{hN as $,fA as A,hA as B,oT as C,RE as D,IT as E,OT as F,Qy as G,RA as H,vA as I,TA as J,AA as K,wA as L,bA as M,MA as N,EE as O,tT as P,cP as Q,$ as R,Oe as S,rT as T,mP as U,gA as V,_A as W,aA as X,XN as Y,MN as Z,dr as _,Ac as a,gb as a$,uT as a0,$N as a1,xN as a2,yx as a3,iN as a4,uN as a5,HN as a6,PN as a7,eN as a8,MT as a9,qN as aA,BN as aB,_T as aC,XT as aD,TT as aE,lN as aF,ZN as aG,aT as aH,vN as aI,yT as aJ,hT as aK,UN as aL,QN as aM,nA as aN,LT as aO,UT as aP,ab as aQ,ub as aR,cb as aS,zT as aT,$T as aU,GN as aV,VT as aW,GT as aX,gd as aY,mb as aZ,yb as a_,mT as aa,sA as ab,wN as ac,_N as ad,kT as ae,ST as af,BT as ag,gT as ah,DA as ai,W1 as aj,iT as ak,dT as al,cT as am,ix as an,D2 as ao,QT as ap,qT as aq,yN as ar,iA as as,vT as at,bT as au,mN as av,Bm as aw,lT as ax,lA as ay,sT as az,d_ as b,CN as b$,sN as b0,CT as b1,TN as b2,DN as b3,jT as b4,FT as b5,ON as b6,oA as b7,rA as b8,xT as b9,WT as bA,oN as bB,tN as bC,nN as bD,rN as bE,pN as bF,fN as bG,zN as bH,NN as bI,dN as bJ,KN as bK,uA as bL,tA as bM,AN as bN,EN as bO,SN as bP,JN as bQ,wb as bR,RN as bS,cA as bT,kN as bU,Eb as bV,xb as bW,Cb as bX,cN as bY,VN as bZ,jN as b_,KT as ba,JT as bb,ET as bc,pT as bd,RT as be,UA as bf,BA as bg,QA as bh,qA as bi,JA as bj,eb as bk,XA as bl,ZA as bm,KA as bn,GA as bo,YA as bp,FN as bq,YT as br,LN as bs,HT as bt,kb as bu,Sb as bv,wT as bw,aN as bx,eA as by,WN as bz,nT as c,YN as c0,ZT as c1,rb as c2,ob as c3,ib as c4,sb as c5,nb as c6,WA as c7,bN as c8,fb as c9,pb as ca,hb as cb,vb as cc,PT as cd,jA as ce,VA as cf,IA as cg,LA as ch,$A as ci,fT as cj,gN as ck,IN as cl,kE as d,Ty as e,PE as f,V as g,SE as h,xE as i,x as j,CE as k,pA as l,AT as m,EA as n,DT as o,kA as p,mA as q,u as r,ue as s,yA as t,Lc as u,SA as v,xA as w,NT as x,CA as y,PA as z};
