import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import { MarkdownPreview } from './markdown-preview';

interface EnhancedMarkdownEditorProps {
  initialContent?: string;
  onChange?: (content: string) => void;
  className?: string;
  minHeight?: number;
  placeholder?: string;
  autoFocus?: boolean;
}

export function EnhancedMarkdownEditor({
  initialContent = '',
  onChange,
  className,
  minHeight = 300,
  placeholder = 'Write your content here using Markdown...',
  autoFocus = false
}: EnhancedMarkdownEditorProps) {
  const [content, setContent] = useState(initialContent);
  const [activeTab, setActiveTab] = useState<'write' | 'preview'>('write');

  useEffect(() => {
    setContent(initialContent);
  }, [initialContent]);

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newContent = e.target.value;
    setContent(newContent);
    if (onChange) onChange(newContent);
  };

  return (
    <div className={cn("border rounded-md", className)}>
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'write' | 'preview')}>
        <TabsList className="bg-muted/30 border-b rounded-none">
          <TabsTrigger value="write">Write</TabsTrigger>
          <TabsTrigger value="preview">Preview</TabsTrigger>
        </TabsList>

        <TabsContent value="write" className="p-0 m-0">
          <Textarea
            id="enhanced-editor-textarea"
            value={content}
            onChange={handleContentChange}
            className="min-h-[300px] border-0 rounded-none rounded-b-md focus-visible:ring-0 resize-y"
            placeholder={placeholder}
            style={{ minHeight: `${minHeight}px` }}
            autoFocus={autoFocus}
          />
        </TabsContent>

        <TabsContent value="preview" className="p-4 m-0 min-h-[300px]" style={{ minHeight: `${minHeight}px` }}>
          <MarkdownPreview content={content} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
