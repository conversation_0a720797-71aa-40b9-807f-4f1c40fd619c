import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createDemographicTables() {
  try {
    console.log('Creating demographic questionnaire tables...');

    // First, let's insert the default questionnaire data directly
    const defaultQuestionnaire = {
      title: 'Student Demographic Questionnaire',
      description: 'Please complete this questionnaire to help us better understand our student demographics.',
      questions: [
        {
          id: "consent",
          question: "Would you like to proceed with the questionnaire?",
          type: "single_choice",
          required: true,
          options: ["Agree", "Disagree"]
        },
        {
          id: "country",
          question: "What country are you from?",
          type: "text",
          required: true
        },
        {
          id: "gender",
          question: "Gender",
          type: "single_choice",
          required: true,
          options: ["Male", "Female"]
        },
        {
          id: "age",
          question: "Please state your age",
          type: "number",
          required: true
        },
        {
          id: "formal_training",
          question: "Have you received any formal training on IV cannulation and contrast administration?",
          type: "single_choice",
          required: true,
          options: ["Yes", "No"]
        },
        {
          id: "role_type",
          question: "Are you a medical imaging student or a practitioner?",
          type: "single_choice",
          required: true,
          options: ["Student", "Practitioner", "Other"]
        },
        {
          id: "student_level",
          question: "Are you an undergraduate or a postgraduate student?",
          type: "single_choice",
          required: false,
          conditional: { field: "role_type", value: "Student" },
          options: ["Undergraduate", "Postgraduate"]
        },
        {
          id: "university",
          question: "Which university do you attend?",
          type: "single_choice",
          required: false,
          conditional: { field: "role_type", value: "Student" },
          options: [
            "University of Ghana (UG)",
            "Kwame Nkrumah University of Science and Technology (KNUST)",
            "University of Cape Coast (UCC)",
            "University of Development Studies (UDS)",
            "University of Health and Allied Sciences (UHAS)",
            "Accra Technical University (ATU)",
            "College of Health and Well-Being, Kintampo (KRHTS)",
            "Other"
          ]
        },
        {
          id: "undergraduate_program",
          question: "What program are you reading?",
          type: "single_choice",
          required: false,
          conditional: { field: "student_level", value: "Undergraduate" },
          options: [
            "BSc Diagnostic Radiography",
            "BSc Diagnostic Sonography",
            "BSc Radiation Therapy",
            "BSc Nuclear Medicine Technology",
            "Other"
          ]
        },
        {
          id: "undergraduate_year",
          question: "What year are you in?",
          type: "single_choice",
          required: false,
          conditional: { field: "student_level", value: "Undergraduate" },
          options: ["1st Year", "2nd Year", "3rd Year", "4th Year", "5th Year", "6th Year"]
        },
        {
          id: "postgraduate_program",
          question: "What program are you reading?",
          type: "single_choice",
          required: false,
          conditional: { field: "student_level", value: "Postgraduate" },
          options: ["MSc", "MPhil", "PhD", "Other"]
        },
        {
          id: "practitioner_work",
          question: "What do you do for work?",
          type: "single_choice",
          required: false,
          conditional: { field: "role_type", value: "Practitioner" },
          options: [
            "Diagnostic Radiographer",
            "Diagnostic Sonography",
            "Radiation Therapy",
            "Nuclear Medicine Technology",
            "Other"
          ]
        },
        {
          id: "workplace",
          question: "Place of work",
          type: "single_choice",
          required: false,
          conditional: { field: "role_type", value: "Practitioner" },
          options: [
            "Tertiary Hospital",
            "Regional Hospital",
            "District Hospital",
            "Diagnostic Center",
            "Other"
          ]
        },
        {
          id: "location",
          question: "Location",
          type: "single_choice",
          required: false,
          conditional: { field: "role_type", value: "Practitioner" },
          options: [
            "Ashanti Region", "Ahafo Region", "Bono Region", "Bono East Region",
            "Central Region", "Eastern Region", "Greater Accra Region",
            "North East Region", "Northern Region", "Oti Region",
            "Savannah Region", "Upper East Region", "Upper West Region",
            "Volta Region", "Western Region", "Western North Region"
          ]
        },
        {
          id: "experience_years",
          question: "Years of Experience",
          type: "single_choice",
          required: false,
          conditional: { field: "role_type", value: "Practitioner" },
          options: ["1 Year", "2 Years", "3 Years", "4 Years", "5 Years", "Other"]
        }
      ],
      is_active: true
    };

    // Check if tables exist by trying to query them
    console.log('Checking if demographic tables exist...');
    
    const { data: existingQuestionnaire, error: checkError } = await supabase
      .from('demographic_questionnaires')
      .select('*')
      .limit(1);

    if (checkError && checkError.code === '42P01') {
      console.log('Tables do not exist. Please create them manually in Supabase dashboard.');
      console.log('Use the SQL from: supabase/migrations/20250115001_demographic_questionnaire.sql');
      return;
    }

    if (existingQuestionnaire && existingQuestionnaire.length > 0) {
      console.log('✅ Demographic questionnaire already exists');
      return;
    }

    // Insert the default questionnaire
    const { data, error } = await supabase
      .from('demographic_questionnaires')
      .insert([defaultQuestionnaire])
      .select();

    if (error) {
      console.error('Error inserting questionnaire:', error);
      return;
    }

    console.log('✅ Default demographic questionnaire created successfully!');
    console.log('Questionnaire ID:', data[0].id);

  } catch (error) {
    console.error('Error:', error);
  }
}

createDemographicTables();
