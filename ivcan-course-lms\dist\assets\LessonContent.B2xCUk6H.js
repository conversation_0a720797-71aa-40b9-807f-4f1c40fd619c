var P=Object.defineProperty,S=Object.defineProperties;var B=Object.getOwnPropertyDescriptors;var p=Object.getOwnPropertySymbols;var L=Object.prototype.hasOwnProperty,E=Object.prototype.propertyIsEnumerable;var k=(r,a,t)=>a in r?P(r,a,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[a]=t,x=(r,a)=>{for(var t in a||(a={}))L.call(a,t)&&k(r,t,a[t]);if(p)for(var t of p(a))E.call(a,t)&&k(r,t,a[t]);return r},h=(r,a)=>S(r,B(a));var f=(r,a)=>{var t={};for(var s in r)L.call(r,s)&&a.indexOf(s)<0&&(t[s]=r[s]);if(r!=null&&p)for(var s of p(r))a.indexOf(s)<0&&E.call(r,s)&&(t[s]=r[s]);return t};import{r as i,j as e,c2 as O,c3 as Y,c4 as M,b2 as _,bU as $,c5 as T,c6 as J,R as W,aX as X}from"./vendor-react.BcAa1DKr.js";import{M as A}from"./markdown-preview.Bw0U-NJA.js";import{c as g,B as q}from"./index.BLDhDn0D.js";import{a4 as m}from"./vendor.DQpuTRuB.js";const D=J,I=i.forwardRef((s,t)=>{var c=s,{className:r}=c,a=f(c,["className"]);return e.jsx(O,x({ref:t,className:g("border border-border/20 rounded-xl bg-card/80 backdrop-blur-sm shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden","hover:border-primary/20 hover:bg-card/90",r)},a))});I.displayName="AccordionItem";const z=i.forwardRef((c,s)=>{var o=c,{className:r,children:a}=o,t=f(o,["className","children"]);return e.jsx(Y,{className:"flex",children:e.jsxs(M,h(x({ref:s,className:g("flex flex-1 items-center justify-between px-6 py-5 font-semibold text-left","bg-gradient-to-r from-primary/3 to-primary/6 hover:from-primary/6 hover:to-primary/10","transition-all duration-300 ease-out","hover:bg-primary/5 focus:bg-primary/8 focus:outline-none focus:ring-2 focus:ring-primary/20","group cursor-pointer text-foreground/90 hover:text-foreground","[&[data-state=open]]:bg-primary/8 [&[data-state=open]]:text-foreground",r)},t),{children:[e.jsx("span",{className:"flex items-center gap-3 text-sm font-medium",children:a}),e.jsxs("div",{className:"h-5 w-5 shrink-0 transition-all duration-300 text-primary/60 group-hover:text-primary",children:[e.jsx(_,{className:"h-5 w-5 group-data-[state=open]:hidden"}),e.jsx($,{className:"h-5 w-5 hidden group-data-[state=open]:block"})]})]}))})});z.displayName=M.displayName;const F=i.forwardRef((c,s)=>{var o=c,{className:r,children:a}=o,t=f(o,["className","children"]);return e.jsx(T,h(x({ref:s,className:"overflow-hidden transition-all duration-300 ease-out data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"},t),{children:e.jsx("div",{className:g("px-6 pb-6 pt-3","bg-gradient-to-b from-background/30 to-background/60","border-t border-border/10",r),children:e.jsx("div",{className:"prose prose-sm max-w-none text-foreground/80",children:a})})}))});F.displayName=T.displayName;const G=W.memo(({content:r,className:a=""})=>{const t=i.useRef(!0),s=i.useRef(null);i.useEffect(()=>(t.current=!0,()=>{t.current=!1,s.current&&(clearTimeout(s.current),s.current=null)}),[]);const c=i.useCallback(l=>{if(!l)return{videoUrl:null,imageUrl:null,textContent:"",externalRedirectUrl:null,mainContent:"",referencesContent:""};let v=null,w=null,d=l,R=null;try{const n=JSON.parse(l);n&&typeof n=="object"&&(v=n.videoUrl||null,w=n.imageUrl||null,d=n.content||l,R=n.externalRedirectUrl||null)}catch(n){d=l}const y=d.match(/(^|\n)(#+\s*References?\s*\n[\s\S]*)$/i);let U=d,C="";if(y){const n=y.index+y[1].length;U=d.slice(0,n).trim(),C=d.slice(n).trim()}return{videoUrl:v,imageUrl:w,textContent:d,externalRedirectUrl:R,mainContent:U,referencesContent:C}},[]),o=i.useMemo(()=>c(r),[r,c]);if(i.useEffect(()=>{const{externalRedirectUrl:l}=o;if(l)return s.current=window.setTimeout(()=>{t.current&&window.open(l,"_blank")},500),()=>{s.current&&(clearTimeout(s.current),s.current=null)}},[o.externalRedirectUrl]),!r)return e.jsx("div",{className:"lesson-content-container",children:e.jsx("div",{className:"flex items-center justify-center p-8 bg-muted/30 rounded-lg",children:e.jsx("p",{className:"text-muted-foreground text-center",children:"No content available for this lesson."})})});const{videoUrl:u,imageUrl:b,mainContent:H,referencesContent:N,externalRedirectUrl:j}=o;return e.jsxs(m.div,{className:`lesson-content-container lesson-content-no-padding ${a}`,initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.5},children:[j&&e.jsxs(m.div,{className:"mb-6 flex flex-col items-center justify-center p-6 bg-primary/5 rounded-xl border border-primary/20",initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.5,delay:.2},children:[e.jsx("h2",{className:"text-xl font-semibold mb-3",children:"External Form"}),e.jsx("p",{className:"text-center mb-4",children:"You are being redirected to an external form. If you are not redirected automatically, please click the button below."}),e.jsxs(q,{size:"lg",onClick:()=>window.open(j,"_blank"),className:"flex items-center",children:[e.jsx(X,{className:"mr-2 h-5 w-5"}),"Open External Form"]})]}),u&&e.jsx(m.div,{className:"mb-6",initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.5,delay:.2},children:e.jsx("div",{className:"video-container aspect-video rounded-lg overflow-hidden shadow-md",children:u.startsWith("data:video/")?e.jsxs("video",{controls:!0,className:"w-full h-full",autoPlay:!1,preload:"metadata",children:[e.jsx("source",{src:u,type:"video/mp4"}),"Your browser does not support the video tag."]}):e.jsx("iframe",{src:u,title:"Lesson video",style:{border:0},allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0,loading:"lazy",className:"w-full h-full"})})}),b&&e.jsx(m.div,{className:"mb-6",initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.5,delay:.2},children:e.jsx("figure",{className:"text-center",children:e.jsx("img",{src:b,alt:"Lesson image",loading:"lazy",className:"max-w-full h-auto rounded-lg shadow-md mx-auto"})})}),e.jsx(m.div,{className:"lesson-prose",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.3},children:e.jsx(A,{content:H,className:"lesson-prose",allowHtml:!0,securityLevel:"extended"})}),N&&e.jsx(m.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.4},className:"mt-8",children:e.jsx(D,{type:"single",collapsible:!0,className:"lesson-accordion",children:e.jsxs(I,{value:"references",className:"lesson-accordion-item",children:[e.jsx(z,{className:"lesson-accordion-trigger",children:e.jsx("span",{className:"flex items-center gap-3 text-lg font-semibold",children:"📚 References & Additional Resources"})}),e.jsx(F,{className:"lesson-accordion-content",children:e.jsx("div",{className:"lesson-prose",children:e.jsx(A,{content:N,className:"lesson-prose",allowHtml:!0,securityLevel:"extended"})})})]})})})]})});G.displayName="LessonContent";export{G as L};
