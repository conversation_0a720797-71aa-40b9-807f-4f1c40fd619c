import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import Layout from '../Layout';
import { useUserRole } from '@/hooks/useUserRole';
import { Loader2, ShieldAlert, Home, BookOpen, Users, Database, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthContext';
// Import getUserRole instead of verifyTeacherRole
import { getUserRole } from '@/services/auth/roleService';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export function AdminLayout({ children }: AdminLayoutProps) {
  const { isTeacher, loading, verifyTeacher } = useUserRole();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [securityVerified, setSecurityVerified] = useState<boolean | null>(null);
  const [verificationLoading, setVerificationLoading] = useState(false);

  // Verify teacher role on component mount
  useEffect(() => {
    const verifyRole = async () => {
      if (user) {
        setVerificationLoading(true);
        try {
          // Use direct database query instead of RPC function
          const userRole = await getUserRole(user.id);
          setSecurityVerified(userRole === 'teacher');
        } catch (error) {
          console.error('Error verifying teacher role:', error);
          setSecurityVerified(false);
        } finally {
          setVerificationLoading(false);
        }
      }
    };

    verifyRole();
  }, [user]);

  if (loading || verificationLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
          <p className="ml-2">Verifying access...</p>
        </div>
      </Layout>
    );
  }

  // Deny access if not a teacher (check both methods)
  if (!isTeacher && securityVerified !== true) {
    return (
      <Layout>
        <div className="flex flex-col items-center justify-center h-64 text-center">
          <ShieldAlert className="w-16 h-16 text-red-500 mb-4" />
          <h1 className="text-2xl font-bold text-red-600">Access Denied</h1>
          <p className="text-gray-600 mt-2 mb-6">You don't have permission to access the admin area.</p>
          <Button onClick={() => navigate('/dashboard')}>
            Return to Dashboard
          </Button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">Admin Dashboard</h1>
            <p className="text-gray-500">Manage courses, lessons, and users</p>
          </div>
          <Button variant="outline" size="sm" onClick={() => navigate('/admin')}>
            <ArrowLeft className="h-4 w-4 mr-2" /> Back to Admin
          </Button>
        </div>

        <div className="flex overflow-x-auto pb-2 mb-4">
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" asChild>
              <Link to="/admin">
                <Home className="h-4 w-4 mr-2" /> Dashboard
              </Link>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link to="/admin">
                <BookOpen className="h-4 w-4 mr-2" /> Courses
              </Link>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link to="/admin">
                <BookOpen className="h-4 w-4 mr-2" /> Lessons
              </Link>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link to="/admin">
                <Users className="h-4 w-4 mr-2" /> Users
              </Link>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link to="/admin">
                <Database className="h-4 w-4 mr-2" /> Storage
              </Link>
            </Button>
          </div>
        </div>

        {children}
      </div>
    </Layout>
  );
}
