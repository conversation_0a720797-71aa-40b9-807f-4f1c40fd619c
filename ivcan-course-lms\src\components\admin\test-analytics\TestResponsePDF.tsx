import React, { useRef, useState } from 'react';
import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import { format } from 'date-fns';
import { PDFExportData } from '@/types/test-analytics';
import { ratingDescriptions } from '@/types/module-test';
import { toast } from 'sonner';

interface TestResponsePDFProps extends PDFExportData {
  onDownload?: () => void;
}

// Export function for generating PDF
export const generateTestResponsePDF = async (data: PDFExportData): Promise<void> => {
  const { student, testResponse, generatedAt } = data;

  const getRatingLabel = (rating: number) => {
    const option = ratingDescriptions.find(desc => desc.value === rating);
    return option ? option.label : `Rating ${rating}`;
  };

  try {
    // Create a temporary container for the PDF content
    const container = document.createElement('div');
    container.style.position = 'absolute';
    container.style.left = '-9999px';
    container.style.top = '-9999px';
    container.style.width = '210mm';
    container.style.backgroundColor = '#ffffff';
    container.style.fontFamily = 'Arial, sans-serif';
    container.style.padding = '20mm';

    // Generate HTML content
    container.innerHTML = `
      <div style="max-width: 170mm; margin: 0 auto;">
        <!-- Header -->
        <div style="border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px;">
          <h1 style="font-size: 28px; font-weight: bold; color: #333; margin: 0 0 10px 0;">Test Response Report</h1>
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; font-size: 12px; color: #666;">
            <div><strong>Generated:</strong> ${format(new Date(generatedAt), 'MMM dd, yyyy HH:mm')}</div>
            <div><strong>Report ID:</strong> ${testResponse.id.slice(0, 8)}</div>
          </div>
        </div>

        <!-- Student Information -->
        <div style="margin-bottom: 30px;">
          <h2 style="font-size: 18px; font-weight: 600; color: #333; margin: 0 0 15px 0; border-bottom: 1px solid #ccc; padding-bottom: 8px;">
            Student Information
          </h2>
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div><strong>Name:</strong> ${student.fullName}</div>
            <div><strong>Email:</strong> ${student.email}</div>
          </div>
        </div>

        <!-- Test Information -->
        <div style="margin-bottom: 30px;">
          <h2 style="font-size: 18px; font-weight: 600; color: #333; margin: 0 0 15px 0; border-bottom: 1px solid #ccc; padding-bottom: 8px;">
            Test Information
          </h2>
          <div style="line-height: 1.6;">
            <div><strong>Test Title:</strong> ${testResponse.test.title}</div>
            <div><strong>Test Type:</strong> ${testResponse.test.type === 'pre_test' ? 'Pre-Test' : 'Post-Test'}</div>
            <div><strong>Course:</strong> ${testResponse.test.module.course.title}</div>
            <div><strong>Module:</strong> ${testResponse.test.module.title}</div>
            <div><strong>Submission Date:</strong> ${format(new Date(testResponse.createdAt), 'MMM dd, yyyy HH:mm')}</div>
            <div><strong>Questions Answered:</strong> ${testResponse.responses.length}</div>
            ${testResponse.test.description ? `
              <div style="margin-top: 15px;">
                <strong>Test Description:</strong>
                <p style="margin: 5px 0; color: #555;">${testResponse.test.description}</p>
              </div>
            ` : ''}
          </div>
        </div>

        <!-- Responses -->
        <div style="margin-bottom: 30px;">
          <h2 style="font-size: 18px; font-weight: 600; color: #333; margin: 0 0 15px 0; border-bottom: 1px solid #ccc; padding-bottom: 8px;">
            Student Responses
          </h2>
          <div style="space-y: 20px;">
            ${testResponse.responses
              .sort((a, b) => a.question.questionNumber - b.question.questionNumber)
              .map((response) => `
                <div style="border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
                  <!-- Question -->
                  <div style="margin-bottom: 12px;">
                    <div style="display: flex; align-items: flex-start; gap: 8px;">
                      <span style="background: #f5f5f5; color: #333; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;">
                        Q${response.question.questionNumber}
                      </span>
                      <p style="font-weight: 500; color: #333; line-height: 1.5; margin: 0; flex: 1;">
                        ${response.question.question}
                      </p>
                    </div>
                  </div>

                  <!-- Answer -->
                  <div style="background: #f9f9f9; border-radius: 6px; padding: 12px; margin-left: 32px;">
                    <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;">
                      <div>
                        <div style="font-weight: 500; color: #2563eb;">
                          ${getRatingLabel(response.rating)}
                        </div>
                        <div style="font-size: 12px; color: #666;">
                          Rating: ${response.rating} out of ${response.question.maxRating}
                        </div>
                      </div>
                      <div style="background: #dbeafe; color: #1e40af; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;">
                        ${response.rating}/${response.question.maxRating}
                      </div>
                    </div>

                    <!-- Rating Scale Reference -->
                    <div style="margin-top: 12px; padding-top: 12px; border-top: 1px solid #ddd;">
                      <div style="display: flex; justify-content: space-between; font-size: 10px; color: #666; margin-bottom: 4px;">
                        <span>${response.question.minLabel}</span>
                        <span>${response.question.maxLabel}</span>
                      </div>
                      <div style="display: flex; justify-content: space-between;">
                        ${Array.from({ length: response.question.maxRating }, (_, i) => `
                          <div style="width: 20px; height: 20px; border-radius: 50%; border: 2px solid ${
                            i + 1 === response.rating ? '#2563eb' : '#ccc'
                          }; background: ${
                            i + 1 === response.rating ? '#2563eb' : 'white'
                          }; display: flex; align-items: center; justify-content: center; font-size: 10px; color: ${
                            i + 1 === response.rating ? 'white' : '#666'
                          };">
                            ${i + 1}
                          </div>
                        `).join('')}
                      </div>
                    </div>
                  </div>
                </div>
              `).join('')}
          </div>
        </div>

        <!-- Footer -->
        <div style="border-top: 1px solid #ccc; padding-top: 15px; text-align: center; font-size: 11px; color: #666;">
          <p style="margin: 0 0 5px 0;">This report was automatically generated by the LMS Test Analytics System</p>
          <p style="margin: 0;">Report generated on ${format(new Date(generatedAt), 'MMMM dd, yyyy \'at\' HH:mm')}</p>
        </div>
      </div>
    `;

    document.body.appendChild(container);

    // Generate canvas
    const canvas = await html2canvas(container, {
      scale: 2,
      logging: false,
      useCORS: true,
      backgroundColor: '#ffffff',
      width: container.offsetWidth,
      height: container.offsetHeight
    });

    // Remove temporary container
    document.body.removeChild(container);

    // Create PDF
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    const imgWidth = 210; // A4 width in mm
    const imgHeight = (canvas.height * imgWidth) / canvas.width;

    // If content is longer than one page, we might need to split it
    const pageHeight = 297; // A4 height in mm
    let heightLeft = imgHeight;
    let position = 0;

    // Add first page
    pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight);
    heightLeft -= pageHeight;

    // Add additional pages if needed
    while (heightLeft >= 0) {
      position = heightLeft - imgHeight;
      pdf.addPage();
      pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;
    }

    // Generate filename
    const filename = `${student.fullName.replace(/\s+/g, '_')}_${testResponse.test.title.replace(/\s+/g, '_')}_${format(new Date(testResponse.createdAt), 'yyyy-MM-dd')}.pdf`;

    // Save PDF
    pdf.save(filename);

    toast.success(`Test report for ${student.fullName} has been downloaded.`);
  } catch (error) {
    console.error('Error generating PDF:', error);
    toast.error('There was an error creating the PDF report. Please try again.');
    throw error;
  }
};

const TestResponsePDF: React.FC<TestResponsePDFProps> = ({
  student,
  testResponse,
  generatedAt,
  onDownload
}) => {
  const pdfRef = useRef<HTMLDivElement>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  const getRatingLabel = (rating: number) => {
    const option = ratingDescriptions.find(desc => desc.value === rating);
    return option ? option.label : `Rating ${rating}`;
  };

  const generatePDF = async () => {
    if (!pdfRef.current) return;

    setIsGenerating(true);
    try {
      // Create canvas from the PDF content
      const canvas = await html2canvas(pdfRef.current, {
        scale: 2,
        logging: false,
        useCORS: true,
        backgroundColor: '#ffffff'
      });

      // Create PDF
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });

      const imgWidth = 210; // A4 width in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      
      // If content is longer than one page, we might need to split it
      const pageHeight = 297; // A4 height in mm
      let heightLeft = imgHeight;
      let position = 0;

      // Add first page
      pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      // Add additional pages if needed
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      // Generate filename
      const filename = `${student.fullName.replace(/\s+/g, '_')}_${testResponse.test.title.replace(/\s+/g, '_')}_${format(new Date(testResponse.createdAt), 'yyyy-MM-dd')}.pdf`;
      
      // Save PDF
      pdf.save(filename);

      toast({
        title: "PDF Generated Successfully",
        description: `Test report for ${student.fullName} has been downloaded.`
      });

      if (onDownload) {
        onDownload();
      }
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast({
        title: "PDF Generation Failed",
        description: "There was an error creating the PDF report. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Auto-generate PDF when component mounts (if triggered)
  React.useEffect(() => {
    if (onDownload) {
      generatePDF();
    }
  }, []);

  return (
    <div className="hidden">
      <div ref={pdfRef} className="bg-white p-8 max-w-4xl mx-auto" style={{ fontFamily: 'Arial, sans-serif' }}>
        {/* Header */}
        <div className="border-b-2 border-gray-300 pb-6 mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">Test Response Report</h1>
          <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
            <div>
              <strong>Generated:</strong> {format(new Date(generatedAt), 'MMM dd, yyyy HH:mm')}
            </div>
            <div>
              <strong>Report ID:</strong> {testResponse.id.slice(0, 8)}
            </div>
          </div>
        </div>

        {/* Student Information */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2">
            Student Information
          </h2>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <strong>Name:</strong> {student.fullName}
            </div>
            <div>
              <strong>Email:</strong> {student.email}
            </div>
          </div>
        </div>

        {/* Test Information */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2">
            Test Information
          </h2>
          <div className="space-y-2">
            <div>
              <strong>Test Title:</strong> {testResponse.test.title}
            </div>
            <div>
              <strong>Test Type:</strong> {testResponse.test.type === 'pre_test' ? 'Pre-Test' : 'Post-Test'}
            </div>
            <div>
              <strong>Course:</strong> {testResponse.test.module.course.title}
            </div>
            <div>
              <strong>Module:</strong> {testResponse.test.module.title}
            </div>
            <div>
              <strong>Submission Date:</strong> {format(new Date(testResponse.createdAt), 'MMM dd, yyyy HH:mm')}
            </div>
            <div>
              <strong>Questions Answered:</strong> {testResponse.responses.length}
            </div>
          </div>
          {testResponse.test.description && (
            <div className="mt-4">
              <strong>Test Description:</strong>
              <p className="mt-1 text-gray-700">{testResponse.test.description}</p>
            </div>
          )}
        </div>

        {/* Responses */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4 border-b border-gray-200 pb-2">
            Student Responses
          </h2>
          <div className="space-y-6">
            {testResponse.responses
              .sort((a, b) => a.question.questionNumber - b.question.questionNumber)
              .map((response, index) => (
                <div key={response.questionId} className="border border-gray-200 rounded-lg p-4">
                  {/* Question */}
                  <div className="mb-3">
                    <div className="flex items-start space-x-2">
                      <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm font-medium">
                        Q{response.question.questionNumber}
                      </span>
                      <p className="font-medium text-gray-800 leading-relaxed">
                        {response.question.question}
                      </p>
                    </div>
                  </div>

                  {/* Answer */}
                  <div className="bg-gray-50 rounded p-3 ml-8">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <div className="font-medium text-blue-700">
                          {getRatingLabel(response.rating)}
                        </div>
                        <div className="text-sm text-gray-600">
                          Rating: {response.rating} out of {response.question.maxRating}
                        </div>
                      </div>
                      <div className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium">
                        {response.rating}/{response.question.maxRating}
                      </div>
                    </div>
                    
                    {/* Rating Scale Reference */}
                    <div className="mt-3 pt-3 border-t border-gray-200">
                      <div className="flex justify-between text-xs text-gray-500 mb-1">
                        <span>{response.question.minLabel}</span>
                        <span>{response.question.maxLabel}</span>
                      </div>
                      <div className="flex justify-between">
                        {Array.from({ length: response.question.maxRating }, (_, i) => (
                          <div
                            key={i + 1}
                            className={`w-6 h-6 rounded-full border-2 flex items-center justify-center text-xs ${
                              i + 1 === response.rating
                                ? 'border-blue-500 bg-blue-500 text-white'
                                : 'border-gray-300'
                            }`}
                          >
                            {i + 1}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-gray-300 pt-4 text-center text-sm text-gray-500">
          <p>This report was automatically generated by the LMS Test Analytics System</p>
          <p>Report generated on {format(new Date(generatedAt), 'MMMM dd, yyyy \'at\' HH:mm')}</p>
        </div>
      </div>
    </div>
  );
};

export default TestResponsePDF;
