import React, { useState, useEffect } from 'react';
import { Navigate, useNavigate, useSearchParams } from 'react-router-dom';
import Layout from '../components/Layout';
import { useUserRole } from '@/hooks/useUserRole';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, Database, ShieldAlert, BarChart3, Users } from 'lucide-react';
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { verifyTeacherRole } from '@/services/auth/roleService';
import { useAuth } from '@/context/AuthContext';

// Import refactored components
import CoursesTab from '@/components/admin/tabs/CoursesTab';
import LessonsTab from '@/components/admin/tabs/LessonsTab';
import UsersTab from '@/components/admin/tabs/UsersTab';
import AdminBreadcrumbs from '@/components/admin/AdminBreadcrumbs';
import DeleteConfirmDialog from '@/components/admin/DeleteConfirmDialog';
import { BucketManager } from '@/components/admin/BucketManager';
import { StorageNotification } from '@/components/admin/StorageNotification';
import TestAnalyticsDashboard from '@/components/admin/test-analytics/TestAnalyticsDashboard';
import { DemographicAnalytics } from '@/components/admin/DemographicAnalytics';

const Admin = () => {
  const { isTeacher, loading, verifyTeacher } = useUserRole();
  const [searchParams, setSearchParams] = useSearchParams();
  const courseIdParam = searchParams.get('course');
  const moduleIdParam = searchParams.get('module');
  const lessonIdParam = searchParams.get('lesson');
  const { user } = useAuth();
  const [securityVerified, setSecurityVerified] = useState<boolean | null>(null);
  const [verificationLoading, setVerificationLoading] = useState(false);

  const [selectedCourseId, setSelectedCourseId] = useState<string | null>(courseIdParam || null);
  const [selectedModuleId, setSelectedModuleId] = useState<string | null>(moduleIdParam || null);
  const [selectedLessonId, setSelectedLessonId] = useState<string | null>(lessonIdParam || null);
  const [isAddingLesson, setIsAddingLesson] = useState(false);
  const [isAddingModule, setIsAddingModule] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<{id: string, type: 'lesson' | 'module' | 'course'} | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  // Simplified role verification - trust the role from useUserRole
  useEffect(() => {
    const verifyAccess = async () => {
      if (!user) {
        setSecurityVerified(false);
        return;
      }

      setVerificationLoading(true);
      try {
        // If isTeacher is true from useUserRole, we'll trust it
        // This avoids the double verification that was causing issues
        setSecurityVerified(true);
        console.log('Teacher role verified from useUserRole');
      } catch (err) {
        console.error('Error verifying teacher role:', err);
        setSecurityVerified(false);
        toast({
          title: "Verification Error",
          description: "Could not verify your access permissions",
          variant: "destructive",
        });
      } finally {
        setVerificationLoading(false);
      }
    };

    if (!loading && isTeacher) {
      verifyAccess();
    } else if (!loading && !isTeacher) {
      setSecurityVerified(false);
    }
  }, [user, loading, isTeacher, toast, navigate]);

  useEffect(() => {
    const params = new URLSearchParams();
    if (selectedCourseId && selectedCourseId !== 'new') params.set('course', selectedCourseId);
    if (selectedModuleId && selectedModuleId !== 'new') params.set('module', selectedModuleId);
    if (selectedLessonId && selectedLessonId !== 'new') params.set('lesson', selectedLessonId);
    setSearchParams(params);
  }, [selectedCourseId, selectedModuleId, selectedLessonId, setSearchParams]);

  useEffect(() => {
    if (!isTeacher || !securityVerified) return;

    const courseChannel = supabase
      .channel('admin-course-changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'courses'
      }, () => {
        queryClient.invalidateQueries({
          queryKey: ['admin-courses'],
        });
      })
      .subscribe();

    const moduleChannel = supabase
      .channel('admin-module-changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'modules'
      }, () => {
        queryClient.invalidateQueries({
          queryKey: ['admin-modules'],
        });
      })
      .subscribe();

    const lessonChannel = supabase
      .channel('admin-lesson-changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'lessons'
      }, () => {
        queryClient.invalidateQueries({
          queryKey: ['admin-lessons'],
        });
      })
      .subscribe();

    return () => {
      supabase.removeChannel(courseChannel);
      supabase.removeChannel(moduleChannel);
      supabase.removeChannel(lessonChannel);
    };
  }, [isTeacher, queryClient, securityVerified]);

  const { data: selectedModule } = useQuery({
    queryKey: ['admin-module', selectedModuleId],
    queryFn: async () => {
      if (!selectedModuleId || selectedModuleId === 'new') return null;

      const { data, error } = await supabase
        .from('modules')
        .select('*, courses(title)')
        .eq('id', selectedModuleId)
        .single();

      if (error) {
        console.error('Error fetching module details:', error);
        return null;
      }

      return data;
    },
    enabled: !!selectedModuleId && selectedModuleId !== 'new' && !!securityVerified,
  });

  const { data: selectedCourse } = useQuery({
    queryKey: ['admin-course', selectedCourseId],
    queryFn: async () => {
      if (!selectedCourseId || selectedCourseId === 'new') return null;

      const { data, error } = await supabase
        .from('courses')
        .select('*')
        .eq('id', selectedCourseId)
        .single();

      if (error) {
        console.error('Error fetching course details:', error);
        return null;
      }

      return data;
    },
    enabled: !!selectedCourseId && selectedCourseId !== 'new' && !!securityVerified,
  });

  const deleteMutation = useMutation({
    mutationFn: async ({ id, type }: { id: string, type: 'lesson' | 'module' | 'course' }) => {
      // Trust the isTeacher flag from useUserRole
      if (!isTeacher) {
        throw new Error('You do not have permission to delete this item');
      }

      if (type === 'module') {
        // For modules, we need to update the course's total_modules count
        // First, get the course_id for this module
        const { data: moduleData, error: fetchError } = await supabase
          .from('modules')
          .select('course_id')
          .eq('id', id)
          .single();

        if (fetchError) throw fetchError;

        const courseId = moduleData.course_id;

        // Delete the module
        const { error } = await supabase
          .from('modules')
          .delete()
          .eq('id', id);

        if (error) throw error;

        // Update the course's total_modules count
        const { data: moduleCount, error: countError } = await supabase
          .from('modules')
          .select('id')
          .eq('course_id', courseId);

        if (countError) throw countError;

        // Update the course with the new count
        const totalModules = moduleCount.length;

        const { error: updateError } = await supabase
          .from('courses')
          .update({ total_modules: totalModules })
          .eq('id', courseId);

        if (updateError) throw updateError;
      } else {
        // For lessons and courses, just delete directly
        const { error } = await supabase
          .from(type === 'lesson' ? 'lessons' : 'courses')
          .delete()
          .eq('id', id);

        if (error) throw error;
      }

      return { id, type };
    },
    onSuccess: (data) => {
      toast({
        title: "Deleted successfully",
        description: `The ${data.type} has been deleted.`,
      });

      if (data.type === 'course') {
        queryClient.invalidateQueries({ queryKey: ['admin-courses'] });
        queryClient.invalidateQueries({ queryKey: ['admin-modules'] });
        queryClient.invalidateQueries({ queryKey: ['admin-lessons'] });
      } else if (data.type === 'module') {
        queryClient.invalidateQueries({ queryKey: ['admin-modules'] });
        queryClient.invalidateQueries({ queryKey: ['admin-lessons'] });
      } else {
        queryClient.invalidateQueries({ queryKey: ['admin-lessons'] });
      }

      if (data.type === 'course' && selectedCourseId === data.id) {
        setSelectedCourseId(null);
      } else if (data.type === 'module' && selectedModuleId === data.id) {
        setSelectedModuleId(null);
      } else if (data.type === 'lesson' && selectedLessonId === data.id) {
        setSelectedLessonId(null);
      }
    },
    onError: (error) => {
      console.error('Error deleting item:', error);
      toast({
        title: "Error",
        description: "Failed to delete. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleDelete = () => {
    if (itemToDelete) {
      deleteMutation.mutate(itemToDelete);
    }
    setDeleteDialogOpen(false);
    setItemToDelete(null);
  };

  const confirmDelete = (id: string, type: 'lesson' | 'module' | 'course') => {
    setItemToDelete({ id, type });
    setDeleteDialogOpen(true);
  };

  const handleBackToCoursesList = () => {
    setSelectedCourseId(null);
    setSelectedModuleId(null);
    setSelectedLessonId(null);
    setIsAddingModule(false);
    setIsAddingLesson(false);
  };

  const handleBackToCourse = () => {
    setSelectedModuleId(null);
    setSelectedLessonId(null);
    setIsAddingLesson(false);
  };

  if (loading || verificationLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
          <p className="ml-2">Verifying access...</p>
        </div>
      </Layout>
    );
  }

  // Deny access if not a teacher or verification failed
  if (!isTeacher || securityVerified === false) {
    return (
      <Layout>
        <div className="flex flex-col items-center justify-center h-64 text-center">
          <ShieldAlert className="w-16 h-16 text-red-500 mb-4" />
          <h1 className="text-2xl font-bold text-red-600">Access Denied</h1>
          <p className="text-gray-600 mt-2 mb-6">You don't have permission to access the admin area.</p>
          <Button onClick={() => navigate('/dashboard')}>
            Return to Dashboard
          </Button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold">Admin Dashboard</h1>
          <p className="text-gray-500">Manage courses, lessons and users</p>
        </div>

        <AdminBreadcrumbs
          selectedCourseId={selectedCourseId}
          selectedModuleId={selectedModuleId}
          selectedLessonId={selectedLessonId}
          selectedCourse={selectedCourse}
          selectedModule={selectedModule}
          handleBackToCoursesList={handleBackToCoursesList}
          handleBackToCourse={handleBackToCourse}
          setSelectedLessonId={setSelectedLessonId}
        />

        <StorageNotification />
        <Tabs defaultValue="courses">
          <TabsList className="grid w-full grid-cols-6 mb-6">
            <TabsTrigger value="courses">Courses</TabsTrigger>
            <TabsTrigger value="lessons">Lessons</TabsTrigger>
            <TabsTrigger value="users">Users</TabsTrigger>
            <TabsTrigger value="analytics"><BarChart3 className="h-4 w-4 mr-2" />Test Analytics</TabsTrigger>
            <TabsTrigger value="demographics"><Users className="h-4 w-4 mr-2" />Demographics</TabsTrigger>
            <TabsTrigger value="storage"><Database className="h-4 w-4 mr-2" />Storage</TabsTrigger>
          </TabsList>

          <TabsContent value="courses" className="p-4 border rounded-md">
            <CoursesTab
              selectedCourseId={selectedCourseId}
              setSelectedCourseId={setSelectedCourseId}
              selectedModuleId={selectedModuleId}
              setSelectedModuleId={setSelectedModuleId}
              setSelectedLessonId={setSelectedLessonId}
              isAddingModule={isAddingModule}
              setIsAddingModule={setIsAddingModule}
              setIsAddingLesson={setIsAddingLesson}
              confirmDelete={confirmDelete}
              handleBackToCoursesList={handleBackToCoursesList}
              handleBackToCourse={handleBackToCourse}
            />
          </TabsContent>

          <TabsContent value="lessons" className="p-4 border rounded-md">
            <LessonsTab
              selectedModuleId={selectedModuleId}
              setSelectedModuleId={setSelectedModuleId}
              selectedLessonId={selectedLessonId}
              setSelectedLessonId={setSelectedLessonId}
              isAddingLesson={isAddingLesson}
              setIsAddingLesson={setIsAddingLesson}
              confirmDelete={confirmDelete}
            />
          </TabsContent>

          <TabsContent value="users" className="p-4 border rounded-md">
            <UsersTab />
          </TabsContent>

          <TabsContent value="analytics" className="p-4 border rounded-md">
            <TestAnalyticsDashboard />
          </TabsContent>

          <TabsContent value="demographics" className="p-4 border rounded-md">
            <DemographicAnalytics />
          </TabsContent>

          <TabsContent value="storage" className="p-4 border rounded-md">
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold">Storage Management</h2>
                <p className="text-sm text-muted-foreground">Manage storage buckets for course images and other uploads</p>
              </div>

              <div className="max-w-md mx-auto">
                <BucketManager />
              </div>

              <div className="bg-amber-50 border border-amber-200 rounded-md p-4 text-amber-800 text-sm">
                <p className="font-medium">Important Notes:</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>If you're experiencing "bucket not found" errors when uploading images, use this tool to create the necessary buckets.</li>
                  <li>You must have the appropriate permissions in Supabase to create buckets.</li>
                  <li>After creating buckets, you may need to set up appropriate RLS (Row Level Security) policies in your Supabase dashboard.</li>
                </ul>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      <DeleteConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        itemType={itemToDelete?.type || null}
        onDelete={handleDelete}
      />
    </Layout>
  );
};

export default Admin;
