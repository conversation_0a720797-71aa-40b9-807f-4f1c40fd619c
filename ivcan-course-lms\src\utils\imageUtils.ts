/**
 * Utility functions for handling images in the application
 */
// Removed local storage fallback imports

/**
 * Returns a placeholder image URL for a course based on its title
 * Uses a consistent color palette based on the first letter of the title
 *
 * @param title The course title
 * @returns A placeholder image URL
 */
export function getCoursePlaceholderImage(title: string | undefined): string | undefined {
  if (!title || title.length === 0) {
    // Return a default placeholder if title is missing
    return '/placeholders/course-placeholder-1.jpg';
  }

  // Use the first letter to determine which placeholder to use
  const firstLetter = title.charAt(0).toLowerCase();
  const letterIndex = firstLetter.charCodeAt(0) - 'a'.charCodeAt(0);

  // Set of placeholder images with WhatsApp-inspired colors
  const placeholders = [
    '/placeholders/course-placeholder-1.jpg',
    '/placeholders/course-placeholder-2.jpg',
    '/placeholders/course-placeholder-3.jpg',
    '/placeholders/course-placeholder-4.jpg',
    '/placeholders/course-placeholder-5.jpg',
  ];

  // Use modulo to cycle through the available placeholders
  const index = letterIndex >= 0 ? letterIndex % placeholders.length : 0;

  return placeholders[index];
}

/**
 * Determines if a URL is a valid image URL
 *
 * @param url The URL to check
 * @returns True if the URL appears to be a valid image URL
 */
export function isValidImageUrl(url: string | undefined | null): boolean {
  if (!url || typeof url !== 'string') return false;

  // Check if the URL has an image extension
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'];
  return imageExtensions.some(ext => url.toLowerCase().endsWith(ext));
}

/**
 * Determines if a string is a data URL
 *
 * @param url The URL to check
 * @returns True if the URL is a data URL
 */
export function isDataUrl(url: string | undefined | null): boolean {
  return typeof url === 'string' && url.startsWith('data:');
}

/**
 * Debug mode - disabled in production
 */
const DEBUG = import.meta.env.DEV || false;

/**
 * Gets the appropriate image source for a course
 * Handles both regular URLs and data URLs
 *
 * @param imageUrl The image URL from the course
 * @param title The course title (for generating a placeholder)
 * @returns The appropriate image source to use
 */
export function getCourseImageSource(imageUrl: string | undefined, title: string | undefined): string | null {
  if (DEBUG) {
    console.log(`getCourseImageSource for "${title || 'Untitled'}":`, imageUrl ?
      (imageUrl.length > 100 ? `${imageUrl.substring(0, 50)}...` : imageUrl) :
      'undefined');
  }

  if (!imageUrl) {
    // If no image URL is provided, use a placeholder based on the title
    const placeholder = getCoursePlaceholderImage(title);
    if (DEBUG) console.log(`Using placeholder for "${title || 'Untitled'}":`, placeholder);
    return placeholder || null;
  }

  // Removed localStorage fallback handling

  // If it's a data URL, use it directly
  if (isDataUrl(imageUrl)) {
    if (DEBUG) console.log(`Data URL detected for "${title || 'Untitled'}"`);
    return imageUrl;
  }

  // If it's a storage path, construct the full URL
  if (imageUrl.startsWith('course-images/')) {
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://jibspqwieubavucdtccv.supabase.co';
    // Make sure the URL has the correct format
    const baseUrl = supabaseUrl.endsWith('/') ? supabaseUrl.slice(0, -1) : supabaseUrl;
    const fullUrl = `${baseUrl}/storage/v1/object/public/${imageUrl}`;
    if (DEBUG) console.log(`Storage path detected for "${title || 'Untitled'}":`, fullUrl);
    return fullUrl;
  }

  // Otherwise, it's a regular URL
  if (DEBUG) console.log(`Regular URL detected for "${title || 'Untitled'}":`, imageUrl);
  return imageUrl;
}
