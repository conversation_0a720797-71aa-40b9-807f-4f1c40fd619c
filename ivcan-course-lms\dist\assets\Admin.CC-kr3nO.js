var Js=Object.defineProperty,Ws=Object.defineProperties;var Xs=Object.getOwnPropertyDescriptors;var Te=Object.getOwnPropertySymbols;var vs=Object.prototype.hasOwnProperty,Ns=Object.prototype.propertyIsEnumerable;var ys=(s,r,t)=>r in s?Js(s,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[r]=t,A=(s,r)=>{for(var t in r||(r={}))vs.call(r,t)&&ys(s,t,r[t]);if(Te)for(var t of Te(r))Ns.call(r,t)&&ys(s,t,r[t]);return s},B=(s,r)=>Ws(s,Xs(r));var de=(s,r)=>{var t={};for(var a in s)vs.call(s,a)&&r.indexOf(a)<0&&(t[a]=s[a]);if(s!=null&&Te)for(var a of Te(s))r.indexOf(a)<0&&Ns.call(s,a)&&(t[a]=s[a]);return t};var E=(s,r,t)=>new Promise((a,i)=>{var c=d=>{try{j(t.next(d))}catch(n){i(n)}},h=d=>{try{j(t.throw(d))}catch(n){i(n)}},j=d=>d.done?a(d.value):Promise.resolve(d.value).then(c,h);j((t=t.apply(s,r)).next())});import{r as v,j as e,S as Zs,aY as er,aZ as sr,a_ as rr,aw as ie,az as z,a$ as Ie,ab as xe,X as ds,b0 as tr,$ as R,u as Ge,b1 as bs,af as fe,aL as ye,aM as ue,ax as ne,b2 as ae,b3 as ar,at as ws,Z as He,R as nr,b4 as Cs,b5 as ir,b6 as Rs,b7 as Us,aW as Xe,as as Ms,ag as lr,E as Es,b8 as Ps,a3 as Ze,a4 as qs,aD as or,b9 as cr,ba as Ae,x as zs,bb as _s,bc as dr,bd as mr,a1 as ur,be as hr}from"./vendor-react.BcAa1DKr.js";import{u as xr,L as es}from"./Layout.DRjmVYQG.js";import{T as Ne,a as be,b as Q,c as D}from"./tabs.B0SF6qIv.js";import{s as q,c as Ee,g as le,B as g,I as P,S as Ue,m as jr,n as $s,o as Qs,p as Ds,q as Bs,r as Ce,t as Fe,D as ts,h as as,i as ns,j as is,k as ls,l as os,v as ss,w as pr,x as gr,u as fr}from"./index.BLDhDn0D.js";import{L as Le}from"./label.D4YlnUPk.js";import{B as me}from"./badge.C87ZuIxl.js";import{a as yr}from"./enrollmentApi.CstnsQi_.js";import{A as vr,a as Nr,b as br}from"./alert.EBLwCiZR.js";import{c as wr}from"./vendor-supabase.sufZ44-y.js";import{a2 as M,aa as ee,ab as Ks,ac as Cr,ad as he,ae as Er,af as Ss}from"./vendor.DQpuTRuB.js";import{T as Me,a as Pe,b as ce,c as K,d as ze,e as $,S as qr,B as Ts,f as ks,g as ke,h as pe,i as As,j as _r}from"./breadcrumb.DyMjeizE.js";import{g as rs}from"./moduleImageUtils.AJjzI8xs.js";import{S as Sr}from"./skeleton.C0Lb8Xms.js";import{C as I,a as re,b as te,c as qe,d as G,e as ms}from"./card.B9V6b2DK.js";import{A as $e,a as cs,b as Qe,c as De,d as Be,e as Ke,f as Ve,g as Oe,h as Ye}from"./alert-dialog.BK1A3Gb_.js";import{b as Tr,c as kr,u as Ar,d as Fr,e as Lr}from"./moduleTestService.BmAHru_J.js";import{c as Rr,a as Fs}from"./module-test.BaVeK2uM.js";import{a as Ur}from"./utils.Qa9QlCj_.js";import{S as Mr}from"./simple-markdown-editor.DQ2eiB7n.js";import{T as Pr}from"./TestAnalyticsDashboard.B08xtLlk.js";import"./markdown-preview.Bw0U-NJA.js";import"./content-converter.L-GziWIP.js";import"./tiptap-image-upload.B_U9gNrF.js";import"./progress.BMuwHUJX.js";const zr=()=>E(void 0,null,function*(){try{const{data:s,error:r}=yield q.from("profiles").select(`
        id,
        first_name,
        last_name,
        user_roles (role)
      `);return r?(console.error("Error fetching users for promotion:",r),[]):s.filter(a=>!a.user_roles||a.user_roles.length===0||a.user_roles[0]&&a.user_roles[0].role==="student")}catch(s){return console.error("Error in getPromotableUsers:",s),[]}}),$r=s=>E(void 0,null,function*(){try{if(!s)return M.error("User ID is required"),!1;const{data:{session:r}}=yield q.auth.getSession();if(!r)return M.error("You must be logged in to perform this action"),!1;const{data:t,error:a}=yield q.from("user_roles").select("role").eq("user_id",r.user.id).single();if(a||!t||t.role!=="teacher")return M.error("Only teachers can promote other users to teacher role"),!1;const{error:i}=yield q.from("user_roles").upsert({user_id:s,role:"teacher"},{onConflict:"user_id"});return i?(console.error("Error promoting user to teacher:",i),M.error(`Failed to promote user: ${i.message}`),!1):(console.log(`Role change: User ${s} promoted to teacher by ${r.user.id}`),M.success("User successfully promoted to teacher role"),!0)}catch(r){return console.error("Unexpected error in promoteUserToTeacher:",r),M.error(`An unexpected error occurred: ${r.message}`),!1}}),Je=sr,Vs=v.createContext({}),H=r=>{var s=de(r,[]);return e.jsx(Vs.Provider,{value:{name:s.name},children:e.jsx(rr,A({},s))})},We=()=>{const s=v.useContext(Vs),r=v.useContext(Os),{getFieldState:t,formState:a}=er(),i=t(s.name,a);if(!s)throw new Error("useFormField should be used within <FormField>");const{id:c}=r;return A({id:c,name:s.name,formItemId:`${c}-form-item`,formDescriptionId:`${c}-form-item-description`,formMessageId:`${c}-form-item-message`},i)},Os=v.createContext({}),V=v.forwardRef((a,t)=>{var i=a,{className:s}=i,r=de(i,["className"]);const c=v.useId();return e.jsx(Os.Provider,{value:{id:c},children:e.jsx("div",A({ref:t,className:Ee("space-y-2",s)},r))})});V.displayName="FormItem";const U=v.forwardRef((a,t)=>{var i=a,{className:s}=i,r=de(i,["className"]);const{error:c,formItemId:h}=We();return e.jsx(Le,A({ref:t,className:Ee(c&&"text-destructive",s),htmlFor:h},r))});U.displayName="FormLabel";const O=v.forwardRef((t,r)=>{var s=de(t,[]);const{error:a,formItemId:i,formDescriptionId:c,formMessageId:h}=We();return e.jsx(Zs,A({ref:r,id:i,"aria-describedby":a?`${c} ${h}`:`${c}`,"aria-invalid":!!a},s))});O.displayName="FormControl";const Re=v.forwardRef((a,t)=>{var i=a,{className:s}=i,r=de(i,["className"]);const{formDescriptionId:c}=We();return e.jsx("p",A({ref:t,id:c,className:Ee("text-sm text-muted-foreground",s)},r))});Re.displayName="FormDescription";const Y=v.forwardRef((i,a)=>{var c=i,{className:s,children:r}=c,t=de(c,["className","children"]);const{error:h,formMessageId:j}=We(),d=h?String(h==null?void 0:h.message):r;return d?e.jsx("p",B(A({ref:a,id:j,className:Ee("text-sm font-medium text-destructive",s)},t),{children:d})):null});Y.displayName="FormMessage";const ve=v.forwardRef((a,t)=>{var i=a,{className:s}=i,r=de(i,["className"]);return e.jsx("textarea",A({className:Ee("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:t},r))});ve.displayName="Textarea";function Qr(s){return new Promise((r,t)=>{const a=new FileReader;a.onload=()=>{typeof a.result=="string"?r(a.result):t(new Error("Failed to convert file to data URL"))},a.onerror=()=>{t(new Error("Error reading file"))},a.readAsDataURL(s)})}function Dr(s,r=600,t=400){return new Promise((a,i)=>{const c=new Image;c.onload=()=>{let h=c.width,j=c.height;h>r&&(j=Math.round(j*(r/h)),h=r),j>t&&(h=Math.round(h*(t/j)),j=t);const d=document.createElement("canvas");d.width=h,d.height=j;const n=d.getContext("2d");if(!n){i(new Error("Failed to get canvas context"));return}n.fillStyle="#FFFFFF",n.fillRect(0,0,h,j),n.drawImage(c,0,0,h,j);const u=d.toDataURL("image/jpeg",.8);a(u)},c.onerror=()=>{i(new Error("Failed to load image"))},c.src=s})}function Ys(s){return E(this,null,function*(){try{const r=yield Qr(s);return yield Dr(r)}catch(r){throw console.error("Error processing image:",r),r}})}function Br(s){return E(this,null,function*(){console.log("Creating course:",s.title);try{const r={title:s.title,slug:s.slug,description:s.description,instructor:s.instructor,total_modules:0,completed_modules:0};if(s.image){console.log("Processing image for course");try{const i=yield Ys(s.image);console.log("Image processed successfully, length:",i.length),r.image_url=i,r.image=i}catch(i){console.error("Failed to process image:",i)}}const{data:t,error:a}=yield q.from("courses").insert([r]).select();if(a)throw console.error("Error creating course:",a),new Error(`Failed to create course: ${a.message}`);return console.log("Course created successfully:",t[0].id),t[0]}catch(r){throw console.error("Error in createCourse:",r),r}})}function Kr(s,r){return E(this,null,function*(){console.log("Updating course:",s);try{const t={title:r.title,slug:r.slug,description:r.description,instructor:r.instructor,updated_at:new Date().toISOString()};if(r.image){console.log("Processing image for course update");try{const c=yield Ys(r.image);console.log("Image processed successfully, length:",c.length),t.image_url=c,t.image=c}catch(c){console.error("Failed to process image:",c)}}const{data:a,error:i}=yield q.from("courses").update(t).eq("id",s).select();if(i)throw console.error("Error updating course:",i),new Error(`Failed to update course: ${i.message}`);return console.log("Course updated successfully"),a[0]}catch(t){throw console.error("Error in updateCourse:",t),t}})}function Vr({courseId:s,onClose:r,initialData:t}){const{toast:a}=le(),i=ie(),c=!s,[h,j]=v.useState(null),[d,n]=v.useState(null),[u,N]=v.useState(!1),[y,x]=v.useState(!1),{data:_=0,isLoading:C}=z({queryKey:["course-enrollment-count",s],queryFn:()=>yr(s||""),enabled:!!s&&s!=="new"}),S=Ie({defaultValues:{title:(t==null?void 0:t.title)||"",slug:(t==null?void 0:t.slug)||"",description:(t==null?void 0:t.description)||"",instructor:(t==null?void 0:t.instructor)||""}});v.useEffect(()=>{t!=null&&t.image_url&&n(t.image_url)},[t]);const m=p=>E(this,null,function*(){try{N(!0);const T=p.slug||p.title.toLowerCase().replace(/\\s+/g,"-");c?(yield Br({title:p.title,slug:T,description:p.description,instructor:p.instructor,image:h}),a({title:"Course created",description:"The course has been created successfully."}),x(!0)):(yield Kr(s,{title:p.title,slug:T,description:p.description,instructor:p.instructor,image:h}),a({title:"Course updated",description:"The course has been updated successfully."}),x(!0)),i.invalidateQueries({queryKey:["admin-courses"]}),i.invalidateQueries({queryKey:["courses"]}),s&&i.invalidateQueries({queryKey:["course",s]}),setTimeout(()=>{r()},2e3)}catch(T){console.error("Error saving course:",T),a({title:"Error",description:T.message||"Failed to save course. Please try again.",variant:"destructive"})}finally{N(!1)}}),o=p=>{var w;const T=(w=p.target.files)==null?void 0:w[0];if(T){if(T.size>5*1024*1024){a({title:"Image too large",description:"Please select an image smaller than 5MB.",variant:"destructive"});return}j(T);const L=URL.createObjectURL(T);n(L),a({title:"Image selected",description:"The image has been selected and will be uploaded when you save the course."})}},l=()=>{j(null),n(null)},b=p=>{const T=p.target.value;if(c||!S.getValues("slug")){const w=T.toLowerCase().replace(/[^\\w\\s-]/g,"").replace(/\\s+/g,"-");S.setValue("slug",w)}};return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex justify-between items-center",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("h2",{className:"text-xl font-semibold",children:c?"Create New Course":"Edit Course"}),!c&&!C&&e.jsxs(me,{variant:"outline",className:"flex items-center gap-1 bg-blue-50 text-blue-700",children:[e.jsx(xe,{className:"h-3 w-3"}),_," ",_===1?"student":"students"," enrolled"]})]})}),y&&e.jsxs(vr,{className:"bg-red-50 border-red-200",children:[e.jsx(Nr,{className:"text-red-800",children:"Success!"}),e.jsxs(br,{className:"text-red-700",children:[c?"Course created successfully.":"Course updated successfully.",h&&" The image has been uploaded and will appear on the course card."]})]}),e.jsx(Je,B(A({},S),{children:e.jsxs("form",{onSubmit:S.handleSubmit(m),className:"space-y-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(U,{htmlFor:"image",children:"Course Image"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Max size: 5MB"})]}),e.jsxs("div",{className:"flex flex-col space-y-4",children:[d?e.jsxs("div",{className:"relative w-full max-w-md aspect-video rounded-lg overflow-hidden border border-border",children:[e.jsx("img",{src:d,alt:"Course preview",className:"w-full h-full object-cover"}),e.jsxs(g,{type:"button",variant:"destructive",size:"sm",className:"absolute top-2 right-2 opacity-90",onClick:l,children:[e.jsx(ds,{className:"h-4 w-4 mr-1"}),"Remove"]})]}):e.jsxs("div",{className:"w-full max-w-md aspect-video rounded-lg bg-muted flex flex-col items-center justify-center border border-dashed border-border p-4",children:[e.jsx(tr,{className:"h-10 w-10 text-muted-foreground mb-2"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"No image selected"}),e.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:"Recommended size: 600x400px"})]}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(P,{id:"image",type:"file",accept:"image/*",onChange:o,className:"max-w-md"})})]})]}),e.jsx(H,{control:S.control,name:"title",render:({field:p})=>e.jsxs(V,{children:[e.jsx(U,{children:"Course Title"}),e.jsx(O,{children:e.jsx(P,B(A({placeholder:"Enter course title"},p),{onChange:T=>{p.onChange(T),b(T)}}))}),e.jsx(Y,{})]})}),e.jsx(H,{control:S.control,name:"slug",render:({field:p})=>e.jsxs(V,{children:[e.jsx(U,{children:"Course Slug"}),e.jsx(O,{children:e.jsx(P,A({placeholder:"course-slug"},p))}),e.jsx(Y,{})]})}),e.jsx(H,{control:S.control,name:"description",render:({field:p})=>e.jsxs(V,{children:[e.jsx(U,{children:"Description"}),e.jsx(O,{children:e.jsx(ve,A({placeholder:"Enter course description",className:"min-h-[100px]"},p))}),e.jsx(Y,{})]})}),e.jsx(H,{control:S.control,name:"instructor",render:({field:p})=>e.jsxs(V,{children:[e.jsx(U,{children:"Instructor"}),e.jsx(O,{children:e.jsx(P,A({placeholder:"Enter instructor name"},p))}),e.jsx(Y,{})]})}),!c&&e.jsxs("div",{className:"pt-4 pb-2",children:[e.jsxs("h3",{className:"text-sm font-medium mb-2 flex items-center gap-2",children:[e.jsx(xe,{className:"h-4 w-4 text-blue-500"}),"Student Enrollment"]}),e.jsx("div",{className:"bg-gray-50 p-3 rounded-md border text-sm",children:C?e.jsxs("div",{className:"flex items-center justify-center py-2",children:[e.jsx(R,{className:"h-4 w-4 animate-spin text-gray-400 mr-2"}),"Loading enrollment data..."]}):_>0?e.jsxs("div",{children:[e.jsxs("p",{className:"text-gray-700",children:["This course has ",e.jsx("span",{className:"font-medium",children:_})," ",_===1?"student":"students"," enrolled."]}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Changes to this course will affect all enrolled students."})]}):e.jsx("p",{className:"text-gray-500",children:"No students are currently enrolled in this course."})})]}),e.jsxs("div",{className:"flex justify-end space-x-2 pt-4",children:[e.jsx(g,{type:"button",variant:"outline",onClick:r,disabled:u,children:"Cancel"}),e.jsx(g,{type:"submit",disabled:u,children:u?e.jsxs(e.Fragment,{children:[e.jsx(R,{className:"mr-2 h-4 w-4 animate-spin"}),"Saving..."]}):c?"Create Course":"Update Course"})]})]})}))]})}const ge=wr(jr,Ue,{auth:{autoRefreshToken:!1,persistSession:!1},global:{headers:{"x-client-info":"admin-api"}}});Ue||console.warn("VITE_SUPABASE_SERVICE_ROLE_KEY is not set. Admin operations will not work.");function Or(s){return E(this,null,function*(){try{const{data:r,error:t}=yield ge.from("user_roles").select("*").eq("user_id",s).maybeSingle();if(t)return M.error(`Failed to check existing role: ${t.message}`),!1;let a;if(r?a=yield ge.from("user_roles").update({role:"teacher"}).eq("user_id",s):a=yield ge.from("user_roles").insert([{user_id:s,role:"teacher"}]),a.error)return M.error(`Failed to assign teacher role: ${a.error.message}`),!1;const{error:i}=yield ge.rpc("has_role",{_user_id:s,_role:"teacher"});return M.success("Teacher role assigned successfully"),!0}catch(r){return M.error(`An unexpected error occurred: ${r.message}`),!1}})}function Yr(s){return E(this,null,function*(){try{const r=Ue?ge:q,{data:t,error:a}=yield r.from("modules").insert([{course_id:s.course_id,title:s.title,slug:s.slug,module_number:s.module_number,is_locked:s.is_locked||!1,is_completed:!1,image_url:s.image_url||null}]).select();if(a)return M.error(`Failed to create module: ${a.message}`),null;try{const i=Ue?ge:q,{data:c,error:h}=yield i.from("modules").select("id").eq("course_id",s.course_id);if(!h){const j=c.length,{error:d}=yield i.from("courses").update({total_modules:j}).eq("id",s.course_id)}}catch(i){}return M.success("Module created successfully"),t[0]}catch(r){return M.error(`An unexpected error occurred: ${r.message}`),null}})}const Ir=({moduleId:s,onEditLesson:r,onDeleteLesson:t,onAddLesson:a})=>{const i=Ge(),{data:c,isLoading:h}=z({queryKey:["module-lessons",s],queryFn:()=>E(void 0,null,function*(){const{data:n,error:u}=yield q.from("lessons").select("*").eq("module_id",s).order("order");if(u)throw u;return n})}),{data:j}=z({queryKey:["module-data",s],queryFn:()=>E(void 0,null,function*(){const{data:n,error:u}=yield q.from("modules").select("course_id").eq("id",s).single();return u?(console.error("Error fetching module data:",u),null):n}),enabled:!!s}),d=n=>{j!=null&&j.course_id&&i(`/course/${j.course_id}/lesson/${n}`)};return h?e.jsx("div",{className:"flex justify-center p-4",children:e.jsx(R,{className:"w-6 h-6 animate-spin text-primary"})}):c!=null&&c.length?e.jsxs("div",{children:[e.jsx("div",{className:"flex justify-end mb-4 gap-2",children:a&&e.jsxs(g,{onClick:a,size:"sm",variant:"outline",className:"flex items-center",children:[e.jsx(bs,{className:"w-4 h-4 mr-1"})," Add Lesson"]})}),e.jsx("div",{className:"border rounded-md overflow-hidden",children:e.jsxs(Me,{children:[e.jsx(Pe,{children:e.jsxs(ce,{children:[e.jsx(K,{children:"Title"}),e.jsx(K,{children:"Type"}),e.jsx(K,{children:"Duration"}),e.jsx(K,{className:"text-right",children:"Actions"})]})}),e.jsx(ze,{children:c.map(n=>e.jsxs(ce,{children:[e.jsx($,{className:"font-medium",children:n.title}),e.jsx($,{children:e.jsx("span",{className:`px-2 py-1 rounded-full text-xs ${n.type==="assignment"?"bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300":"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"}`,children:n.type.charAt(0).toUpperCase()+n.type.slice(1)})}),e.jsx($,{children:n.duration}),e.jsx($,{className:"text-right",children:e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx(g,{variant:"ghost",size:"sm",onClick:()=>d(n.slug),title:"View lesson",children:e.jsx(fe,{className:"h-4 w-4"})}),e.jsx(g,{variant:"ghost",size:"sm",onClick:()=>r(n.id),title:"Edit lesson",children:e.jsx(ye,{className:"h-4 w-4"})}),e.jsx(g,{variant:"ghost",size:"sm",onClick:()=>t(n.id),className:"text-red-500 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-950/30",title:"Delete lesson",children:e.jsx(ue,{className:"h-4 w-4"})})]})})]},n.id))})]})})]}):e.jsxs("div",{className:"text-center py-6 bg-muted/30 rounded-lg border border-dashed border-border",children:[e.jsx("p",{className:"text-muted-foreground mb-4",children:"No lessons in this module yet"}),e.jsx("div",{className:"flex justify-center gap-2",children:a&&e.jsxs(g,{onClick:a,size:"sm",className:"flex items-center",children:[e.jsx(bs,{className:"w-4 h-4 mr-1"})," Add Lesson"]})})]})};function Gr({src:s,alt:r,width:t,height:a,className:i="",fallbackSrc:c="/images/placeholder.jpg",priority:h=!1}){const[j,d]=v.useState(!1),[n,u]=v.useState(!1),N=()=>{d(!0)},y=()=>{u(!0),d(!0)},x=_=>_.startsWith("http")||_.startsWith("https")||_.startsWith("/")?_:`/${_}`;return e.jsxs("div",{className:"relative",style:{width:t,height:a},children:[!j&&e.jsx(Sr,{className:"absolute inset-0 rounded-md",style:{width:t,height:a}}),e.jsx("img",{src:x(n?c:s),alt:r,width:t,height:a,loading:h?"eager":"lazy",onLoad:N,onError:y,className:`transition-opacity duration-300 ${j?"opacity-100":"opacity-0"} ${i}`,style:{objectFit:"cover"}})]})}const Hr=ee.object({title:ee.string().min(1,"Title is required"),description:ee.string().optional(),type:ee.enum(["pre_test","post_test"]),questions:ee.array(ee.object({id:ee.string(),type:ee.literal("rating"),question:ee.string().min(1,"Question text is required"),questionNumber:ee.number(),minRating:ee.number(),maxRating:ee.number(),minLabel:ee.string(),maxLabel:ee.string()}))}),Jr=({moduleId:s,testId:r,testType:t="pre_test",onClose:a})=>{v.useState(0);const[i,c]=v.useState([]),{toast:h}=le(),j=ie(),d=()=>{if(!r){const m=Fs(s,t);return{title:m.title||"",description:m.description||"",type:t,questions:[]}}return{title:"",description:"",type:t,questions:[]}},n=Ie({resolver:Ks(Hr),defaultValues:d()}),{isLoading:u}=z({queryKey:["module-test",r],queryFn:()=>E(void 0,null,function*(){if(!r)return null;try{const m=yield Tr(r),o=m.questions.map(l=>({id:l.id,type:"rating",question:l.question,questionNumber:l.questionNumber,minRating:l.minRating,maxRating:l.maxRating,minLabel:l.minLabel,maxLabel:l.maxLabel}));return n.reset({title:m.title,description:m.description||"",type:m.type,questions:o}),c(o),m}catch(m){return console.error("Error fetching module test:",m),h({title:"Error",description:"Failed to load test data",variant:"destructive"}),null}}),enabled:!!r}),N=ne({mutationFn:m=>E(void 0,null,function*(){return kr({moduleId:s,title:m.title,type:m.type,description:m.description,questions:m.questions})}),onSuccess:()=>{h({title:"Success",description:"Test created successfully"}),j.invalidateQueries({queryKey:["module-tests",s]}),a()},onError:m=>{console.error("Error creating test:",m),h({title:"Error",description:m.message||"Failed to create test",variant:"destructive"})}}),y=ne({mutationFn:m=>E(void 0,null,function*(){if(!r)throw new Error("Test ID is required for update");return Ar(r,{title:m.title,description:m.description,questions:m.questions})}),onSuccess:()=>{h({title:"Success",description:"Test updated successfully"}),j.invalidateQueries({queryKey:["module-tests",s]}),j.invalidateQueries({queryKey:["module-test",r]}),a()},onError:m=>{console.error("Error updating test:",m),h({title:"Error",description:m.message||"Failed to update test",variant:"destructive"})}});v.useEffect(()=>{if(!r&&i.length===0){const m=Fs(s,t);n.reset({title:m.title||"",description:m.description||"",type:t,questions:[]}),x()}},[r,i.length]);const x=()=>{const m=i.length>0?Math.max(...i.map(b=>b.questionNumber))+1:1,o=Rr(m),l=[...i,o];c(l),n.setValue("questions",l)},_=m=>{const o=[...i];o.splice(m,1),o.forEach((l,b)=>{l.questionNumber=b+1}),c(o),n.setValue("questions",o)},C=(m,o,l)=>{const b=[...i];b[m][o]=l,c(b),n.setValue("questions",b)},S=m=>{const o=B(A({},m),{questions:i.map(b=>({id:b.id,type:"rating",question:b.question,questionNumber:b.questionNumber,minRating:b.minRating,maxRating:b.maxRating,minLabel:b.minLabel,maxLabel:b.maxLabel}))});if(i.length===0){h({title:"Validation Error",description:"Please add at least one question to the test",variant:"destructive"});return}const l=i.filter(b=>!b.question.trim());if(l.length>0){h({title:"Validation Error",description:`Please fill in text for all questions (${l.length} empty)`,variant:"destructive"});return}r?y.mutate(o):N.mutate(o)};return u?e.jsx("div",{className:"flex justify-center py-8",children:e.jsx(R,{className:"w-8 h-8 animate-spin"})}):e.jsxs(I,{children:[e.jsxs(re,{children:[e.jsx(te,{children:r?"Edit Module Test":"Create Module Test"}),e.jsx(qe,{children:t==="pre_test"?"Pre-tests are shown before the first lesson in a module":"Post-tests are shown after the last lesson in a module"})]}),e.jsx(G,{children:e.jsx(Je,B(A({},n),{children:e.jsxs("form",{onSubmit:n.handleSubmit(S),children:[e.jsxs(Ne,{defaultValue:"basic",className:"w-full",children:[e.jsxs(be,{className:"mb-4",children:[e.jsx(Q,{value:"basic",children:"Basic Info"}),e.jsx(Q,{value:"questions",children:"Questions"})]}),e.jsxs(D,{value:"basic",className:"space-y-4",children:[e.jsx(H,{control:n.control,name:"title",render:({field:m})=>e.jsxs(V,{children:[e.jsx(U,{children:"Title"}),e.jsx(O,{children:e.jsx(P,A({placeholder:"Enter test title"},m))}),e.jsx(Re,{children:"Title shown to students taking the test"}),e.jsx(Y,{})]})}),e.jsx(H,{control:n.control,name:"description",render:({field:m})=>e.jsxs(V,{children:[e.jsx(U,{children:"Description"}),e.jsx(O,{children:e.jsx(ve,B(A({placeholder:"Enter test description"},m),{value:m.value||""}))}),e.jsx(Re,{children:"Instructions for students taking the test"}),e.jsx(Y,{})]})}),e.jsx(H,{control:n.control,name:"type",render:({field:m})=>e.jsxs(V,{children:[e.jsx(U,{children:"Test Type"}),e.jsxs($s,{onValueChange:m.onChange,defaultValue:m.value,disabled:!!r,children:[e.jsx(O,{children:e.jsx(Qs,{children:e.jsx(Ds,{placeholder:"Select test type"})})}),e.jsxs(Bs,{children:[e.jsx(Ce,{value:"pre_test",children:"Pre-Test"}),e.jsx(Ce,{value:"post_test",children:"Post-Test"})]})]}),e.jsx(Re,{children:"Pre-tests are shown before first lesson, post-tests after last lesson"}),e.jsx(Y,{})]})})]}),e.jsxs(D,{value:"questions",className:"space-y-6",children:[e.jsxs("div",{className:"mb-4 flex justify-between items-center",children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Test Questions"}),e.jsxs(g,{type:"button",onClick:x,variant:"outline",size:"sm",children:[e.jsx(ae,{className:"w-4 h-4 mr-2"})," Add Question"]})]}),i.length===0?e.jsxs("div",{className:"text-center py-8 border border-dashed rounded-lg",children:[e.jsx("p",{className:"text-muted-foreground",children:"No questions added yet"}),e.jsxs(g,{type:"button",onClick:x,variant:"outline",size:"sm",className:"mt-2",children:[e.jsx(ae,{className:"w-4 h-4 mr-2"})," Add Question"]})]}):e.jsx("div",{className:"space-y-6",children:i.map((m,o)=>e.jsxs(I,{className:"border border-border",children:[e.jsx(re,{className:"pb-2",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs(te,{className:"text-base",children:["Question ",m.questionNumber]}),e.jsxs($e,{children:[e.jsx(cs,{asChild:!0,children:e.jsx(g,{size:"icon",variant:"ghost",className:"h-8 w-8",children:e.jsx(ue,{className:"h-4 w-4"})})}),e.jsxs(Qe,{children:[e.jsxs(De,{children:[e.jsx(Be,{children:"Are you sure?"}),e.jsx(Ke,{children:"This will permanently delete this question."})]}),e.jsxs(Ve,{children:[e.jsx(Oe,{children:"Cancel"}),e.jsx(Ye,{onClick:()=>_(o),children:"Delete"})]})]})]})]})}),e.jsxs(G,{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(U,{htmlFor:`question-${o}`,children:"Question Text"}),e.jsx(ve,{id:`question-${o}`,value:m.question,onChange:l=>C(o,"question",l.target.value),placeholder:"Enter question text",className:"min-h-20"}),m.question.trim()===""&&e.jsx("p",{className:"text-sm text-destructive",children:"Question text is required"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(U,{htmlFor:`minRating-${o}`,children:"Min Rating"}),e.jsx(P,{id:`minRating-${o}`,type:"number",value:m.minRating,onChange:l=>C(o,"minRating",parseInt(l.target.value)),min:1,max:3})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(U,{htmlFor:`maxRating-${o}`,children:"Max Rating"}),e.jsx(P,{id:`maxRating-${o}`,type:"number",value:m.maxRating,onChange:l=>C(o,"maxRating",parseInt(l.target.value)),min:2,max:5})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(U,{htmlFor:`minLabel-${o}`,children:"Min Label"}),e.jsx(P,{id:`minLabel-${o}`,value:m.minLabel,onChange:l=>C(o,"minLabel",l.target.value),placeholder:"e.g., Not familiar"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(U,{htmlFor:`maxLabel-${o}`,children:"Max Label"}),e.jsx(P,{id:`maxLabel-${o}`,value:m.maxLabel,onChange:l=>C(o,"maxLabel",l.target.value),placeholder:"e.g., Very familiar"})]})]})]})]},m.id))})]})]}),e.jsxs("div",{className:"flex justify-end gap-3 mt-6",children:[e.jsx(g,{type:"button",variant:"outline",onClick:a,children:"Cancel"}),e.jsx(g,{type:"submit",disabled:N.isPending||y.isPending,children:N.isPending||y.isPending?e.jsxs(e.Fragment,{children:[e.jsx(R,{className:"w-4 h-4 mr-2 animate-spin"}),r?"Updating...":"Creating..."]}):e.jsxs(e.Fragment,{children:[e.jsx(ar,{className:"w-4 h-4 mr-2"}),r?"Update":"Create"]})})]})]})}))})]})},Wr=({moduleId:s,moduleName:r})=>{const[t,a]=v.useState(!1),[i,c]=v.useState(!1),[h,j]=v.useState("pre_test"),[d,n]=v.useState(null),{toast:u}=le(),N=ie(),{data:y,isLoading:x}=z({queryKey:["module-tests",s],queryFn:()=>E(void 0,null,function*(){try{return yield Fr(s)}catch(p){return console.error("Error fetching module tests:",p),u({title:"Error",description:"Failed to load module tests",variant:"destructive"}),[]}})}),_=ne({mutationFn:p=>E(void 0,null,function*(){yield Lr(p)}),onSuccess:()=>{u({title:"Success",description:"Test deleted successfully"}),N.invalidateQueries({queryKey:["module-tests",s]})},onError:p=>{console.error("Error deleting test:",p),u({title:"Error",description:p.message||"Failed to delete test",variant:"destructive"})}}),C=p=>{j(p),n(null),a(!0),c(!1)},S=p=>{j(p.type),n(p.id),c(!0),a(!1)},m=p=>{_.mutate(p)},o=()=>{a(!1),c(!1),n(null)},l=(y==null?void 0:y.filter(p=>p.type==="pre_test"))||[],b=(y==null?void 0:y.filter(p=>p.type==="post_test"))||[];return x?e.jsx("div",{className:"flex justify-center py-8",children:e.jsx(R,{className:"w-8 h-8 animate-spin"})}):t||i?e.jsxs("div",{className:"space-y-4",children:[e.jsx(g,{variant:"ghost",size:"sm",onClick:o,className:"mb-4",children:"← Back to Tests List"}),e.jsx(Jr,{moduleId:s,testId:d||void 0,testType:h,onClose:o})]}):e.jsxs(I,{children:[e.jsxs(re,{children:[e.jsxs(te,{className:"text-xl",children:["Module Tests",r&&e.jsxs("span",{className:"text-muted-foreground ml-2 text-base",children:["(",r,")"]})]}),e.jsx(qe,{children:"Manage pre-tests and post-tests for this module"})]}),e.jsx(G,{children:e.jsxs(Ne,{defaultValue:"pre-tests",children:[e.jsxs(be,{className:"mb-4",children:[e.jsx(Q,{value:"pre-tests",children:"Pre-Tests"}),e.jsx(Q,{value:"post-tests",children:"Post-Tests"})]}),e.jsx(D,{value:"pre-tests",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Pre-Intervention Tests"}),l.length===0&&e.jsxs(g,{onClick:()=>C("pre_test"),size:"sm",children:[e.jsx(ae,{className:"w-4 h-4 mr-2"})," Add Pre-Test"]})]}),l.length===0?e.jsxs("div",{className:"text-center py-8 border border-dashed rounded-lg",children:[e.jsx("p",{className:"text-muted-foreground mb-2",children:"No pre-tests have been created for this module"}),e.jsxs(g,{onClick:()=>C("pre_test"),variant:"outline",size:"sm",children:[e.jsx(ae,{className:"w-4 h-4 mr-2"})," Add Pre-Test"]})]}):e.jsxs(Me,{children:[e.jsx(Pe,{children:e.jsxs(ce,{children:[e.jsx(K,{children:"Title"}),e.jsx(K,{children:"Questions"}),e.jsx(K,{children:"Last Updated"}),e.jsx(K,{className:"text-right",children:"Actions"})]})}),e.jsx(ze,{children:l.map(p=>e.jsxs(ce,{children:[e.jsx($,{className:"font-medium",children:p.title}),e.jsxs($,{children:[p.questions.length," questions"]}),e.jsx($,{children:new Date(p.updatedAt).toLocaleDateString()}),e.jsxs($,{className:"text-right space-x-2",children:[e.jsx(g,{onClick:()=>S(p),variant:"ghost",size:"icon",children:e.jsx(ye,{className:"w-4 h-4"})}),e.jsxs($e,{children:[e.jsx(cs,{asChild:!0,children:e.jsx(g,{variant:"ghost",size:"icon",children:e.jsx(ue,{className:"w-4 h-4"})})}),e.jsxs(Qe,{children:[e.jsxs(De,{children:[e.jsx(Be,{children:"Are you sure?"}),e.jsx(Ke,{children:"This will permanently delete this test and all student responses to it."})]}),e.jsxs(Ve,{children:[e.jsx(Oe,{children:"Cancel"}),e.jsx(Ye,{onClick:()=>m(p.id),children:"Delete"})]})]})]})]})]},p.id))})]})]})}),e.jsx(D,{value:"post-tests",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Post-Intervention Tests"}),b.length===0&&e.jsxs(g,{onClick:()=>C("post_test"),size:"sm",children:[e.jsx(ae,{className:"w-4 h-4 mr-2"})," Add Post-Test"]})]}),b.length===0?e.jsxs("div",{className:"text-center py-8 border border-dashed rounded-lg",children:[e.jsx("p",{className:"text-muted-foreground mb-2",children:"No post-tests have been created for this module"}),e.jsxs(g,{onClick:()=>C("post_test"),variant:"outline",size:"sm",children:[e.jsx(ae,{className:"w-4 h-4 mr-2"})," Add Post-Test"]})]}):e.jsxs(Me,{children:[e.jsx(Pe,{children:e.jsxs(ce,{children:[e.jsx(K,{children:"Title"}),e.jsx(K,{children:"Questions"}),e.jsx(K,{children:"Last Updated"}),e.jsx(K,{className:"text-right",children:"Actions"})]})}),e.jsx(ze,{children:b.map(p=>e.jsxs(ce,{children:[e.jsx($,{className:"font-medium",children:p.title}),e.jsxs($,{children:[p.questions.length," questions"]}),e.jsx($,{children:new Date(p.updatedAt).toLocaleDateString()}),e.jsxs($,{className:"text-right space-x-2",children:[e.jsx(g,{onClick:()=>S(p),variant:"ghost",size:"icon",children:e.jsx(ye,{className:"w-4 h-4"})}),e.jsxs($e,{children:[e.jsx(cs,{asChild:!0,children:e.jsx(g,{variant:"ghost",size:"icon",children:e.jsx(ue,{className:"w-4 h-4"})})}),e.jsxs(Qe,{children:[e.jsxs(De,{children:[e.jsx(Be,{children:"Are you sure?"}),e.jsx(Ke,{children:"This will permanently delete this test and all student responses to it."})]}),e.jsxs(Ve,{children:[e.jsx(Oe,{children:"Cancel"}),e.jsx(Ye,{onClick:()=>m(p.id),children:"Delete"})]})]})]})]})]},p.id))})]})]})})]})})]})},Ls=({courseId:s,moduleId:r,onClose:t,onAddLesson:a})=>{const{toast:i}=le(),c=ie(),[h,j]=v.useState(1),[d,n]=v.useState(null),[u,N]=v.useState("basic"),y=!r,x=Ie({defaultValues:{title:"",slug:"",module_number:1,image_url:""}}),{isLoading:_}=z({queryKey:["module-editor",r],queryFn:()=>E(void 0,null,function*(){if(!r)return null;const{data:l,error:b}=yield q.from("modules").select("*").eq("id",r).single();if(b)return console.error("Error fetching module:",b),i({title:"Error",description:"Failed to load module data",variant:"destructive"}),null;if(x.reset({title:l.title,slug:l.slug,module_number:l.module_number,image_url:l.image_url||""}),l.image_url){n(l.image_url);const p=l.image_url.match(/module-(\d+)/);p&&p[1]&&j(parseInt(p[1]))}return l}),enabled:!!r}),C=l=>{j(l);const b=rs(l);n(b),x.setValue("image_url",b)},S=ne({mutationFn:l=>E(void 0,null,function*(){if(!s)throw new Error("Course ID is required");const b=l.slug||l.title.toLowerCase().replace(/\s+/g,"-"),p=rs(h);if(r){const{data:T,error:w}=yield q.from("modules").update({title:l.title,slug:b,module_number:l.module_number,image_url:p}).eq("id",r).select().single();if(w)throw w;return T}else{const T={course_id:s,title:l.title,slug:b,module_number:l.module_number,is_locked:!1,image_url:p},w=yield Yr(T);if(!w)throw new Error("Failed to create module");return w}}),onSuccess:l=>{i({title:y?"Module Created":"Module Updated",description:`The module has been ${y?"created":"updated"} successfully.`}),c.invalidateQueries({queryKey:["modules"]}),c.invalidateQueries({queryKey:["course-modules"]}),c.invalidateQueries({queryKey:["module-editor",r]}),y&&c.invalidateQueries({queryKey:["course-modules",s]}),t()},onError:l=>{console.error("Error saving module:",l),i({title:"Error",description:l.message||"Failed to save module",variant:"destructive"})}}),m=ne({mutationFn:l=>E(void 0,null,function*(){const{error:b}=yield q.from("lessons").delete().eq("id",l);if(b)throw b}),onSuccess:()=>{i({title:"Lesson Deleted",description:"The lesson has been deleted successfully."}),c.invalidateQueries({queryKey:["module-lessons",r]})},onError:l=>{console.error("Error deleting lesson:",l),i({title:"Error",description:l.message||"Failed to delete lesson",variant:"destructive"})}}),o=l=>{S.mutate(l)};return _?e.jsx("div",{className:"flex justify-center py-8",children:e.jsx(R,{className:"w-8 h-8 animate-spin"})}):e.jsx("div",{className:"space-y-6",children:e.jsxs(Ne,{value:u,onValueChange:N,children:[e.jsxs(be,{className:"mb-6",children:[e.jsx(Q,{value:"basic",children:"Module Info"}),e.jsx(Q,{value:"lessons",children:"Lessons"}),e.jsx(Q,{value:"tests",children:"Tests"})]}),e.jsx(D,{value:"basic",children:e.jsx(Je,B(A({},x),{children:e.jsxs("form",{onSubmit:x.handleSubmit(o),className:"space-y-6",children:[e.jsx(H,{control:x.control,name:"title",rules:{required:"Title is required"},render:({field:l})=>e.jsxs(V,{children:[e.jsx(U,{children:"Module Title"}),e.jsx(O,{children:e.jsx(P,A({placeholder:"Enter module title"},l))}),e.jsx(Y,{})]})}),e.jsx(H,{control:x.control,name:"slug",render:({field:l})=>e.jsxs(V,{children:[e.jsx(U,{children:"Module Slug"}),e.jsx(O,{children:e.jsx(P,A({placeholder:"Enter module slug or leave empty to auto-generate"},l))}),e.jsx(Y,{})]})}),e.jsx(H,{control:x.control,name:"module_number",rules:{required:"Module number is required"},render:({field:l})=>e.jsxs(V,{children:[e.jsx(U,{children:"Module Order"}),e.jsx(O,{children:e.jsx(P,B(A({type:"number",min:"1",placeholder:"Enter module order"},l),{value:l.value,onChange:b=>l.onChange(parseInt(b.target.value))}))}),e.jsx(Y,{})]})}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(U,{children:"Module Image"}),e.jsx("div",{className:"grid grid-cols-4 gap-4",children:[1,2,3,4,5,6].map(l=>e.jsx("div",{className:`border rounded-md p-2 cursor-pointer transition-all ${h===l?"ring-2 ring-primary":"hover:border-primary"}`,onClick:()=>C(l),children:e.jsx(Gr,{src:rs(l),alt:`Module Image ${l}`,width:100,height:60,className:"w-full h-auto object-cover rounded"})},l))})]}),e.jsxs("div",{className:"flex justify-end space-x-2 pt-4",children:[e.jsx(g,{type:"button",variant:"outline",onClick:t,children:"Cancel"}),e.jsx(g,{type:"submit",disabled:S.isPending,children:S.isPending?e.jsxs(e.Fragment,{children:[e.jsx(R,{className:"mr-2 h-4 w-4 animate-spin"}),"Saving..."]}):"Save Module"})]})]})}))}),e.jsxs(D,{value:"lessons",children:[!y&&r&&e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium mb-4",children:"Module Lessons"}),e.jsx(Ir,{moduleId:r,onEditLesson:l=>{t()},onDeleteLesson:l=>{m.mutate(l)},onAddLesson:a?()=>a(r):void 0})]}),y&&e.jsxs("div",{className:"p-6 text-center border border-dashed rounded-lg",children:[e.jsx("p",{className:"text-muted-foreground mb-2",children:"You must save the module before adding lessons"}),e.jsx(g,{onClick:()=>N("basic"),variant:"outline",size:"sm",children:"Go Back to Module Info"})]})]}),e.jsx(D,{value:"tests",children:!y&&r?e.jsx(Wr,{moduleId:r,moduleName:x.watch("title")}):e.jsxs("div",{className:"p-6 text-center border border-dashed rounded-lg",children:[e.jsx("p",{className:"text-muted-foreground mb-2",children:"You must save the module before managing tests"}),e.jsx(g,{onClick:()=>N("basic"),variant:"outline",size:"sm",children:"Go Back to Module Info"})]})})]})})};function Xr({courseId:s}){const[r,t]=v.useState(!1),a=ie(),i="https://docs.google.com/forms/d/e/1FAIpQLScCgeernaUISyIWuL6qHpt8hpjb-E59m_A5y1r4o-D5L_LNcA/viewform?usp=header",c=()=>E(this,null,function*(){if(!s){M.error("Course ID is required");return}t(!0);try{(yield Ur(s,i))?(M.success("FINAL EXAMINATION module added successfully"),a.invalidateQueries({queryKey:["admin-course-modules",s]}),a.invalidateQueries({queryKey:["courseModules",s]})):M.error("Failed to add FINAL EXAMINATION module")}catch(h){console.error("Error adding FINAL EXAMINATION module:",h),M.error("An error occurred while adding the FINAL EXAMINATION module")}finally{t(!1)}});return e.jsxs(g,{onClick:c,disabled:r,className:"flex items-center",children:[r?e.jsx(R,{className:"w-4 h-4 mr-2 animate-spin"}):e.jsx(ae,{className:"w-4 h-4 mr-2"}),"Add Final Exam"]})}const Zr=({courseId:s})=>{const{data:r,isLoading:t}=z({queryKey:["course-enrollments",s],queryFn:()=>E(void 0,null,function*(){try{const{count:a,error:i}=yield Fe(()=>E(void 0,null,function*(){return yield q.from("user_course_enrollment").select("*",{count:"exact",head:!0}).eq("course_id",s)}));if(i)throw i;return a||0}catch(a){return console.error("Error fetching enrollment count:",a),0}})});return t?null:e.jsxs("div",{className:"flex items-center text-sm text-gray-500",children:[e.jsx(xe,{size:14,className:"mr-1"}),r||0," enrolled"]})},et=({selectedCourseId:s,setSelectedCourseId:r,selectedModuleId:t,setSelectedModuleId:a,setSelectedLessonId:i,isAddingModule:c,setIsAddingModule:h,setIsAddingLesson:j,confirmDelete:d,handleBackToCoursesList:n,handleBackToCourse:u})=>{const{toast:N}=le();ie();const y=Ge(),{data:x,isLoading:_,refetch:C}=z({queryKey:["admin-courses"],queryFn:()=>E(void 0,null,function*(){try{const{data:o,error:l}=yield Fe(()=>E(void 0,null,function*(){return yield q.from("courses").select("*").order("created_at",{ascending:!1})}));if(l)throw console.error("Error fetching courses:",l),N({title:"Error fetching courses",description:l.message,variant:"destructive"}),l;return o}catch(o){throw N({title:"Failed to fetch courses from database",description:o.message||"Please try again in a few moments",variant:"destructive"}),o}})}),{data:S}=z({queryKey:["admin-course",s],queryFn:()=>E(void 0,null,function*(){if(!s||s==="new")return null;try{const{data:o,error:l}=yield Fe(()=>E(void 0,null,function*(){return yield q.from("courses").select("*").eq("id",s).single()}));return l?(console.error("Error fetching course details:",l),null):o}catch(o){return console.error("Error in fetchCourseDetails:",o),null}}),enabled:!!s&&s!=="new"}),{data:m}=z({queryKey:["admin-course-modules",s],queryFn:()=>E(void 0,null,function*(){if(!s||s==="new")return[];try{const{data:o,error:l}=yield Fe(()=>E(void 0,null,function*(){return yield q.from("modules").select("*").eq("course_id",s).order("module_number",{ascending:!0})}));return l?(console.error("Error fetching course modules:",l),[]):o}catch(o){return console.error("Error in fetchCourseModules:",o),[]}}),enabled:!!s&&s!=="new"});return s?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsxs(g,{variant:"ghost",size:"sm",onClick:n,className:"mr-2",children:[e.jsx(ws,{className:"w-4 h-4 mr-1"})," Back"]}),e.jsx("h2",{className:"text-xl font-semibold",children:s==="new"?"Create New Course":`Editing: ${(S==null?void 0:S.title)||"Course"}`})]}),s!=="new"&&e.jsxs(g,{variant:"outline",size:"sm",onClick:()=>d(s,"course"),className:"text-red-500 hover:text-red-700 hover:bg-red-50 border-red-200",children:[e.jsx(ue,{className:"w-4 h-4 mr-1"})," Delete Course"]})]}),e.jsx(Vr,{courseId:s==="new"?void 0:s,onClose:n,initialData:S}),s!=="new"&&e.jsxs("div",{className:"mt-8 space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Course Modules"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(Xr,{courseId:s}),e.jsxs(g,{onClick:()=>h(!0),className:"flex items-center",children:[e.jsx(ae,{className:"w-4 h-4 mr-1"})," Add Module"]})]})]}),c?e.jsx(Ls,{courseId:s,onClose:()=>h(!1),onAddLesson:o=>{a(o),j(!0)}}):t&&t!=="new"?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsxs(g,{variant:"ghost",size:"sm",onClick:()=>a(null),className:"mr-2",children:[e.jsx(ws,{className:"w-4 h-4 mr-1"})," Back to Modules"]}),e.jsx("h3",{className:"text-lg font-medium",children:"Edit Module"})]}),e.jsx(Ls,{courseId:s,moduleId:t,onClose:()=>a(null),onAddLesson:o=>{a(o),j(!0)}})]}):e.jsxs("div",{className:"space-y-2",children:[!(m!=null&&m.length)&&e.jsxs("div",{className:"text-center py-8 bg-gray-50 rounded-lg border border-dashed",children:[e.jsx("p",{className:"text-gray-500 mb-2",children:"This course doesn't have any modules yet"}),e.jsxs(g,{onClick:()=>h(!0),className:"flex items-center mx-auto",children:[e.jsx(ae,{className:"w-4 h-4 mr-1"})," Add First Module"]})]}),m==null?void 0:m.map(o=>e.jsx("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium",children:o.title}),e.jsxs("p",{className:"text-sm text-gray-500",children:["Module ",o.module_number]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(g,{variant:"outline",size:"sm",onClick:()=>{a(o.id),j(!0)},children:[e.jsx(fe,{className:"w-3 h-3 mr-1"})," Add Lesson"]}),e.jsx(g,{variant:"ghost",size:"sm",onClick:()=>a(o.id),children:e.jsx(ye,{className:"w-4 h-4"})}),e.jsx(g,{variant:"ghost",size:"sm",onClick:()=>d(o.id,"module"),className:"text-red-500 hover:text-red-700 hover:bg-red-50",children:e.jsx(ue,{className:"w-4 h-4"})})]})]})},o.id))]})]})]}):e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Course Management"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(g,{variant:"outline",size:"sm",onClick:()=>C(),disabled:_,children:e.jsx(He,{className:"w-4 h-4"})}),e.jsxs(g,{onClick:()=>r("new"),className:"flex items-center",children:[e.jsx(ae,{className:"w-4 h-4 mr-1"})," New Course"]})]})]}),e.jsx("div",{className:"space-y-4",children:_?e.jsx("div",{className:"flex justify-center p-8",children:e.jsx(R,{className:"w-6 h-6 animate-spin text-blue-500"})}):e.jsxs("div",{className:"grid gap-4 md:grid-cols-2",children:[x==null?void 0:x.map(o=>e.jsxs("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow relative group",children:[e.jsxs("div",{className:"cursor-pointer",onClick:()=>r(o.id),children:[e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsx("h3",{className:"font-medium",children:o.title}),e.jsx(Zr,{courseId:o.id})]}),e.jsx("p",{className:"text-sm text-gray-500 line-clamp-2",children:o.description}),e.jsxs("div",{className:"mt-2 text-xs text-gray-400",children:["Last updated: ",new Date(o.updated_at).toLocaleDateString()]})]}),e.jsxs("div",{className:"absolute top-3 right-3 flex gap-2 transition-opacity",children:[e.jsx(g,{variant:"ghost",size:"sm",onClick:l=>{l.stopPropagation(),y(`/module-management/${o.id}`)},className:"h-8 w-8 p-0",title:"Manage Modules",children:e.jsx(fe,{className:"h-4 w-4 text-gray-500"})}),e.jsx(g,{variant:"ghost",size:"sm",onClick:l=>{l.stopPropagation(),r(o.id)},className:"h-8 w-8 p-0",title:"Edit Course",children:e.jsx(ye,{className:"h-4 w-4 text-gray-500"})}),e.jsx(g,{variant:"ghost",size:"sm",onClick:l=>{l.stopPropagation(),d(o.id,"course")},className:"h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50",title:"Delete Course",children:e.jsx(ue,{className:"h-4 w-4"})})]})]},o.id)),(x==null?void 0:x.length)===0&&e.jsxs("div",{className:"col-span-2 text-center py-12 bg-gray-50 rounded-lg border border-dashed",children:[e.jsx("p",{className:"text-gray-500 mb-4",children:"No courses yet"}),e.jsxs(g,{onClick:()=>r("new"),className:"flex items-center mx-auto",children:[e.jsx(ae,{className:"w-4 h-4 mr-1"})," Create Your First Course"]})]})]})})]})},st=({form:s,handleTitleChange:r,restrictType:t=!1})=>e.jsxs("div",{className:"space-y-4",children:[e.jsx(H,{control:s.control,name:"title",render:({field:a})=>e.jsxs(V,{children:[e.jsx(U,{children:"Title"}),e.jsx(O,{children:e.jsx(P,B(A({placeholder:"Enter title"},a),{onChange:i=>{a.onChange(i),r&&r(i)}}))}),e.jsx(Y,{})]})}),e.jsx(H,{control:s.control,name:"slug",render:({field:a})=>e.jsxs(V,{children:[e.jsx(U,{children:"URL Slug"}),e.jsx(O,{children:e.jsx(P,A({placeholder:"content-slug"},a))}),e.jsx(Y,{})]})}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(H,{control:s.control,name:"duration",render:({field:a})=>e.jsxs(V,{children:[e.jsx(U,{children:"Duration (mm:ss)"}),e.jsx(O,{children:e.jsx(P,A({placeholder:"15:00"},a))}),e.jsx(Y,{})]})}),e.jsx(H,{control:s.control,name:"type",render:({field:a})=>e.jsxs(V,{children:[e.jsx(U,{children:"Content Type"}),t?e.jsx(P,{value:a.value.charAt(0).toUpperCase()+a.value.slice(1),disabled:!0,className:"bg-muted"}):e.jsxs($s,{onValueChange:i=>{a.onChange(i),i==="quiz"&&s.setValue("isRichContent",!0)},value:a.value,children:[e.jsx(O,{children:e.jsx(Qs,{children:e.jsx(Ds,{placeholder:"Select a content type"})})}),e.jsxs(Bs,{children:[e.jsx(Ce,{value:"lesson",children:"Lesson (Markdown)"}),e.jsx(Ce,{value:"quiz",children:"Quiz"}),e.jsx(Ce,{value:"assignment",children:"Assignment (Markdown)"})]})]}),e.jsx(Y,{})]})})]}),e.jsx(H,{control:s.control,name:"requirement",render:({field:a})=>e.jsxs(V,{children:[e.jsx(U,{children:"Requirements (Optional)"}),e.jsx(O,{children:e.jsx(P,B(A({placeholder:"Any prerequisites or requirements"},a),{value:a.value||""}))}),e.jsx(Y,{})]})})]}),rt=({quizQuestions:s,setQuizQuestions:r,onSubmit:t,isSubmitting:a=!1})=>{const i=nr.useMemo(()=>s.length===0?!1:s.every(n=>{const u=n.question.trim().length>0,N=n.options.some(y=>y.trim().length>0);return u&&N}),[s]),c=()=>{r([...s,{id:crypto.randomUUID(),question:"",options:["","","",""],correctAnswer:0}])},h=n=>{const u=[...s];u.splice(n,1),r(u)},j=(n,u,N)=>{const y=[...s];y[n][u]=N,r(y)},d=(n,u,N)=>{const y=[...s];y[n].options[u]=N,r(y)};return e.jsxs("div",{className:"space-y-6",children:[s.length===0&&e.jsxs("div",{className:"bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-900 rounded-md p-4 mb-4",children:[e.jsx("h4",{className:"text-blue-800 dark:text-blue-300 font-medium mb-2",children:"Creating a Quiz"}),e.jsx("p",{className:"text-blue-700 dark:text-blue-400 text-sm mb-2",children:"Quizzes are a great way to test student knowledge. Add questions with multiple-choice answers and mark the correct option."}),e.jsx("p",{className:"text-blue-700 dark:text-blue-400 text-sm",children:"Students will need to complete the quiz to progress through the course."})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h4",{className:"text-sm font-medium",children:"Quiz Questions"}),e.jsxs(g,{type:"button",variant:"outline",size:"sm",onClick:c,children:[e.jsx(Cs,{className:"w-4 h-4 mr-1"})," Add Question"]})]}),s.length===0?e.jsxs("div",{className:"text-center py-8 border border-dashed border-border rounded-md bg-muted/30 dark:bg-muted/10",children:[e.jsx("p",{className:"text-muted-foreground mb-3",children:"No questions added yet"}),e.jsxs(g,{type:"button",variant:"outline",size:"sm",onClick:c,children:[e.jsx(Cs,{className:"w-4 h-4 mr-1"})," Add Your First Question"]})]}):e.jsx("div",{className:"space-y-6",children:s.map((n,u)=>e.jsx(I,{className:"relative",children:e.jsxs(G,{className:"pt-6",children:[e.jsx("div",{className:"absolute top-3 right-3",children:e.jsx(g,{type:"button",variant:"ghost",size:"sm",onClick:()=>h(u),className:"h-8 w-8 p-0 text-gray-400 hover:text-red-500",children:e.jsx(ir,{className:"h-4 w-4"})})}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"mb-4",children:[e.jsxs(Le,{htmlFor:`question-${u}`,children:["Question ",u+1]}),e.jsx(P,{id:`question-${u}`,value:n.question,onChange:N=>j(u,"question",N.target.value),className:"mt-1",placeholder:"Enter your question"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(Le,{children:"Answer Options"}),n.options.map((N,y)=>e.jsx("div",{className:"flex items-center space-x-2",children:e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(P,{value:N,onChange:x=>d(u,y,x.target.value),placeholder:`Option ${y+1}`,className:"flex-1"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(qr,{checked:n.correctAnswer===y,onCheckedChange:()=>j(u,"correctAnswer",y),"aria-label":"Set as correct answer"}),e.jsx(Le,{className:"text-xs",children:"Correct"})]})]})})},y))]})]})]})},n.id))}),s.length>0&&!i&&e.jsxs("div",{className:"mt-4 p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-md text-amber-800 dark:text-amber-300 text-sm",children:[e.jsx("p",{className:"font-medium",children:"Please complete your quiz before submitting:"}),e.jsxs("ul",{className:"list-disc list-inside mt-1",children:[s.some(n=>!n.question.trim())&&e.jsx("li",{children:"All questions must have text"}),s.some(n=>!n.options.some(u=>u.trim().length>0))&&e.jsx("li",{children:"Each question must have at least one answer option"})]})]}),s.length>0&&t&&e.jsx("div",{className:"mt-8 pt-4 border-t border-border flex justify-end",children:e.jsx(g,{type:"button",onClick:t,disabled:a||!i,className:"bg-primary hover:bg-primary/90 text-white",children:a?e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"mr-2",children:"Creating Quiz"}),e.jsx("span",{className:"animate-spin",children:"⟳"})]}):"Create Quiz"})})]})},tt=({form:s,lessonType:r,content:t,quizQuestions:a,setQuizQuestions:i,onSubmit:c,isSubmitting:h=!1,courseId:j,moduleId:d})=>{const n=s.watch("content");return r==="quiz"&&a&&i?e.jsx(rt,{quizQuestions:a,setQuizQuestions:i,onSubmit:c,isSubmitting:h}):e.jsxs("div",{className:"space-y-6",children:[!n&&e.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4 mb-4",children:[e.jsxs("h4",{className:"text-blue-800 font-medium mb-2",children:["Creating a ",r==="assignment"?"Assignment":"Lesson"]}),e.jsx("p",{className:"text-blue-700 text-sm mb-2",children:r==="assignment"?"Assignments allow students to submit work for review. Provide clear instructions and requirements.":"Lessons can include text content, videos, and images. Use Markdown to format your content."}),e.jsx("p",{className:"text-blue-700 text-sm",children:"You can add videos and images to enhance the learning experience."})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"flex justify-between items-center",children:e.jsx(U,{children:r==="assignment"?"Assignment Details":"Lesson Content"})}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"flex justify-between items-center mb-2",children:e.jsx("label",{className:"text-sm font-medium",children:"Content"})}),e.jsx(H,{control:s.control,name:"content",render:({field:u})=>e.jsxs(V,{children:[e.jsx(O,{children:e.jsx(Mr,{initialContent:u.value||"",onChange:u.onChange,placeholder:`Enter ${r} content...`,minHeight:400,courseId:j,moduleId:d})}),e.jsx(Y,{}),e.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:"Rich text editor with Markdown output. Use the toolbar for formatting or switch to preview mode to see the rendered content."})]})})]})]})]})},at=s=>s.toLowerCase().replace(/[^\w\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim(),nt=Cr({title:he().min(1,"Title is required"),slug:he().min(1,"Slug is required"),duration:he(),type:Er(["lesson","assignment","quiz","video"]),content:he().optional(),requirement:he().optional(),videoUrl:he().optional(),imageUrl:he().optional()}),it=({moduleId:s,lessonId:r,defaultType:t="lesson",restrictType:a=!1,onClose:i})=>{const{toast:c}=le(),h=ie(),[j,d]=v.useState("basic"),[n,u]=v.useState(!1),[N,y]=v.useState(!1),[x,_]=v.useState([]),[C,S]=v.useState(void 0),m=Ie({resolver:Ks(nt),defaultValues:{title:"",slug:"",duration:"15:00",type:t,content:"",requirement:"",videoUrl:"",imageUrl:""}});z({queryKey:["module",s],queryFn:()=>E(void 0,null,function*(){const{data:w,error:L}=yield q.from("modules").select("course_id").eq("id",s).single();return L?(console.error("Error fetching module:",L),null):(S(w.course_id),w)}),enabled:!!s});const o=m.watch("title");v.useEffect(()=>{if(o&&!r){const w=at(o);m.setValue("slug",w)}},[o,m,r]);const{isLoading:l}=z({queryKey:["lesson-editor",r],queryFn:()=>E(void 0,null,function*(){if(!r)return null;const{data:w,error:L}=yield q.from("lessons").select("*").eq("id",r).single();if(L)return console.error("Error fetching lesson:",L),c({title:"Error",description:"Failed to load lesson data",variant:"destructive"}),null;const k=w;if(m.reset({title:k.title,slug:k.slug,duration:k.duration||"15:00",type:k.type,content:k.content||"",requirement:k.requirement||"",videoUrl:k.video_url||k.videoUrl||"",imageUrl:k.image_url||k.imageUrl||""}),u(!!(k.video_url||k.videoUrl)),y(!!(k.image_url||k.imageUrl)),k.type==="quiz"&&k.content)try{const W=JSON.parse(k.content);W.questions&&_(W.questions)}catch(W){console.error("Error parsing quiz questions:",W)}return w}),enabled:!!r}),b=ne({mutationFn:w=>E(void 0,null,function*(){let L=w.content;w.type==="quiz"&&x.length>0&&(L=JSON.stringify({questions:x})),(n||N)&&(L=JSON.stringify({content:w.content,videoUrl:w.videoUrl,imageUrl:w.imageUrl}));const k={module_id:s,title:w.title,slug:w.slug,duration:w.duration,type:w.type,content:L,requirement:w.requirement},{data:W,error:X}=yield q.from("lessons").insert([k]).select().single();if(X)throw X;return W}),onSuccess:()=>{c({title:"Success",description:"Lesson created successfully"}),h.invalidateQueries({queryKey:["module-lessons"]}),h.invalidateQueries({queryKey:["lessons"]}),i()},onError:w=>{console.error("Error creating lesson:",w),c({title:"Error",description:w.message||"Failed to create lesson",variant:"destructive"})}}),p=ne({mutationFn:w=>E(void 0,null,function*(){if(!r)throw new Error("Lesson ID is required for update");let L=w.content;w.type==="quiz"&&x.length>0&&(L=JSON.stringify({questions:x})),(n||N)&&(L=JSON.stringify({content:w.content,videoUrl:w.videoUrl,imageUrl:w.imageUrl}));const k={title:w.title,slug:w.slug,duration:w.duration||"15:00",type:w.type,content:L,requirement:w.requirement||null,updated_at:new Date().toISOString()},{data:W,error:X}=yield q.from("lessons").update(k).eq("id",r).select().single();if(X)throw console.error("Lesson update error:",X),X;return W}),onSuccess:()=>{c({title:"Success",description:"Lesson updated successfully"}),h.invalidateQueries({queryKey:["module-lessons"]}),h.invalidateQueries({queryKey:["lessons"]}),h.invalidateQueries({queryKey:["lesson-editor",r]}),i()},onError:w=>{console.error("Error updating lesson:",w),c({title:"Error",description:w.message||"Failed to update lesson",variant:"destructive"})}}),T=w=>{var L,k;if(!((L=w.title)!=null&&L.trim())){c({title:"Validation Error",description:"Title is required",variant:"destructive"});return}if(!((k=w.slug)!=null&&k.trim())){c({title:"Validation Error",description:"Slug is required",variant:"destructive"});return}r?p.mutate(w):b.mutate(w)};return l?e.jsx("div",{className:"flex justify-center py-8",children:e.jsx(R,{className:"w-8 h-8 animate-spin"})}):e.jsx(Je,B(A({},m),{children:e.jsxs("form",{onSubmit:m.handleSubmit(T),children:[e.jsxs(Ne,{value:j,onValueChange:d,children:[e.jsxs(be,{children:[e.jsx(Q,{value:"basic",children:"Basic Info"}),e.jsx(Q,{value:"content",children:"Content"})]}),e.jsx(D,{value:"basic",className:"space-y-4",children:e.jsx(st,{form:m,hasVideo:n,setHasVideo:u,hasImage:N,setHasImage:y,restrictType:a})}),e.jsx(D,{value:"content",className:"space-y-4",children:e.jsx(tt,{form:m,lessonType:m.watch("type"),content:m.watch("content"),quizQuestions:x,setQuizQuestions:_,onSubmit:()=>m.handleSubmit(T)(),courseId:C,moduleId:s})})]}),e.jsxs("div",{className:"flex justify-end gap-2 mt-4 pt-4 border-t",children:[e.jsx(g,{type:"button",variant:"outline",onClick:i,children:"Cancel"}),e.jsx(g,{type:"submit",disabled:b.isPending||p.isPending,children:b.isPending||p.isPending?e.jsxs(e.Fragment,{children:[e.jsx(R,{className:"w-4 h-4 mr-2 animate-spin"}),r?"Updating...":"Creating..."]}):r?"Update Lesson":"Create Lesson"})]})]})}))},lt=({selectedModuleId:s,setSelectedModuleId:r,selectedLessonId:t,setSelectedLessonId:a,isAddingLesson:i,setIsAddingLesson:c,confirmDelete:h})=>{const[j,d]=v.useState(""),{toast:n}=le();ie();const u=Ge(),{data:N}=z({queryKey:["admin-modules"],queryFn:()=>E(void 0,null,function*(){const{data:o,error:l}=yield q.from("modules").select("*, courses(title)").order("created_at",{ascending:!1});if(l)throw console.error("Error fetching modules:",l),n({title:"Error fetching modules",description:l.message,variant:"destructive"}),l;return o})}),{data:y,isLoading:x,refetch:_}=z({queryKey:["admin-lessons"],queryFn:()=>E(void 0,null,function*(){const{data:o,error:l}=yield q.from("lessons").select("*, modules(title, course_id, courses(title))").not("type","eq","quiz").order("created_at",{ascending:!1});if(l)throw console.error("Error fetching lessons:",l),n({title:"Error fetching lessons",description:l.message,variant:"destructive"}),l;return o})}),C=y==null?void 0:y.filter(o=>{var b,p,T,w,L;if(o.type==="quiz")return!1;if(!j)return!0;const l=j.toLowerCase();return o.title.toLowerCase().includes(l)||o.type.toLowerCase().includes(l)||((p=(b=o.modules)==null?void 0:b.title)==null?void 0:p.toLowerCase().includes(l))||((L=(w=(T=o.modules)==null?void 0:T.courses)==null?void 0:w.title)==null?void 0:L.toLowerCase().includes(l))}),S=(o,l)=>{u(`/course/${o}/lesson/${l}`)},m=()=>{a(null),c(!1),r(null)};return e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Lesson Management"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("div",{className:"relative",children:[e.jsx(Rs,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-gray-500"}),e.jsx(P,{type:"search",placeholder:"Search lessons...",className:"pl-8 w-64",value:j,onChange:o=>d(o.target.value)})]}),e.jsx(g,{variant:"outline",size:"sm",onClick:()=>_(),disabled:x,children:e.jsx(He,{className:"w-4 h-4"})})]})]}),t||i?e.jsx("div",{className:"bg-card text-card-foreground p-4 rounded-lg border border-border mb-4 shadow-sm",children:s?e.jsx(it,{moduleId:s,lessonId:t||void 0,defaultType:"lesson",restrictType:!0,onClose:m}):e.jsxs("div",{className:"p-4 text-center",children:[e.jsx("p",{className:"text-destructive",children:"Please select a module first"}),e.jsx(g,{variant:"outline",size:"sm",onClick:m,className:"mt-2",children:"Cancel"})]})}):e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex flex-wrap gap-2 mb-4",children:N==null?void 0:N.map(o=>e.jsxs(g,{variant:"outline",size:"sm",onClick:()=>{r(o.id),c(!0)},children:[e.jsx(fe,{className:"w-3 h-3 mr-1"}),"Add Lesson to ",o.title]},o.id))}),x?e.jsx("div",{className:"flex justify-center p-8",children:e.jsx(R,{className:"w-6 h-6 animate-spin text-primary"})}):e.jsx("div",{className:"border rounded-md",children:e.jsxs(Me,{children:[e.jsx(Pe,{children:e.jsxs(ce,{children:[e.jsx(K,{children:"Title"}),e.jsx(K,{children:"Type"}),e.jsx(K,{children:"Module"}),e.jsx(K,{children:"Course"}),e.jsx(K,{className:"text-right",children:"Actions"})]})}),e.jsxs(ze,{children:[(C==null?void 0:C.length)===0&&e.jsx(ce,{children:e.jsx($,{colSpan:5,className:"text-center py-4 text-muted-foreground",children:"No lessons found"})}),C==null?void 0:C.map(o=>{var l,b,p;return e.jsxs(ce,{children:[e.jsx($,{className:"font-medium",children:o.title}),e.jsx($,{children:e.jsx("span",{className:`px-2 py-1 rounded-full text-xs ${o.type==="quiz"?"bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300":o.type==="assignment"?"bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300":"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"}`,children:o.type.charAt(0).toUpperCase()+o.type.slice(1)})}),e.jsx($,{children:(l=o.modules)==null?void 0:l.title}),e.jsx($,{children:(p=(b=o.modules)==null?void 0:b.courses)==null?void 0:p.title}),e.jsx($,{className:"text-right",children:e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx(g,{variant:"ghost",size:"sm",onClick:()=>{var T;return S((T=o.modules)==null?void 0:T.course_id,o.slug)},title:"View lesson",children:e.jsx(fe,{className:"h-4 w-4"})}),e.jsx(g,{variant:"ghost",size:"sm",onClick:()=>{r(o.module_id),a(o.id)},title:"Edit lesson",children:e.jsx(ye,{className:"h-4 w-4"})}),e.jsx(g,{variant:"ghost",size:"sm",onClick:()=>h(o.id,"lesson"),className:"text-red-500 hover:text-red-700 hover:bg-red-50",title:"Delete lesson",children:e.jsx(ue,{className:"h-4 w-4"})})]})})]},o.id)})]})]})})]})]})},ot=()=>{const{toast:s}=le(),r=ie(),[t,a]=v.useState(null),{data:i,isLoading:c,refetch:h}=z({queryKey:["promotable-users"],queryFn:zr}),j=ne({mutationFn:n=>$r(n),onSuccess:n=>{n&&(r.invalidateQueries({queryKey:["promotable-users"]}),a(null))},onError:n=>{console.error("Error promoting user:",n),s({title:"Error",description:"Failed to promote user. Please try again.",variant:"destructive"}),a(null)}}),d=n=>n.first_name&&n.last_name?`${n.first_name} ${n.last_name}`:n.id;return e.jsxs(I,{children:[e.jsxs(re,{className:"flex flex-row items-center justify-between pb-2",children:[e.jsx(te,{className:"text-xl",children:"User Management"}),e.jsx(g,{variant:"outline",size:"sm",onClick:()=>h(),disabled:c,children:c?e.jsx(R,{className:"h-4 w-4 animate-spin"}):e.jsx(He,{className:"h-4 w-4"})})]}),e.jsxs(G,{children:[c?e.jsx("div",{className:"flex justify-center py-8",children:e.jsx(R,{className:"h-8 w-8 animate-spin text-blue-500"})}):!i||i.length===0?e.jsx("div",{className:"text-center py-8 text-gray-500",children:"No users available to promote to teacher role."}):e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"text-sm font-medium mb-2",children:"Promote Users to Teacher"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Teachers can create courses, lessons, and manage other teaching functions."})]}),e.jsx("div",{className:"grid gap-2",children:i.map(n=>e.jsxs("div",{className:"flex items-center justify-between p-3 border rounded-md hover:bg-gray-50",children:[e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:d(n)}),e.jsxs("div",{className:"text-xs text-gray-500",children:["ID: ",n.id]})]}),e.jsxs(g,{variant:"outline",size:"sm",onClick:()=>a({id:n.id,name:d(n)}),className:"flex items-center gap-1",children:[e.jsx(Us,{className:"h-3.5 w-3.5"}),"Promote to Teacher"]})]},n.id))})]}),e.jsx($e,{open:!!t,onOpenChange:n=>!n&&a(null),children:e.jsxs(Qe,{children:[e.jsxs(De,{children:[e.jsx(Be,{children:"Promote to Teacher Role"}),e.jsxs(Ke,{children:["Are you sure you want to promote ",e.jsx("span",{className:"font-medium",children:t==null?void 0:t.name})," to a teacher role? They will be able to create and manage courses, lessons, and other educational content."]})]}),e.jsxs(Ve,{children:[e.jsx(Oe,{children:"Cancel"}),e.jsx(Ye,{onClick:()=>{t&&j.mutate(t.id)},disabled:j.isPending,children:j.isPending?e.jsxs(e.Fragment,{children:[e.jsx(R,{className:"mr-2 h-4 w-4 animate-spin"}),"Promoting..."]}):"Confirm Promotion"})]})]})})]})]})},ct=()=>{const[s,r]=v.useState(""),[t,a]=v.useState(!1),[i,c]=v.useState(null),h=()=>E(void 0,null,function*(){if(s){a(!0),c(null);try{const j=yield Or(s);c(j?{success:!0,message:`Teacher role successfully assigned to user ${s}`}:{success:!1,message:"Failed to assign teacher role. Please check the user ID and try again."})}catch(j){c({success:!1,message:`Error: ${j.message||"Unknown error"}`})}finally{a(!1)}}});return e.jsxs(I,{className:"w-full",children:[e.jsxs(re,{children:[e.jsx(te,{children:"Assign Teacher Role"}),e.jsx(qe,{children:"Enter a user ID to assign the teacher role. This will give the user permission to manage course content."})]}),e.jsxs(G,{children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(P,{placeholder:"User ID",value:s,onChange:j=>r(j.target.value),disabled:t}),e.jsxs(g,{onClick:h,disabled:!s||t,children:[t?e.jsx(R,{className:"h-4 w-4 animate-spin mr-2"}):null,"Assign Role"]})]}),i&&e.jsx("div",{className:`mt-4 p-3 rounded-md ${i.success,"bg-red-50 text-red-800"}`,children:i.message})]}),e.jsx(ms,{className:"text-sm text-gray-500",children:e.jsxs("p",{children:["You can find a user's ID in the Supabase dashboard under Authentication ",">"," Users."]})})]})},dt=()=>{var T,w,L,k,W,X;const[s,r]=v.useState(""),[t,a]=v.useState(null),[i,c]=v.useState(!1),[h,j]=v.useState(!1),[d,n]=v.useState(""),u=ie(),{data:N,isLoading:y,error:x}=z({queryKey:["role-requests"],queryFn:()=>E(void 0,null,function*(){try{const{data:f,error:Z}=yield q.from("role_requests").select("*, user:user_id(email, user_metadata)").order("requested_at",{ascending:!1});if(Z)throw Z;return f}catch(f){throw console.error("Error fetching role requests:",f),M.error("Failed to load role requests"),f}})}),_=ne({mutationFn:je=>E(void 0,[je],function*({requestId:f,notes:Z}){const{data:se,error:oe}=yield q.rpc("approve_role_request",{_request_id:f,_notes:Z});if(oe)throw oe;return se}),onSuccess:()=>{M.success("Role request approved successfully"),u.invalidateQueries({queryKey:["role-requests"]}),c(!1),a(null),n("")},onError:f=>{console.error("Error approving role request:",f),M.error(f.message||"Failed to approve role request")}}),C=ne({mutationFn:je=>E(void 0,[je],function*({requestId:f,notes:Z}){const{data:se,error:oe}=yield q.rpc("reject_role_request",{_request_id:f,_notes:Z});if(oe)throw oe;return se}),onSuccess:()=>{M.success("Role request rejected"),u.invalidateQueries({queryKey:["role-requests"]}),j(!1),a(null),n("")},onError:f=>{console.error("Error rejecting role request:",f),M.error(f.message||"Failed to reject role request")}}),S=f=>{a(f),n(""),c(!0)},m=f=>{a(f),n(""),j(!0)},o=()=>{if(t){_.mutate({requestId:t.id,notes:d});try{q.from("notifications").insert([{user_id:t.user_id,type:"role_approved",title:"Teacher Role Approved",message:"Your request for teacher privileges has been approved. You now have access to create and manage courses.",data:{request_id:t.id,notes:d||void 0},read:!1,created_at:new Date().toISOString()}]).then(({error:f})=>{f&&console.error("Error sending approval notification:",f)})}catch(f){console.error("Error in approval notification:",f)}}},l=()=>{if(t){C.mutate({requestId:t.id,notes:d});try{q.from("notifications").insert([{user_id:t.user_id,type:"role_rejected",title:"Teacher Role Request Declined",message:"Your request for teacher privileges has been declined.",data:{request_id:t.id,notes:d||void 0},read:!1,created_at:new Date().toISOString()}]).then(({error:f})=>{f&&console.error("Error sending rejection notification:",f)})}catch(f){console.error("Error in rejection notification:",f)}}},b=N==null?void 0:N.filter(f=>{var oe,_e,we;const Z=((oe=f.user)==null?void 0:oe.email)||"",je=((we=(_e=f.user)==null?void 0:_e.user_metadata)==null?void 0:we.full_name)||"",se=s.toLowerCase();return Z.toLowerCase().includes(se)||je.toLowerCase().includes(se)||f.requested_role.toLowerCase().includes(se)||f.status.toLowerCase().includes(se)}),p=f=>{switch(f){case"pending":return e.jsx(me,{variant:"outline",className:"bg-yellow-100 text-yellow-800 border-yellow-300",children:"Pending"});case"approved":return e.jsx(me,{variant:"outline",className:"bg-red-100 text-red-800 border-red-300",children:"Approved"});case"rejected":return e.jsx(me,{variant:"outline",className:"bg-red-100 text-red-800 border-red-300",children:"Rejected"});default:return e.jsx(me,{variant:"outline",children:f})}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Role Requests"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("div",{className:"relative",children:[e.jsx(Rs,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),e.jsx(P,{type:"search",placeholder:"Search requests...",className:"pl-8 w-64",value:s,onChange:f=>r(f.target.value)})]}),e.jsx(g,{variant:"outline",size:"sm",onClick:()=>u.invalidateQueries({queryKey:["role-requests"]}),children:"Refresh"})]})]}),y?e.jsx("div",{className:"flex justify-center p-12",children:e.jsx(R,{className:"h-8 w-8 animate-spin text-primary"})}):x?e.jsx(I,{className:"border-red-300 bg-red-50",children:e.jsx(G,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center text-red-800",children:[e.jsx(Xe,{className:"h-5 w-5 mr-2"}),e.jsx("p",{children:"Failed to load role requests. Please try again."})]})})}):(b==null?void 0:b.length)===0?e.jsx(I,{children:e.jsx(G,{className:"pt-6 text-center text-muted-foreground",children:s?"No requests match your search":"No role requests found"})}):e.jsx("div",{className:"grid gap-4",children:b==null?void 0:b.map(f=>{var Z,je,se;return e.jsxs(I,{className:"overflow-hidden",children:[e.jsx(re,{className:"pb-2",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx(te,{className:"text-lg",children:((je=(Z=f.user)==null?void 0:Z.user_metadata)==null?void 0:je.full_name)||"Unknown User"}),e.jsx(qe,{children:((se=f.user)==null?void 0:se.email)||"No email available"})]}),p(f.status)]})}),e.jsxs(G,{className:"pb-2",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"flex items-center text-sm",children:[e.jsx(Ms,{className:"h-4 w-4 mr-2 text-muted-foreground"}),e.jsx("span",{children:"Requested role: "}),e.jsx("span",{className:"font-medium ml-1 capitalize",children:f.requested_role})]}),e.jsxs("div",{className:"flex items-center text-sm",children:[e.jsx(lr,{className:"h-4 w-4 mr-2 text-muted-foreground"}),e.jsx("span",{children:"Requested: "}),e.jsx("span",{className:"font-medium ml-1",children:Ss(new Date(f.requested_at),{addSuffix:!0})})]})]}),f.notes&&e.jsxs("div",{className:"mt-2 p-2 bg-muted rounded text-sm",children:[e.jsx("p",{className:"font-medium",children:"Notes:"}),e.jsx("p",{children:f.notes})]})]}),e.jsx(ms,{className:"pt-2",children:f.status==="pending"?e.jsxs("div",{className:"flex gap-2 w-full",children:[e.jsxs(g,{variant:"outline",size:"sm",className:"flex-1 border-red-500 text-red-600 hover:bg-red-50",onClick:()=>S(f),children:[e.jsx(Es,{className:"h-4 w-4 mr-2"}),"Approve"]}),e.jsxs(g,{variant:"outline",size:"sm",className:"flex-1 border-red-500 text-red-600 hover:bg-red-50",onClick:()=>m(f),children:[e.jsx(Xe,{className:"h-4 w-4 mr-2"}),"Reject"]})]}):e.jsx("div",{className:"text-sm text-muted-foreground w-full text-right",children:f.processed_at&&e.jsxs("span",{children:[f.status==="approved"?"Approved":"Rejected"," ",Ss(new Date(f.processed_at),{addSuffix:!0})]})})})]},f.id)})}),e.jsx(ts,{open:i,onOpenChange:c,children:e.jsxs(as,{children:[e.jsxs(ns,{children:[e.jsx(is,{children:"Approve Role Request"}),e.jsx(ls,{children:"Are you sure you want to approve this request? This will grant teacher privileges to the user."})]}),e.jsxs("div",{className:"space-y-4 py-2",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"text-sm font-medium",children:"User:"}),e.jsxs("p",{className:"text-sm",children:[(w=(T=t==null?void 0:t.user)==null?void 0:T.user_metadata)==null?void 0:w.full_name," (",(L=t==null?void 0:t.user)==null?void 0:L.email,")"]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"text-sm font-medium",children:"Requested Role:"}),e.jsx("p",{className:"text-sm capitalize",children:t==null?void 0:t.requested_role})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{htmlFor:"notes",className:"text-sm font-medium",children:"Notes (optional):"}),e.jsx(ve,{id:"notes",placeholder:"Add any notes about this approval",value:d,onChange:f=>n(f.target.value)})]})]}),e.jsxs(os,{children:[e.jsx(g,{variant:"outline",onClick:()=>c(!1),children:"Cancel"}),e.jsx(g,{onClick:o,disabled:_.isPending,className:"bg-red-600 hover:bg-red-700",children:_.isPending?e.jsxs(e.Fragment,{children:[e.jsx(R,{className:"h-4 w-4 mr-2 animate-spin"}),"Approving..."]}):e.jsxs(e.Fragment,{children:[e.jsx(Es,{className:"h-4 w-4 mr-2"}),"Approve"]})})]})]})}),e.jsx(ts,{open:h,onOpenChange:j,children:e.jsxs(as,{children:[e.jsxs(ns,{children:[e.jsx(is,{children:"Reject Role Request"}),e.jsx(ls,{children:"Are you sure you want to reject this request? The user will remain with their current role."})]}),e.jsxs("div",{className:"space-y-4 py-2",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"text-sm font-medium",children:"User:"}),e.jsxs("p",{className:"text-sm",children:[(W=(k=t==null?void 0:t.user)==null?void 0:k.user_metadata)==null?void 0:W.full_name," (",(X=t==null?void 0:t.user)==null?void 0:X.email,")"]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"text-sm font-medium",children:"Requested Role:"}),e.jsx("p",{className:"text-sm capitalize",children:t==null?void 0:t.requested_role})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{htmlFor:"reject-notes",className:"text-sm font-medium",children:"Reason for rejection (optional):"}),e.jsx(ve,{id:"reject-notes",placeholder:"Provide a reason for rejecting this request",value:d,onChange:f=>n(f.target.value)})]})]}),e.jsxs(os,{children:[e.jsx(g,{variant:"outline",onClick:()=>j(!1),children:"Cancel"}),e.jsx(g,{onClick:l,disabled:C.isPending,variant:"destructive",children:C.isPending?e.jsxs(e.Fragment,{children:[e.jsx(R,{className:"h-4 w-4 mr-2 animate-spin"}),"Rejecting..."]}):e.jsxs(e.Fragment,{children:[e.jsx(Xe,{className:"h-4 w-4 mr-2"}),"Reject"]})})]})]})})]})},mt=()=>{const[s,r]=v.useState("users");return e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold mb-4",children:"User Management"}),e.jsxs(Ne,{value:s,onValueChange:r,className:"w-full",children:[e.jsxs(be,{className:"grid w-full grid-cols-3 mb-6",children:[e.jsxs(Q,{value:"users",className:"flex items-center gap-2",children:[e.jsx(Ms,{className:"h-4 w-4"}),"Users"]}),e.jsxs(Q,{value:"roles",className:"flex items-center gap-2",children:[e.jsx(Us,{className:"h-4 w-4"}),"Assign Roles"]}),e.jsxs(Q,{value:"requests",className:"flex items-center gap-2",children:[e.jsx(Ps,{className:"h-4 w-4"}),"Role Requests"]})]}),e.jsx(D,{value:"users",children:e.jsx(ot,{})}),e.jsx(D,{value:"roles",children:e.jsx(ct,{})}),e.jsx(D,{value:"requests",children:e.jsx(dt,{})})]})]})};function ut({data:s,className:r,selectedCourseId:t,selectedModuleId:a,selectedLessonId:i,selectedCourse:c,selectedModule:h,handleBackToCoursesList:j,handleBackToCourse:d,setSelectedLessonId:n}){const u=x=>{switch(x){case"course":return e.jsx(cr,{className:"h-4 w-4 mr-1 inline"});case"module":return e.jsx(fe,{className:"h-4 w-4 mr-1 inline"});case"lesson":return e.jsx(or,{className:"h-4 w-4 mr-1 inline"});default:return e.jsx(qs,{className:"h-4 w-4 mr-1 inline"})}},N=(x,_,C=!1)=>e.jsx(Ts,{className:_||r||"mb-4",children:e.jsxs(ks,{children:[e.jsx(ke,{children:C&&j?e.jsx(pe,{onClick:j,className:"cursor-pointer",children:"All Courses"}):e.jsx(pe,{asChild:!0,children:e.jsxs(Ze,{to:"/admin?tab=courses",children:[u(x.parentType||"home"),"All Courses"]})})}),x.parentId&&x.parentTitle&&x.parentType&&e.jsxs(e.Fragment,{children:[e.jsx(As,{}),e.jsx(ke,{children:C&&d?e.jsx(pe,{onClick:d,className:"cursor-pointer",children:x.parentTitle}):e.jsx(pe,{asChild:!0,children:e.jsxs(Ze,{to:`/admin?tab=${x.parentType==="course"?"courses":"modules"}&${x.parentType}Id=${x.parentId}`,children:[u(x.parentType),x.parentTitle]})})})]}),x.title&&e.jsxs(e.Fragment,{children:[e.jsx(As,{}),e.jsx(ke,{children:C&&x.type==="module"&&n?e.jsx(pe,{onClick:()=>n(null),className:"cursor-pointer",children:x.title}):e.jsxs(_r,{children:[u(x.type),x.title]})})]})]})});if(t!==void 0){if(t||a||i){let x;if(i?x={id:i,title:i==="new"?"New Lesson":"Lesson",type:"lesson",parentId:a||void 0,parentTitle:(h==null?void 0:h.title)||"Module",parentType:"module"}:a?x={id:a,title:a==="new"?"New Module":(h==null?void 0:h.title)||"Module",type:"module",parentId:t||void 0,parentTitle:(c==null?void 0:c.title)||"Course",parentType:"course"}:t&&(x={id:t,title:t==="new"?"New Course":(c==null?void 0:c.title)||"Course",type:"course"}),x)return N(x,r,!0)}return null}return s?N(s):e.jsx(Ts,{className:r,children:e.jsx(ks,{children:e.jsx(ke,{children:e.jsx(pe,{asChild:!0,children:e.jsxs(Ze,{to:"/admin",children:[e.jsx(qs,{className:"h-4 w-4 mr-1 inline"}),"Admin"]})})})})})}const ht=({open:s,onOpenChange:r,itemType:t,onDelete:a})=>e.jsx(ts,{open:s,onOpenChange:r,children:e.jsxs(as,{children:[e.jsxs(ns,{children:[e.jsx(is,{children:"Confirm Deletion"}),e.jsxs(ls,{children:["Are you sure you want to delete this ",t,"? This action cannot be undone.",t==="course"&&e.jsxs("div",{className:"mt-2 p-2 bg-red-50 border border-red-200 rounded-md text-red-700 text-sm",children:[e.jsx("strong",{children:"Warning:"})," Deleting a course will also delete all associated modules, lessons, and quizzes."]})]})]}),e.jsxs(os,{children:[e.jsx(g,{variant:"outline",onClick:()=>r(!1),children:"Cancel"}),e.jsx(g,{variant:"destructive",onClick:a,children:"Delete"})]})]})});function xt(){const{toast:s}=le(),[r,t]=v.useState(!1),[a,i]=v.useState([{name:ss.STORAGE.COURSE_IMAGES,exists:!1,isChecking:!1,isCreating:!1},{name:ss.STORAGE.AVATARS,exists:!1,isChecking:!1,isCreating:!1},{name:ss.STORAGE.APP_UPLOADS,exists:!1,isChecking:!1,isCreating:!1},{name:"uploads",exists:!1,isChecking:!1,isCreating:!1}]),c=()=>E(this,null,function*(){t(!0);try{const{data:d,error:n}=yield q.storage.listBuckets();if(n){console.error("Error listing buckets:",n),s({title:"Error",description:`Failed to list buckets: ${n.message}`,variant:"destructive"}),t(!1);return}const u=(d==null?void 0:d.map(N=>N.name))||[];i(N=>N.map(y=>B(A({},y),{exists:u.includes(y.name)}))),s({title:"Buckets Checked",description:`Found ${u.length} buckets.`})}catch(d){console.error("Error checking buckets:",d),s({title:"Error",description:`An unexpected error occurred: ${d.message}`,variant:"destructive"})}finally{t(!1)}}),h=d=>E(this,null,function*(){i(n=>n.map(u=>u.name===d?B(A({},u),{isCreating:!0}):u));try{const{data:n,error:u}=yield q.storage.createBucket(d,{public:!0});if(u){console.error(`Error creating bucket '${d}':`,u),s({title:"Error",description:`Failed to create bucket '${d}': ${u.message}`,variant:"destructive"}),i(N=>N.map(y=>y.name===d?B(A({},y),{isCreating:!1}):y));return}i(N=>N.map(y=>y.name===d?B(A({},y),{exists:!0,isCreating:!1}):y)),s({title:"Bucket Created",description:`Successfully created bucket '${d}'.`})}catch(n){console.error(`Error creating bucket '${d}':`,n),s({title:"Error",description:`An unexpected error occurred: ${n.message}`,variant:"destructive"}),i(u=>u.map(N=>N.name===d?B(A({},N),{isCreating:!1}):N))}}),j=()=>E(this,null,function*(){t(!0);try{const d=a.filter(n=>!n.exists);for(const n of d)yield h(n.name);s({title:"Buckets Created",description:`Attempted to create ${d.length} missing buckets.`})}catch(d){console.error("Error creating buckets:",d),s({title:"Error",description:`An unexpected error occurred: ${d.message}`,variant:"destructive"})}finally{t(!1)}});return e.jsxs(I,{children:[e.jsxs(re,{children:[e.jsxs(te,{className:"flex items-center gap-2",children:[e.jsx(Ae,{className:"h-5 w-5"})," Storage Bucket Manager"]}),e.jsx(qe,{children:"Manage Supabase storage buckets required for the application"})]}),e.jsx(G,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h3",{className:"text-sm font-medium",children:"Storage Buckets"}),e.jsxs(g,{variant:"outline",size:"sm",onClick:c,disabled:r,children:[r?e.jsx(R,{className:"h-4 w-4 mr-2 animate-spin"}):e.jsx(He,{className:"h-4 w-4 mr-2"}),"Check Buckets"]})]}),e.jsx("div",{className:"space-y-2",children:a.map(d=>e.jsxs("div",{className:"flex justify-between items-center p-3 rounded-md border",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"font-mono text-sm",children:d.name}),d.exists?e.jsxs(me,{variant:"outline",className:"bg-red-50 text-red-700 border-red-200",children:[e.jsx(zs,{className:"h-3 w-3 mr-1"})," Exists"]}):e.jsxs(me,{variant:"outline",className:"bg-amber-50 text-amber-700 border-amber-200",children:[e.jsx(ds,{className:"h-3 w-3 mr-1"})," Missing"]})]}),!d.exists&&e.jsxs(g,{size:"sm",variant:"outline",onClick:()=>h(d.name),disabled:d.isCreating,children:[d.isCreating?e.jsx(R,{className:"h-3 w-3 mr-1 animate-spin"}):e.jsx(Ae,{className:"h-3 w-3 mr-1"}),"Create"]})]},d.name))})]})}),e.jsx(ms,{children:e.jsxs(g,{onClick:j,disabled:r||a.every(d=>d.exists),className:"w-full",children:[r?e.jsx(R,{className:"h-4 w-4 mr-2 animate-spin"}):e.jsx(Ae,{className:"h-4 w-4 mr-2"}),"Create All Missing Buckets"]})})]})}function jt(){const[s,r]=v.useState(!1),{toast:t}=le();v.useEffect(()=>{const i=localStorage.getItem("storage-notification-dismissed")==="true";r(i)},[]);const a=()=>{localStorage.setItem("storage-notification-dismissed","true"),r(!0)};return s?null:e.jsx("div",{className:"bg-red-50 border border-red-200 rounded-md p-4 mb-6",children:e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx(zs,{className:"h-5 w-5 text-red-600"})}),e.jsxs("div",{className:"ml-3 flex-1",children:[e.jsx("h3",{className:"text-sm font-medium text-red-800",children:"New Basic Image Upload System"}),e.jsxs("div",{className:"mt-2 text-sm text-red-700",children:[e.jsx("p",{children:"We've implemented a completely new, basic image upload system that works reliably. Images are automatically resized to 600x400 pixels and stored directly in the database. You can now add images to courses and they will appear on the course cards."}),e.jsxs("div",{className:"mt-3",children:[e.jsx(g,{variant:"outline",size:"sm",className:"mr-2 bg-white text-red-700 border-red-300 hover:bg-red-50",onClick:()=>{t({title:"About the Basic Image System",description:"Images are now processed with a simple, reliable approach that ensures they appear on course cards. The system handles all the technical details for you.",duration:5e3})},children:"Learn More"}),e.jsx(g,{variant:"ghost",size:"sm",className:"text-red-700 hover:bg-red-100",onClick:a,children:"Dismiss"})]})]})]}),e.jsx("div",{className:"ml-4 flex-shrink-0",children:e.jsxs(g,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:a,children:[e.jsx("span",{className:"sr-only",children:"Close"}),e.jsx(ds,{className:"h-4 w-4 text-red-500"})]})})]})})}function pt(){var h,j,d,n;const{data:s,isLoading:r,error:t}=z({queryKey:["demographic-analytics"],queryFn:pr}),{data:a,isLoading:i}=z({queryKey:["demographic-responses"],queryFn:gr});if(r||i)return e.jsx("div",{className:"flex items-center justify-center p-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx(R,{className:"h-8 w-8 animate-spin mx-auto mb-4"}),e.jsx("p",{className:"text-muted-foreground",children:"Loading demographic analytics..."})]})});if(t)return e.jsx("div",{className:"text-center p-8",children:e.jsx("p",{className:"text-red-500",children:"Error loading demographic analytics"})});const c=(u,N,y,x)=>e.jsxs(I,{children:[e.jsxs(re,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(te,{className:"text-sm font-medium",children:u}),N]}),e.jsx(G,{children:e.jsx("div",{className:"space-y-2",children:Object.entries(y).sort(([,_],[,C])=>C-_).map(([_,C])=>{const S=x>0?(C/x*100).toFixed(1):"0";return e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-muted-foreground truncate flex-1 mr-2",children:_}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(me,{variant:"secondary",className:"text-xs",children:C}),e.jsxs("span",{className:"text-xs text-muted-foreground min-w-[3rem] text-right",children:[S,"%"]})]})]},_)})})})]});return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold mb-2",children:"Demographic Analytics"}),e.jsx("p",{className:"text-muted-foreground",children:"Overview of student demographic information collected through the onboarding questionnaire."})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs(I,{children:[e.jsxs(re,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(te,{className:"text-sm font-medium",children:"Total Responses"}),e.jsx(xe,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(G,{children:[e.jsx("div",{className:"text-2xl font-bold",children:(s==null?void 0:s.total_responses)||0}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Students completed questionnaire"})]})]}),e.jsxs(I,{children:[e.jsxs(re,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(te,{className:"text-sm font-medium",children:"Completion Rate"}),e.jsx(Ps,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(G,{children:[e.jsxs("div",{className:"text-2xl font-bold",children:[(s==null?void 0:s.completion_rate)||0,"%"]}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Of all registered users"})]})]}),e.jsxs(I,{children:[e.jsxs(re,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(te,{className:"text-sm font-medium",children:"Countries Represented"}),e.jsx(_s,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(G,{children:[e.jsx("div",{className:"text-2xl font-bold",children:(h=s==null?void 0:s.response_breakdown)!=null&&h.by_country?Object.keys(s.response_breakdown.by_country).length:0}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Different countries"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[((j=s==null?void 0:s.response_breakdown)==null?void 0:j.by_country)&&c("By Country",e.jsx(_s,{className:"h-4 w-4 text-muted-foreground"}),s.response_breakdown.by_country,s.total_responses),((d=s==null?void 0:s.response_breakdown)==null?void 0:d.by_gender)&&c("By Gender",e.jsx(xe,{className:"h-4 w-4 text-muted-foreground"}),s.response_breakdown.by_gender,s.total_responses),((n=s==null?void 0:s.response_breakdown)==null?void 0:n.by_role)&&c("By Role Type",e.jsx(dr,{className:"h-4 w-4 text-muted-foreground"}),s.response_breakdown.by_role,s.total_responses)]}),a&&a.length>0&&e.jsxs(I,{children:[e.jsx(re,{children:e.jsx(te,{children:"Recent Responses"})}),e.jsx(G,{children:e.jsx("div",{className:"space-y-4",children:a.slice(0,10).map(u=>{var N,y;return e.jsxs("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx("div",{className:"font-medium",children:(N=u.profiles)!=null&&N.first_name&&((y=u.profiles)!=null&&y.last_name)?`${u.profiles.first_name} ${u.profiles.last_name}`:"Anonymous User"}),e.jsxs("div",{className:"text-sm text-muted-foreground",children:[u.responses.country&&`From ${u.responses.country}`,u.responses.role_type&&` • ${u.responses.role_type}`]})]}),e.jsx("div",{className:"text-sm text-muted-foreground",children:new Date(u.completed_at).toLocaleDateString()})]},u.id)})})})]}),(!a||a.length===0)&&e.jsx(I,{children:e.jsxs(G,{className:"text-center py-8",children:[e.jsx(xe,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium mb-2",children:"No Responses Yet"}),e.jsx("p",{className:"text-muted-foreground",children:"Demographic data will appear here once students complete the onboarding questionnaire."})]})})]})}const Bt=()=>{const{isTeacher:s,loading:r,verifyTeacher:t}=xr(),[a,i]=mr(),c=a.get("course"),h=a.get("module"),j=a.get("lesson"),{user:d}=fr(),[n,u]=v.useState(null),[N,y]=v.useState(!1),[x,_]=v.useState(c||null),[C,S]=v.useState(h||null),[m,o]=v.useState(j||null),[l,b]=v.useState(!1),[p,T]=v.useState(!1),[w,L]=v.useState(!1),[k,W]=v.useState(null),{toast:X}=le(),f=ie(),Z=Ge();v.useEffect(()=>{!r&&s?E(void 0,null,function*(){if(!d){u(!1);return}y(!0);try{u(!0),console.log("Teacher role verified from useUserRole")}catch(J){console.error("Error verifying teacher role:",J),u(!1),X({title:"Verification Error",description:"Could not verify your access permissions",variant:"destructive"})}finally{y(!1)}}):!r&&!s&&u(!1)},[d,r,s,X,Z]),v.useEffect(()=>{const F=new URLSearchParams;x&&x!=="new"&&F.set("course",x),C&&C!=="new"&&F.set("module",C),m&&m!=="new"&&F.set("lesson",m),i(F)},[x,C,m,i]),v.useEffect(()=>{if(!s||!n)return;const F=q.channel("admin-course-changes").on("postgres_changes",{event:"*",schema:"public",table:"courses"},()=>{f.invalidateQueries({queryKey:["admin-courses"]})}).subscribe(),J=q.channel("admin-module-changes").on("postgres_changes",{event:"*",schema:"public",table:"modules"},()=>{f.invalidateQueries({queryKey:["admin-modules"]})}).subscribe(),Is=q.channel("admin-lesson-changes").on("postgres_changes",{event:"*",schema:"public",table:"lessons"},()=>{f.invalidateQueries({queryKey:["admin-lessons"]})}).subscribe();return()=>{q.removeChannel(F),q.removeChannel(J),q.removeChannel(Is)}},[s,f,n]);const{data:je}=z({queryKey:["admin-module",C],queryFn:()=>E(void 0,null,function*(){if(!C||C==="new")return null;const{data:F,error:J}=yield q.from("modules").select("*, courses(title)").eq("id",C).single();return J?(console.error("Error fetching module details:",J),null):F}),enabled:!!C&&C!=="new"&&!!n}),{data:se}=z({queryKey:["admin-course",x],queryFn:()=>E(void 0,null,function*(){if(!x||x==="new")return null;const{data:F,error:J}=yield q.from("courses").select("*").eq("id",x).single();return J?(console.error("Error fetching course details:",J),null):F}),enabled:!!x&&x!=="new"&&!!n}),oe=ne({mutationFn:Is=>E(void 0,[Is],function*({id:F,type:J}){if(!s)throw new Error("You do not have permission to delete this item");if(J==="module"){const{data:Se,error:xs}=yield q.from("modules").select("course_id").eq("id",F).single();if(xs)throw xs;const js=Se.course_id,{error:ps}=yield q.from("modules").delete().eq("id",F);if(ps)throw ps;const{data:Gs,error:gs}=yield q.from("modules").select("id").eq("course_id",js);if(gs)throw gs;const Hs=Gs.length,{error:fs}=yield q.from("courses").update({total_modules:Hs}).eq("id",js);if(fs)throw fs}else{const{error:Se}=yield q.from(J==="lesson"?"lessons":"courses").delete().eq("id",F);if(Se)throw Se}return{id:F,type:J}}),onSuccess:F=>{X({title:"Deleted successfully",description:`The ${F.type} has been deleted.`}),F.type==="course"?(f.invalidateQueries({queryKey:["admin-courses"]}),f.invalidateQueries({queryKey:["admin-modules"]}),f.invalidateQueries({queryKey:["admin-lessons"]})):F.type==="module"?(f.invalidateQueries({queryKey:["admin-modules"]}),f.invalidateQueries({queryKey:["admin-lessons"]})):f.invalidateQueries({queryKey:["admin-lessons"]}),F.type==="course"&&x===F.id?_(null):F.type==="module"&&C===F.id?S(null):F.type==="lesson"&&m===F.id&&o(null)},onError:F=>{console.error("Error deleting item:",F),X({title:"Error",description:"Failed to delete. Please try again.",variant:"destructive"})}}),_e=()=>{k&&oe.mutate(k),L(!1),W(null)},we=(F,J)=>{W({id:F,type:J}),L(!0)},us=()=>{_(null),S(null),o(null),T(!1),b(!1)},hs=()=>{S(null),o(null),b(!1)};return r||N?e.jsx(es,{children:e.jsxs("div",{className:"flex items-center justify-center h-64",children:[e.jsx(R,{className:"w-8 h-8 animate-spin text-blue-500"}),e.jsx("p",{className:"ml-2",children:"Verifying access..."})]})}):!s||n===!1?e.jsx(es,{children:e.jsxs("div",{className:"flex flex-col items-center justify-center h-64 text-center",children:[e.jsx(ur,{className:"w-16 h-16 text-red-500 mb-4"}),e.jsx("h1",{className:"text-2xl font-bold text-red-600",children:"Access Denied"}),e.jsx("p",{className:"text-gray-600 mt-2 mb-6",children:"You don't have permission to access the admin area."}),e.jsx(g,{onClick:()=>Z("/dashboard"),children:"Return to Dashboard"})]})}):e.jsxs(es,{children:[e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold",children:"Admin Dashboard"}),e.jsx("p",{className:"text-gray-500",children:"Manage courses, lessons and users"})]}),e.jsx(ut,{selectedCourseId:x,selectedModuleId:C,selectedLessonId:m,selectedCourse:se,selectedModule:je,handleBackToCoursesList:us,handleBackToCourse:hs,setSelectedLessonId:o}),e.jsx(jt,{}),e.jsxs(Ne,{defaultValue:"courses",children:[e.jsxs(be,{className:"grid w-full grid-cols-6 mb-6",children:[e.jsx(Q,{value:"courses",children:"Courses"}),e.jsx(Q,{value:"lessons",children:"Lessons"}),e.jsx(Q,{value:"users",children:"Users"}),e.jsxs(Q,{value:"analytics",children:[e.jsx(hr,{className:"h-4 w-4 mr-2"}),"Test Analytics"]}),e.jsxs(Q,{value:"demographics",children:[e.jsx(xe,{className:"h-4 w-4 mr-2"}),"Demographics"]}),e.jsxs(Q,{value:"storage",children:[e.jsx(Ae,{className:"h-4 w-4 mr-2"}),"Storage"]})]}),e.jsx(D,{value:"courses",className:"p-4 border rounded-md",children:e.jsx(et,{selectedCourseId:x,setSelectedCourseId:_,selectedModuleId:C,setSelectedModuleId:S,setSelectedLessonId:o,isAddingModule:p,setIsAddingModule:T,setIsAddingLesson:b,confirmDelete:we,handleBackToCoursesList:us,handleBackToCourse:hs})}),e.jsx(D,{value:"lessons",className:"p-4 border rounded-md",children:e.jsx(lt,{selectedModuleId:C,setSelectedModuleId:S,selectedLessonId:m,setSelectedLessonId:o,isAddingLesson:l,setIsAddingLesson:b,confirmDelete:we})}),e.jsx(D,{value:"users",className:"p-4 border rounded-md",children:e.jsx(mt,{})}),e.jsx(D,{value:"analytics",className:"p-4 border rounded-md",children:e.jsx(Pr,{})}),e.jsx(D,{value:"demographics",className:"p-4 border rounded-md",children:e.jsx(pt,{})}),e.jsx(D,{value:"storage",className:"p-4 border rounded-md",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Storage Management"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Manage storage buckets for course images and other uploads"})]}),e.jsx("div",{className:"max-w-md mx-auto",children:e.jsx(xt,{})}),e.jsxs("div",{className:"bg-amber-50 border border-amber-200 rounded-md p-4 text-amber-800 text-sm",children:[e.jsx("p",{className:"font-medium",children:"Important Notes:"}),e.jsxs("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[e.jsx("li",{children:`If you're experiencing "bucket not found" errors when uploading images, use this tool to create the necessary buckets.`}),e.jsx("li",{children:"You must have the appropriate permissions in Supabase to create buckets."}),e.jsx("li",{children:"After creating buckets, you may need to set up appropriate RLS (Row Level Security) policies in your Supabase dashboard."})]})]})]})})]})]}),e.jsx(ht,{open:w,onOpenChange:L,itemType:(k==null?void 0:k.type)||null,onDelete:_e})]})};export{Bt as default};
