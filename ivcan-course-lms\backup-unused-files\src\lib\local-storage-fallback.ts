/**
 * Utilities for handling localStorage-based fallbacks for file storage
 * This is used when Supabase storage is not available or has permission issues
 */

/**
 * Check if a URL is a localStorage fallback URL
 * @param url The URL to check
 * @returns True if the URL is a localStorage fallback URL
 */
export function isLocalStorageFallbackUrl(url: string | null | undefined): boolean {
  return typeof url === 'string' && url.startsWith('data:localStorageFile:');
}

/**
 * Get the actual data URL from localStorage for a fallback URL
 * @param fallbackUrl The fallback URL (data:localStorageFile:key)
 * @returns The actual data URL from localStorage, or null if not found
 */
export function getDataUrlFromLocalStorage(fallbackUrl: string | null | undefined): string | null {
  if (!fallbackUrl || typeof fallbackUrl !== 'string' || !isLocalStorageFallbackUrl(fallbackUrl)) {
    return null;
  }

  try {
    const storageKey = fallbackUrl.replace('data:localStorageFile:', '');
    if (!storageKey) {
      console.warn('Invalid localStorage key in fallback URL');
      return null;
    }
    
    const dataUrl = localStorage.getItem(storageKey);
    return dataUrl;
  } catch (error) {
    console.error('Error retrieving data URL from localStorage:', error);
    return null;
  }
}

/**
 * Get the actual image source for a URL, handling localStorage fallbacks
 * @param url The URL to get the image source for
 * @returns The actual image source (either the original URL or a data URL from localStorage)
 */
export function getActualImageSource(url: string | null | undefined): string | null {
  if (!url) {
    return null;
  }

  try {
    if (isLocalStorageFallbackUrl(url)) {
      return getDataUrlFromLocalStorage(url) || null;
    }
    return url;
  } catch (error) {
    console.error('Error in getActualImageSource:', error);
    return null;
  }
}

/**
 * Store a file in localStorage and return a fallback URL
 * @param file The file to store
 * @param bucketName The bucket name (for the storage key)
 * @param filePath The file path (for the storage key)
 * @returns A promise that resolves to a fallback URL
 */
export async function storeFileInLocalStorage(
  file: File,
  bucketName: string,
  filePath: string
): Promise<string> {
  return new Promise((resolve, reject) => {
    try {
      const reader = new FileReader();
      reader.onload = () => {
        const dataUrl = reader.result as string;
        const storageKey = `file_${bucketName}_${filePath.replace(/[^a-zA-Z0-9]/g, '_')}`;
        localStorage.setItem(storageKey, dataUrl);
        console.log(`Stored file in localStorage with key: ${storageKey}`);
        resolve(`data:localStorageFile:${storageKey}`);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Clear all localStorage fallback files
 * This can be used to free up space
 */
export function clearLocalStorageFallbackFiles(): void {
  try {
    const keysToRemove: string[] = [];
    
    // Find all localStorage keys that are for fallback files
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('file_')) {
        keysToRemove.push(key);
      }
    }
    
    // Remove all fallback files
    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
    });
    
    console.log(`Cleared ${keysToRemove.length} fallback files from localStorage`);
  } catch (error) {
    console.error('Error clearing localStorage fallback files:', error);
  }
}
