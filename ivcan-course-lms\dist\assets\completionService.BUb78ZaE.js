const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/accessControlService.DuaMmltt.js","assets/index.BLDhDn0D.js","assets/vendor-react.BcAa1DKr.js","assets/vendor.DQpuTRuB.js","assets/vendor-supabase.sufZ44-y.js","assets/css/index.CBAYZKm-.css","assets/userRoleService.Crm_K_HM.js"])))=>i.map(i=>d[i]);
var p=(e,r,t)=>new Promise((n,l)=>{var c=o=>{try{s(t.next(o))}catch(i){l(i)}},m=o=>{try{s(t.throw(o))}catch(i){l(i)}},s=o=>o.done?n(o.value):Promise.resolve(o.value).then(c,m);s((t=t.apply(e,r)).next())});import{W as f}from"./vendor.DQpuTRuB.js";import{s as a}from"./index.BLDhDn0D.js";import{b as C}from"./achievementService.Vkx_BHml.js";import"./vendor-react.BcAa1DKr.js";import"./vendor-supabase.sufZ44-y.js";function w(e,r){return p(this,null,function*(){if(!e||!r)return console.error("Missing required parameters"),!1;try{const t=new Date().toISOString(),{error:n}=yield a.rpc("complete_course",{p_user_id:r,p_course_id:e});if(n){console.error("Failed to complete course using RPC:",n);const{error:s}=yield a.from("user_course_enrollment").upsert({user_id:r,course_id:e,status:"completed",enrolled_at:t,completed_at:t,updated_at:t},{onConflict:"user_id,course_id"});if(s)return console.error("Failed to update enrollment:",s),!1}const{error:l}=yield a.from("user_course_progress").upsert({user_id:r,course_id:e,completed_modules:100,updated_at:t},{onConflict:"user_id,course_id"});l&&console.error("Failed to update progress:",l);try{const s=localStorage.getItem("completedCourses")||"[]",o=JSON.parse(s);o.includes(e)||(o.push(e),localStorage.setItem("completedCourses",JSON.stringify(o))),localStorage.setItem(`course_${e}_completed_at`,t),localStorage.setItem(`course_${e}_completed_by`,r),localStorage.setItem("lastCompletedCourse",e)}catch(s){console.error("LocalStorage update failed:",s)}const{data:c,error:m}=yield a.from("user_course_enrollment").select("status, completed_at").eq("user_id",r).eq("course_id",e).single();return m?(console.error("Failed to verify completion:",m),!1):(c==null?void 0:c.status)==="completed"&&!!(c!=null&&c.completed_at)}catch(t){return console.error("Course completion failed:",t),!1}})}const N=(e,r,t)=>p(void 0,null,function*(){if(console.log(`[COMPLETION SERVICE] Starting lesson completion for lesson ${e} and user ${r}`),!e||!r)return console.error("[COMPLETION SERVICE] Missing required parameters",{lessonId:e,userId:r}),{success:!1,error:"Missing required parameters"};if(!/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(e))return console.error("[COMPLETION SERVICE] Invalid lesson ID format (not a UUID)",{lessonId:e}),{success:!1,error:`Invalid lesson ID format: "${e}" is not a valid UUID`};try{const{data:l,error:c}=yield a.from("user_lesson_progress").select("*").eq("user_id",r).eq("lesson_id",e).maybeSingle();if(!c&&(l!=null&&l.is_completed))return console.log("[COMPLETION SERVICE] Lesson already marked as completed"),{success:!0,data:l};const{data:m,error:s}=yield a.rpc("mark_lesson_completed",{p_user_id:r,p_lesson_id:e});if(s){console.error("[COMPLETION SERVICE] Error using mark_lesson_completed function:",s);const o=new Date().toISOString(),{data:i,error:d}=yield a.from("user_lesson_progress").upsert({user_id:r,lesson_id:e,is_completed:!0,completed_at:o,updated_at:o},{onConflict:"user_id,lesson_id"}).select("*").maybeSingle();if(d){console.error("[COMPLETION SERVICE] Fallback update also failed:",d);try{return localStorage.setItem(`lesson_${e}_completed`,"true"),localStorage.setItem(`lesson_${e}_completed_at`,o),{success:!0,data:{is_completed:!0,warning:"Completion saved locally only due to database errors"}}}catch(u){return console.error("[COMPLETION SERVICE] Even localStorage fallback failed:",u),{success:!1,error:`Failed to mark lesson as completed: ${d.message}`}}}return console.log("[COMPLETION SERVICE] Lesson marked as completed using fallback method"),setTimeout(()=>{try{a.from("lessons").select("module_id").eq("id",e).single().then(({data:u})=>{u!=null&&u.module_id&&a.rpc("check_module_completion",{p_user_id:r,p_module_id:u.module_id}).then(({error:E})=>{E&&console.error("[COMPLETION SERVICE] Error checking module completion:",E)})})}catch(u){console.error("[COMPLETION SERVICE] Error in module completion check:",u)}},100),{success:!0,data:i||{is_completed:!0}}}console.log("[COMPLETION SERVICE] Lesson successfully marked as completed using RPC function");try{const{updateUserAccess:o}=yield f(()=>p(void 0,null,function*(){const{updateUserAccess:i}=yield import("./accessControlService.DuaMmltt.js");return{updateUserAccess:i}}),__vite__mapDeps([0,1,2,3,4,5,6]));yield o(r),console.log("[COMPLETION SERVICE] User access updated after lesson completion")}catch(o){console.error("[COMPLETION SERVICE] Error updating user access:",o)}return{success:!0,data:{is_completed:!0}}}catch(l){return console.error("[COMPLETION SERVICE] Unexpected error in markLessonAsCompleted:",l),{success:!1,error:`Unexpected error: ${l.message||"Unknown error"}`}}}),R=()=>p(void 0,null,function*(){try{const{data:e,error:r}=yield a.rpc("get_column_info",{table_name:"user_lesson_progress"});if(r)return{success:!1,error:`Could not fetch column info: ${r.message}`};const{data:t,error:n}=yield a.from("completion_audit_log").select("*").order("completed_at",{ascending:!1}).limit(10);return n&&console.error("[COMPLETION SERVICE] Error fetching recent completion attempts:",n),{success:!0,data:{tableInfo:e,recentAttempts:t||[]}}}catch(e){return{success:!1,error:`Unexpected error: ${e.message||"Unknown error"}`}}}),T=(e,r)=>p(void 0,null,function*(){if(console.log(`[COMPLETION SERVICE] Starting explicit course finish for course ${e} and user ${r}`),!e||!r)return console.error("[COMPLETION SERVICE] Missing required parameters",{courseId:e,userId:r}),{success:!1,error:"Missing required parameters"};try{const{data:t,error:n}=yield a.from("modules").select("id").eq("course_id",e).order("module_number");if(n||!t||t.length===0)return console.error("[COMPLETION SERVICE] Error fetching modules or no modules found:",n),{success:!1,error:"Could not verify course modules"};const{data:l,error:c}=yield a.from("user_module_progress").select("module_id").eq("user_id",r).eq("is_completed",!0).in("module_id",t.map(d=>d.id));if(c)return console.error("[COMPLETION SERVICE] Error checking module progress:",c),{success:!1,error:"Could not verify module completion status"};const m=(l==null?void 0:l.length)||0,s=t.length;if(m!==s)return console.error(`[COMPLETION SERVICE] Not all modules completed: ${m}/${s}`),{success:!1,error:`Only ${m} of ${s} modules completed. Please complete all modules first.`};console.log(`[COMPLETION SERVICE] All ${s} modules verified as completed`);const o=new Date().toISOString();console.log("[COMPLETION SERVICE] Updating enrollment status to completed");const{error:i}=yield a.from("user_course_enrollment").upsert({user_id:r,course_id:e,status:"completed",enrolled_at:o,completed_at:o,updated_at:o},{onConflict:"user_id,course_id"});if(i)return console.error("[COMPLETION SERVICE] Failed to update enrollment:",i),{success:!1,error:"Failed to complete course enrollment"};console.log("[COMPLETION SERVICE] Successfully updated enrollment status to completed");try{console.log(`[COMPLETION SERVICE] Awarding course badge for course ${e}`);const d=C(r,e),u=new Promise((E,_)=>setTimeout(()=>_(new Error("Badge awarding timeout")),1e4));yield Promise.race([d,u]),console.log("[COMPLETION SERVICE] Course badge awarded successfully")}catch(d){console.error("[COMPLETION SERVICE] Error awarding course badge:",d)}return console.log(`[COMPLETION SERVICE] Successfully finished course ${e} for user ${r}`),{success:!0,data:{courseId:e,userId:r,completedAt:o}}}catch(t){return console.error("[COMPLETION SERVICE] Unexpected error in finishCourse:",t),{success:!1,error:"An unexpected error occurred while finishing the course"}}});export{w as completeCourse,T as finishCourse,R as getCompletionSystemStatus,N as markLessonAsCompleted};
