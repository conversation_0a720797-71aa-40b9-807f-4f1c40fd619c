import{j as e}from"./vendor-react.BcAa1DKr.js";import{M as a}from"./markdown-preview.Bw0U-NJA.js";import"./vendor.DQpuTRuB.js";import"./content-converter.L-GziWIP.js";import"./index.BLDhDn0D.js";import"./vendor-supabase.sufZ44-y.js";const r=`# Table Parsing Test

This is a test to verify that markdown tables are being parsed correctly.

## Test Table

| SIZES | COLOR | FLOW RATE | INDICATIONS |
|-------|-------|-----------|-------------|
| 14 G | Orange | Faster flow | Trauma, surgical procedures |
| 16 G | Grey | Faster flow | Trauma, surgical procedures |
| 18 G | Green | Medium flow | Trauma, quick blood transfusion |
| 20 G | Pink | Medium flow | Normal IV or blood transfusion |
| 22 G | Blue | Slower flow | Children, older adults |
| 24 G | Yellow | Slower flow rate | Neonates, children, old elderly |
| 26 G | Purple/violet | Slower flow rate | Neonates |

## Another Test Table

| Feature | Description | Status |
|---------|-------------|--------|
| **Bold text** | This should be bold | ✅ Working |
| *Italic text* | This should be italic | ✅ Working |
| \`Code text\` | This should be code | ✅ Working |
| [Link](https://example.com) | This should be a link | ✅ Working |

## Text After Table

This text should appear normally after the table.
`;function n(){return e.jsx("div",{className:"container mx-auto p-8 max-w-4xl",children:e.jsxs("div",{className:"bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6",children:[e.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Markdown Table Parsing Test"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-lg font-semibold mb-3",children:"Raw Markdown:"}),e.jsx("pre",{className:"bg-gray-100 dark:bg-gray-800 p-4 rounded-md text-sm overflow-x-auto",children:r})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-lg font-semibold mb-3",children:"Rendered Output:"}),e.jsx("div",{className:"border border-gray-200 dark:border-gray-700 rounded-md p-4",children:e.jsx(a,{content:r,className:"professional-prose"})})]})]})]})})}export{n as default};
