var N=Object.defineProperty,w=Object.defineProperties;var h=Object.getOwnPropertyDescriptors;var c=Object.getOwnPropertySymbols;var u=Object.prototype.hasOwnProperty,p=Object.prototype.propertyIsEnumerable;var f=(a,e,r)=>e in a?N(a,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[e]=r,l=(a,e)=>{for(var r in e||(e={}))u.call(e,r)&&f(a,r,e[r]);if(c)for(var r of c(e))p.call(e,r)&&f(a,r,e[r]);return a},m=(a,e)=>w(a,h(e));var d=(a,e)=>{var r={};for(var t in a)u.call(a,t)&&e.indexOf(t)<0&&(r[t]=a[t]);if(a!=null&&c)for(var t of c(a))e.indexOf(t)<0&&p.call(a,t)&&(r[t]=a[t]);return r};import{r as n,j as o,bf as x,bg as j,S as y,a9 as R}from"./vendor-react.BcAa1DKr.js";import{c as i}from"./index.BLDhDn0D.js";const T=n.forwardRef((t,r)=>{var s=t,{className:a}=s,e=d(s,["className"]);return o.jsx(x,m(l({className:i("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a)},e),{ref:r,children:o.jsx(j,{className:i("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}))});T.displayName=x.displayName;const B=n.forwardRef((t,r)=>{var s=t,{className:a}=s,e=d(s,["className"]);return o.jsx("div",{className:"relative w-full overflow-auto",children:o.jsx("table",l({ref:r,className:i("w-full caption-bottom text-sm",a)},e))})});B.displayName="Table";const k=n.forwardRef((t,r)=>{var s=t,{className:a}=s,e=d(s,["className"]);return o.jsx("thead",l({ref:r,className:i("[&_tr]:border-b",a)},e))});k.displayName="TableHeader";const v=n.forwardRef((t,r)=>{var s=t,{className:a}=s,e=d(s,["className"]);return o.jsx("tbody",l({ref:r,className:i("[&_tr:last-child]:border-0",a)},e))});v.displayName="TableBody";const C=n.forwardRef((t,r)=>{var s=t,{className:a}=s,e=d(s,["className"]);return o.jsx("tfoot",l({ref:r,className:i("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",a)},e))});C.displayName="TableFooter";const S=n.forwardRef((t,r)=>{var s=t,{className:a}=s,e=d(s,["className"]);return o.jsx("tr",l({ref:r,className:i("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",a)},e))});S.displayName="TableRow";const H=n.forwardRef((t,r)=>{var s=t,{className:a}=s,e=d(s,["className"]);return o.jsx("th",l({ref:r,className:i("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",a)},e))});H.displayName="TableHead";const L=n.forwardRef((t,r)=>{var s=t,{className:a}=s,e=d(s,["className"]);return o.jsx("td",l({ref:r,className:i("p-4 align-middle [&:has([role=checkbox])]:pr-0",a)},e))});L.displayName="TableCell";const E=n.forwardRef((t,r)=>{var s=t,{className:a}=s,e=d(s,["className"]);return o.jsx("caption",l({ref:r,className:i("mt-4 text-sm text-muted-foreground",a)},e))});E.displayName="TableCaption";const F=n.forwardRef((r,e)=>{var a=d(r,[]);return o.jsx("nav",l({ref:e,"aria-label":"breadcrumb"},a))});F.displayName="Breadcrumb";const I=n.forwardRef((t,r)=>{var s=t,{className:a}=s,e=d(s,["className"]);return o.jsx("ol",l({ref:r,className:i("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",a)},e))});I.displayName="BreadcrumbList";const P=n.forwardRef((t,r)=>{var s=t,{className:a}=s,e=d(s,["className"]);return o.jsx("li",l({ref:r,className:i("inline-flex items-center gap-1.5",a)},e))});P.displayName="BreadcrumbItem";const _=n.forwardRef((s,t)=>{var b=s,{asChild:a,className:e}=b,r=d(b,["asChild","className"]);const g=a?y:"a";return o.jsx(g,l({ref:t,className:i("transition-colors hover:text-foreground",e)},r))});_.displayName="BreadcrumbLink";const z=n.forwardRef((t,r)=>{var s=t,{className:a}=s,e=d(s,["className"]);return o.jsx("span",l({ref:r,role:"link","aria-disabled":"true","aria-current":"page",className:i("font-normal text-foreground",a)},e))});z.displayName="BreadcrumbPage";const q=t=>{var s=t,{children:a,className:e}=s,r=d(s,["children","className"]);return o.jsx("li",m(l({role:"presentation","aria-hidden":"true",className:i("[&>svg]:size-3.5",e)},r),{children:a!=null?a:o.jsx(R,{})}))};q.displayName="BreadcrumbSeparator";export{F as B,T as S,B as T,k as a,S as b,H as c,v as d,L as e,I as f,P as g,_ as h,q as i,z as j};
