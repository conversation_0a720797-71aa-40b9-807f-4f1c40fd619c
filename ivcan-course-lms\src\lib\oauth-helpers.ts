/**
 * OAuth Helper Functions
 *
 * This file contains utility functions for OAuth authentication
 */

/**
 * Get the site URL for OAuth redirects
 * Handles different environments (development, production)
 */
export function getSiteUrl(): string {
  // For development environment
  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    return `${window.location.protocol}//${window.location.host}`;
  }

  // For production environment
  return window.location.origin;
}

/**
 * Get the callback URL for OAuth providers
 * This is the URL that the OAuth provider will redirect to after authentication
 */
export function getCallbackUrl(): string {
  const baseUrl = getSiteUrl();
  return `${baseUrl}/auth/callback`;
}

/**
 * Get the redirect URL for OAuth providers
 * @param path The path to redirect to after authentication
 */
export function getRedirectUrl(path: string = '/dashboard'): string {
  // Always use the callback URL for the initial OAuth redirect
  // The callback handler will then redirect to the final destination
  return getCallbackUrl();
}

/**
 * Get Google OAuth query parameters
 * These parameters help ensure a better OAuth experience
 */
export function getGoogleOAuthParams() {
  return {
    // Request offline access to get a refresh token
    access_type: 'offline',
    // Force the account selection screen to appear every time
    prompt: 'select_account',
    // Request basic profile information
    scope: 'profile email'
  };
}
