/* GitHub-flavored Markdown styles for TipTap editor and preview */

.tiptap-markdown-editor {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
}

/* Editor content styling */
.tiptap-markdown-editor .ProseMirror {
  outline: none;
  padding: 1rem;
  min-height: 400px;
  line-height: 1.6;
  color: var(--foreground);
  background-color: var(--background);
}

.tiptap-markdown-editor .ProseMirror p {
  margin: 0.75em 0;
}

.tiptap-markdown-editor .ProseMirror h1,
.tiptap-markdown-editor .ProseMirror h2,
.tiptap-markdown-editor .ProseMirror h3,
.tiptap-markdown-editor .ProseMirror h4,
.tiptap-markdown-editor .ProseMirror h5,
.tiptap-markdown-editor .ProseMirror h6 {
  margin: 1.5em 0 0.5em 0;
  font-weight: 600;
  line-height: 1.25;
}

.tiptap-markdown-editor .ProseMirror h1 {
  font-size: 2em;
  border-bottom: 1px solid var(--border);
  padding-bottom: 0.3em;
}

.tiptap-markdown-editor .ProseMirror h2 {
  font-size: 1.5em;
  border-bottom: 1px solid var(--border);
  padding-bottom: 0.3em;
}

.tiptap-markdown-editor .ProseMirror h3 {
  font-size: 1.25em;
}

.tiptap-markdown-editor .ProseMirror h4 {
  font-size: 1em;
}

.tiptap-markdown-editor .ProseMirror h5 {
  font-size: 0.875em;
}

.tiptap-markdown-editor .ProseMirror h6 {
  font-size: 0.85em;
  color: var(--muted-foreground);
}

/* Lists */
.tiptap-markdown-editor .ProseMirror ul,
.tiptap-markdown-editor .ProseMirror ol {
  margin: 0.75em 0;
  padding-left: 2em;
}

.tiptap-markdown-editor .ProseMirror li {
  margin: 0.25em 0;
}

.tiptap-markdown-editor .ProseMirror ul[data-type="taskList"] {
  list-style: none;
  padding-left: 0;
}

.tiptap-markdown-editor .ProseMirror li[data-type="taskItem"] {
  display: flex;
  align-items: flex-start;
  margin: 0.25em 0;
}

.tiptap-markdown-editor .ProseMirror li[data-type="taskItem"] > label {
  margin-right: 0.5em;
  user-select: none;
}

.tiptap-markdown-editor .ProseMirror li[data-type="taskItem"] > div {
  flex: 1;
}

/* Code */
.tiptap-markdown-editor .ProseMirror code {
  background-color: var(--muted);
  border-radius: 3px;
  font-family: ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 85%;
  padding: 0.2em 0.4em;
}

.tiptap-markdown-editor .ProseMirror pre {
  background-color: var(--muted);
  border-radius: 6px;
  color: var(--foreground);
  font-family: ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 85%;
  line-height: 1.45;
  margin: 1em 0;
  overflow: auto;
  padding: 16px;
}

.tiptap-markdown-editor .ProseMirror pre code {
  background: none;
  border-radius: 0;
  color: inherit;
  font-size: inherit;
  padding: 0;
}

/* Blockquotes */
.tiptap-markdown-editor .ProseMirror blockquote {
  border-left: 4px solid var(--border);
  color: var(--muted-foreground);
  margin: 1em 0;
  padding: 0 1em;
}

.tiptap-markdown-editor .ProseMirror blockquote p {
  margin: 0.5em 0;
}

/* Links */
.tiptap-markdown-editor .ProseMirror a {
  color: hsl(var(--primary));
  text-decoration: underline;
}

.tiptap-markdown-editor .ProseMirror a:hover {
  text-decoration: none;
}

/* Images */
.tiptap-markdown-editor .ProseMirror img {
  border-radius: 6px;
  height: auto;
  max-width: 100%;
}

/* Horizontal rule */
.tiptap-markdown-editor .ProseMirror hr {
  background-color: var(--border);
  border: 0;
  height: 1px;
  margin: 2em 0;
}

/* Highlight */
.tiptap-markdown-editor .ProseMirror mark {
  background-color: hsl(var(--warning) / 0.3);
  border-radius: 3px;
  padding: 0.1em 0.2em;
}

/* Tables */
.tiptap-markdown-editor .ProseMirror table {
  border-collapse: collapse;
  margin: 1em 0;
  overflow: hidden;
  table-layout: fixed;
  width: 100%;
}

.tiptap-markdown-editor .ProseMirror table td,
.tiptap-markdown-editor .ProseMirror table th {
  border: 1px solid var(--border);
  box-sizing: border-box;
  min-width: 1em;
  padding: 6px 8px;
  position: relative;
  vertical-align: top;
}

.tiptap-markdown-editor .ProseMirror table th {
  background-color: var(--muted);
  font-weight: bold;
  text-align: left;
}

/* Placeholder */
.tiptap-markdown-editor .ProseMirror p.is-editor-empty:first-child::before {
  color: var(--muted-foreground);
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

/* Focus styles */
.tiptap-markdown-editor .ProseMirror:focus {
  outline: none;
}

/* Selection */
.tiptap-markdown-editor .ProseMirror ::selection {
  background-color: hsl(var(--primary) / 0.2);
}

/* Dark mode adjustments */
.dark .tiptap-markdown-editor .ProseMirror {
  color: var(--foreground);
}

.dark .tiptap-markdown-editor .ProseMirror code {
  background-color: var(--muted);
}

.dark .tiptap-markdown-editor .ProseMirror pre {
  background-color: var(--muted);
}

.dark .tiptap-markdown-editor .ProseMirror mark {
  background-color: hsl(var(--warning) / 0.4);
}

/* GitHub-style preview */
.github-markdown-preview {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  word-wrap: break-word;
}

.github-markdown-preview h1,
.github-markdown-preview h2 {
  border-bottom: 1px solid var(--border);
  padding-bottom: 0.3em;
}

.github-markdown-preview h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

.github-markdown-preview h2 {
  font-size: 1.5em;
  margin: 0.83em 0;
}

.github-markdown-preview h3 {
  font-size: 1.25em;
  margin: 1em 0;
}

.github-markdown-preview h4 {
  font-size: 1em;
  margin: 1.33em 0;
}

.github-markdown-preview h5 {
  font-size: 0.875em;
  margin: 1.67em 0;
}

.github-markdown-preview h6 {
  color: var(--muted-foreground);
  font-size: 0.85em;
  margin: 2.33em 0;
}

.github-markdown-preview p {
  margin: 1em 0;
}

.github-markdown-preview blockquote {
  border-left: 0.25em solid var(--border);
  color: var(--muted-foreground);
  margin: 0;
  padding: 0 1em;
}

.github-markdown-preview ul,
.github-markdown-preview ol {
  margin: 1em 0;
  padding-left: 2em;
}

.github-markdown-preview li {
  margin: 0.25em 0;
}

.github-markdown-preview code {
  background-color: var(--muted);
  border-radius: 6px;
  font-family: ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 85%;
  margin: 0;
  padding: 0.2em 0.4em;
}

.github-markdown-preview pre {
  background-color: var(--muted);
  border-radius: 6px;
  font-size: 85%;
  line-height: 1.45;
  margin: 1em 0;
  overflow: auto;
  padding: 16px;
}

.github-markdown-preview pre code {
  background: transparent;
  border: 0;
  display: inline;
  line-height: inherit;
  margin: 0;
  max-width: auto;
  overflow: visible;
  padding: 0;
  word-wrap: normal;
}

.github-markdown-preview img {
  border-style: none;
  box-sizing: content-box;
  max-width: 100%;
}

.github-markdown-preview hr {
  background-color: var(--border);
  border: 0;
  height: 0.25em;
  margin: 24px 0;
  padding: 0;
}

.github-markdown-preview table {
  border-collapse: collapse;
  border-spacing: 0;
  display: table;
  margin: 1em 0;
  overflow: auto;
  width: 100%;
  max-width: 100%;
}

.github-markdown-preview table th,
.github-markdown-preview table td {
  border: 1px solid var(--border);
  padding: 6px 13px;
}

.github-markdown-preview table th {
  background-color: var(--muted);
  font-weight: 600;
}

.github-markdown-preview table tr {
  background-color: var(--background);
  border-top: 1px solid var(--border);
}

.github-markdown-preview table tr:nth-child(2n) {
  background-color: var(--muted);
}

/* Additional table styling for better display */
.markdown-preview table {
  border-collapse: collapse;
  border-spacing: 0;
  display: table;
  margin: 1em 0;
  width: 100%;
  max-width: 100%;
}

.markdown-preview table th,
.markdown-preview table td {
  border: 1px solid var(--border);
  padding: 6px 13px;
  text-align: left;
}

.markdown-preview table th {
  background-color: var(--muted);
  font-weight: 600;
}

.markdown-preview table tr {
  background-color: var(--background);
  border-top: 1px solid var(--border);
}

.markdown-preview table tr:nth-child(2n) {
  background-color: var(--muted);
}

/* Ensure tables are visible in all contexts */
.prose table {
  border-collapse: collapse !important;
  border-spacing: 0 !important;
  display: table !important;
  margin: 1em 0 !important;
  width: 100% !important;
}

.prose table th,
.prose table td {
  border: 1px solid var(--border) !important;
  padding: 6px 13px !important;
  text-align: left !important;
}

.prose table th {
  background-color: var(--muted) !important;
  font-weight: 600 !important;
}

.prose table tr {
  background-color: var(--background) !important;
  border-top: 1px solid var(--border) !important;
}

.prose table tr:nth-child(2n) {
  background-color: var(--muted) !important;
}
