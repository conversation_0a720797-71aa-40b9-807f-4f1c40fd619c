import { supabaseAdmin } from '@/integrations/supabase/serviceClient';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

// Types
export interface CourseData {
  id?: string;
  title: string;
  slug: string;
  description: string;
  instructor: string;
}

export interface ModuleData {
  id?: string;
  course_id: string;
  title: string;
  slug: string;
  module_number: number;
  is_locked?: boolean;
}

export interface LessonData {
  id?: string;
  module_id: string;
  title: string;
  slug: string;
  duration: string;
  type: string;
  content?: string;
  requirement?: string;
}

// Helper function to check if service role key is available
const checkServiceRoleKey = () => {
  if (!import.meta.env.VITE_SUPABASE_SERVICE_ROLE_KEY) {
    console.error('Service role key is not available. Admin operations will not work.');
    toast.error('Admin operations are not available. Please contact the administrator.');
    return false;
  }
  return true;
};

// =====================
// USER ROLES
// =====================

/**
 * Assigns the teacher role to a user using the service role client.
 * This is the single source of truth for teacher role assignment.
 */
export async function assignTeacherRole(userId: string): Promise<boolean> {
  if (!checkServiceRoleKey()) return false;

  try {
    console.log(`Assigning teacher role to user: ${userId}`);

    // First, check if the user exists
    const { data: user, error: userError } = await supabaseAdmin
      .from('auth.users')
      .select('id')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error('Error checking user existence:', userError);
      toast.error(`User not found: ${userError.message}`);
      return false;
    }

    // Check if the user already has a role
    const { data: existingRole, error: checkError } = await supabaseAdmin
      .from('user_roles')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();

    if (checkError) {
      console.error('Error checking existing role:', checkError);
      toast.error(`Failed to check existing role: ${checkError.message}`);
      return false;
    }

    // Use upsert to handle both insert and update cases
    const { error: upsertError } = await supabaseAdmin
      .from('user_roles')
      .upsert({
        user_id: userId,
        role: 'teacher',
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id'
      });

    if (upsertError) {
      console.error('Error assigning teacher role:', upsertError);
      toast.error(`Failed to assign teacher role: ${upsertError.message}`);
      return false;
    }

    // Log the action for audit purposes
    logEvent('teacher_role_assigned', {
      userId,
      timestamp: new Date().toISOString()
    });

    toast.success('Teacher role assigned successfully');
    return true;
  } catch (error: any) {
    console.error('Error in assignTeacherRole:', error);
    toast.error(`An unexpected error occurred: ${error.message}`);
    return false;
  }
}

// =====================
// COURSES
// =====================

/**
 * Creates a new course
 */
export async function createCourse(courseData: CourseData): Promise<CourseData | null> {
  if (!checkServiceRoleKey()) return null;

  try {
    console.log('Creating course:', courseData);

    const { data, error } = await supabaseAdmin
      .from('courses')
      .insert([{
        title: courseData.title,
        slug: courseData.slug,
        description: courseData.description,
        instructor: courseData.instructor,
        total_modules: 0,
        completed_modules: 0
      }])
      .select();

    if (error) {
      console.error('Error creating course:', error);
      toast.error(`Failed to create course: ${error.message}`);
      return null;
    }

    toast.success('Course created successfully');
    return data[0];
  } catch (error: any) {
    console.error('Error in createCourse:', error);
    toast.error(`An unexpected error occurred: ${error.message}`);
    return null;
  }
}

/**
 * Updates an existing course
 */
export async function updateCourse(courseId: string, courseData: Partial<CourseData>): Promise<CourseData | null> {
  if (!checkServiceRoleKey()) return null;

  try {
    console.log(`Updating course ${courseId}:`, courseData);

    const { data, error } = await supabaseAdmin
      .from('courses')
      .update({
        title: courseData.title,
        slug: courseData.slug,
        description: courseData.description,
        instructor: courseData.instructor,
        updated_at: new Date().toISOString()
      })
      .eq('id', courseId)
      .select();

    if (error) {
      console.error('Error updating course:', error);
      toast.error(`Failed to update course: ${error.message}`);
      return null;
    }

    toast.success('Course updated successfully');
    return data[0];
  } catch (error: any) {
    console.error('Error in updateCourse:', error);
    toast.error(`An unexpected error occurred: ${error.message}`);
    return null;
  }
}

/**
 * Deletes a course
 */
export async function deleteCourse(courseId: string): Promise<boolean> {
  if (!checkServiceRoleKey()) return false;

  try {
    console.log(`Deleting course ${courseId}`);

    const { error } = await supabaseAdmin
      .from('courses')
      .delete()
      .eq('id', courseId);

    if (error) {
      console.error('Error deleting course:', error);
      toast.error(`Failed to delete course: ${error.message}`);
      return false;
    }

    toast.success('Course deleted successfully');
    return true;
  } catch (error: any) {
    console.error('Error in deleteCourse:', error);
    toast.error(`An unexpected error occurred: ${error.message}`);
    return false;
  }
}

// =====================
// MODULES
// =====================

/**
 * Creates a new module
 */
export async function createModule(moduleData: ModuleData): Promise<ModuleData | null> {
  if (!checkServiceRoleKey()) return null;

  try {
    console.log('Creating module:', moduleData);

    const { data, error } = await supabaseAdmin
      .from('modules')
      .insert([{
        course_id: moduleData.course_id,
        title: moduleData.title,
        slug: moduleData.slug,
        module_number: moduleData.module_number,
        is_locked: moduleData.is_locked || false,
        is_completed: false
        // Removed is_published field as it doesn't exist in the schema
      }])
      .select();

    if (error) {
      console.error('Error creating module:', error);
      toast.error(`Failed to create module: ${error.message}`);
      return null;
    }

    toast.success('Module created successfully');
    return data[0];
  } catch (error: any) {
    console.error('Error in createModule:', error);
    toast.error(`An unexpected error occurred: ${error.message}`);
    return null;
  }
}

/**
 * Updates an existing module
 */
export async function updateModule(moduleId: string, moduleData: Partial<ModuleData>): Promise<ModuleData | null> {
  if (!checkServiceRoleKey()) return null;

  try {
    console.log(`Updating module ${moduleId}:`, moduleData);

    const { data, error } = await supabaseAdmin
      .from('modules')
      .update({
        title: moduleData.title,
        slug: moduleData.slug,
        module_number: moduleData.module_number,
        is_locked: moduleData.is_locked,
        updated_at: new Date().toISOString()
      })
      .eq('id', moduleId)
      .select();

    if (error) {
      console.error('Error updating module:', error);
      toast.error(`Failed to update module: ${error.message}`);
      return null;
    }

    toast.success('Module updated successfully');
    return data[0];
  } catch (error: any) {
    console.error('Error in updateModule:', error);
    toast.error(`An unexpected error occurred: ${error.message}`);
    return null;
  }
}

/**
 * Deletes a module
 */
export async function deleteModule(moduleId: string): Promise<boolean> {
  if (!checkServiceRoleKey()) return false;

  try {
    console.log(`Deleting module ${moduleId}`);

    // First, get the course_id for this module
    const { data: moduleData, error: fetchError } = await supabaseAdmin
      .from('modules')
      .select('course_id')
      .eq('id', moduleId)
      .single();

    if (fetchError) {
      console.error('Error fetching module data:', fetchError);
      toast.error(`Failed to delete module: ${fetchError.message}`);
      return false;
    }

    const courseId = moduleData.course_id;

    // Delete the module
    const { error } = await supabaseAdmin
      .from('modules')
      .delete()
      .eq('id', moduleId);

    if (error) {
      console.error('Error deleting module:', error);
      toast.error(`Failed to delete module: ${error.message}`);
      return false;
    }

    // Update the course's total_modules count
    try {
      console.log('Updating course total_modules count after deletion');

      // Get the current count of modules for this course
      const { data: moduleCount, error: countError } = await supabaseAdmin
        .from('modules')
        .select('id')
        .eq('course_id', courseId);

      if (countError) {
        console.error('Error counting modules:', countError);
      } else {
        // Update the course with the new count
        const totalModules = moduleCount.length;
        console.log(`Updating course ${courseId} with ${totalModules} total modules`);

        const { error: updateError } = await supabaseAdmin
          .from('courses')
          .update({ total_modules: totalModules })
          .eq('id', courseId);

        if (updateError) {
          console.error('Error updating course total_modules:', updateError);
        } else {
          console.log('Course total_modules updated successfully');
        }
      }
    } catch (updateError) {
      console.error('Error updating course total_modules:', updateError);
      // Continue anyway as this is not critical
    }

    toast.success('Module deleted successfully');
    return true;
  } catch (error: any) {
    console.error('Error in deleteModule:', error);
    toast.error(`An unexpected error occurred: ${error.message}`);
    return false;
  }
}

// =====================
// LESSONS
// =====================

/**
 * Creates a new lesson
 */
export async function createLesson(lessonData: LessonData): Promise<LessonData | null> {
  if (!checkServiceRoleKey()) return null;

  try {
    console.log('Creating lesson:', lessonData);

    const { data, error } = await supabaseAdmin
      .from('lessons')
      .insert([{
        module_id: lessonData.module_id,
        title: lessonData.title,
        slug: lessonData.slug,
        duration: lessonData.duration,
        type: lessonData.type,
        content: lessonData.content || null,
        requirement: lessonData.requirement || null,
        completed: false
      }])
      .select();

    if (error) {
      console.error('Error creating lesson:', error);
      toast.error(`Failed to create lesson: ${error.message}`);
      return null;
    }

    toast.success('Lesson created successfully');
    return data[0];
  } catch (error: any) {
    console.error('Error in createLesson:', error);
    toast.error(`An unexpected error occurred: ${error.message}`);
    return null;
  }
}

/**
 * Updates an existing lesson
 */
export async function updateLesson(lessonId: string, lessonData: Partial<LessonData>): Promise<LessonData | null> {
  if (!checkServiceRoleKey()) return null;

  try {
    console.log(`Updating lesson ${lessonId}:`, lessonData);

    const { data, error } = await supabaseAdmin
      .from('lessons')
      .update({
        title: lessonData.title,
        slug: lessonData.slug,
        duration: lessonData.duration,
        type: lessonData.type,
        content: lessonData.content || null,
        requirement: lessonData.requirement || null,
        updated_at: new Date().toISOString()
      })
      .eq('id', lessonId)
      .select();

    if (error) {
      console.error('Error updating lesson:', error);
      toast.error(`Failed to update lesson: ${error.message}`);
      return null;
    }

    toast.success('Lesson updated successfully');
    return data[0];
  } catch (error: any) {
    console.error('Error in updateLesson:', error);
    toast.error(`An unexpected error occurred: ${error.message}`);
    return null;
  }
}

/**
 * Deletes a lesson
 */
export async function deleteLesson(lessonId: string): Promise<boolean> {
  if (!checkServiceRoleKey()) return false;

  try {
    console.log(`Deleting lesson ${lessonId}`);

    const { error } = await supabaseAdmin
      .from('lessons')
      .delete()
      .eq('id', lessonId);

    if (error) {
      console.error('Error deleting lesson:', error);
      toast.error(`Failed to delete lesson: ${error.message}`);
      return false;
    }

    toast.success('Lesson deleted successfully');
    return true;
  } catch (error: any) {
    console.error('Error in deleteLesson:', error);
    toast.error(`An unexpected error occurred: ${error.message}`);
    return false;
  }
}

// =====================
// FALLBACK FUNCTIONS
// =====================

/**
 * Fallback function to create a course using the regular client
 * This is used when the service role key is not available
 */
export async function createCourseFallback(courseData: CourseData): Promise<CourseData | null> {
  try {
    console.log('Creating course using fallback method:', courseData);

    const { data, error } = await supabase
      .from('courses')
      .insert([{
        title: courseData.title,
        slug: courseData.slug,
        description: courseData.description,
        instructor: courseData.instructor,
        total_modules: 0,
        completed_modules: 0
      }])
      .select();

    if (error) {
      console.error('Error creating course (fallback):', error);
      toast.error(`Failed to create course: ${error.message}`);
      return null;
    }

    toast.success('Course created successfully');
    return data[0];
  } catch (error: any) {
    console.error('Error in createCourseFallback:', error);
    toast.error(`An unexpected error occurred: ${error.message}`);
    return null;
  }
}

/**
 * Fallback function to create a module using the regular client
 * This is used when the service role key is not available
 */
export async function createModuleFallback(moduleData: ModuleData): Promise<ModuleData | null> {
  try {
    console.log('Creating module using fallback method:', moduleData);

    const { data, error } = await supabase
      .from('modules')
      .insert([{
        course_id: moduleData.course_id,
        title: moduleData.title,
        slug: moduleData.slug,
        module_number: moduleData.module_number,
        is_locked: moduleData.is_locked || false,
        is_completed: false
        // Removed is_published field as it doesn't exist in the schema
      }])
      .select();

    if (error) {
      console.error('Error creating module (fallback):', error);
      toast.error(`Failed to create module: ${error.message}`);
      return null;
    }

    toast.success('Module created successfully');
    return data[0];
  } catch (error: any) {
    console.error('Error in createModuleFallback:', error);
    toast.error(`An unexpected error occurred: ${error.message}`);
    return null;
  }
}

/**
 * Fallback function to create a lesson using the regular client
 * This is used when the service role key is not available
 */
export async function createLessonFallback(lessonData: LessonData): Promise<LessonData | null> {
  try {
    console.log('Creating lesson using fallback method:', lessonData);

    const { data, error } = await supabase
      .from('lessons')
      .insert([{
        module_id: lessonData.module_id,
        title: lessonData.title,
        slug: lessonData.slug,
        duration: lessonData.duration,
        type: lessonData.type,
        content: lessonData.content || null,
        requirement: lessonData.requirement || null,
        completed: false
      }])
      .select();

    if (error) {
      console.error('Error creating lesson (fallback):', error);
      toast.error(`Failed to create lesson: ${error.message}`);
      return null;
    }

    toast.success('Lesson created successfully');
    return data[0];
  } catch (error: any) {
    console.error('Error in createLessonFallback:', error);
    toast.error(`An unexpected error occurred: ${error.message}`);
    return null;
  }
}
