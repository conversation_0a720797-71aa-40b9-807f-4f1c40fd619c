import React, { useState, useEffect } from 'react';
import { z } from 'zod';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Loader2, Plus, Trash2, GripVertical, Check, X } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useQueryClient } from '@tanstack/react-query';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Card, CardContent } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { cn } from '@/lib/utils';

// Define the question option schema
const questionOptionSchema = z.object({
  text: z.string().min(1, { message: 'Option text is required' }),
  isCorrect: z.boolean().default(false),
});

// Define the question schema
const questionSchema = z.object({
  text: z.string().min(3, { message: 'Question text is required' }),
  type: z.enum(['multiple_choice', 'single_choice', 'true_false', 'rating_scale']),
  options: z.array(questionOptionSchema).min(2, { message: 'At least 2 options are required' }),
  explanation: z.string().optional(),
  minRating: z.number().int().min(1).optional(),
  maxRating: z.number().int().max(10).optional(),
  minLabel: z.string().optional(),
  maxLabel: z.string().optional(),
});

// Define the form schema
const quizFormSchema = z.object({
  title: z.string().min(3, { message: 'Title must be at least 3 characters' }),
  description: z.string().min(10, { message: 'Description must be at least 10 characters' }),
  courseId: z.string({ required_error: 'Course is required' }),
  lessonId: z.string().optional(),
  quizType: z.enum(['standard', 'questionnaire']).default('standard'),
  passingScore: z.number().int().min(0).max(100).default(70),
  timeLimit: z.number().int().min(0).default(0),
  randomizeQuestions: z.boolean().default(false),
  showExplanations: z.boolean().default(true),
  scaleDescription: z.string().optional(),
  sectionTitle: z.string().optional(),
  questions: z.array(questionSchema).min(1, { message: 'At least 1 question is required' }),
});

type QuizFormValues = z.infer<typeof quizFormSchema>;

interface QuizFormProps {
  initialData?: QuizFormValues & { id?: string };
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function QuizForm({ initialData, onSuccess, onCancel }: QuizFormProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [courses, setCourses] = useState<any[]>([]);
  const [lessons, setLessons] = useState<any[]>([]);
  const [selectedCourseId, setSelectedCourseId] = useState<string | undefined>(initialData?.courseId);

  // Initialize form with default values
  const form = useForm<QuizFormValues>({
    resolver: zodResolver(quizFormSchema),
    defaultValues: initialData || {
      title: '',
      description: '',
      courseId: '',
      lessonId: '',
      quizType: 'standard',
      passingScore: 70,
      timeLimit: 0,
      randomizeQuestions: false,
      showExplanations: true,
      scaleDescription: '',
      sectionTitle: '',
      questions: [
        {
          text: '',
          type: 'multiple_choice',
          options: [
            { text: 'Option 1', isCorrect: false },
            { text: 'Option 2', isCorrect: false },
          ],
          explanation: '',
        },
      ],
    },
  });

  // Use field array for questions
  const { fields: questionFields, append: appendQuestion, remove: removeQuestion } = useFieldArray({
    control: form.control,
    name: 'questions',
  });

  // Fetch courses on component mount
  useEffect(() => {
    const fetchCourses = async () => {
      const { data, error } = await supabase
        .from('courses')
        .select('id, title')
        .order('title');

      if (error) {
        toast({
          title: 'Error',
          description: 'Failed to load courses',
          variant: 'destructive',
        });
        return;
      }

      setCourses(data || []);
    };

    fetchCourses();
  }, [toast]);

  // Fetch lessons when course is selected
  useEffect(() => {
    if (!selectedCourseId) {
      setLessons([]);
      return;
    }

    const fetchLessons = async () => {
      const { data, error } = await supabase
        .from('lessons')
        .select('id, title')
        .eq('course_id', selectedCourseId)
        .order('order');

      if (error) {
        toast({
          title: 'Error',
          description: 'Failed to load lessons',
          variant: 'destructive',
        });
        return;
      }

      setLessons(data || []);
    };

    fetchLessons();
  }, [selectedCourseId, toast]);

  // Watch for quiz type changes
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === 'quizType') {
        const quizType = value.quizType as 'standard' | 'questionnaire';

        // If changing to questionnaire and there are questions, ask if user wants to convert them
        if (quizType === 'questionnaire' && questionFields.length > 0) {
          const hasRatingScaleQuestions = questionFields.some(
            (_, i) => form.getValues(`questions.${i}.type`) === 'rating_scale'
          );

          if (!hasRatingScaleQuestions) {
            const confirm = window.confirm(
              'Would you like to convert existing questions to rating scale questions for the questionnaire? ' +
              'This will replace all current questions. Cancel to keep your current questions.'
            );

            if (confirm) {
              // Remove all existing questions
              while (questionFields.length > 0) {
                removeQuestion(0);
              }

              // Add a default rating scale question
              appendQuestion({
                text: '',
                type: 'rating_scale',
                options: [
                  { text: '1', isCorrect: false },
                  { text: '2', isCorrect: false },
                  { text: '3', isCorrect: false },
                  { text: '4', isCorrect: false },
                  { text: '5', isCorrect: false },
                ],
                explanation: '',
                minRating: 1,
                maxRating: 5,
                minLabel: 'Poor',
                maxLabel: 'Excellent',
              });
            }
          }
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [form, questionFields, appendQuestion, removeQuestion]);

  // Handle course selection change
  const handleCourseChange = (courseId: string) => {
    setSelectedCourseId(courseId);
    form.setValue('courseId', courseId);
    form.setValue('lessonId', ''); // Reset lesson when course changes
  };

  // Add a new question
  const handleAddQuestion = () => {
    const quizType = form.getValues('quizType');

    // For questionnaires, default to rating scale questions
    if (quizType === 'questionnaire') {
      appendQuestion({
        text: '',
        type: 'rating_scale',
        options: [
          { text: '1', isCorrect: false },
          { text: '2', isCorrect: false },
          { text: '3', isCorrect: false },
          { text: '4', isCorrect: false },
          { text: '5', isCorrect: false },
        ],
        explanation: '',
        minRating: 1,
        maxRating: 5,
        minLabel: 'Poor',
        maxLabel: 'Excellent',
      });
    } else {
      // For standard quizzes, default to multiple choice
      appendQuestion({
        text: '',
        type: 'multiple_choice',
        options: [
          { text: 'Option 1', isCorrect: false },
          { text: 'Option 2', isCorrect: false },
        ],
        explanation: '',
      });
    }
  };

  // Add a new option to a question
  const handleAddOption = (questionIndex: number) => {
    const currentOptions = form.getValues(`questions.${questionIndex}.options`);
    const newOptionNumber = currentOptions.length + 1;
    form.setValue(`questions.${questionIndex}.options`, [
      ...currentOptions,
      { text: `Option ${newOptionNumber}`, isCorrect: false },
    ]);
  };

  // Remove an option from a question
  const handleRemoveOption = (questionIndex: number, optionIndex: number) => {
    const currentOptions = form.getValues(`questions.${questionIndex}.options`);
    if (currentOptions.length <= 2) {
      toast({
        title: 'Error',
        description: 'A question must have at least 2 options',
        variant: 'destructive',
      });
      return;
    }

    const newOptions = [...currentOptions];
    newOptions.splice(optionIndex, 1);
    form.setValue(`questions.${questionIndex}.options`, newOptions);
  };

  // Handle form submission
  const onSubmit = async (data: QuizFormValues) => {
    setIsSubmitting(true);

    try {
      // For standard quizzes, validate that at least one option is marked as correct for each question
      if (data.quizType === 'standard') {
        for (let i = 0; i < data.questions.length; i++) {
          const question = data.questions[i];
          const hasCorrectOption = question.options.some(option => option.isCorrect);

          if (!hasCorrectOption) {
            throw new Error(`Question ${i + 1} must have at least one correct answer`);
          }
        }
      }

      // Prepare quiz data
      const quizData = {
        title: data.title,
        description: data.description,
        course_id: data.courseId,
        lesson_id: data.lessonId || null,
        quiz_type: data.quizType,
        passing_score: data.passingScore,
        time_limit: data.timeLimit,
        randomize_questions: data.randomizeQuestions,
        show_explanations: data.showExplanations,
        scale_description: data.scaleDescription || null,
        section_title: data.sectionTitle || null,
        questions: data.questions,
      };

      // Update or create quiz
      if (initialData?.id) {
        // Update existing quiz
        const { error } = await supabase
          .from('quizzes')
          .update(quizData)
          .eq('id', initialData.id);

        if (error) throw error;

        toast({
          title: 'Quiz updated',
          description: 'The quiz has been successfully updated.',
        });
      } else {
        // Create new quiz
        const { error } = await supabase
          .from('quizzes')
          .insert([quizData]);

        if (error) throw error;

        toast({
          title: 'Quiz created',
          description: 'The quiz has been successfully created.',
        });
      }

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['quizzes'] });

      // Call success callback
      if (onSuccess) onSuccess();

    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'An error occurred while saving the quiz.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
      <div className="space-y-6">
        {/* Quiz Basic Information */}
        <div className="grid gap-6 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="title">
              Quiz Title <span className="text-destructive">*</span>
            </Label>
            <Input
              id="title"
              {...form.register('title')}
              placeholder="Enter quiz title"
            />
            {form.formState.errors.title && (
              <p className="text-sm text-destructive">{form.formState.errors.title.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="courseId">
              Course <span className="text-destructive">*</span>
            </Label>
            <Select
              value={form.getValues('courseId')}
              onValueChange={handleCourseChange}
            >
              <SelectTrigger id="courseId">
                <SelectValue placeholder="Select a course" />
              </SelectTrigger>
              <SelectContent>
                {courses.map((course) => (
                  <SelectItem key={course.id} value={course.id}>
                    {course.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {form.formState.errors.courseId && (
              <p className="text-sm text-destructive">{form.formState.errors.courseId.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="quizType">
              Quiz Type <span className="text-destructive">*</span>
            </Label>
            <Select
              value={form.getValues('quizType')}
              onValueChange={(value: 'standard' | 'questionnaire') => form.setValue('quizType', value)}
            >
              <SelectTrigger id="quizType">
                <SelectValue placeholder="Select quiz type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="standard">Standard Quiz</SelectItem>
                <SelectItem value="questionnaire">Questionnaire</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              {form.getValues('quizType') === 'standard'
                ? 'Standard quizzes have correct answers and scoring.'
                : 'Questionnaires collect feedback without correct/incorrect answers.'}
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="lessonId">Lesson (Optional)</Label>
            <Select
              value={form.getValues('lessonId') || "none"}
              onValueChange={(value) => form.setValue('lessonId', value === "none" ? "" : value)}
              disabled={!selectedCourseId || lessons.length === 0}
            >
              <SelectTrigger id="lessonId">
                <SelectValue placeholder="Select a lesson" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None (Course Quiz)</SelectItem>
                {lessons.map((lesson) => (
                  <SelectItem key={lesson.id} value={lesson.id}>
                    {lesson.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {form.getValues('quizType') === 'standard' && (
            <div className="space-y-2">
              <Label htmlFor="passingScore">Passing Score (%)</Label>
              <Input
                id="passingScore"
                type="number"
                min="0"
                max="100"
                {...form.register('passingScore', { valueAsNumber: true })}
              />
            </div>
          )}

          {form.getValues('quizType') === 'questionnaire' && (
            <div className="space-y-2">
              <Label htmlFor="sectionTitle">Section Title (Optional)</Label>
              <Input
                id="sectionTitle"
                {...form.register('sectionTitle')}
                placeholder="Enter section title"
              />
            </div>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">
            Description <span className="text-destructive">*</span>
          </Label>
          <Textarea
            id="description"
            {...form.register('description')}
            placeholder="Enter quiz description"
            rows={3}
          />
          {form.formState.errors.description && (
            <p className="text-sm text-destructive">{form.formState.errors.description.message}</p>
          )}
        </div>

        {form.getValues('quizType') === 'questionnaire' && (
          <div className="space-y-2">
            <Label htmlFor="scaleDescription">Scale Description (Optional)</Label>
            <Textarea
              id="scaleDescription"
              {...form.register('scaleDescription')}
              placeholder="Enter description of the rating scale for questionnaires"
              rows={2}
            />
            <p className="text-xs text-muted-foreground">
              Explain what the rating scale means (e.g., "1 = Poor, 5 = Excellent")
            </p>
          </div>
        )}

        <div className="grid gap-6 md:grid-cols-3">
          {form.getValues('quizType') === 'standard' && (
            <div className="space-y-2">
              <Label htmlFor="timeLimit">Time Limit (minutes, 0 for no limit)</Label>
              <Input
                id="timeLimit"
                type="number"
                min="0"
                {...form.register('timeLimit', { valueAsNumber: true })}
              />
            </div>
          )}

          <div className="flex items-center space-x-2">
            <Switch
              id="randomizeQuestions"
              checked={form.getValues('randomizeQuestions')}
              onCheckedChange={(checked) => form.setValue('randomizeQuestions', checked)}
            />
            <Label htmlFor="randomizeQuestions">Randomize Questions</Label>
          </div>

          {form.getValues('quizType') === 'standard' && (
            <div className="flex items-center space-x-2">
              <Switch
                id="showExplanations"
                checked={form.getValues('showExplanations')}
                onCheckedChange={(checked) => form.setValue('showExplanations', checked)}
              />
              <Label htmlFor="showExplanations">Show Explanations</Label>
            </div>
          )}
        </div>

        {/* Questions Section */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Questions</h3>
            <Button
              type="button"
              onClick={handleAddQuestion}
              variant="outline"
              size="sm"
            >
              <Plus className="h-4 w-4 mr-1" /> Add Question
            </Button>
          </div>

          {form.formState.errors.questions?.message && (
            <p className="text-sm text-destructive">{form.formState.errors.questions.message}</p>
          )}

          {questionFields.map((field, questionIndex) => (
            <Card key={field.id} className="relative">
              <CardContent className="pt-6">
                <div className="absolute top-2 right-2 flex gap-2">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeQuestion(questionIndex)}
                    disabled={questionFields.length <= 1}
                    className="h-8 w-8 p-0"
                  >
                    <Trash2 className="h-4 w-4 text-destructive" />
                  </Button>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">Question {questionIndex + 1}</span>
                    <Select
                      value={form.getValues(`questions.${questionIndex}.type`)}
                      onValueChange={(value: 'multiple_choice' | 'single_choice' | 'true_false' | 'rating_scale') => {
                        form.setValue(`questions.${questionIndex}.type`, value);

                        // If true/false, set predefined options
                        if (value === 'true_false') {
                          form.setValue(`questions.${questionIndex}.options`, [
                            { text: 'True', isCorrect: false },
                            { text: 'False', isCorrect: false },
                          ]);
                        }

                        // If rating scale, set default values
                        if (value === 'rating_scale') {
                          form.setValue(`questions.${questionIndex}.minRating`, 1);
                          form.setValue(`questions.${questionIndex}.maxRating`, 5);
                          form.setValue(`questions.${questionIndex}.minLabel`, 'Poor');
                          form.setValue(`questions.${questionIndex}.maxLabel`, 'Excellent');
                          form.setValue(`questions.${questionIndex}.options`, [
                            { text: '1', isCorrect: false },
                            { text: '2', isCorrect: false },
                            { text: '3', isCorrect: false },
                            { text: '4', isCorrect: false },
                            { text: '5', isCorrect: false },
                          ]);
                        }
                      }}
                    >
                      <SelectTrigger className="w-40">
                        <SelectValue placeholder="Question type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="multiple_choice">Multiple Choice</SelectItem>
                        <SelectItem value="single_choice">Single Choice</SelectItem>
                        <SelectItem value="true_false">True/False</SelectItem>
                        {form.getValues('quizType') === 'questionnaire' && (
                          <SelectItem value="rating_scale">Rating Scale</SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Input
                      placeholder="Enter question text"
                      {...form.register(`questions.${questionIndex}.text`)}
                    />
                    {form.formState.errors.questions?.[questionIndex]?.text && (
                      <p className="text-sm text-destructive">
                        {form.formState.errors.questions[questionIndex]?.text?.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <Label>Answer Options</Label>
                      {form.getValues(`questions.${questionIndex}.type`) !== 'true_false' && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => handleAddOption(questionIndex)}
                        >
                          <Plus className="h-3 w-3 mr-1" /> Add Option
                        </Button>
                      )}
                    </div>

                    {form.getValues(`questions.${questionIndex}.type`) === 'multiple_choice' && (
                      <div className="space-y-2">
                        {form.getValues(`questions.${questionIndex}.options`).map((_, optionIndex) => (
                          <div key={optionIndex} className="flex items-center gap-2">
                            <Checkbox
                              id={`q${questionIndex}-opt${optionIndex}`}
                              checked={form.getValues(`questions.${questionIndex}.options.${optionIndex}.isCorrect`)}
                              onCheckedChange={(checked) => {
                                form.setValue(
                                  `questions.${questionIndex}.options.${optionIndex}.isCorrect`,
                                  checked === true
                                );
                              }}
                            />
                            <Input
                              placeholder={`Option ${optionIndex + 1}`}
                              className="flex-1"
                              {...form.register(`questions.${questionIndex}.options.${optionIndex}.text`)}
                            />
                            {form.getValues(`questions.${questionIndex}.type`) !== 'true_false' && (
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => handleRemoveOption(questionIndex, optionIndex)}
                                disabled={form.getValues(`questions.${questionIndex}.options`).length <= 2}
                                className="h-8 w-8 p-0"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        ))}
                      </div>
                    )}

                    {form.getValues(`questions.${questionIndex}.type`) === 'single_choice' && (
                      <RadioGroup
                        value={form.getValues(`questions.${questionIndex}.options`).findIndex(opt => opt.isCorrect) >= 0
                          ? form.getValues(`questions.${questionIndex}.options`).findIndex(opt => opt.isCorrect).toString()
                          : "0"} // Default to first option if none selected
                        onValueChange={(value) => {
                          const options = [...form.getValues(`questions.${questionIndex}.options`)];
                          options.forEach((_, i) => {
                            form.setValue(
                              `questions.${questionIndex}.options.${i}.isCorrect`,
                              i.toString() === value
                            );
                          });
                        }}
                        className="space-y-2"
                      >
                        {form.getValues(`questions.${questionIndex}.options`).map((_, optionIndex) => (
                          <div key={optionIndex} className="flex items-center gap-2">
                            <RadioGroupItem
                              value={optionIndex.toString()}
                              id={`q${questionIndex}-opt${optionIndex}-radio`}
                            />
                            <Input
                              placeholder={`Option ${optionIndex + 1}`}
                              className="flex-1"
                              {...form.register(`questions.${questionIndex}.options.${optionIndex}.text`)}
                            />
                            {form.getValues(`questions.${questionIndex}.type`) !== 'true_false' && (
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => handleRemoveOption(questionIndex, optionIndex)}
                                disabled={form.getValues(`questions.${questionIndex}.options`).length <= 2}
                                className="h-8 w-8 p-0"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        ))}
                      </RadioGroup>
                    )}

                    {form.getValues(`questions.${questionIndex}.type`) === 'true_false' && (
                      <RadioGroup
                        value={form.getValues(`questions.${questionIndex}.options`)[0].isCorrect ? "0" : "1"}
                        onValueChange={(value) => {
                          form.setValue(`questions.${questionIndex}.options.0.isCorrect`, value === "0");
                          form.setValue(`questions.${questionIndex}.options.1.isCorrect`, value === "1");
                        }}
                        className="space-y-2"
                      >
                        <div className="flex items-center gap-2">
                          <RadioGroupItem value="0" id={`q${questionIndex}-true`} />
                          <Label htmlFor={`q${questionIndex}-true`}>True</Label>
                        </div>
                        <div className="flex items-center gap-2">
                          <RadioGroupItem value="1" id={`q${questionIndex}-false`} />
                          <Label htmlFor={`q${questionIndex}-false`}>False</Label>
                        </div>
                      </RadioGroup>
                    )}

                    {form.getValues(`questions.${questionIndex}.type`) === 'rating_scale' && (
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor={`minRating-${questionIndex}`}>Minimum Rating</Label>
                            <Input
                              id={`minRating-${questionIndex}`}
                              type="number"
                              min="1"
                              max="5"
                              value={form.getValues(`questions.${questionIndex}.minRating`) || 1}
                              onChange={(e) => form.setValue(`questions.${questionIndex}.minRating`, parseInt(e.target.value))}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor={`maxRating-${questionIndex}`}>Maximum Rating</Label>
                            <Input
                              id={`maxRating-${questionIndex}`}
                              type="number"
                              min="2"
                              max="10"
                              value={form.getValues(`questions.${questionIndex}.maxRating`) || 5}
                              onChange={(e) => form.setValue(`questions.${questionIndex}.maxRating`, parseInt(e.target.value))}
                            />
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor={`minLabel-${questionIndex}`}>Minimum Label</Label>
                            <Input
                              id={`minLabel-${questionIndex}`}
                              placeholder="e.g., Poor"
                              value={form.getValues(`questions.${questionIndex}.minLabel`) || ''}
                              onChange={(e) => form.setValue(`questions.${questionIndex}.minLabel`, e.target.value)}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor={`maxLabel-${questionIndex}`}>Maximum Label</Label>
                            <Input
                              id={`maxLabel-${questionIndex}`}
                              placeholder="e.g., Excellent"
                              value={form.getValues(`questions.${questionIndex}.maxLabel`) || ''}
                              onChange={(e) => form.setValue(`questions.${questionIndex}.maxLabel`, e.target.value)}
                            />
                          </div>
                        </div>
                        <div className="flex items-center justify-center space-x-2 pt-2">
                          {Array.from({ length: (form.getValues(`questions.${questionIndex}.maxRating`) || 5) - (form.getValues(`questions.${questionIndex}.minRating`) || 1) + 1 }).map((_, i) => {
                            const rating = (form.getValues(`questions.${questionIndex}.minRating`) || 1) + i;
                            return (
                              <div key={i} className="flex flex-col items-center">
                                <div className="text-sm font-medium">{rating}</div>
                                <div className="h-6 w-6 rounded-full border border-primary flex items-center justify-center text-xs">
                                  {rating}
                                </div>
                                {i === 0 && (
                                  <div className="text-xs mt-1">{form.getValues(`questions.${questionIndex}.minLabel`) || 'Poor'}</div>
                                )}
                                {i === (form.getValues(`questions.${questionIndex}.maxRating`) || 5) - (form.getValues(`questions.${questionIndex}.minRating`) || 1) && (
                                  <div className="text-xs mt-1">{form.getValues(`questions.${questionIndex}.maxLabel`) || 'Excellent'}</div>
                                )}
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    )}

                    {form.formState.errors.questions?.[questionIndex]?.options && (
                      <p className="text-sm text-destructive">
                        {form.formState.errors.questions[questionIndex]?.options?.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`explanation-${questionIndex}`}>Explanation (Optional)</Label>
                    <Textarea
                      id={`explanation-${questionIndex}`}
                      placeholder="Explain the correct answer"
                      {...form.register(`questions.${questionIndex}.explanation`)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end gap-2 pt-4 border-t border-border">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            initialData?.id ? 'Update Quiz' : 'Create Quiz'
          )}
        </Button>
      </div>
    </form>
  );
}
