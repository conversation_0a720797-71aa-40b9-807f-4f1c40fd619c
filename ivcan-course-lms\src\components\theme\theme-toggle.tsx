import { Moon, Sun } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useTheme } from "./enhanced-theme-provider"
import { motion } from "framer-motion"

export function ThemeToggle() {
  const { theme, setTheme } = useTheme()

  return (
    <motion.div
      whileTap={{ scale: 0.95 }}
      className="relative"
    >
      <Button
        variant="ghost"
        size="icon"
        onClick={() => setTheme(theme === "light" ? "dark" : "light")}
        className={`rounded-full ${theme === 'light' ? 'bg-primary/10 shadow-sm hover:bg-primary/20' : 'bg-white/10 backdrop-blur-sm hover:bg-white/20'} border-none shadow-sm`}
      >
        <motion.div
          initial={{ rotate: 0 }}
          animate={{ rotate: theme === "dark" ? 180 : 0 }}
          transition={{ duration: 0.4, type: "spring" }}
          className="relative w-5 h-5"
        >
          <Sun className={`absolute inset-0 h-5 w-5 rotate-0 scale-100 ${theme === 'light' ? 'text-gray-800' : 'text-white'} transition-all dark:-rotate-90 dark:scale-0`} />
          <Moon className={`absolute inset-0 h-5 w-5 rotate-90 scale-0 ${theme === 'light' ? 'text-gray-800' : 'text-white'} transition-all dark:rotate-0 dark:scale-100`} />
        </motion.div>
        <span className="sr-only">Toggle theme</span>
      </Button>
    </motion.div>
  )
}

export function ThemeToggleMinimal() {
  const { theme, setTheme } = useTheme()

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={() => setTheme(theme === "light" ? "dark" : "light")}
      className={`${theme === 'light' ? 'text-gray-800 hover:text-gray-900 hover:bg-primary/10' : 'text-white/90 hover:text-white hover:bg-white/10'}`}
    >
      <Sun className={`h-5 w-5 rotate-0 scale-100 ${theme === 'light' ? 'text-gray-800' : 'text-white'} transition-all dark:-rotate-90 dark:scale-0`} />
      <Moon className={`absolute h-5 w-5 rotate-90 scale-0 ${theme === 'light' ? 'text-gray-800' : 'text-white'} transition-all dark:rotate-0 dark:scale-100`} />
      <span className="sr-only">Toggle theme</span>
    </Button>
  )
}
