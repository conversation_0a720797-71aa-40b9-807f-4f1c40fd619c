-- Fix completed_at column in user_course_enrollment table
-- This migration ensures the completed_at column exists and is properly recognized by Supabase

-- Make sure the completed_at column exists
ALTER TABLE IF EXISTS public.user_course_enrollment 
ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP WITH TIME ZONE;

-- Update existing completed enrollments that don't have a completed_at date
UPDATE public.user_course_enrollment
SET completed_at = updated_at
WHERE status = 'completed' AND completed_at IS NULL;

-- Update the schema cache by commenting on the table and column
COMMENT ON TABLE public.user_course_enrollment IS 'Table to track user enrollments in courses';
COMMENT ON COLUMN public.user_course_enrollment.completed_at IS 'Timestamp when the course was completed';

-- Ensure the RLS policies are properly set
ALTER TABLE IF EXISTS public.user_course_enrollment ENABLE ROW LEVEL SECURITY;

-- Drop all existing policies to avoid conflicts
DROP POLICY IF EXISTS "Users can view their own course enrollments" ON public.user_course_enrollment;
DROP POLICY IF EXISTS "Users can insert their own course enrollments" ON public.user_course_enrollment;
DROP POLICY IF EXISTS "Users can update their own course enrollments" ON public.user_course_enrollment;
DROP POLICY IF EXISTS "Anyone can view course enrollments" ON public.user_course_enrollment;
DROP POLICY IF EXISTS "Anyone can insert course enrollments" ON public.user_course_enrollment;
DROP POLICY IF EXISTS "Anyone can update course enrollments" ON public.user_course_enrollment;
DROP POLICY IF EXISTS "Anyone can delete course enrollments" ON public.user_course_enrollment;
DROP POLICY IF EXISTS "Anyone can execute complete_course function" ON public.user_course_enrollment;
DROP POLICY IF EXISTS "Teachers can view all enrollments" ON public.user_course_enrollment;

-- Create new policies with proper permissions
CREATE POLICY "Users can view their own enrollments" 
ON public.user_course_enrollment FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Teachers can view all enrollments" 
ON public.user_course_enrollment FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Users can insert their own enrollments" 
ON public.user_course_enrollment FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own enrollments" 
ON public.user_course_enrollment FOR UPDATE 
USING (auth.uid() = user_id);

-- Grant execute permission on the complete_course function
GRANT EXECUTE ON FUNCTION public.complete_course TO authenticated;
GRANT EXECUTE ON FUNCTION public.complete_course TO anon;
GRANT EXECUTE ON FUNCTION public.complete_course TO service_role;
