import React from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';

interface TeacherControlsProps {
  onAdd: () => void;
  onAddLesson?: () => void;
  disabled?: boolean;
}

const TeacherControls: React.FC<TeacherControlsProps> = ({ 
  onAdd, 
  onAddLesson, 
  disabled 
}) => {
  return (
    <div className="flex justify-end mb-3">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="text-xs flex items-center hover:bg-primary/5 hover:border-primary/30 transition-all duration-200"
            disabled={disabled}
          >
            <Plus className="h-3 w-3 mr-1" /> Add Content
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="min-w-[160px]">
          <DropdownMenuItem onClick={onAdd} className="cursor-pointer gap-2">
            <Plus className="h-3.5 w-3.5" />
            Add Assignment
          </DropdownMenuItem>
          {onAddLesson && (
            <DropdownMenuItem onClick={onAddLesson} className="cursor-pointer gap-2">
              <Plus className="h-3.5 w-3.5" />
              Add Lesson
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default TeacherControls;
