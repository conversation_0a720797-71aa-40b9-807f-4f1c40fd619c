var es=Object.defineProperty;var q=Object.getOwnPropertySymbols;var we=Object.prototype.hasOwnProperty,ye=Object.prototype.propertyIsEnumerable;var X=(a,t,i)=>t in a?es(a,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):a[t]=i,ee=(a,t)=>{for(var i in t||(t={}))we.call(t,i)&&X(a,i,t[i]);if(q)for(var i of q(t))ye.call(t,i)&&X(a,i,t[i]);return a};var Ne=(a,t)=>{var i={};for(var n in a)we.call(a,n)&&t.indexOf(n)<0&&(i[n]=a[n]);if(a!=null&&q)for(var n of q(a))t.indexOf(n)<0&&ye.call(a,n)&&(i[n]=a[n]);return i};var se=(a,t,i)=>X(a,typeof t!="symbol"?t+"":t,i);var te=(a,t,i)=>new Promise((n,r)=>{var d=p=>{try{g(i.next(p))}catch(b){r(b)}},f=p=>{try{g(i.throw(p))}catch(b){r(b)}},g=p=>p.done?n(p.value):Promise.resolve(p.value).then(d,f);g((i=i.apply(a,t)).next())});import{r as l,j as e,bu as ze,bv as ss,$ as Te,bw as ts,bx as as,by as is,bz as ns,bA as he,bB as ls,bC as rs,bD as os,bE as cs,bF as ds,bG as us,bH as hs,bI as ms,bJ as gs,b0 as ps,bK as xs,bL as fs,m as js,aF as bs,bM as vs,bN as ks,a6 as Cs,a7 as ws,bO as ys,bP as Ns,bQ as Ts,aq as Ae,bR as ae,aD as Ss,bS as Is,bT as $s}from"./vendor-react.BcAa1DKr.js";import{ai as zs,aj as As,ak as Ls,Z as Hs,al as Ps,am as Rs,an as Ms,ao as Bs,ap as Ds,aq as Es,ar as Us,as as Fs,at as qs,au as Os,av as Ws,aw as Ys,ax as _s,ay as Vs,a2 as ie,az as Gs}from"./vendor.DQpuTRuB.js";import{c as Le,T as Ks,d as u,e as h,B as o,f as m,n as Js,o as Qs,q as Zs,r as M,D as ne,h as le,i as re,j as oe,I as z,l as ce}from"./index.BLDhDn0D.js";import{T as Xs,a as et,b as de,c as ue}from"./tabs.B0SF6qIv.js";import{L as B}from"./label.D4YlnUPk.js";import{E as Se}from"./enhanced-markdown-preview.Du81wch8.js";import{v as st,c as tt,u as at}from"./tiptap-image-upload.B_U9gNrF.js";import{C as O,a as W,b as Y,d as _,c as Ie}from"./card.B9V6b2DK.js";import{B as S}from"./badge.C87ZuIxl.js";import"./vendor-supabase.sufZ44-y.js";import"./content-converter.L-GziWIP.js";const A=l.forwardRef((d,r)=>{var f=d,{className:a,orientation:t="horizontal",decorative:i=!0}=f,n=Ne(f,["className","orientation","decorative"]);return e.jsx(ze,ee({ref:r,decorative:i,orientation:t,className:Le("shrink-0 bg-border",t==="horizontal"?"h-[1px] w-full":"h-full w-[1px]",a)},n))});A.displayName=ze.displayName;class He{constructor(t={}){se(this,"options");se(this,"turndownService");this.options=ee({tightLists:!1,preserveWhitespace:!1,gfmTables:!0,taskLists:!0,details:!0,callouts:!0},t),this.turndownService=new zs({headingStyle:"atx",hr:"---",bulletListMarker:"-",codeBlockStyle:"fenced",fence:"```",emDelimiter:"*",strongDelimiter:"**",linkStyle:"inlined",linkReferenceStyle:"full"}),this.turndownService.use(As),this.addCustomRules()}addCustomRules(){this.options.taskLists&&this.turndownService.addRule("taskListItem",{filter:t=>t.nodeName==="LI"&&t.getAttribute("data-type")==="taskItem",replacement:(t,i)=>{const n=i.querySelector('input[type="checkbox"]');return`- [${n!=null&&n.checked?"x":" "}] ${t}
`}}),this.options.details&&this.turndownService.addRule("details",{filter:"details",replacement:(t,i)=>{const n=i.querySelector("summary"),r=(n==null?void 0:n.textContent)||"Details",d=t.replace(r,"").trim();return`<details>
<summary>${r}</summary>

${d}
</details>

`}}),this.options.callouts&&this.turndownService.addRule("callout",{filter:t=>t.nodeName==="DIV"&&t.getAttribute("data-callout")!==null,replacement:(t,i)=>`> [!${(i.getAttribute("data-type")||"info").toUpperCase()}]
> ${t.split(`
`).join(`
> `)}

`}),this.turndownService.addRule("codeBlock",{filter:t=>t.nodeName==="PRE"&&t.querySelector("code"),replacement:(t,i)=>{var f,g;const n=i.querySelector("code"),r=((g=(f=n==null?void 0:n.className)==null?void 0:f.match(/language-(\w+)/))==null?void 0:g[1])||"",d=(n==null?void 0:n.textContent)||t;return`\`\`\`${r}
${d}
\`\`\`

`}}),this.turndownService.addRule("highlight",{filter:"mark",replacement:t=>`==${t}==`}),this.turndownService.addRule("underline",{filter:"u",replacement:t=>`<u>${t}</u>`}),this.turndownService.addRule("youtube",{filter:t=>t.nodeName==="DIV"&&t.getAttribute("data-youtube-video")!==null,replacement:(t,i)=>{const n=i.querySelector("iframe"),r=(n==null?void 0:n.getAttribute("src"))||"";return`[![YouTube Video](https://img.youtube.com/vi/${this.extractYouTubeId(r)}/0.jpg)](${r})

`}}),this.turndownService.addRule("image",{filter:"img",replacement:(t,i)=>{const n=i.getAttribute("src")||"",r=i.getAttribute("alt")||"",d=i.getAttribute("title"),f=d?` "${d}"`:"";return`![${r}](${n}${f})`}})}extractYouTubeId(t){const i=t.match(/(?:youtube\.com\/embed\/|youtu\.be\/)([^?&]+)/);return(i==null?void 0:i[1])||""}serialize(t){const i=this.nodeToHtml(t),n=this.turndownService.turndown(i);return this.cleanMarkdown(n)}nodeToHtml(t){return this.serializeNodeToHtml(t)}serializeNodeToHtml(t){switch(t.type.name){case"doc":return this.serializeChildrenToHtml(t);case"paragraph":const i=this.serializeChildrenToHtml(t);return i?`<p>${i}</p>`:"";case"heading":const n=t.attrs.level||1,r=this.serializeChildrenToHtml(t);return`<h${n}>${r}</h${n}>`;case"bulletList":return`<ul>${this.serializeChildrenToHtml(t)}</ul>`;case"orderedList":return`<ol>${this.serializeChildrenToHtml(t)}</ol>`;case"listItem":return`<li>${this.serializeChildrenToHtml(t)}</li>`;case"taskList":return`<ul data-type="taskList">${this.serializeChildrenToHtml(t)}</ul>`;case"taskItem":const d=t.attrs.checked?"checked":"",f=this.serializeChildrenToHtml(t);return`<li data-type="taskItem"><input type="checkbox" ${d}>${f}</li>`;case"codeBlock":const g=t.attrs.language||"",p=t.textContent;return`<pre><code class="language-${g}">${p}</code></pre>`;case"blockquote":return`<blockquote>${this.serializeChildrenToHtml(t)}</blockquote>`;case"horizontalRule":return"<hr>";case"image":const b=t.attrs.src||"",k=t.attrs.alt||"",C=t.attrs.title||"";return`<img src="${b}" alt="${k}" title="${C}">`;case"table":return`<table>${this.serializeChildrenToHtml(t)}</table>`;case"tableRow":return`<tr>${this.serializeChildrenToHtml(t)}</tr>`;case"tableHeader":return`<th>${this.serializeChildrenToHtml(t)}</th>`;case"tableCell":return`<td>${this.serializeChildrenToHtml(t)}</td>`;case"details":return`<details>${this.serializeChildrenToHtml(t)}</details>`;case"detailsSummary":return`<summary>${this.serializeChildrenToHtml(t)}</summary>`;case"detailsContent":return`<div data-details-content>${this.serializeChildrenToHtml(t)}</div>`;case"callout":return`<div data-callout data-type="${t.attrs.type||"info"}">${this.serializeChildrenToHtml(t)}</div>`;case"text":let x=t.text||"";if(t.marks)for(const N of t.marks)switch(N.type.name){case"bold":x=`<strong>${x}</strong>`;break;case"italic":x=`<em>${x}</em>`;break;case"underline":x=`<u>${x}</u>`;break;case"strike":x=`<s>${x}</s>`;break;case"code":x=`<code>${x}</code>`;break;case"highlight":x=`<mark>${x}</mark>`;break;case"link":const D=N.attrs.href||"",y=N.attrs.target||"";x=`<a href="${D}" target="${y}">${x}</a>`;break}return x;default:return this.serializeChildrenToHtml(t)}}serializeChildrenToHtml(t){let i="";return t.forEach(n=>{i+=this.serializeNodeToHtml(n)}),i}cleanMarkdown(t){return t=t.replace(/\n{3,}/g,`

`),t=t.replace(/^(#{1,6}\s.+)$/gm,`
$1
`),t=t.replace(/^(\s*[-*+]\s)/gm,"$1"),t.trim()}}function it(a,t){return new He(t).serialize(a)}function nt(a,t){return new He(t).turndownService.turndown(a)}const lt=Ls(Gs);function rt({initialContent:a="",onChange:t,placeholder:i="Start writing your content...",className:n="",minHeight:r=500,autoFocus:d=!1,courseId:f,moduleId:g,showToolbar:p=!0,showPreview:b=!0,fullscreen:k=!1,onFullscreenChange:C}){const{theme:w,setTheme:x}=Hs(),[N,D]=l.useState("split"),[y,I]=l.useState(a),[V,$]=l.useState(!1),[L,j]=l.useState(!1),[Pe,E]=l.useState(!1),[me,ge]=l.useState(!1),[U,pe]=l.useState(""),[G,K]=l.useState(""),[H,xe]=l.useState(""),[J,fe]=l.useState(""),[F,je]=l.useState(""),[Q,be]=l.useState(null),[ve,ke]=l.useState(null),[P,Ce]=l.useState(k),Re=l.useRef(null),s=ss({extensions:[Ps.configure({codeBlock:!1}),Rs.configure({lowlight:lt,defaultLanguage:"plaintext"}),Ms.configure({HTMLAttributes:{class:"rounded-lg max-w-full h-auto cursor-zoom-in"}}),Bs.configure({openOnClick:!1,HTMLAttributes:{rel:"noopener noreferrer",target:"_blank",class:"text-primary hover:text-primary/80 underline transition-colors"}}),Ds,Es.configure({HTMLAttributes:{class:"bg-yellow-200 dark:bg-yellow-800 px-1 rounded"}}),Us,Fs.configure({nested:!0}),qs.configure({resizable:!0,HTMLAttributes:{class:"border-collapse border border-border rounded-lg overflow-hidden"}}),Os,Ws.configure({HTMLAttributes:{class:"bg-muted font-semibold"}}),Ys.configure({HTMLAttributes:{class:"border border-border px-4 py-2"}}),_s.configure({controls:!0,nocookie:!0,HTMLAttributes:{class:"rounded-lg overflow-hidden my-4"}}),Vs.configure({placeholder:i})],content:a,autofocus:d,onUpdate:({editor:c})=>{try{const v=c.state.doc,T=it(v);I(T),t==null||t(T)}catch(v){console.error("Error converting to markdown:",v);const T=c.getHTML(),R=nt(T);I(R),t==null||t(R)}}});l.useEffect(()=>{s&&a!==y&&(I(a),s.commands.setContent(a))},[a,s,y]),l.useEffect(()=>{Ce(k)},[k]);const Me=l.useCallback(()=>{const c=!P;Ce(c),C==null||C(c)},[P,C]),Be=l.useCallback(()=>s==null?void 0:s.chain().focus().toggleBold().run(),[s]),De=l.useCallback(()=>s==null?void 0:s.chain().focus().toggleItalic().run(),[s]),Ee=l.useCallback(()=>s==null?void 0:s.chain().focus().toggleUnderline().run(),[s]),Ue=l.useCallback(()=>s==null?void 0:s.chain().focus().toggleStrike().run(),[s]),Fe=l.useCallback(()=>s==null?void 0:s.chain().focus().toggleCode().run(),[s]),qe=l.useCallback(()=>s==null?void 0:s.chain().focus().toggleHighlight().run(),[s]),Z=l.useCallback(c=>{s==null||s.chain().focus().toggleHeading({level:c}).run()},[s]),Oe=l.useCallback(()=>s==null?void 0:s.chain().focus().toggleBulletList().run(),[s]),We=l.useCallback(()=>s==null?void 0:s.chain().focus().toggleOrderedList().run(),[s]),Ye=l.useCallback(()=>s==null?void 0:s.chain().focus().toggleTaskList().run(),[s]),_e=l.useCallback(()=>s==null?void 0:s.chain().focus().toggleBlockquote().run(),[s]),Ve=l.useCallback(()=>s==null?void 0:s.chain().focus().toggleCodeBlock().run(),[s]);l.useCallback(()=>s==null?void 0:s.chain().focus().setHorizontalRule().run(),[s]);const Ge=l.useCallback(()=>s==null?void 0:s.chain().focus().undo().run(),[s]),Ke=l.useCallback(()=>s==null?void 0:s.chain().focus().redo().run(),[s]),Je=l.useCallback(()=>{s==null||s.chain().focus().insertTable({rows:3,cols:3,withHeaderRow:!0}).run()},[s]);l.useCallback(()=>s==null?void 0:s.chain().focus().addColumnBefore().run(),[s]),l.useCallback(()=>s==null?void 0:s.chain().focus().addColumnAfter().run(),[s]),l.useCallback(()=>s==null?void 0:s.chain().focus().deleteColumn().run(),[s]),l.useCallback(()=>s==null?void 0:s.chain().focus().addRowBefore().run(),[s]),l.useCallback(()=>s==null?void 0:s.chain().focus().addRowAfter().run(),[s]),l.useCallback(()=>s==null?void 0:s.chain().focus().deleteRow().run(),[s]),l.useCallback(()=>s==null?void 0:s.chain().focus().deleteTable().run(),[s]);const Qe=l.useCallback(()=>{s==null||s.chain().focus().insertContent("<details><summary>Click to expand</summary><p>Content goes here...</p></details>").run()},[s]),Ze=l.useCallback((c="info")=>{const v=`> [!${c.toUpperCase()}]
> This is a ${c} callout with important information.`;s==null||s.chain().focus().insertContent(v).run()},[s]);return s?e.jsx(Ks,{children:e.jsxs("div",{className:Le("advanced-markdown-editor border border-border rounded-lg overflow-hidden bg-background",P&&"fixed inset-0 z-50 rounded-none border-0",n),children:[p&&e.jsx("div",{className:"border-b border-border bg-muted/50 p-2",children:e.jsxs("div",{className:"flex items-center gap-1 flex-wrap",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsxs(u,{children:[e.jsx(h,{asChild:!0,children:e.jsx(o,{variant:s.isActive("bold")?"default":"ghost",size:"sm",onClick:Be,children:e.jsx(ts,{className:"h-4 w-4"})})}),e.jsx(m,{children:"Bold (Ctrl+B)"})]}),e.jsxs(u,{children:[e.jsx(h,{asChild:!0,children:e.jsx(o,{variant:s.isActive("italic")?"default":"ghost",size:"sm",onClick:De,children:e.jsx(as,{className:"h-4 w-4"})})}),e.jsx(m,{children:"Italic (Ctrl+I)"})]}),e.jsxs(u,{children:[e.jsx(h,{asChild:!0,children:e.jsx(o,{variant:s.isActive("underline")?"default":"ghost",size:"sm",onClick:Ee,children:e.jsx(is,{className:"h-4 w-4"})})}),e.jsx(m,{children:"Underline (Ctrl+U)"})]}),e.jsxs(u,{children:[e.jsx(h,{asChild:!0,children:e.jsx(o,{variant:s.isActive("strike")?"default":"ghost",size:"sm",onClick:Ue,children:e.jsx(ns,{className:"h-4 w-4"})})}),e.jsx(m,{children:"Strikethrough"})]}),e.jsxs(u,{children:[e.jsx(h,{asChild:!0,children:e.jsx(o,{variant:s.isActive("code")?"default":"ghost",size:"sm",onClick:Fe,children:e.jsx(he,{className:"h-4 w-4"})})}),e.jsx(m,{children:"Inline Code"})]}),e.jsxs(u,{children:[e.jsx(h,{asChild:!0,children:e.jsx(o,{variant:s.isActive("highlight")?"default":"ghost",size:"sm",onClick:qe,children:e.jsx(ls,{className:"h-4 w-4"})})}),e.jsx(m,{children:"Highlight"})]})]}),e.jsx(A,{orientation:"vertical",className:"h-6"}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsxs(u,{children:[e.jsx(h,{asChild:!0,children:e.jsx(o,{variant:s.isActive("heading",{level:1})?"default":"ghost",size:"sm",onClick:()=>Z(1),children:e.jsx(rs,{className:"h-4 w-4"})})}),e.jsx(m,{children:"Heading 1"})]}),e.jsxs(u,{children:[e.jsx(h,{asChild:!0,children:e.jsx(o,{variant:s.isActive("heading",{level:2})?"default":"ghost",size:"sm",onClick:()=>Z(2),children:e.jsx(os,{className:"h-4 w-4"})})}),e.jsx(m,{children:"Heading 2"})]}),e.jsxs(u,{children:[e.jsx(h,{asChild:!0,children:e.jsx(o,{variant:s.isActive("heading",{level:3})?"default":"ghost",size:"sm",onClick:()=>Z(3),children:e.jsx(cs,{className:"h-4 w-4"})})}),e.jsx(m,{children:"Heading 3"})]})]}),e.jsx(A,{orientation:"vertical",className:"h-6"}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsxs(u,{children:[e.jsx(h,{asChild:!0,children:e.jsx(o,{variant:s.isActive("bulletList")?"default":"ghost",size:"sm",onClick:Oe,children:e.jsx(ds,{className:"h-4 w-4"})})}),e.jsx(m,{children:"Bullet List"})]}),e.jsxs(u,{children:[e.jsx(h,{asChild:!0,children:e.jsx(o,{variant:s.isActive("orderedList")?"default":"ghost",size:"sm",onClick:We,children:e.jsx(us,{className:"h-4 w-4"})})}),e.jsx(m,{children:"Numbered List"})]}),e.jsxs(u,{children:[e.jsx(h,{asChild:!0,children:e.jsx(o,{variant:s.isActive("taskList")?"default":"ghost",size:"sm",onClick:Ye,children:e.jsx(hs,{className:"h-4 w-4"})})}),e.jsx(m,{children:"Task List"})]})]}),e.jsx(A,{orientation:"vertical",className:"h-6"}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsxs(u,{children:[e.jsx(h,{asChild:!0,children:e.jsx(o,{variant:s.isActive("blockquote")?"default":"ghost",size:"sm",onClick:_e,children:e.jsx(ms,{className:"h-4 w-4"})})}),e.jsx(m,{children:"Quote"})]}),e.jsxs(u,{children:[e.jsx(h,{asChild:!0,children:e.jsx(o,{variant:"ghost",size:"sm",onClick:Ve,children:e.jsx(he,{className:"h-4 w-4"})})}),e.jsx(m,{children:"Code Block"})]}),e.jsxs(u,{children:[e.jsx(h,{asChild:!0,children:e.jsx(o,{variant:"ghost",size:"sm",onClick:()=>j(!0),children:e.jsx(gs,{className:"h-4 w-4"})})}),e.jsx(m,{children:"Insert Link"})]}),e.jsxs(u,{children:[e.jsx(h,{asChild:!0,children:e.jsx(o,{variant:"ghost",size:"sm",onClick:()=>$(!0),children:e.jsx(ps,{className:"h-4 w-4"})})}),e.jsx(m,{children:"Insert Image"})]}),e.jsxs(u,{children:[e.jsx(h,{asChild:!0,children:e.jsx(o,{variant:"ghost",size:"sm",onClick:Je,children:e.jsx(xs,{className:"h-4 w-4"})})}),e.jsx(m,{children:"Insert Table"})]}),e.jsxs(u,{children:[e.jsx(h,{asChild:!0,children:e.jsx(o,{variant:"ghost",size:"sm",onClick:()=>E(!0),children:e.jsx(fs,{className:"h-4 w-4"})})}),e.jsx(m,{children:"Insert YouTube Video"})]}),e.jsxs(u,{children:[e.jsx(h,{asChild:!0,children:e.jsx(o,{variant:"ghost",size:"sm",onClick:Qe,children:e.jsx(js,{className:"h-4 w-4"})})}),e.jsx(m,{children:"Insert Collapsible Section"})]}),e.jsxs(Js,{onValueChange:c=>Ze(c),children:[e.jsx(Qs,{className:"w-auto h-8 px-2",children:e.jsx(bs,{className:"h-4 w-4"})}),e.jsxs(Zs,{children:[e.jsx(M,{value:"info",children:"Info Callout"}),e.jsx(M,{value:"warning",children:"Warning Callout"}),e.jsx(M,{value:"success",children:"Success Callout"}),e.jsx(M,{value:"error",children:"Error Callout"}),e.jsx(M,{value:"tip",children:"Tip Callout"})]})]})]}),e.jsx(A,{orientation:"vertical",className:"h-6"}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsxs(u,{children:[e.jsx(h,{asChild:!0,children:e.jsx(o,{variant:"ghost",size:"sm",onClick:Ge,disabled:!s.can().undo(),children:e.jsx(vs,{className:"h-4 w-4"})})}),e.jsx(m,{children:"Undo (Ctrl+Z)"})]}),e.jsxs(u,{children:[e.jsx(h,{asChild:!0,children:e.jsx(o,{variant:"ghost",size:"sm",onClick:Ke,disabled:!s.can().redo(),children:e.jsx(ks,{className:"h-4 w-4"})})}),e.jsx(m,{children:"Redo (Ctrl+Y)"})]})]}),e.jsxs("div",{className:"ml-auto flex items-center gap-2",children:[e.jsxs(u,{children:[e.jsx(h,{asChild:!0,children:e.jsx(o,{variant:"ghost",size:"sm",onClick:()=>x(w==="dark"?"light":"dark"),children:w==="dark"?e.jsx(Cs,{className:"h-4 w-4"}):e.jsx(ws,{className:"h-4 w-4"})})}),e.jsx(m,{children:"Toggle Theme"})]}),e.jsxs(u,{children:[e.jsx(h,{asChild:!0,children:e.jsx(o,{variant:"ghost",size:"sm",onClick:Me,children:P?e.jsx(ys,{className:"h-4 w-4"}):e.jsx(Ns,{className:"h-4 w-4"})})}),e.jsx(m,{children:P?"Exit Fullscreen":"Fullscreen"})]})]})]})}),b?e.jsxs(Xs,{value:N,onValueChange:c=>D(c),className:"h-full",children:[e.jsxs(et,{className:"grid w-full grid-cols-3 bg-muted/50 border-b border-border rounded-none",children:[e.jsxs(de,{value:"editor",className:"flex items-center gap-2",children:[e.jsx(Ts,{className:"h-4 w-4"}),"Editor"]}),e.jsxs(de,{value:"split",className:"flex items-center gap-2",children:[e.jsxs("div",{className:"flex gap-1",children:[e.jsx("div",{className:"w-2 h-4 bg-current opacity-60"}),e.jsx("div",{className:"w-2 h-4 bg-current opacity-60"})]}),"Split"]}),e.jsxs(de,{value:"preview",className:"flex items-center gap-2",children:[e.jsx(Ae,{className:"h-4 w-4"}),"Preview"]})]}),e.jsx(ue,{value:"editor",className:"mt-0 h-full",children:e.jsx("div",{className:"obsidian-editor h-full",children:e.jsx(ae,{editor:s,style:{minHeight:`${r}px`},className:"h-full"})})}),e.jsx(ue,{value:"split",className:"mt-0 h-full",children:e.jsxs("div",{className:"grid grid-cols-2 h-full",children:[e.jsx("div",{className:"obsidian-editor border-r border-border",children:e.jsx(ae,{editor:s,style:{minHeight:`${r}px`},className:"h-full"})}),e.jsx("div",{className:"h-full overflow-auto",children:e.jsx(Se,{content:y,className:"p-6",style:{minHeight:`${r}px`}})})]})}),e.jsx(ue,{value:"preview",className:"mt-0 h-full",children:e.jsx("div",{className:"h-full overflow-auto",children:e.jsx(Se,{content:y,className:"p-6",style:{minHeight:`${r}px`}})})})]}):e.jsx("div",{className:"obsidian-editor h-full",children:e.jsx(ae,{editor:s,style:{minHeight:`${r}px`},className:"h-full"})}),e.jsx(ne,{open:V,onOpenChange:$,children:e.jsxs(le,{className:"sm:max-w-md",children:[e.jsx(re,{children:e.jsx(oe,{children:"Insert Image"})}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(B,{htmlFor:"image-file",children:"Upload Image"}),e.jsx(z,{id:"image-file",type:"file",accept:"image/*",ref:Re,onChange:c=>te(this,null,function*(){var T;const v=(T=c.target.files)==null?void 0:T[0];if(v){const R=st(v);if(!R.valid){ie.error(R.message);return}be(v),K(v.name.replace(/\.[^/.]+$/,""));const Xe=yield tt(v);ke(Xe)}})})]}),ve&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("img",{src:ve,alt:"Preview",className:"max-w-full h-32 object-cover rounded"}),e.jsx(z,{placeholder:"Alt text",value:G,onChange:c=>K(c.target.value)})]}),e.jsx("div",{className:"text-center text-muted-foreground",children:"or"}),e.jsxs("div",{children:[e.jsx(B,{htmlFor:"image-url",children:"Image URL"}),e.jsx(z,{id:"image-url",placeholder:"https://example.com/image.jpg",value:U,onChange:c=>pe(c.target.value)})]})]}),e.jsxs(ce,{children:[e.jsx(o,{variant:"outline",onClick:()=>$(!1),children:"Cancel"}),e.jsxs(o,{onClick:()=>te(this,null,function*(){if(Q)try{ge(!0);const c=yield at(Q,{courseId:f,moduleId:g});s==null||s.chain().focus().setImage({src:c,alt:G}).run(),ie.success("Image uploaded successfully!")}catch(c){ie.error("Failed to upload image")}finally{ge(!1)}else U&&(s==null||s.chain().focus().setImage({src:U,alt:G}).run());$(!1),be(null),ke(null),pe(""),K("")}),disabled:me||!Q&&!U,children:[me&&e.jsx(Te,{className:"h-4 w-4 mr-2 animate-spin"}),"Insert Image"]})]})]})}),e.jsx(ne,{open:L,onOpenChange:j,children:e.jsxs(le,{className:"sm:max-w-md",children:[e.jsx(re,{children:e.jsx(oe,{children:"Insert Link"})}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(B,{htmlFor:"link-text",children:"Link Text"}),e.jsx(z,{id:"link-text",placeholder:"Link text",value:J,onChange:c=>fe(c.target.value)})]}),e.jsxs("div",{children:[e.jsx(B,{htmlFor:"link-url",children:"URL"}),e.jsx(z,{id:"link-url",placeholder:"https://example.com",value:H,onChange:c=>xe(c.target.value)})]})]}),e.jsxs(ce,{children:[e.jsx(o,{variant:"outline",onClick:()=>j(!1),children:"Cancel"}),e.jsx(o,{onClick:()=>{H&&(J?s==null||s.chain().focus().insertContent(`<a href="${H}">${J}</a>`).run():s==null||s.chain().focus().setLink({href:H}).run()),j(!1),xe(""),fe("")},disabled:!H,children:"Insert Link"})]})]})}),e.jsx(ne,{open:Pe,onOpenChange:E,children:e.jsxs(le,{className:"sm:max-w-md",children:[e.jsx(re,{children:e.jsx(oe,{children:"Insert YouTube Video"})}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{children:[e.jsx(B,{htmlFor:"youtube-url",children:"YouTube URL"}),e.jsx(z,{id:"youtube-url",placeholder:"https://www.youtube.com/watch?v=...",value:F,onChange:c=>je(c.target.value)})]})}),e.jsxs(ce,{children:[e.jsx(o,{variant:"outline",onClick:()=>E(!1),children:"Cancel"}),e.jsx(o,{onClick:()=>{F&&(s==null||s.chain().focus().setYoutubeVideo({src:F}).run()),E(!1),je("")},disabled:!F,children:"Insert Video"})]})]})})]})}):e.jsx("div",{className:"flex items-center justify-center h-64",children:e.jsx(Te,{className:"h-8 w-8 animate-spin"})})}function ot(a){const t=[],i=[];if(!a||a.trim().length===0)return t.push("Content is empty"),{valid:!1,errors:t,warnings:i};const n=(a.match(/\[/g)||[]).length,r=(a.match(/\]/g)||[]).length;n!==r&&t.push("Unmatched square brackets detected");const d=(a.match(/\(/g)||[]).length,f=(a.match(/\)/g)||[]).length;d!==f&&i.push("Unmatched parentheses detected - may affect links");const g=a.match(/\[([^\]]*)\]\([^)]*$/gm);g&&t.push(`${g.length} malformed link(s) detected`);const p=a.match(/!\[([^\]]*)\]\([^)]*$/gm);p&&t.push(`${p.length} malformed image(s) detected`);const b=a.match(/\[([^\]]*)\]\(\s*\)/g);b&&i.push(`${b.length} empty link(s) detected`);const k=a.match(/!\[\s*\]\([^)]+\)/g);k&&i.push(`${k.length} image(s) without alt text detected`),(a.match(/```/g)||[]).length%2!==0&&t.push("Unclosed code block detected");const w=a.match(/^\|.*\|$/gm);if(w){const x=a.match(/^\|[-\s|:]+\|$/gm);w.length>0&&!x&&i.push("Table detected without proper header separator")}return{valid:t.length===0,errors:t,warnings:i}}function ct(a){if(!a)return{wordCount:0,characterCount:0,paragraphCount:0,headingCount:0,listCount:0,imageCount:0,linkCount:0,codeBlockCount:0,tableCount:0,readingTimeMinutes:0};const n=a.replace(/```[\s\S]*?```/g,"").replace(/`[^`]+`/g,"").replace(/[^\w\s]/g," ").split(/\s+/).filter(L=>L.length>0).length,r=a.length,f=a.split(`
`).filter(L=>{const j=L.trim();return j.length>0&&!j.startsWith("#")&&!j.startsWith("-")&&!j.startsWith("*")&&!j.startsWith("+")&&!j.match(/^\d+\./)&&!j.startsWith("|")&&!j.startsWith(">")&&!j.startsWith("```")}).length,p=(a.match(/^#{1,6}\s/gm)||[]).length,k=(a.match(/^[\s]*[-*+]\s|^[\s]*\d+\.\s/gm)||[]).length,w=(a.match(/!\[([^\]]*)\]\([^)]+\)/g)||[]).length,N=(a.match(/\[([^\]]+)\]\([^)]+\)/g)||[]).length,y=(a.match(/```[\s\S]*?```/g)||[]).length;a.match(/^\|.*\|$/gm);const I=a.match(/^\|[-\s|:]+\|$/gm),V=I?I.length:0,$=Math.ceil(n/200);return{wordCount:n,characterCount:r,paragraphCount:f,headingCount:p,listCount:k,imageCount:w,linkCount:N,codeBlockCount:y,tableCount:V,readingTimeMinutes:$}}const $e=`# Professional Lesson Content Demo

Welcome to our **professional-grade** markdown editor designed specifically for educational content creation. This demo showcases the advanced features that make lessons engaging and visually appealing.

## 📚 Course Overview

This lesson demonstrates the comprehensive features available in our advanced markdown editor, designed to create professional, engaging educational content.

### 🎯 Learning Objectives

By the end of this lesson, you will understand:
- How to create professional-looking lesson content
- Advanced formatting techniques for educational materials
- Interactive elements that enhance student engagement
- Best practices for content organization

## 📝 Text Formatting & Typography

Our editor supports comprehensive text formatting options:

- **Bold text** for emphasis and key concepts
- *Italic text* for subtle emphasis and definitions
- ~~Strikethrough~~ for corrections or outdated information
- ==Highlighted text== for important takeaways
- \`inline code\` for technical terms and commands
- <u>Underlined text</u> for additional emphasis

### Professional Lists

#### Bullet Points for Key Concepts
- Primary learning objectives
- Important terminology
- Key takeaways
  - Supporting details
  - Additional context
    - Deeper explanations
    - Examples and illustrations

#### Numbered Steps for Procedures
1. **Preparation Phase**
   - Gather required materials
   - Set up your workspace
2. **Implementation Phase**
   - Follow the step-by-step process
   - Monitor progress carefully
3. **Review Phase**
   - Evaluate results
   - Document lessons learned

## ✅ Interactive Task Lists

Track your progress through the lesson with interactive checkboxes:

- [x] Read the introduction and objectives
- [x] Review text formatting options
- [ ] Complete the practice exercises
- [ ] Submit your assignment
- [ ] Participate in the discussion forum
  - [ ] Post your initial response
  - [ ] Reply to at least two classmates
  - [ ] Engage in meaningful dialogue

## 📊 Professional Data Tables

Our tables are designed for clarity and professional presentation:

| Learning Module | Duration | Difficulty | Prerequisites |
|-----------------|----------|------------|---------------|
| Introduction to Concepts | 45 minutes | Beginner | None |
| Practical Applications | 90 minutes | Intermediate | Module 1 |
| Advanced Techniques | 120 minutes | Advanced | Modules 1-2 |
| Final Project | 180 minutes | Expert | All previous |

| Assessment Type | Weight | Due Date | Format |
|----------------|--------|----------|---------|
| Quiz 1 | 15% | Week 3 | Multiple Choice |
| Assignment 1 | 25% | Week 5 | Written Report |
| Midterm Exam | 30% | Week 8 | Comprehensive |
| Final Project | 30% | Week 12 | Presentation |

## 💻 Code Examples with Syntax Highlighting

### JavaScript Example
\`\`\`javascript
// Professional code example with proper formatting
class LearningModule {
  constructor(title, duration, difficulty) {
    this.title = title;
    this.duration = duration;
    this.difficulty = difficulty;
    this.completed = false;
  }

  markComplete() {
    this.completed = true;
    console.log(\`Module "\${this.title}" completed!\`);
  }

  getProgress() {
    return {
      title: this.title,
      status: this.completed ? 'Completed' : 'In Progress',
      timeSpent: this.duration
    };
  }
}

// Usage example
const module1 = new LearningModule('Introduction', '45 minutes', 'Beginner');
module1.markComplete();
\`\`\`

### Python Data Analysis
\`\`\`python
import pandas as pd
import matplotlib.pyplot as plt

# Load and analyze student performance data
def analyze_student_performance(data_file):
    """
    Analyze student performance metrics and generate insights.

    Args:
        data_file (str): Path to the CSV file containing student data

    Returns:
        dict: Analysis results with key metrics
    """
    df = pd.read_csv(data_file)

    # Calculate key metrics
    metrics = {
        'average_score': df['score'].mean(),
        'pass_rate': (df['score'] >= 70).mean() * 100,
        'top_performers': df[df['score'] >= 90]['student_name'].tolist()
    }

    # Generate visualization
    plt.figure(figsize=(10, 6))
    plt.hist(df['score'], bins=20, alpha=0.7, color='skyblue')
    plt.title('Distribution of Student Scores')
    plt.xlabel('Score')
    plt.ylabel('Frequency')
    plt.show()

    return metrics

# Example usage
results = analyze_student_performance('student_scores.csv')
print(f"Class average: {results['average_score']:.2f}")
print(f"Pass rate: {results['pass_rate']:.1f}%")
\`\`\`

## 📋 Professional Callouts & Alerts

### Information Callouts
> [!INFO]
> **Important Information**: This callout provides essential context and background information that students need to understand before proceeding with the lesson content.

> [!NOTE]
> **Study Note**: Take special note of this information as it will be covered in the upcoming assessment. Consider adding this to your study notes.

### Warning & Caution Callouts
> [!WARNING]
> **Attention Required**: This section contains critical information that could affect your understanding of subsequent topics. Please read carefully.

> [!CAUTION]
> **Proceed with Care**: The following steps require careful attention to detail. Double-check your work before moving forward.

### Success & Achievement Callouts
> [!SUCCESS]
> **Well Done!** You've successfully completed this section. Your understanding of these concepts will serve as a foundation for more advanced topics.

### Error Prevention
> [!ERROR]
> **Common Mistake**: Many students struggle with this concept. Avoid the common pitfall of confusing correlation with causation in your analysis.

### Tips & Best Practices
> [!TIP]
> **Pro Tip**: Use keyboard shortcuts to speed up your workflow. Press Ctrl+B for bold, Ctrl+I for italic, and Ctrl+K to insert links quickly.

> [!IMPORTANT]
> **Key Takeaway**: This concept is fundamental to understanding the entire course. Make sure you fully grasp it before moving on.

## 🔽 Expandable Content Sections

<details>
<summary>📖 Additional Reading Materials</summary>

This section contains supplementary materials that provide deeper insights into the topic:

### Recommended Books
- "The Art of Learning" by Josh Waitzkin
- "Make It Stick" by Peter Brown
- "Peak: Secrets from the New Science of Expertise" by Anders Ericsson

### Online Resources
- Khan Academy courses on related topics
- Coursera specializations
- MIT OpenCourseWare lectures

### Research Papers
- Recent studies on effective learning techniques
- Peer-reviewed articles on educational psychology
- Meta-analyses of teaching methodologies

</details>

<details>
<summary>🧪 Practice Exercises</summary>

Complete these exercises to reinforce your understanding:

### Exercise 1: Concept Application
Apply the concepts learned in this lesson to solve the following problem:

**Scenario**: You are designing a learning management system for a university. How would you implement the features discussed in this lesson?

**Requirements**:
- Create a user interface mockup
- Define the data structure
- Outline the key functionalities

### Exercise 2: Critical Analysis
Analyze the effectiveness of different content presentation methods:

1. Compare traditional text-based content with interactive elements
2. Evaluate the impact of visual aids on learning retention
3. Propose improvements to existing educational platforms

</details>

<details>
<summary>🎯 Assessment Criteria</summary>

Your work will be evaluated based on the following criteria:

| Criterion | Excellent (4) | Good (3) | Satisfactory (2) | Needs Improvement (1) |
|-----------|---------------|----------|------------------|----------------------|
| Understanding | Demonstrates deep comprehension | Shows good understanding | Basic understanding evident | Limited understanding |
| Application | Skillfully applies concepts | Applies concepts well | Some application shown | Minimal application |
| Analysis | Thorough and insightful | Good analytical skills | Basic analysis | Superficial analysis |
| Communication | Clear and professional | Well communicated | Adequate communication | Poor communication |

</details>

## 🖼️ Professional Image Handling

Images are automatically optimized for professional presentation with captions and zoom functionality:

![Learning Analytics Dashboard](https://via.placeholder.com/800x400/2563eb/ffffff?text=Learning+Analytics+Dashboard "Interactive dashboard showing student progress and engagement metrics")

![Course Structure Diagram](https://via.placeholder.com/600x350/059669/ffffff?text=Course+Structure "Hierarchical view of course modules and lessons")

## 💬 Professional Blockquotes

> "The beautiful thing about learning is that no one can take it away from you. Education is the most powerful weapon which you can use to change the world."
>
> — Nelson Mandela

> "Tell me and I forget, teach me and I may remember, involve me and I learn."
>
> — Benjamin Franklin

---

## 🚀 Advanced Features Summary

This professional lesson preview system includes:

- 🎨 **Beautiful Typography** with proper hierarchy and spacing
- 📊 **Interactive Tables** with hover effects and professional styling
- 🔽 **Collapsible Sections** for organized content delivery
- 💡 **Professional Callouts** with contextual icons and styling
- ✅ **Interactive Task Lists** for student engagement
- 🖼️ **Enhanced Image Handling** with captions and zoom functionality
- 💻 **Syntax-Highlighted Code** with copy functionality
- 📱 **Responsive Design** that works on all devices
- 🌙 **Dark Mode Support** for comfortable viewing
- ♿ **Accessibility Features** for inclusive learning

## 📚 Next Steps

1. **Practice**: Try creating your own lesson content using these features
2. **Experiment**: Test different combinations of elements
3. **Feedback**: Share your experience with the development team
4. **Iterate**: Continuously improve your content based on student feedback

Ready to create professional, engaging educational content? Start building your lessons today!`;function Ct(){const[a,t]=l.useState($e),[i,n]=l.useState(!1),r=ct(a),d=ot(a),f=[{icon:e.jsx(Ss,{className:"h-5 w-5"}),title:"GitHub Flavored Markdown",description:"Full GFM support including tables, task lists, and strikethrough"},{icon:e.jsx(Ae,{className:"h-5 w-5"}),title:"Live Preview",description:"Real-time preview with split-pane and full-screen modes"},{icon:e.jsx(he,{className:"h-5 w-5"}),title:"Syntax Highlighting",description:"Code blocks with syntax highlighting for 20+ languages"},{icon:e.jsx(Is,{className:"h-5 w-5"}),title:"Obsidian-inspired Design",description:"Clean, readable typography with dark mode support"},{icon:e.jsx($s,{className:"h-5 w-5"}),title:"Advanced Features",description:"Image upload, callouts, collapsible sections, and more"}];return e.jsxs("div",{className:"container mx-auto py-8 px-4 max-w-7xl",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-4xl font-bold mb-4",children:"Advanced Markdown Editor"}),e.jsx("p",{className:"text-xl text-muted-foreground mb-6",children:"A powerful, Notion-like markdown editor with GitHub Flavored Markdown support, live preview, and advanced features for content creation."}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8",children:f.map((g,p)=>e.jsxs(O,{className:"h-full",children:[e.jsx(W,{className:"pb-3",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 bg-primary/10 rounded-lg text-primary",children:g.icon}),e.jsx(Y,{className:"text-lg",children:g.title})]})}),e.jsx(_,{children:e.jsx(Ie,{children:g.description})})]},p))}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",children:[e.jsxs(O,{children:[e.jsx(W,{children:e.jsx(Y,{className:"text-lg",children:"Content Statistics"})}),e.jsxs(_,{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Words:"}),e.jsx(S,{variant:"secondary",children:r.wordCount})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Characters:"}),e.jsx(S,{variant:"secondary",children:r.characterCount})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Headings:"}),e.jsx(S,{variant:"secondary",children:r.headingCount})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Images:"}),e.jsx(S,{variant:"secondary",children:r.imageCount})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Code Blocks:"}),e.jsx(S,{variant:"secondary",children:r.codeBlockCount})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Reading Time:"}),e.jsxs(S,{variant:"secondary",children:[r.readingTimeMinutes," min"]})]})]})]}),e.jsxs(O,{children:[e.jsx(W,{children:e.jsx(Y,{className:"text-lg",children:"Content Validation"})}),e.jsxs(_,{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{children:"Status:"}),e.jsx(S,{variant:d.valid?"default":"destructive",children:d.valid?"Valid":"Issues Found"})]}),d.errors.length>0&&e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-medium text-destructive",children:"Errors:"}),e.jsx("ul",{className:"text-sm text-muted-foreground mt-1 space-y-1",children:d.errors.map((g,p)=>e.jsxs("li",{children:["• ",g]},p))})]}),d.warnings.length>0&&e.jsxs("div",{children:[e.jsx("span",{className:"text-sm font-medium text-warning",children:"Warnings:"}),e.jsx("ul",{className:"text-sm text-muted-foreground mt-1 space-y-1",children:d.warnings.map((g,p)=>e.jsxs("li",{children:["• ",g]},p))})]}),d.valid&&d.warnings.length===0&&e.jsx("p",{className:"text-sm text-muted-foreground",children:"No issues found. Content is well-formatted!"})]})]})]}),e.jsx(A,{className:"mb-8"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-2xl font-semibold",children:"Try the Editor"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(o,{variant:"outline",onClick:()=>t($e),children:"Reset Demo Content"}),e.jsx(o,{variant:"outline",onClick:()=>t(""),children:"Clear Editor"})]})]}),e.jsx(rt,{initialContent:a,onChange:t,placeholder:"Start writing your markdown content...",minHeight:600,showToolbar:!0,showPreview:!0,enableCodeCopy:!0,enableExport:!0,fullscreen:i,onFullscreenChange:n,className:"border-2 border-dashed border-border"})]}),e.jsx("div",{className:"mt-12",children:e.jsxs(O,{children:[e.jsxs(W,{children:[e.jsx(Y,{children:"How to Use"}),e.jsx(Ie,{children:"Get started with the advanced markdown editor"})]}),e.jsxs(_,{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold mb-2",children:"Keyboard Shortcuts"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm",children:[e.jsxs("div",{children:[e.jsx("kbd",{className:"px-2 py-1 bg-muted rounded",children:"Ctrl+B"})," - Bold"]}),e.jsxs("div",{children:[e.jsx("kbd",{className:"px-2 py-1 bg-muted rounded",children:"Ctrl+I"})," - Italic"]}),e.jsxs("div",{children:[e.jsx("kbd",{className:"px-2 py-1 bg-muted rounded",children:"Ctrl+U"})," - Underline"]}),e.jsxs("div",{children:[e.jsx("kbd",{className:"px-2 py-1 bg-muted rounded",children:"Ctrl+Z"})," - Undo"]}),e.jsxs("div",{children:[e.jsx("kbd",{className:"px-2 py-1 bg-muted rounded",children:"Ctrl+Y"})," - Redo"]}),e.jsxs("div",{children:[e.jsx("kbd",{className:"px-2 py-1 bg-muted rounded",children:"Ctrl+Shift+D"})," - Details"]})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold mb-2",children:"Special Features"}),e.jsxs("ul",{className:"text-sm space-y-1 text-muted-foreground",children:[e.jsx("li",{children:"• Click images to zoom in"}),e.jsx("li",{children:"• Hover over code blocks to see copy button"}),e.jsx("li",{children:"• Use the toolbar for quick formatting"}),e.jsx("li",{children:"• Switch between editor, split, and preview modes"}),e.jsx("li",{children:"• Toggle dark mode with the theme button"}),e.jsx("li",{children:"• Use fullscreen mode for distraction-free writing"})]})]})]})]})})]})}export{Ct as default};
