import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON><PERSON>, Check, Palette, Monitor, Moon, Sun, Settings } from 'lucide-react';
import { useTheme } from './theme-provider';
import { cn } from '@/lib/utils';
import { useMotion } from '@/context/MotionContext';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

// Define theme presets
const themes = [
  {
    name: 'Red',
    primaryColor: 'hsl(0 70% 50%)', // Red #E63946
    primaryLight: 'hsl(0 70% 60%)',
    primaryDark: 'hsl(0 70% 40%)',
    accentColor: 'hsl(0 60% 30%)',
  },
  {
    name: '<PERSON>',
    primaryColor: 'hsl(346.8 77.2% 49.8%)',
    primaryLight: 'hsl(339.6 89.6% 68%)',
    primaryDark: 'hsl(336.1 80.5% 38.8%)',
    accentColor: 'hsl(328.6 85.5% 70.2%)',
  },
  {
    name: 'Blue',
    primaryColor: 'hsl(221.2 83.2% 53.3%)',
    primaryLight: 'hsl(217.2 91.2% 59.8%)',
    primaryDark: 'hsl(224.3 76.3% 48%)',
    accentColor: 'hsl(262.1 83.3% 57.8%)',
  },
  {
    name: 'Crimson',
    primaryColor: 'hsl(0 80% 40%)', // Darker red
    primaryLight: 'hsl(0 80% 50%)',
    primaryDark: 'hsl(0 80% 30%)',
    accentColor: 'hsl(0 70% 20%)',
  },
  {
    name: 'Amber',
    primaryColor: 'hsl(38 92.7% 50.2%)',
    primaryLight: 'hsl(43 96.4% 58.8%)',
    primaryDark: 'hsl(32 94.6% 43.7%)',
    accentColor: 'hsl(20.5 90.2% 48.2%)',
  },
  {
    name: 'Teal',
    primaryColor: 'hsl(173.4 80.4% 40%)',
    primaryLight: 'hsl(172.5 66% 50.4%)',
    primaryDark: 'hsl(174.7 83.9% 31.6%)',
    accentColor: 'hsl(199 89.5% 48%)',
  },
];

export function ThemeCustomizer() {
  const { theme, setTheme } = useTheme();
  const { enableAnimations, setEnableAnimations } = useMotion();
  const [selectedTheme, setSelectedTheme] = useState('Red');
  const [isOpen, setIsOpen] = useState(false);

  // Load saved theme on mount or use Red as default
  useEffect(() => {
    const savedTheme = localStorage.getItem('selectedTheme');
    if (savedTheme) {
      setSelectedTheme(savedTheme);
      applyTheme(savedTheme);
    } else {
      // Apply Red theme as default if no theme is saved
      setSelectedTheme('Red');
      applyTheme('Red');
    }
  }, []);

  // Apply theme colors to CSS variables
  const applyTheme = (themeName: string) => {
    const selectedTheme = themes.find(t => t.name === themeName);
    if (!selectedTheme) return;

    const root = document.documentElement;
    root.style.setProperty('--primary', selectedTheme.primaryColor);
    root.style.setProperty('--primary-light', selectedTheme.primaryLight);
    root.style.setProperty('--primary-dark', selectedTheme.primaryDark);
    root.style.setProperty('--accent', selectedTheme.accentColor);

    localStorage.setItem('selectedTheme', themeName);
  };

  // Handle theme selection
  const handleThemeChange = (themeName: string) => {
    setSelectedTheme(themeName);
    applyTheme(themeName);
  };

  // Handle animation toggle
  const handleAnimationToggle = (checked: boolean) => {
    setEnableAnimations(checked);
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="rounded-full bg-background/80 backdrop-blur-sm border border-border/40 shadow-sm"
        >
          <Settings className="h-5 w-5 text-muted-foreground" />
          <span className="sr-only">Customize theme</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80" align="end">
        <Tabs defaultValue="theme">
          <TabsList className="w-full mb-4">
            <TabsTrigger value="theme" className="flex-1">
              <Palette className="h-4 w-4 mr-2" />
              Theme
            </TabsTrigger>
            <TabsTrigger value="appearance" className="flex-1">
              <Monitor className="h-4 w-4 mr-2" />
              Appearance
            </TabsTrigger>
          </TabsList>

          <TabsContent value="theme" className="space-y-4">
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Color Themes</h4>
              <div className="grid grid-cols-3 gap-2">
                {themes.map((t) => (
                  <button
                    key={t.name}
                    onClick={() => handleThemeChange(t.name)}
                    className={cn(
                      "relative flex flex-col items-center justify-center rounded-md border-2 border-muted bg-popover p-2 hover:bg-accent hover:text-accent-foreground",
                      selectedTheme === t.name && "border-primary"
                    )}
                  >
                    <div className="flex gap-1 mb-1">
                      <div
                        className="h-5 w-5 rounded-full"
                        style={{ backgroundColor: t.primaryColor }}
                      />
                      <div
                        className="h-5 w-5 rounded-full"
                        style={{ backgroundColor: t.accentColor }}
                      />
                    </div>
                    <span className="text-xs">{t.name}</span>
                    {selectedTheme === t.name && (
                      <Check className="absolute top-1 right-1 h-4 w-4 text-primary" />
                    )}
                  </button>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="appearance" className="space-y-4">
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Mode</h4>
              <div className="grid grid-cols-3 gap-2">
                <button
                  onClick={() => setTheme("light")}
                  className={cn(
                    "relative flex flex-col items-center justify-center rounded-md border-2 border-muted bg-popover p-2 hover:bg-accent hover:text-accent-foreground",
                    theme === "light" && "border-primary"
                  )}
                >
                  <Sun className="h-5 w-5 mb-1" />
                  <span className="text-xs">Light</span>
                  {theme === "light" && (
                    <Check className="absolute top-1 right-1 h-4 w-4 text-primary" />
                  )}
                </button>
                <button
                  onClick={() => setTheme("dark")}
                  className={cn(
                    "relative flex flex-col items-center justify-center rounded-md border-2 border-muted bg-popover p-2 hover:bg-accent hover:text-accent-foreground",
                    theme === "dark" && "border-primary"
                  )}
                >
                  <Moon className="h-5 w-5 mb-1" />
                  <span className="text-xs">Dark</span>
                  {theme === "dark" && (
                    <Check className="absolute top-1 right-1 h-4 w-4 text-primary" />
                  )}
                </button>
                <button
                  onClick={() => setTheme("system")}
                  className={cn(
                    "relative flex flex-col items-center justify-center rounded-md border-2 border-muted bg-popover p-2 hover:bg-accent hover:text-accent-foreground",
                    theme === "system" && "border-primary"
                  )}
                >
                  <Monitor className="h-5 w-5 mb-1" />
                  <span className="text-xs">System</span>
                  {theme === "system" && (
                    <Check className="absolute top-1 right-1 h-4 w-4 text-primary" />
                  )}
                </button>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="text-sm font-medium">Animations</h4>
              <div className="flex items-center justify-between">
                <Label htmlFor="animations" className="text-xs">Enable animations</Label>
                <Switch
                  id="animations"
                  checked={enableAnimations}
                  onCheckedChange={handleAnimationToggle}
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </PopoverContent>
    </Popover>
  );
}
