import { toast } from 'sonner';
import { executeWithRetry } from './connection-manager';

// Constants
const OFFLINE_QUEUE_KEY = 'ivcan-offline-queue';
const MAX_QUEUE_SIZE = 100;

// Types
type QueuedOperation = {
  id: string;
  operation: string;
  params: any;
  timestamp: number;
  retries: number;
};

/**
 * Get the offline operation queue from local storage
 */
export function getOfflineQueue(): QueuedOperation[] {
  try {
    const queue = localStorage.getItem(OFFLINE_QUEUE_KEY);
    return queue ? JSON.parse(queue) : [];
  } catch (error) {
    console.error('Error retrieving offline queue:', error);
    return [];
  }
}

/**
 * Save the offline operation queue to local storage
 */
export function saveOfflineQueue(queue: QueuedOperation[]): void {
  try {
    // Limit queue size to prevent storage issues
    const limitedQueue = queue.slice(-MAX_QUEUE_SIZE);
    localStorage.setItem(OFFLINE_QUEUE_KEY, JSON.stringify(limitedQueue));
  } catch (error) {
    console.error('Error saving offline queue:', error);
    toast.error('Unable to save offline operations');
  }
}

/**
 * Add an operation to the offline queue
 */
export function queueOfflineOperation(operation: string, params: any): string {
  try {
    const queue = getOfflineQueue();
    const id = crypto.randomUUID();
    
    const newOperation: QueuedOperation = {
      id,
      operation,
      params,
      timestamp: Date.now(),
      retries: 0
    };
    
    queue.push(newOperation);
    saveOfflineQueue(queue);
    
    toast.info('Operation queued for when you are back online');
    return id;
  } catch (error) {
    console.error('Error queueing offline operation:', error);
    toast.error('Failed to queue operation for offline use');
    return '';
  }
}

/**
 * Remove an operation from the offline queue
 */
export function removeFromOfflineQueue(id: string): boolean {
  try {
    const queue = getOfflineQueue();
    const newQueue = queue.filter(op => op.id !== id);
    
    if (newQueue.length !== queue.length) {
      saveOfflineQueue(newQueue);
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error removing operation from offline queue:', error);
    return false;
  }
}

/**
 * Process the offline queue when back online
 * @param processor Function to process each queued operation
 * @returns Promise that resolves when all operations are processed
 */
export async function processOfflineQueue(
  processor: (operation: string, params: any) => Promise<any>
): Promise<{ successful: number, failed: number }> {
  const queue = getOfflineQueue();
  let successful = 0;
  let failed = 0;
  
  if (queue.length === 0) {
    return { successful, failed };
  }
  
  toast.info(`Processing ${queue.length} offline operations...`);
  
  const newQueue: QueuedOperation[] = [];
  
  for (const item of queue) {
    try {
      // Execute the operation with retry
      await executeWithRetry(async () => {
        await processor(item.operation, item.params);
      }, 2);
      
      successful++;
    } catch (error) {
      console.error(`Failed to process offline operation ${item.id}:`, error);
      
      // If it has been retried less than 3 times, keep it in the queue
      if (item.retries < 3) {
        newQueue.push({
          ...item,
          retries: item.retries + 1
        });
      } else {
        failed++;
        toast.error(`Failed to process an offline operation after multiple attempts`);
      }
    }
  }
  
  // Save the updated queue with only failed operations
  saveOfflineQueue(newQueue);
  
  if (successful > 0) {
    toast.success(`Successfully processed ${successful} offline operations`);
  }
  
  if (newQueue.length > 0) {
    toast.error(`${newQueue.length} operations remain in the offline queue`);
  }
  
  return { successful, failed };
}

/**
 * Execute an operation with offline support
 * If online, executes immediately
 * If offline, queues for later execution
 */
export async function executeWithOfflineSupport<T>(
  operation: string,
  params: any,
  onlineFn: () => Promise<T>,
  isOnline: boolean
): Promise<T | null> {
  if (isOnline) {
    try {
      // Execute online with retry capability
      return await executeWithRetry(onlineFn, 3);
    } catch (error) {
      console.error(`Online operation failed (${operation}):`, error);
      
      // If it's likely a network issue, queue for later
      if (
        error instanceof Error &&
        (error.message.includes('network') ||
         error.message.includes('timeout') ||
         error.message.includes('connection'))
      ) {
        queueOfflineOperation(operation, params);
        return null;
      }
      
      // Re-throw other errors
      throw error;
    }
  } else {
    // Offline mode - queue the operation
    queueOfflineOperation(operation, params);
    return null;
  }
} 