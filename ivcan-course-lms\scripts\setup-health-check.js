// This script sets up the health check table in Supabase
const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Replace these with your Supabase URL and service key
const SUPABASE_URL = "https://jibspqwieubavucdtccv.supabase.co";
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY || "";

if (!SUPABASE_SERVICE_KEY) {
  console.error("Error: SUPABASE_SERVICE_KEY environment variable is not set");
  console.error("Please set it to your Supabase service role key");
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function setupHealthCheck() {
  try {
    console.log("Setting up health check table...");
    
    // Read the SQL file
    const sqlPath = path.join(__dirname, 'create-health-check.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // Execute the SQL
    const { error } = await supabase.rpc('create_health_check_table');
    
    if (error) {
      // The function might not exist yet, so we need to create it first
      console.log("Creating function and table...");
      const { error: sqlError } = await supabase.sql(sql);
      
      if (sqlError) {
        console.error("Error creating function:", sqlError);
        return;
      }
      
      // Now try to call the function
      const { error: rpcError } = await supabase.rpc('create_health_check_table');
      
      if (rpcError) {
        console.error("Error calling function:", rpcError);
        return;
      }
    }
    
    console.log("Health check table set up successfully!");
  } catch (error) {
    console.error("Error setting up health check:", error);
  }
}

setupHealthCheck();
