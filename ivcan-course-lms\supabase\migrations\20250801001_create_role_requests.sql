-- Create a table to track role requests
CREATE TABLE IF NOT EXISTS public.role_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users NOT NULL,
  requested_role TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending',
  requested_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  processed_at TIMESTAMP WITH TIME ZONE,
  processed_by UUID REFERENCES auth.users,
  notes TEXT
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS role_requests_user_id_idx ON public.role_requests (user_id);
CREATE INDEX IF NOT EXISTS role_requests_status_idx ON public.role_requests (status);

-- Enable Row Level Security
ALTER TABLE public.role_requests ENABLE ROW LEVEL SECURITY;

-- Create policies for role_requests table
CREATE POLICY "Users can view their own role requests" 
ON public.role_requests FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Teachers can view all role requests" 
ON public.role_requests FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can update role requests" 
ON public.role_requests FOR UPDATE 
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- Create a function to approve a role request
CREATE OR REPLACE FUNCTION public.approve_role_request(
  _request_id UUID,
  _notes TEXT DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  _user_id UUID;
  _requested_role TEXT;
BEGIN
  -- Check if the request exists and is pending
  SELECT user_id, requested_role INTO _user_id, _requested_role
  FROM public.role_requests
  WHERE id = _request_id AND status = 'pending';
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Role request not found or not pending';
  END IF;
  
  -- Update the request status
  UPDATE public.role_requests
  SET 
    status = 'approved',
    processed_at = now(),
    processed_by = auth.uid(),
    notes = _notes
  WHERE id = _request_id;
  
  -- Assign the requested role to the user
  IF EXISTS (SELECT 1 FROM public.user_roles WHERE user_id = _user_id) THEN
    -- Update existing role
    UPDATE public.user_roles
    SET role = _requested_role
    WHERE user_id = _user_id;
  ELSE
    -- Insert new role
    INSERT INTO public.user_roles (user_id, role)
    VALUES (_user_id, _requested_role);
  END IF;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE;
END;
$$;

-- Create a function to reject a role request
CREATE OR REPLACE FUNCTION public.reject_role_request(
  _request_id UUID,
  _notes TEXT DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the request exists and is pending
  IF NOT EXISTS (
    SELECT 1 FROM public.role_requests
    WHERE id = _request_id AND status = 'pending'
  ) THEN
    RAISE EXCEPTION 'Role request not found or not pending';
  END IF;
  
  -- Update the request status
  UPDATE public.role_requests
  SET 
    status = 'rejected',
    processed_at = now(),
    processed_by = auth.uid(),
    notes = _notes
  WHERE id = _request_id;
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RAISE;
END;
$$;

-- Grant permissions to use these functions
GRANT EXECUTE ON FUNCTION public.approve_role_request TO authenticated;
GRANT EXECUTE ON FUNCTION public.reject_role_request TO authenticated;

-- Add the role_requests table to the realtime publication
ALTER PUBLICATION supabase_realtime ADD TABLE public.role_requests;

-- Set up replica identity for realtime
ALTER TABLE public.role_requests REPLICA IDENTITY FULL;
