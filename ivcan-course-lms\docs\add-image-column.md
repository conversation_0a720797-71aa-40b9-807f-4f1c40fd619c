# Adding the image_url Column to the Database

If you're seeing the error "Database error: Could not find the 'image_url' column of 'courses' in the schema cache", you need to add the `image_url` column to your database. Here are several ways to do this:

## Option 1: Using the Supabase Dashboard (Easiest)

1. Log in to your Supabase dashboard at https://app.supabase.com/
2. Select your project
3. Go to the "Table Editor" in the left sidebar
4. Find the "courses" table
5. Click on "Edit" or the pencil icon
6. Click "Add Column"
7. Enter the following details:
   - Name: `image_url`
   - Type: `text`
   - Default Value: leave empty
   - Is Nullable: Yes
8. Click "Save" or "Add Column"

## Option 2: Using SQL in the Supabase Dashboard

1. Log in to your Supabase dashboard
2. Go to the "SQL Editor" in the left sidebar
3. Create a new query
4. Enter the following SQL:
   ```sql
   ALTER TABLE public.courses ADD COLUMN IF NOT EXISTS image_url TEXT;
   ```
5. Click "Run" or "Execute"

## Option 3: Using the Migration Script

We've included a migration script in the project. To run it:

1. Make sure you have the Supabase CLI installed
2. Navigate to your project directory in the terminal
3. Run:
   ```bash
   supabase migration up
   ```

## Option 4: Using the JavaScript Helper

We've included a JavaScript helper script that can add the column:

1. Navigate to your project directory in the terminal
2. Run:
   ```bash
   node scripts/add-image-url-column.js
   ```

## Verifying the Column Was Added

After adding the column, you can verify it was added successfully by:

1. Going to the "Table Editor" in the Supabase dashboard
2. Selecting the "courses" table
3. Checking that the `image_url` column appears in the list of columns

Once the column is added, you should be able to upload images to courses without any errors.
