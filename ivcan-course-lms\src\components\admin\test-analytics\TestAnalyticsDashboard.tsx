import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Loader2, Users, FileText, Search, Eye, Download, BarChart3 } from 'lucide-react';
import { getTestAnalyticsOverview, getOverallAnalyticsExport } from '@/services/test-analytics/testAnalyticsService';
import { StudentTestAnalytics, OverallAnalyticsExport } from '@/types/test-analytics';
import { format } from 'date-fns';
import StudentTestDetail from './StudentTestDetail';
import { generateOverallAnalyticsPDF } from './OverallAnalyticsPDF';
import OverallAnalyticsPreview from './OverallAnalyticsPreview';

const TestAnalyticsDashboard: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStudent, setSelectedStudent] = useState<StudentTestAnalytics | null>(null);
  const [selectedResponseId, setSelectedResponseId] = useState<string | null>(null);
  const [isGeneratingOverallReport, setIsGeneratingOverallReport] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [previewData, setPreviewData] = useState<OverallAnalyticsExport | null>(null);

  const { data: analytics, isLoading, error } = useQuery({
    queryKey: ['test-analytics-overview'],
    queryFn: getTestAnalyticsOverview,
    refetchOnWindowFocus: false
  });

  const filteredStudents = analytics?.studentsWithTests.filter(student =>
    student.student.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.student.email.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const handleViewStudentDetail = (student: StudentTestAnalytics, responseId: string) => {
    setSelectedStudent(student);
    setSelectedResponseId(responseId);
  };

  const handleBackToList = () => {
    setSelectedStudent(null);
    setSelectedResponseId(null);
  };

  const handleDownloadOverallAnalytics = async () => {
    console.log('Download Overall Analytics button clicked!');
    setIsGeneratingOverallReport(true);
    try {
      console.log('Calling getOverallAnalyticsExport...');
      const overallData = await getOverallAnalyticsExport();
      console.log('Received overall data:', overallData);
      setPreviewData(overallData);
      setShowPreviewModal(true);
    } catch (error) {
      console.error('Error generating overall analytics:', error);
    } finally {
      setIsGeneratingOverallReport(false);
    }
  };

  const handleConfirmDownload = async () => {
    if (previewData) {
      setShowPreviewModal(false);
      try {
        console.log('Starting PDF generation...');
        await generateOverallAnalyticsPDF(previewData);
        console.log('PDF generation completed!');
      } catch (error) {
        console.error('PDF generation failed:', error);
      }
      setPreviewData(null);
    }
  };

  const handleCancelPreview = () => {
    setShowPreviewModal(false);
    setPreviewData(null);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="w-8 h-8 animate-spin text-primary" />
        <span className="ml-2">Loading test analytics...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8">
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="text-destructive">Error Loading Analytics</CardTitle>
            <CardDescription>
              Failed to load test analytics data. Please try again.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  if (selectedStudent && selectedResponseId) {
    return (
      <StudentTestDetail
        responseId={selectedResponseId}
        onBack={handleBackToList}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Overall Analytics Button */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Test Analytics Dashboard</h2>
          <p className="text-muted-foreground">Comprehensive analysis of student test responses</p>
        </div>
        <Button
          onClick={handleDownloadOverallAnalytics}
          disabled={isGeneratingOverallReport || isLoading || !analytics}
          className="flex items-center gap-2"
        >
          {isGeneratingOverallReport ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <BarChart3 className="h-4 w-4" />
          )}
          {isGeneratingOverallReport ? 'Generating Report...' : 'Download Overall Analytics'}
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics?.totalStudents || 0}</div>
            <p className="text-xs text-muted-foreground">
              Students who have completed tests
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Test Submissions</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics?.totalTestsCompleted || 0}</div>
            <p className="text-xs text-muted-foreground">
              Across all students and modules
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Tests per Student</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics?.totalStudents ? 
                Math.round((analytics.totalTestsCompleted / analytics.totalStudents) * 10) / 10 : 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Tests completed per student
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Student Test Records</CardTitle>
          <CardDescription>
            View detailed test responses for individual students
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search students by name or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
          </div>

          {/* Students List */}
          <div className="space-y-4">
            {filteredStudents.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                {searchTerm ? 'No students found matching your search.' : 'No test submissions found.'}
              </div>
            ) : (
              filteredStudents.map((studentData) => (
                <Card key={studentData.student.id} className="border-l-4 border-l-primary">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-lg">{studentData.student.fullName}</CardTitle>
                        <CardDescription>{studentData.student.email}</CardDescription>
                      </div>
                      <Badge variant="secondary">
                        {studentData.totalTestsCompleted} test{studentData.totalTestsCompleted !== 1 ? 's' : ''}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {studentData.testSubmissions.map((submission) => (
                        <div key={submission.id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                          <div className="flex-1">
                            <div className="font-medium">{submission.testTitle}</div>
                            <div className="text-sm text-muted-foreground">
                              {submission.courseTitle} • {submission.moduleTitle}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              Submitted: {format(new Date(submission.submittedAt), 'MMM dd, yyyy HH:mm')}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge variant={submission.testType === 'pre_test' ? 'default' : 'secondary'}>
                              {submission.testType === 'pre_test' ? 'Pre-Test' : 'Post-Test'}
                            </Badge>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleViewStudentDetail(studentData, submission.id)}
                            >
                              <Eye className="h-4 w-4 mr-1" />
                              View Details
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Preview Modal */}
      <Dialog open={showPreviewModal} onOpenChange={setShowPreviewModal}>
        <DialogContent className="max-w-4xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>Overall Analytics Preview</DialogTitle>
            <DialogDescription>
              Review the analytics summary before downloading the full PDF report
            </DialogDescription>
          </DialogHeader>

          {previewData && <OverallAnalyticsPreview data={previewData} />}

          <DialogFooter>
            <Button variant="outline" onClick={handleCancelPreview}>
              Cancel
            </Button>
            <Button onClick={handleConfirmDownload} className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Download Full PDF Report
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TestAnalyticsDashboard;
