/**
 * Test suite for YouTube functionality in SimpleMarkdownEditor
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { SimpleMarkdownEditor } from '@/components/ui/simple-markdown-editor';

// Mock dependencies
vi.mock('@/lib/tiptap-image-upload', () => ({
  uploadEditorImage: vi.fn().mockResolvedValue('https://example.com/image.jpg'),
}));

vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn()
  }
}));

vi.mock('@/lib/enhanced-markdown-serializer', () => ({
  tiptapToMarkdown: vi.fn().mockReturnValue('# Test Content'),
  htmlToMarkdown: vi.fn().mockReturnValue('# Test Content'),
}));

vi.mock('@/lib/content-converter', () => ({
  markdownToHtml: vi.fn().mockReturnValue('<h1>Test Content</h1>'),
}));

describe('SimpleMarkdownEditor YouTube Functionality', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render YouTube button in toolbar', () => {
    render(<SimpleMarkdownEditor />);
    
    const youtubeButton = screen.getByTitle('Add YouTube Video');
    expect(youtubeButton).toBeInTheDocument();
  });

  it('should open YouTube dialog when button is clicked', async () => {
    const user = userEvent.setup();
    render(<SimpleMarkdownEditor />);
    
    const youtubeButton = screen.getByTitle('Add YouTube Video');
    await user.click(youtubeButton);
    
    expect(screen.getByText('Insert YouTube Video')).toBeInTheDocument();
    expect(screen.getByLabelText('YouTube URL')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('https://www.youtube.com/watch?v=...')).toBeInTheDocument();
  });

  it('should close dialog when cancel is clicked', async () => {
    const user = userEvent.setup();
    render(<SimpleMarkdownEditor />);
    
    // Open dialog
    const youtubeButton = screen.getByTitle('Add YouTube Video');
    await user.click(youtubeButton);
    
    // Close dialog
    const cancelButton = screen.getByText('Cancel');
    await user.click(cancelButton);
    
    expect(screen.queryByText('Insert YouTube Video')).not.toBeInTheDocument();
  });

  it('should handle YouTube URL input', async () => {
    const user = userEvent.setup();
    render(<SimpleMarkdownEditor />);
    
    // Open dialog
    const youtubeButton = screen.getByTitle('Add YouTube Video');
    await user.click(youtubeButton);
    
    // Enter URL
    const urlInput = screen.getByLabelText('YouTube URL');
    await user.type(urlInput, 'https://www.youtube.com/watch?v=dQw4w9WgXcQ');
    
    expect(urlInput).toHaveValue('https://www.youtube.com/watch?v=dQw4w9WgXcQ');
  });

  it('should disable insert button when URL is empty', async () => {
    const user = userEvent.setup();
    render(<SimpleMarkdownEditor />);
    
    // Open dialog
    const youtubeButton = screen.getByTitle('Add YouTube Video');
    await user.click(youtubeButton);
    
    const insertButton = screen.getByText('Insert Video');
    expect(insertButton).toBeDisabled();
  });

  it('should enable insert button when URL is provided', async () => {
    const user = userEvent.setup();
    render(<SimpleMarkdownEditor />);
    
    // Open dialog
    const youtubeButton = screen.getByTitle('Add YouTube Video');
    await user.click(youtubeButton);
    
    // Enter URL
    const urlInput = screen.getByLabelText('YouTube URL');
    await user.type(urlInput, 'https://www.youtube.com/watch?v=dQw4w9WgXcQ');
    
    const insertButton = screen.getByText('Insert Video');
    expect(insertButton).not.toBeDisabled();
  });

  it('should call onChange when content is modified', () => {
    const mockOnChange = vi.fn();
    render(<SimpleMarkdownEditor onChange={mockOnChange} />);
    
    // The onChange should be called during editor initialization
    expect(mockOnChange).toHaveBeenCalled();
  });

  it('should handle initial content properly', () => {
    const initialContent = '# Test Content\n\nThis is test content.';
    render(<SimpleMarkdownEditor initialContent={initialContent} />);
    
    // Editor should be rendered with content
    expect(screen.getByRole('textbox')).toBeInTheDocument();
  });

  it('should switch between editor and preview tabs', async () => {
    const user = userEvent.setup();
    render(<SimpleMarkdownEditor />);
    
    // Should start in editor mode
    expect(screen.getByText('Write')).toBeInTheDocument();
    expect(screen.getByText('Preview')).toBeInTheDocument();
    
    // Switch to preview
    await user.click(screen.getByText('Preview'));
    
    // Should show preview content
    expect(screen.getByRole('tabpanel')).toBeInTheDocument();
  });
});
