import * as React from "react"
import * as AccordionPrimitive from "@radix-ui/react-accordion"
import { Plus, Minus } from "lucide-react"

import { cn } from "@/lib/utils"

const Accordion = AccordionPrimitive.Root

const AccordionItem = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>
>(({ className, ...props }, ref) => (
  <AccordionPrimitive.Item
    ref={ref}
    className={cn(
      "border border-border/20 rounded-xl bg-card/80 backdrop-blur-sm shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden",
      "hover:border-primary/20 hover:bg-card/90",
      className
    )}
    {...props}
  />
))
AccordionItem.displayName = "AccordionItem"

const AccordionTrigger = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>
>(({ className, children, ...props }, ref) => (
  <AccordionPrimitive.Header className="flex">
    <AccordionPrimitive.Trigger
      ref={ref}
      className={cn(
        "flex flex-1 items-center justify-between px-6 py-5 font-semibold text-left",
        "bg-gradient-to-r from-primary/3 to-primary/6 hover:from-primary/6 hover:to-primary/10",
        "transition-all duration-300 ease-out",
        "hover:bg-primary/5 focus:bg-primary/8 focus:outline-none focus:ring-2 focus:ring-primary/20",
        "group cursor-pointer text-foreground/90 hover:text-foreground",
        "[&[data-state=open]]:bg-primary/8 [&[data-state=open]]:text-foreground",
        className
      )}
      {...props}
    >
      <span className="flex items-center gap-3 text-sm font-medium">
        {children}
      </span>
      <div className="h-5 w-5 shrink-0 transition-all duration-300 text-primary/60 group-hover:text-primary">
        <Plus className="h-5 w-5 group-data-[state=open]:hidden" />
        <Minus className="h-5 w-5 hidden group-data-[state=open]:block" />
      </div>
    </AccordionPrimitive.Trigger>
  </AccordionPrimitive.Header>
))
AccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName

const AccordionContent = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <AccordionPrimitive.Content
    ref={ref}
    className="overflow-hidden transition-all duration-300 ease-out data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
    {...props}
  >
    <div className={cn(
      "px-6 pb-6 pt-3",
      "bg-gradient-to-b from-background/30 to-background/60",
      "border-t border-border/10",
      className
    )}>
      <div className="prose prose-sm max-w-none text-foreground/80">
        {children}
      </div>
    </div>
  </AccordionPrimitive.Content>
))

AccordionContent.displayName = AccordionPrimitive.Content.displayName

export { Accordion, AccordionItem, AccordionTrigger, AccordionContent }
