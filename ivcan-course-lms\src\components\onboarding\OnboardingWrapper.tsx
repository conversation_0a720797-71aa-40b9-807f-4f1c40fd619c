import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { OnboardingFlow } from './OnboardingFlow';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { hasUserCompletedDemographics } from '@/services/demographicApi';

interface OnboardingWrapperProps {
  children: React.ReactNode;
}

export function OnboardingWrapper({ children }: OnboardingWrapperProps) {
  const { user } = useAuth();
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [checkingStatus, setCheckingStatus] = useState(true);

  useEffect(() => {
    const checkOnboardingStatus = async () => {
      if (!user) {
        setCheckingStatus(false);
        return;
      }

      try {
        setCheckingStatus(true);

        // Check if onboarding is completed in user metadata
        const hasCompletedOnboarding = user.user_metadata?.onboarding_completed === true;

        // Also check localStorage as a fallback
        const localStorageCompleted = localStorage.getItem('onboarding_completed') === 'true';

        // Check if demographic questionnaire is completed
        const hasCompletedDemographicQuestionnaire = await hasUserCompletedDemographics(user.id);

        // User must complete both onboarding AND demographic questionnaire
        if ((hasCompletedOnboarding || localStorageCompleted) && hasCompletedDemographicQuestionnaire) {
          setShowOnboarding(false);
        } else {
          // Show onboarding if either is incomplete
          setShowOnboarding(true);
        }
      } catch (error) {
        console.error('Error checking onboarding status:', error);
        // Default to showing onboarding if there's an error (safer approach)
        setShowOnboarding(true);
      } finally {
        setCheckingStatus(false);
      }
    };

    checkOnboardingStatus();
  }, [user]);

  const handleOnboardingComplete = async () => {
    setShowOnboarding(false);
    toast.success('Welcome to IVCan Study!', {
      description: 'Your profile has been set up successfully.',
    });
  };

  // If still checking status or no user, just render children
  if (checkingStatus || !user) {
    return <>{children}</>;
  }

  return (
    <>
      {showOnboarding && <OnboardingFlow onComplete={handleOnboardingComplete} />}
      {children}
    </>
  );
}
