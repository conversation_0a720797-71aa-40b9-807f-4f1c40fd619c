import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Layout from '../components/Layout';
import { useAuth } from '@/context/AuthContext';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Bell,
  User,
  Shield,
  Settings,
} from 'lucide-react';
import { toast } from 'sonner';
// Remove framer-motion to improve performance
import { type Profile as ProfileType, getProfile, updateProfile } from '@/lib/profile';
import { ProfileHeader } from '@/components/profile/ProfileHeader';
import { AccountSection } from '@/components/profile/AccountSection';
import { SecuritySection } from '@/components/profile/SecuritySection';
import { NotificationsSection } from '@/components/profile/NotificationsSection';
import AutoCompletionSetting from '@/components/settings/AutoCompletionSetting';
import { supabase } from '@/integrations/supabase/client';

const Profile = () => {
  const { user, signOut } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [profile, setProfile] = useState<ProfileType | null>(null);

  useEffect(() => {
    if (!user) {
      navigate('/login');
      return;
    }

    async function loadProfileData() {
      try {
        setLoading(true);
        const profileData = await getProfile(user.id);
        setProfile(profileData);
      } catch (error: any) {
        toast.error('Error loading profile data');
        console.error(error);
      } finally {
        setLoading(false);
      }
    }

    loadProfileData();
  }, [user, navigate]);

  const handleSignOut = async () => {
    try {
      console.log('Profile: Starting sign out');
      if (signOut) {
        await signOut();
      } else {
        toast.error('Sign out function is not available');
        console.error('signOut function is undefined');
      }
      console.log('Profile: Sign out completed');
    } catch (error) {
      console.error('Profile: Error during sign out:', error);
      toast.error('Failed to sign out. Please try again.');
    }
  };

  const handleAvatarUpdate = async (avatarUrl: string) => {
    try {
      // Update the profile in the database
      if (user) {
        await updateProfile(user.id, { avatar_url: avatarUrl });

        // Update the local state
        setProfile(prev => prev ? { ...prev, avatar_url: avatarUrl } : null);

        // Update the user metadata in Supabase auth
        await supabase.auth.updateUser({
          data: { avatar_url: avatarUrl }
        });

        toast.success('Profile picture updated successfully');
      }
    } catch (error) {
      console.error('Error updating avatar:', error);
      toast.error('Failed to update profile picture');
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-96">
          <div className="animate-pulse">Loading profile...</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto max-w-6xl py-8 px-4">
        {/* Header Section */}
        <ProfileHeader
          firstName={profile?.first_name}
          lastName={profile?.last_name}
          email={user?.email}
          avatarUrl={profile?.avatar_url}
          onSignOut={handleSignOut}
          onAvatarUpdate={handleAvatarUpdate}
        />

        {/* Tabs Section */}
        <Tabs defaultValue="account" className="space-y-6">
          <TabsList>
            <TabsTrigger value="account" className="flex items-center gap-2">
              <User className="w-4 h-4" />
              Account
            </TabsTrigger>
            <TabsTrigger value="security" className="flex items-center gap-2">
              <Shield className="w-4 h-4" />
              Security
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Settings className="w-4 h-4" />
              Settings
            </TabsTrigger>
            <TabsTrigger value="notifications" className="flex items-center gap-2">
              <Bell className="w-4 h-4" />
              Notifications
            </TabsTrigger>
          </TabsList>

          <TabsContent value="account">
            <AccountSection email={user?.email} />
          </TabsContent>

          <TabsContent value="security">
            <SecuritySection />
          </TabsContent>

          <TabsContent value="settings">
            <div className="space-y-6">
              <AutoCompletionSetting />
            </div>
          </TabsContent>

          <TabsContent value="notifications">
            <NotificationsSection />
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
};

export default Profile;
