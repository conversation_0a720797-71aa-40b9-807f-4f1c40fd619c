-- Fix RLS policies for user_roles table

-- First, make sure the user_roles table exists
CREATE TABLE IF NOT EXISTS public.user_roles (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  role TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(user_id)
);

-- Enable Row Level Security
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Anyone can view user_roles" ON public.user_roles;
DROP POLICY IF EXISTS "Teachers can insert user_roles" ON public.user_roles;
DROP POLICY IF EXISTS "Teachers can update user_roles" ON public.user_roles;
DROP POLICY IF EXISTS "Teachers can delete user_roles" ON public.user_roles;

-- Create new policies
CREATE POLICY "Anyone can view user_roles" 
ON public.user_roles FOR SELECT 
USING (true);

CREATE POLICY "Teachers can insert user_roles" 
ON public.user_roles FOR INSERT 
WITH CHECK (
  auth.uid() = user_id OR
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can update user_roles" 
ON public.user_roles FOR UPDATE 
USING (
  auth.uid() = user_id OR
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can delete user_roles" 
ON public.user_roles FOR DELETE 
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- Create a function to check if a user has a specific role
CREATE OR REPLACE FUNCTION public.has_role(
  _user_id UUID,
  _role TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM public.user_roles
    WHERE user_id = _user_id AND role = _role
  );
END;
$$;

-- Grant permissions to use this function
GRANT EXECUTE ON FUNCTION public.has_role TO authenticated;

-- Create a function to assign a role to a user
CREATE OR REPLACE FUNCTION public.assign_role(
  _user_id UUID,
  _role TEXT
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the user already has a role
  IF EXISTS (SELECT 1 FROM public.user_roles WHERE user_id = _user_id) THEN
    -- Update the existing role
    UPDATE public.user_roles
    SET role = _role
    WHERE user_id = _user_id;
  ELSE
    -- Insert a new role
    INSERT INTO public.user_roles (user_id, role)
    VALUES (_user_id, _role);
  END IF;
END;
$$;

-- Grant permissions to use this function
GRANT EXECUTE ON FUNCTION public.assign_role TO authenticated;

-- Add the user_roles table to the realtime publication
ALTER PUBLICATION supabase_realtime ADD TABLE public.user_roles;

-- Set up replica identity for realtime
ALTER TABLE public.user_roles REPLICA IDENTITY FULL;

-- Create a policy for bypassing RLS for service role
CREATE POLICY "Service role bypass" 
ON public.user_roles 
USING (auth.jwt() ->> 'role' = 'service_role');

-- Create a policy for bypassing RLS for service role on lessons
CREATE POLICY "Service role bypass" 
ON public.lessons 
USING (auth.jwt() ->> 'role' = 'service_role');

-- Create a policy for bypassing RLS for service role on modules
CREATE POLICY "Service role bypass" 
ON public.modules 
USING (auth.jwt() ->> 'role' = 'service_role');

-- Create a policy for bypassing RLS for service role on courses
CREATE POLICY "Service role bypass" 
ON public.courses 
USING (auth.jwt() ->> 'role' = 'service_role');
