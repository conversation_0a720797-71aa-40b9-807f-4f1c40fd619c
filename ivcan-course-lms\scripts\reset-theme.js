/**
 * This script resets the theme to the default Red color scheme
 * It clears any saved theme preferences in localStorage
 */

// Function to reset theme when run in the browser
function resetThemeInBrowser() {
  console.log('Resetting theme to default Red color scheme...');
  
  // Clear any saved theme preferences
  localStorage.removeItem('selectedTheme');
  
  // Set the theme CSS variables directly
  const root = document.documentElement;
  
  // Red theme values
  root.style.setProperty('--primary', 'hsl(0 70% 50%)'); // Red #E63946
  root.style.setProperty('--primary-light', 'hsl(0 70% 60%)');
  root.style.setProperty('--primary-dark', 'hsl(0 70% 40%)');
  root.style.setProperty('--accent', 'hsl(0 60% 30%)');
  
  console.log('Theme has been reset to Red');
}

// If running in Node.js environment
if (typeof window === 'undefined') {
  console.log('This script is meant to be run in the browser.');
  console.log('To reset the theme:');
  console.log('1. Open your browser console on the application');
  console.log('2. Copy and paste the following code:');
  console.log(`
  // Reset theme to default Red
  localStorage.removeItem('selectedTheme');
  document.documentElement.style.setProperty('--primary', 'hsl(0 70% 50%)');
  document.documentElement.style.setProperty('--primary-light', 'hsl(0 70% 60%)');
  document.documentElement.style.setProperty('--primary-dark', 'hsl(0 70% 40%)');
  document.documentElement.style.setProperty('--accent', 'hsl(0 60% 30%)');
  console.log('Theme reset to Red');
  `);
} else {
  // Running in browser
  resetThemeInBrowser();
}
