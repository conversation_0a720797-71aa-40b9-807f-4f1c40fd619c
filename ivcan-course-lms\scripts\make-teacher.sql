-- SQL script to make a user a teacher
-- Replace the user_id with the actual user ID
-- Run this in the Supabase SQL Editor

-- Check if the user exists in auth.users
DO $$
DECLARE
  user_exists BOOLEAN;
  user_id UUID := 'f09b18f8-045d-46a9-bfa8-fb346a2acc07'; -- Replace with your user ID
BEGIN
  -- Check if user exists
  SELECT EXISTS(
    SELECT 1 FROM auth.users WHERE id = user_id
  ) INTO user_exists;
  
  IF NOT user_exists THEN
    RAISE EXCEPTION 'User with ID % does not exist', user_id;
  END IF;
  
  -- Check if user already has a role
  IF EXISTS(SELECT 1 FROM public.user_roles WHERE user_id = user_id) THEN
    -- Update existing role to teacher
    UPDATE public.user_roles
    SET role = 'teacher'
    WHERE user_id = user_id;
    
    RAISE NOTICE 'Updated user % role to teacher', user_id;
  ELSE
    -- Insert new role
    INSERT INTO public.user_roles (user_id, role)
    VALUES (user_id, 'teacher');
    
    RAISE NOTICE 'Added teacher role to user %', user_id;
  END IF;
END $$;
