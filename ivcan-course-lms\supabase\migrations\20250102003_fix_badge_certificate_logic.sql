-- Migration: Fix Badge and Certificate Logic
-- Created: 2025-01-02
-- Description: Separate badge awarding from certificate generation and fix completion logic

-- =============================================
-- UPDATE ENROLLMENT STATUS OPTIONS
-- =============================================

-- Add new status option for when all modules are completed but course not finished
-- This allows us to differentiate between module completion and certificate generation
ALTER TABLE public.user_course_enrollment 
DROP CONSTRAINT IF EXISTS user_course_enrollment_status_check;

ALTER TABLE public.user_course_enrollment 
ADD CONSTRAINT user_course_enrollment_status_check 
CHECK (status IN ('enrolled', 'in_progress', 'modules_completed', 'completed', 'dropped'));

-- =============================================
-- UPDATE COURSE COMPLETION LOGIC
-- =============================================

-- Update the complete_course function to handle the new workflow
CREATE OR REPLACE FUNCTION public.complete_course(
  p_user_id UUID,
  p_course_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_now TIMESTAMP WITH TIME ZONE := NOW();
  v_modules_count INTEGER;
  v_completed_modules_count INTEGER;
BEGIN
  -- Log the function call
  RAISE NOTICE 'complete_course called for user % and course %', p_user_id, p_course_id;

  -- Validate input parameters
  IF p_user_id IS NULL OR p_course_id IS NULL THEN
    RAISE NOTICE 'Invalid parameters: user_id=%, course_id=%', p_user_id, p_course_id;
    RETURN FALSE;
  END IF;

  -- Check if all modules in the course are completed
  SELECT COUNT(*) INTO v_modules_count
  FROM public.modules
  WHERE course_id = p_course_id;

  SELECT COUNT(*) INTO v_completed_modules_count
  FROM public.user_module_progress ump
  JOIN public.modules m ON ump.module_id = m.id
  WHERE ump.user_id = p_user_id 
    AND m.course_id = p_course_id 
    AND ump.is_completed = TRUE;

  RAISE NOTICE 'Course % has % modules, user % has completed %', 
    p_course_id, v_modules_count, p_user_id, v_completed_modules_count;

  -- Only allow course completion if all modules are completed
  IF v_completed_modules_count < v_modules_count THEN
    RAISE NOTICE 'Cannot complete course: only % of % modules completed', 
      v_completed_modules_count, v_modules_count;
    RETURN FALSE;
  END IF;

  -- Update enrollment status to completed (this generates the certificate)
  INSERT INTO public.user_course_enrollment (
    user_id, 
    course_id, 
    status, 
    enrolled_at, 
    completed_at, 
    created_at, 
    updated_at
  )
  VALUES (
    p_user_id, 
    p_course_id, 
    'completed', 
    v_now, 
    v_now, 
    v_now, 
    v_now
  )
  ON CONFLICT (user_id, course_id) 
  DO UPDATE SET
    status = 'completed',
    completed_at = v_now,
    updated_at = v_now;

  -- Also ensure there's a progress record
  INSERT INTO public.user_course_progress (
    user_id, 
    course_id, 
    hours_spent, 
    last_accessed_at, 
    created_at, 
    updated_at
  )
  VALUES (
    p_user_id, 
    p_course_id, 
    0, 
    v_now, 
    v_now, 
    v_now
  )
  ON CONFLICT (user_id, course_id) 
  DO UPDATE SET
    last_accessed_at = v_now,
    updated_at = v_now;

  RAISE NOTICE 'Successfully completed course % for user %', p_course_id, p_user_id;
  RETURN TRUE;

EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in complete_course: %', SQLERRM;
    RETURN FALSE;
END;
$$;

-- =============================================
-- CREATE FUNCTION TO MARK MODULES AS COMPLETED
-- =============================================

-- Function to mark all modules as completed (sets status to modules_completed)
CREATE OR REPLACE FUNCTION public.mark_modules_completed(
  p_user_id UUID,
  p_course_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_now TIMESTAMP WITH TIME ZONE := NOW();
  v_modules_count INTEGER;
  v_completed_modules_count INTEGER;
BEGIN
  -- Check if all modules in the course are completed
  SELECT COUNT(*) INTO v_modules_count
  FROM public.modules
  WHERE course_id = p_course_id;

  SELECT COUNT(*) INTO v_completed_modules_count
  FROM public.user_module_progress ump
  JOIN public.modules m ON ump.module_id = m.id
  WHERE ump.user_id = p_user_id 
    AND m.course_id = p_course_id 
    AND ump.is_completed = TRUE;

  -- Only mark as modules_completed if all modules are actually completed
  IF v_completed_modules_count = v_modules_count THEN
    -- Update enrollment status to modules_completed
    INSERT INTO public.user_course_enrollment (
      user_id, 
      course_id, 
      status, 
      enrolled_at, 
      created_at, 
      updated_at
    )
    VALUES (
      p_user_id, 
      p_course_id, 
      'modules_completed', 
      v_now, 
      v_now, 
      v_now
    )
    ON CONFLICT (user_id, course_id) 
    DO UPDATE SET
      status = 'modules_completed',
      updated_at = v_now;

    RETURN TRUE;
  END IF;

  RETURN FALSE;
END;
$$;

-- =============================================
-- GRANT PERMISSIONS
-- =============================================

-- Grant execute permissions for new functions
GRANT EXECUTE ON FUNCTION public.complete_course(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.mark_modules_completed(UUID, UUID) TO authenticated;

-- =============================================
-- COMMENTS FOR DOCUMENTATION
-- =============================================

COMMENT ON FUNCTION public.complete_course(UUID, UUID) IS 'Complete a course for a user and generate certificate (only when all modules are done)';
COMMENT ON FUNCTION public.mark_modules_completed(UUID, UUID) IS 'Mark all modules as completed for a course (sets status to modules_completed)';
