# Storage Setup Guide

This guide explains how to set up the storage buckets required for the application.

## Required Storage Buckets

The application uses the following storage buckets:

1. `course-images` - For storing course images
2. `avatars` - For storing user profile pictures
3. `app-uploads` - General purpose uploads

## Automatic Setup

You can run the provided script to automatically create these buckets:

```bash
node scripts/create-storage-buckets.js
```

## Manual Setup

If you prefer to set up the buckets manually, follow these steps:

1. Log in to your Supabase dashboard
2. Navigate to the Storage section
3. Create the following buckets:
   - `course-images` (set to public)
   - `avatars` (set to public)
   - `app-uploads` (set to public)

## Bucket Permissions

Make sure the buckets have the appropriate permissions:

1. For anonymous users (if you want to allow public access):
   - Select the bucket
   - Go to "Policies"
   - Add a policy for SELECT operations with the condition `true` to allow anyone to view files
   - Add a policy for INSERT operations with appropriate conditions

2. For authenticated users:
   - Add policies for INSERT, UPDATE, and DELETE operations with conditions like `auth.uid() = auth.uid()`

## Troubleshooting

If you encounter issues with storage buckets:

## "Bucket not found" Error

If you see a "bucket not found" error when trying to upload images, follow these steps:

1. **Use the Admin Storage Manager**:
   - Go to the Admin Dashboard
   - Click on the "Storage" tab
   - Click "Check Buckets" to see which buckets exist
   - Click "Create All Missing Buckets" to create any missing buckets

2. **Check Bucket Permissions**:
   - Log in to your Supabase dashboard
   - Go to Storage → Buckets
   - Make sure the required buckets exist (`course-images`, `uploads`, etc.)
   - Check that the buckets have the appropriate RLS policies

3. **Run the Setup Script**:
   - Run the script to create all required buckets:
     ```bash
     node scripts/create-storage-buckets.js
     ```

## Permission Issues

If you have permission-related errors:

1. **Check API Keys**:
   - Make sure your Supabase API keys have the necessary permissions
   - For anonymous uploads, ensure the bucket is set to public

2. **RLS Policies**:
   - Add appropriate RLS policies to allow uploads
   - Example policy for public read access:
     ```sql
     CREATE POLICY "Public Access" ON storage.objects
       FOR SELECT USING (bucket_id = 'course-images');
     ```
   - Example policy for authenticated uploads:
     ```sql
     CREATE POLICY "Authenticated Uploads" ON storage.objects
       FOR INSERT WITH CHECK (auth.role() = 'authenticated');
     ```

## Upload Failures

For general upload failures:

1. **Check Console Errors**:
   - Open your browser's developer console (F12)
   - Look for detailed error messages related to storage or uploads

2. **File Limitations**:
   - Verify file size limits (default is 50MB)
   - Ensure the file type is allowed
   - Try with a smaller or different file format

3. **Network Issues**:
   - Check your internet connection
   - Verify that you can access the Supabase project
   - Try disabling any ad blockers or security extensions
