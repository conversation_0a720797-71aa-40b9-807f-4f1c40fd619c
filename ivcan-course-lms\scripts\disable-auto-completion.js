// This script disables auto-completion for all students
// Run this script with: node scripts/disable-auto-completion.js

const { createClient } = require('@supabase/supabase-js');

// Replace these with your Supabase URL and anon key
const SUPABASE_URL = "https://jibspqwieubavucdtccv.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMxMjUzNzIsImV4cCI6MjA1ODcwMTM3Mn0.H1cvGLXWvIMibv8dYsYIvY37IRoVR80HyeF1wM93IUI";

// Create Supabase client with anon key (limited permissions)
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Try to create a service role client if environment variables are available
let serviceRoleClient = null;
if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.log('Using service role client for elevated permissions');
  serviceRoleClient = createClient(SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);
}

async function disableAutoCompletionForStudents() {
  try {
    const client = serviceRoleClient || supabase;
    console.log('Disabling auto-completion for all students...');
    
    // Step 1: Get all users who are not teachers
    console.log('Finding all non-teacher users...');
    
    // Get all users with teacher role
    const { data: teachers, error: teacherError } = await client
      .from('user_roles')
      .select('user_id')
      .eq('role', 'teacher');
    
    if (teacherError) {
      console.error('Error fetching teachers:', teacherError);
      process.exit(1);
    }
    
    // Extract teacher IDs
    const teacherIds = teachers ? teachers.map(t => t.user_id) : [];
    console.log(`Found ${teacherIds.length} teachers`);
    
    // Step 2: Get all user preferences where auto_complete_courses is true
    console.log('Finding users with auto-completion enabled...');
    const { data: preferences, error: prefError } = await client
      .from('user_preferences')
      .select('user_id, auto_complete_courses')
      .eq('auto_complete_courses', true);
    
    if (prefError) {
      console.error('Error fetching preferences:', prefError);
      process.exit(1);
    }
    
    if (!preferences || preferences.length === 0) {
      console.log('No users found with auto-completion enabled');
      return;
    }
    
    console.log(`Found ${preferences.length} users with auto-completion enabled`);
    
    // Step 3: Filter out teachers
    const studentsWithAutoCompletion = preferences.filter(p => !teacherIds.includes(p.user_id));
    
    if (studentsWithAutoCompletion.length === 0) {
      console.log('No students found with auto-completion enabled');
      return;
    }
    
    console.log(`Found ${studentsWithAutoCompletion.length} students with auto-completion enabled`);
    
    // Step 4: Disable auto-completion for all students
    console.log('Disabling auto-completion for students...');
    
    let successCount = 0;
    for (const student of studentsWithAutoCompletion) {
      const { error: updateError } = await client
        .from('user_preferences')
        .update({
          auto_complete_courses: false,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', student.user_id);
      
      if (updateError) {
        console.error(`Error updating preferences for user ${student.user_id}:`, updateError);
      } else {
        successCount++;
      }
    }
    
    console.log(`Successfully disabled auto-completion for ${successCount} out of ${studentsWithAutoCompletion.length} students`);
    
    // Step 5: Ensure all future students have auto-completion disabled by default
    console.log('Setting up default preferences for new users...');
    
    // This would typically be done with a database trigger or in the application code
    // For now, we'll just log a reminder
    console.log('IMPORTANT: Auto-completion is now disabled by default for all new users in the application code');
    
  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  }
}

// Run the function
disableAutoCompletionForStudents();
