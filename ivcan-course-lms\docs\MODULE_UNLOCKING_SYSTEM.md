# Mo<PERSON>le and Lesson Unlocking System

## Overview

The IVCAN Course LMS now implements a progressive unlocking system where:

1. **All modules (except module 1) are locked** until all lessons in the previous modules are completed
2. **All lessons within a module are locked** until the previous lesson is completed
3. **Teachers have access to all content** regardless of completion status
4. **Students must complete content sequentially** to unlock new material

## Database Schema

### New Tables

#### `user_module_access`
Tracks individual user access to modules:
```sql
CREATE TABLE public.user_module_access (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  module_id UUID REFERENCES public.modules(id) ON DELETE CASCADE,
  has_access BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(user_id, module_id)
);
```

#### `user_lesson_access`
Tracks individual user access to lessons:
```sql
CREATE TABLE public.user_lesson_access (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  lesson_id UUID REFERENCES public.lessons(id) ON DELETE CASCADE,
  has_access BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(user_id, lesson_id)
);
```

### New Database Functions

#### `check_module_access(p_user_id UUID, p_module_id UUID)`
- Returns `BOOLEAN` indicating if user should have access to a module
- Teachers always return `TRUE`
- Module 1 is always accessible
- Other modules require previous module completion

#### `check_lesson_access(p_user_id UUID, p_lesson_id UUID)`
- Returns `BOOLEAN` indicating if user should have access to a lesson
- Teachers always return `TRUE`
- Checks if module is accessible first
- First lesson in module is accessible if module is accessible
- Other lessons require previous lesson completion

#### `update_module_locks_for_user(p_user_id UUID, p_course_id UUID)`
- Updates access status for all modules/lessons for a user
- Called automatically when lessons are completed
- Updates the access tracking tables

### Triggers

Automatic triggers update access when progress changes:
- `on_lesson_progress_update_access` - Fires when lesson is completed
- `on_module_progress_update_access` - Fires when module is completed

## Frontend Implementation

### Updated Components

#### `ModuleList.tsx`
- Shows lock icons for inaccessible modules
- Prevents navigation to locked modules
- Displays lock status visually

#### `LessonItem.tsx`
- Shows lock icons for inaccessible lessons
- Prevents navigation to locked lessons
- Teachers can access all lessons regardless of lock status

#### Course API (`courseApi.ts`)
- Calculates module lock status based on user progress
- Integrates with access control service
- Updates lesson lock status

### New Services

#### `accessControlService.ts`
Provides functions for checking access:
- `checkModuleAccess(userId, moduleId)` - Check module access
- `checkLessonAccess(userId, lessonId)` - Check lesson access
- `getCourseModuleAccess(userId, courseId)` - Get all module access for course
- `getModuleLessonAccess(userId, moduleId)` - Get all lesson access for module
- `updateUserAccess(userId, courseId)` - Update access after progress changes

#### `useAccessControl.ts` (React Hooks)
Provides React hooks for components:
- `useModuleAccess(moduleId)` - Hook for module access
- `useLessonAccess(lessonId)` - Hook for lesson access
- `useCourseModuleAccess(courseId)` - Hook for course module access
- `useModuleLessonAccess(moduleId)` - Hook for module lesson access
- `useIsTeacher()` - Hook to check if user is teacher

## Access Rules

### For Students

1. **Module 1**: Always accessible
2. **Module N**: Accessible only if Module N-1 is completed (all lessons finished)
3. **Lesson 1 in any module**: Accessible if module is accessible
4. **Lesson N in a module**: Accessible only if Lesson N-1 is completed

### For Teachers

- **All modules**: Always accessible
- **All lessons**: Always accessible
- **No restrictions**: Teachers can access any content at any time

## Visual Indicators

### Locked Modules
- Opacity reduced to 75%
- "Locked" overlay on module image
- Click disabled

### Locked Lessons
- Lock icon instead of lesson number
- Grayed out appearance
- Click disabled
- Tooltip explaining why locked

### Accessible Content
- Normal appearance
- Clickable
- Progress indicators work normally

## Integration Points

### Lesson Completion
When a lesson is completed:
1. `markLessonAsCompleted()` is called
2. Database triggers fire automatically
3. `updateUserAccess()` is called to refresh access
4. Next lesson/module becomes available if conditions met

### Module Completion
When all lessons in a module are completed:
1. Module is marked as completed
2. Next module becomes accessible
3. Access tables are updated automatically

## Testing

### Manual Testing Steps

1. **Create a test student account**
2. **Verify Module 1 is accessible**
3. **Verify other modules are locked**
4. **Complete lessons in Module 1 sequentially**
5. **Verify each lesson unlocks the next**
6. **Complete all lessons in Module 1**
7. **Verify Module 2 becomes accessible**
8. **Test with teacher account - all content accessible**

### Database Testing

```sql
-- Check access for a user
SELECT public.check_module_access('user-uuid', 'module-uuid');
SELECT public.check_lesson_access('user-uuid', 'lesson-uuid');

-- View access tables
SELECT * FROM public.user_module_access WHERE user_id = 'user-uuid';
SELECT * FROM public.user_lesson_access WHERE user_id = 'user-uuid';
```

## Migration Applied

The system was implemented via migration:
- `supabase/migrations/20250102002_module_unlocking_system.sql`

All database functions, tables, and triggers are created automatically when this migration is applied.

## Security

- Row Level Security (RLS) enabled on access tables
- Users can only view/modify their own access records
- Database functions use `SECURITY DEFINER` with proper `search_path`
- Teachers identified via `user_roles` table

## Performance Considerations

- Access checks are cached in React Query (5-minute stale time)
- Database functions are optimized for quick lookups
- Triggers only fire on actual completion changes
- Access tables provide fast lookup without complex joins

## Future Enhancements

Potential improvements:
1. **Conditional unlocking** - Unlock based on quiz scores
2. **Time-based unlocking** - Unlock after specific time periods
3. **Prerequisite lessons** - Cross-module dependencies
4. **Partial access** - Preview locked content
5. **Admin override** - Manual unlock for specific users
