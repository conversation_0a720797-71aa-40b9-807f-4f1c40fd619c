/* Desktop Enhancements for Modern UI */

/* Apply only to desktop screens */
@media (min-width: 1024px) {
  /* General UI improvements */
  :root {
    --content-max-width: 1200px;
    --content-padding-x: 1.75rem;
    --section-spacing: 1.5rem;
    --card-border-radius: 0.75rem;
    --transition-slow: 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    --shadow-subtle: 0 2px 12px rgba(0, 0, 0, 0.05);
    --shadow-hover: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  /* Enhanced container width */
  .container {
    max-width: var(--content-max-width);
    padding-left: var(--content-padding-x);
    padding-right: var(--content-padding-x);
    margin-left: auto;
    margin-right: auto;
  }

  /* Enhanced Sidebar */

  /* Sidebar scroll container */

  .dark .sidebar-container > div {
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
  }

  /* Main content with proper sidebar spacing */

  /* Card and grid improvements */
  .card {
    border-radius: var(--card-border-radius);
    transition: var(--transition-slow);
    box-shadow: var(--shadow-subtle);
  }

  .card:hover {
    box-shadow: var(--shadow-hover);
  }

  /* Grid layout improvements */

  /* Section spacing */
  .section + .section {
    margin-top: calc(var(--section-spacing) * 2);
  }

  /* Typography improvements */
  h1 {
    font-size: 2.5rem;
    line-height: 1.2;
    margin-bottom: 1.5rem;
  }

  h2 {
    font-size: 2rem;
    line-height: 1.3;
    margin-bottom: 1.25rem;
  }

  h3 {
    font-size: 1.5rem;
    line-height: 1.4;
    margin-bottom: 1rem;
  }

  /* Form elements */
  input, select, textarea {
    font-size: 1rem;
    padding: 0.75rem 1rem;
  }

  /* Button improvements */
  button, .button {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    transition: var(--transition-slow);
  }

  /* Course stats cards - smaller and more professional */
  .course-stats-container {
    gap: 1rem !important;
    margin-bottom: 1.5rem !important;
  }
  
  /* Module container - more compact and professional */
  
  .dark .module-container {
    border: 1px solid rgba(255, 255, 255, 0.05) !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
  }
  
  /* Module header - more compact and professional */
  
  /* Module title - smaller text */
  
  .dark .module-title {
    color: #e0e0e0 !important;
  }

  /* Module info (lessons count and time) - smaller text */
   
  .text-gray-500,
  .flex.items-center.gap-4.text-gray-500 {
    font-size: 0.65rem !important;
    line-height: 1.3 !important;
    color: #666 !important;
  }
  
  .dark .module-info, 
  .dark .text-gray-500,
  .dark .flex.items-center.gap-4.text-gray-500 {
    color: #999 !important;
  }
  
  /* Smaller icons in module info */
  
  .text-gray-500 svg,
  .flex.items-center.gap-4.text-gray-500 svg {
    width: 0.7rem !important;
    height: 0.7rem !important;
    margin-right: 0.1rem !important;
    color: #888 !important;
  }
  
  .dark .module-info svg,
  .dark .text-gray-500 svg,
  .dark .flex.items-center.gap-4.text-gray-500 svg {
    color: #aaa !important;
  }
  
  /* Module content padding - more compact */
  
  /* Lesson item - more compact and professional */
  
  .dark .lesson-item {
    border: 1px solid rgba(255, 255, 255, 0.05) !important;
  }
  
  /* Lesson title - more professional font size */
  
  .dark .lesson-title {
    color: #d1d5db !important;
  }
  
  /* Lesson meta info - smaller */
  
  .flex.items-center.gap-3.mt-1,
  .flex.items-center.gap-3.text-gray-500 {
    font-size: 0.65rem !important;
    line-height: 1.3 !important;
    color: #777 !important;
  }
  
  .dark .lesson-meta,
  .dark .flex.items-center.gap-3.mt-1,
  .dark .flex.items-center.gap-3.text-gray-500 {
    color: #9ca3af !important;
  }
  
  /* Lesson meta icons - smaller */
  
  .flex.items-center.gap-3.mt-1 svg,
  .flex.items-center.gap-3.text-gray-500 svg {
    width: 0.7rem !important;
    height: 0.7rem !important;
    margin-right: 0.1rem !important;
  }
  
  /* Lesson icon - smaller */
  
  /* Course title on course content page */
  
  /* Card hover effects - more subtle and professional */
  
  /* Modern scrollbar - slightly thinner */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.03);
    border-radius: 8px;
  }
  
  ::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    border: 2px solid rgba(0, 0, 0, 0.03);
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.15);
  }
  
  .dark ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
  }
  
  .dark ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.05);
  }
  
  .dark ::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.15);
  }
  
  /* Enhanced typography - more professional */
  h1, h2, h3, h4, h5, h6 {
    letter-spacing: -0.02em;
  }
  
  /* Enhanced card shadows - more subtle */
  
  .dark .enhanced-card {
    box-shadow: 
      0 1px 2px rgba(0, 0, 0, 0.08),
      0 2px 6px rgba(0, 0, 0, 0.12);
    border: 1px solid rgba(255, 255, 255, 0.04);
  }
  
  .dark .enhanced-card:hover {
    box-shadow: 
      0 2px 4px rgba(0, 0, 0, 0.1),
      0 4px 10px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 255, 255, 0.06);
  }
  
  /* Glass morphism effects - more subtle */
  
  .dark .glass-effect {
    background: rgba(15, 23, 42, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }
  
  /* Enhanced image containers - more subtle zoom */
  
  /* Enhanced button styles - more subtle */
  
  /* Enhanced module container transition */
  
  /* Enhanced lesson items transition */
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  :root {
    --shadow-subtle: 0 2px 12px rgba(0, 0, 0, 0.2);
    --shadow-hover: 0 4px 20px rgba(0, 0, 0, 0.3);
  }
}

/* Extra large screens (1440px and up) */
@media (min-width: 1440px) {
  .course-detail-container {
    --content-max-width: 1400px;
    --content-padding-x: 2rem;
    --section-spacing: 2rem;
  }
} 