import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Loader2, Plus } from 'lucide-react';
import { addFinalExamModule } from '@/services/course/utils';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

interface AddFinalExamButtonProps {
  courseId: string;
}

export function AddFinalExamButton({ courseId }: AddFinalExamButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const queryClient = useQueryClient();
  
  // Google Form URL provided in the user query
  const googleFormUrl = 'https://docs.google.com/forms/d/e/1FAIpQLScCgeernaUISyIWuL6qHpt8hpjb-E59m_A5y1r4o-D5L_LNcA/viewform?usp=header';

  const handleAddFinalExam = async () => {
    if (!courseId) {
      toast.error('Course ID is required');
      return;
    }

    setIsLoading(true);

    try {
      const success = await addFinalExamModule(courseId, googleFormUrl);
      
      if (success) {
        toast.success('FINAL EXAMINATION module added successfully');
        
        // Invalidate relevant queries to refresh the UI
        queryClient.invalidateQueries({ queryKey: ['admin-course-modules', courseId] });
        queryClient.invalidateQueries({ queryKey: ['courseModules', courseId] });
      } else {
        toast.error('Failed to add FINAL EXAMINATION module');
      }
    } catch (error) {
      console.error('Error adding FINAL EXAMINATION module:', error);
      toast.error('An error occurred while adding the FINAL EXAMINATION module');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button 
      onClick={handleAddFinalExam}
      disabled={isLoading}
      className="flex items-center"
    >
      {isLoading ? (
        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
      ) : (
        <Plus className="w-4 h-4 mr-2" />
      )}
      Add Final Exam
    </Button>
  );
} 