-- Add image_url column to modules table
ALTER TABLE modules ADD COLUMN IF NOT EXISTS image_url TEXT;

-- Create storage policy for module images
INSERT INTO storage.buckets (id, name, public)
VALUES ('module-images', 'module-images', true)
ON CONFLICT (id) DO NOTHING;

-- Allow public access to module images
CREATE POLICY "Public Access"
ON storage.objects FOR SELECT
USING ( bucket_id = 'module-images' );

-- Allow authenticated users to upload module images
CREATE POLICY "Authenticated users can upload module images"
ON storage.objects FOR INSERT
WITH CHECK (
    bucket_id = 'module-images' 
    AND (
        SELECT EXISTS (
            SELECT 1 FROM auth.users
            WHERE id = auth.uid()
        )
    )
); 