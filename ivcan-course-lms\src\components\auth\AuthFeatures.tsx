
import React from 'react';
import { Check } from 'lucide-react';

const AuthFeatures: React.FC = () => {
  return (
    <div className="hidden md:block md:flex-1 bg-primary relative">
      <div className="absolute inset-0 opacity-20 bg-[url('data:image/svg+xml;charset=utf-8,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 width=%2232%22 height=%2232%22 viewBox=%220 0 24 24%22%3E%3Cpath fill=%22%23fff%22 d=%22M3.055 11H5a2 2 0 0 1 2 2v1a2 2 0 0 0 2 2h2m10.313-5.313-3.21 3.21a1 1 0 0 1-1.415 0l-3.211-3.21m10.384 3.21-3.131 3.131a1 1 0 0 1-1.415 0l-5.259-5.258a1 1 0 0 1 0-1.415l3.132-3.131M17 21l-2.212-2.211c-.586-.587-.586-1.536 0-2.122l5.511-5.511c.586-.586 1.536-.586 2.122 0L24.633 13M3 6l3 1m0 0 3 9a4 4 0 0 0 4 4h4m-6 0a2 2 0 0 1 0 4h4a2 2 0 0 1 0-4%22/%3E%3C/svg%3E')] bg-repeat"></div>
      <div className="absolute inset-0 flex items-center justify-center p-8">
        <div className="max-w-md text-center">
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 shadow-xl">
            <h3 className="text-2xl font-bold text-white mb-4">Streamlined Learning Experience</h3>
            <ul className="space-y-3 text-left">
              {[
                "Progressive module unlocking",
                "Comprehensive progress tracking",
                "Interactive assignments and quizzes",
                "Achievement badges and gamification"
              ].map((feature, i) => (
                <li key={i} className="flex items-start space-x-3 text-white/90">
                  <Check className="h-5 w-5 text-red-300 flex-shrink-0 mt-0.5" />
                  <span>{feature}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthFeatures;
