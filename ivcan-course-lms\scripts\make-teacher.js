/**
 * <PERSON><PERSON><PERSON> to make a user a teacher
 * 
 * This script adds the "teacher" role to a specified user.
 * 
 * Usage:
 * node scripts/make-teacher.js <user_id> <supabase_url> <supabase_service_key>
 */

const { createClient } = require('@supabase/supabase-js');

// Get the arguments from command line
const userId = process.argv[2];
const supabaseUrl = process.argv[3];
const supabaseKey = process.argv[4];

if (!userId || !supabaseUrl || !supabaseKey) {
  console.error('Error: All parameters are required');
  console.log('Usage: node scripts/make-teacher.js <user_id> <supabase_url> <supabase_service_key>');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function makeUserTeacher(userId) {
  console.log(`Making user ${userId} a teacher...`);

  try {
    // First, check if the user exists in auth.users
    const { data: authUser, error: authError } = await supabase.auth.admin.getUserById(userId);

    if (authError) {
      console.error('Error checking if user exists in auth:', authError);
      return false;
    }

    if (!authUser || !authUser.user) {
      console.error(`User with ID ${userId} not found in auth system`);
      return false;
    }

    console.log(`User found: ${authUser.user.email}`);

    // Check if the user already has a role
    const { data: existingRole, error: roleError } = await supabase
      .from('user_roles')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (roleError && !roleError.message.includes('No rows found')) {
      console.error('Error checking existing role:', roleError);
      return false;
    }

    if (existingRole) {
      if (existingRole.role === 'teacher') {
        console.log(`User ${userId} is already a teacher`);
        return true;
      }

      // Update the existing role to teacher
      const { error: updateError } = await supabase
        .from('user_roles')
        .update({ role: 'teacher' })
        .eq('user_id', userId);

      if (updateError) {
        console.error('Error updating user role:', updateError);
        return false;
      }

      console.log(`Updated user ${userId} role to teacher`);
      return true;
    }

    // Insert a new role
    const { error: insertError } = await supabase
      .from('user_roles')
      .insert([
        {
          user_id: userId,
          role: 'teacher'
        }
      ]);

    if (insertError) {
      console.error('Error inserting user role:', insertError);
      return false;
    }

    console.log(`Successfully made user ${userId} a teacher`);
    return true;
  } catch (error) {
    console.error('Unexpected error:', error);
    return false;
  }
}

// Run the function
makeUserTeacher(userId)
  .then(success => {
    if (success) {
      console.log('Operation completed successfully');
      process.exit(0);
    } else {
      console.error('Operation failed');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
