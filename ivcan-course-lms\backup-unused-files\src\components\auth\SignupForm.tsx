import React, { useState, useEffect } from 'react';
import { Eye, EyeOff, LogIn, AlertCircle, User, Check } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { validatePassword, isPasswordValid } from '@/utils/passwordUtils';
import PasswordStrengthIndicator from './PasswordStrengthIndicator';
import { toast } from 'sonner';
import { motion } from 'framer-motion';

interface SignupFormProps {
  email: string;
  setEmail: (email: string) => void;
  password: string;
  setPassword: (password: string) => void;
  name: string;
  setName: (name: string) => void;
  isSubmitting: boolean;
}

// Define the user role type
type UserRole = 'student' | 'teacher';

const SignupForm: React.FC<SignupFormProps> = ({
  email,
  setEmail,
  password,
  setPassword,
  name,
  setName,
  isSubmitting
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordsMatch, setPasswordsMatch] = useState(true);
  const [isFormValid, setIsFormValid] = useState(false);
  // Set the role to student only, removing the option to select teacher
  const selectedRole: UserRole = 'student';
  const { signUp } = useAuth();

  // Validate form on input change
  useEffect(() => {
    const isValid =
      email.trim() !== '' &&
      isPasswordValid(password) &&
      password === confirmPassword &&
      name.trim() !== '';

    setIsFormValid(isValid);

    // Check if passwords match when both have values
    if (password && confirmPassword) {
      setPasswordsMatch(password === confirmPassword);
    }
  }, [email, password, confirmPassword, name]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Final validation before submission
    if (!isFormValid) {
      if (!isPasswordValid(password)) {
        toast.error('Password must be at least 8 characters long');
        return;
      }

      if (password !== confirmPassword) {
        toast.error('Passwords do not match');
        return;
      }

      if (!email.trim()) {
        toast.error('Please enter your email');
        return;
      }

      if (!name.trim()) {
        toast.error('Please enter your name');
        return;
      }

      return;
    }

    try {
      const nameParts = name.split(' ');
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';

      // Create a userData object with user metadata, always setting role to student
      const userData = {
        first_name: firstName,
        last_name: lastName,
        full_name: name.trim(),
        requested_role: 'student' // Always set to student
      };

      // Pass email, password, and userData object to signUp
      await signUp(email, password, userData);
    } catch (error) {
      // Error is handled in auth context
    }
  };

  return (
    <form className="space-y-5" onSubmit={handleSubmit}>
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="space-y-1.5"
      >
        <label htmlFor="name" className="block text-sm font-medium text-foreground">
          Full Name
        </label>
        <div className="relative">
          <input
            id="name"
            name="name"
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            required
            className="block w-full px-4 py-3 text-foreground border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-card/50 transition-all duration-200 placeholder:text-muted-foreground/60"
            placeholder="John Doe"
          />
          <User className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground/60" />
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="space-y-1.5"
      >
        <label htmlFor="signup-email" className="block text-sm font-medium text-foreground">
          Email Address
        </label>
        <input
          id="signup-email"
          name="email"
          type="email"
          autoComplete="email"
          required
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          className="block w-full px-4 py-3 text-foreground border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-card/50 transition-all duration-200 placeholder:text-muted-foreground/60"
          placeholder="<EMAIL>"
        />
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="space-y-1.5"
      >
        <label htmlFor="signup-password" className="block text-sm font-medium text-foreground">
          Password
        </label>
        <div className="relative">
          <input
            id="signup-password"
            name="password"
            type={showPassword ? "text" : "password"}
            autoComplete="new-password"
            required
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="block w-full px-4 py-3 text-foreground border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent pr-10 bg-card/50 transition-all duration-200"
            placeholder="••••••••"
          />
          <button
            type="button"
            className="absolute inset-y-0 right-0 pr-3 flex items-center text-muted-foreground hover:text-foreground transition-colors"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </button>
        </div>
        {password && <PasswordStrengthIndicator password={password} className="mt-1" />}
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="space-y-1.5"
      >
        <label htmlFor="confirm-password" className="block text-sm font-medium text-foreground">
          Confirm Password
        </label>
        <div className="relative">
          <input
            id="confirm-password"
            name="confirmPassword"
            type={showPassword ? "text" : "password"}
            autoComplete="new-password"
            required
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            className={`block w-full px-4 py-3 text-foreground border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent pr-10 bg-card/50 transition-all duration-200 ${
              confirmPassword && !passwordsMatch ? 'border-red-500' : 'border-border'
            }`}
            placeholder="••••••••"
          />
          {confirmPassword && passwordsMatch && (
            <Check className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
          )}
        </div>
        {confirmPassword && !passwordsMatch && (
          <p className="text-xs text-red-500 mt-1 flex items-center">
            <AlertCircle className="h-3 w-3 mr-1" />
            Passwords do not match
          </p>
        )}
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="flex items-center"
      >
        <input
          id="terms"
          name="terms"
          type="checkbox"
          required
          className="h-4 w-4 text-primary focus:ring-primary border-border rounded"
        />
        <label htmlFor="terms" className="ml-2 block text-sm text-foreground">
          I agree to the{" "}
          <a 
            href="#" 
            className="inline-flex items-center px-2 py-0.5 bg-primary/10 border border-primary/30 rounded text-primary font-medium hover:bg-primary hover:text-white transition-colors"
          >
            Terms
          </a>{" "}
          and{" "}
          <a 
            href="#" 
            className="inline-flex items-center px-2 py-0.5 bg-primary/10 border border-primary/30 rounded text-primary font-medium hover:bg-primary hover:text-white transition-colors"
          >
            Privacy Policy
          </a>
        </label>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="pt-2"
      >
        <button
          type="submit"
          disabled={isSubmitting || !isFormValid}
          className="relative w-full flex justify-center items-center px-4 py-3 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed overflow-hidden group"
        >
          <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-primary to-primary transition-all duration-300 transform group-hover:scale-102 opacity-0 group-hover:opacity-100"></span>
          <span className="flex items-center relative z-10">
            {isSubmitting ? 'Processing...' : (
              <>
                <LogIn className="w-4 h-4 mr-2" />
                Create Account
              </>
            )}
          </span>
        </button>
      </motion.div>
    </form>
  );
};

export default SignupForm;
