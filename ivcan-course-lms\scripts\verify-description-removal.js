#!/usr/bin/env node

/**
 * Verification script to confirm that test descriptions have been successfully removed
 * and the interface is now streamlined
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function verifyDescriptionRemoval() {
  console.log('🔍 Verifying test description removal...\n');
  
  try {
    // 1. Check that no tests have descriptions
    console.log('1. Checking for any remaining descriptions...');
    
    const { data: testsWithDescriptions, error: descError } = await supabase
      .from('module_tests')
      .select('id, title, description')
      .not('description', 'is', null);
    
    if (descError) {
      console.log('❌ Error checking descriptions:', descError.message);
      return;
    }
    
    if (testsWithDescriptions && testsWithDescriptions.length > 0) {
      console.log(`❌ Found ${testsWithDescriptions.length} tests still with descriptions:`);
      testsWithDescriptions.forEach(test => {
        console.log(`   - ${test.title}: "${test.description}"`);
      });
      return;
    }
    
    console.log('✅ No tests have descriptions - all cleaned successfully');
    
    // 2. Verify all tests have null descriptions
    console.log('\n2. Verifying all tests have null descriptions...');
    
    const { data: allTests, error: allError } = await supabase
      .from('module_tests')
      .select('id, title, type, description');
    
    if (allError) {
      console.log('❌ Error fetching all tests:', allError.message);
      return;
    }
    
    if (!allTests || allTests.length === 0) {
      console.log('⚠️  No tests found in database');
      return;
    }
    
    const testsWithNullDesc = allTests.filter(test => test.description === null);
    console.log(`✅ Total tests: ${allTests.length}`);
    console.log(`✅ Tests with null descriptions: ${testsWithNullDesc.length}`);
    
    if (testsWithNullDesc.length === allTests.length) {
      console.log('✅ All tests have null descriptions');
    } else {
      console.log(`❌ ${allTests.length - testsWithNullDesc.length} tests still have descriptions`);
    }
    
    // 3. Show sample test structure
    console.log('\n3. Sample test structure after cleanup:');
    if (allTests.length > 0) {
      const sampleTest = allTests[0];
      console.log('✅ Sample test:');
      console.log(`   ID: ${sampleTest.id}`);
      console.log(`   Title: ${sampleTest.title}`);
      console.log(`   Type: ${sampleTest.type}`);
      console.log(`   Description: ${sampleTest.description}`);
    }
    
    // 4. Verify question structure still intact
    console.log('\n4. Verifying question structure integrity...');
    
    const { data: testWithQuestions, error: questionsError } = await supabase
      .from('module_tests')
      .select('questions')
      .not('questions', 'is', null)
      .limit(1);
    
    if (questionsError) {
      console.log('❌ Error checking questions:', questionsError.message);
      return;
    }
    
    if (testWithQuestions && testWithQuestions.length > 0) {
      const questions = testWithQuestions[0].questions;
      if (Array.isArray(questions) && questions.length > 0) {
        const sampleQuestion = questions[0];
        console.log('✅ Question structure intact:');
        console.log(`   Question: ${sampleQuestion.question}`);
        console.log(`   Min Label: ${sampleQuestion.minLabel}`);
        console.log(`   Max Label: ${sampleQuestion.maxLabel}`);
        console.log(`   Rating Range: ${sampleQuestion.minRating}-${sampleQuestion.maxRating}`);
      }
    }
    
    console.log('\n🎉 Verification completed successfully!');
    console.log('\n📋 Summary of changes:');
    console.log('   ✅ Removed description display from test component');
    console.log('   ✅ Removed all description text from database records');
    console.log('   ✅ Cleaned up unused imports');
    console.log('   ✅ Maintained question structure and functionality');
    
    console.log('\n✨ Interface improvements:');
    console.log('   • Cleaner, more streamlined test page');
    console.log('   • No instructional text above questions');
    console.log('   • Self-explanatory answer options');
    console.log('   • Better focus on actual test content');
    console.log('   • Reduced visual clutter');
    
  } catch (error) {
    console.error('❌ Verification failed:', error.message);
  }
}

// Run the verification
verifyDescriptionRemoval();
