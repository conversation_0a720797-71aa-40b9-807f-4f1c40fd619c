import React, { useState, useEffect } from 'react';
import { ArrowRight, Clock, Video, BookOpen, FileText } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { getCoursePlaceholderImage, getCourseImageSource } from '@/utils/imageUtils';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import { OptimizedImage } from '@/components/ui/optimized-image';
import { transformImageUrl } from '@/lib/image-utils';
import { toast } from 'sonner';
import { getModuleImageByNumber, getModuleFallbackImage } from '@/utils/moduleImageUtils';

interface ModuleCardProps {
  id: string;
  courseId: string;
  title: string;
  description: string;
  progress?: number;
  hoursSpent?: number;
  totalLessons?: number;
  completedLessons?: number;
  hasVideo?: boolean;
  status?: string;
  image?: string;
  module_number?: number;
}

const ModuleCard: React.FC<ModuleCardProps> = ({
  id,
  courseId,
  title,
  description,
  progress = 0,
  hoursSpent = 0,
  totalLessons = 0,
  completedLessons = 0,
  hasVideo = false,
  status = 'not_started',
  image,
  module_number = 1,
}) => {
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const [imageError, setImageError] = useState(false);
  const [renderError, setRenderError] = useState<Error | null>(null);

  const handleModuleClick = () => {
    if (!id || !courseId) {
      console.error('Module ID or Course ID is missing');
      toast.error('Unable to open module: ID is missing');
      return;
    }
    navigate(`/course/${courseId}/module/${id}`);
  };

  // Determine the status badge style
  const getBadgeStyle = () => {
    if (status === 'in_progress') {
      return 'bg-primary/20 text-primary dark:bg-primary/30 dark:text-primary-foreground';
    } else if (status === 'completed') {
      return 'bg-primary/30 text-primary dark:bg-primary/40 dark:text-primary-foreground';
    } else {
      return 'bg-muted text-muted-foreground';
    }
  };

  // Get static module image based on module number
  const getModuleImage = () => {
    try {
      // If we already have a static image that follows our pattern
      if (image?.includes('/images/m') && image?.endsWith('.jpg')) {
        return image;
      }
      
      // Use the module number to get the appropriate image
      return getModuleImageByNumber(module_number);
    } catch (error) {
      console.error('Error getting module image:', error);
      return getModuleFallbackImage();
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'in_progress':
        return 'Active';
      case 'completed':
        return 'Completed';
      default:
        return 'Not Started';
    }
  };

  const getActionText = () => {
    switch (status) {
      case 'in_progress':
        return 'Continue';
      case 'completed':
        return 'Review';
      default:
        return 'Start';
    }
  };

  try {
    return (
      <div
        className={cn(
          "flex flex-col sm:flex-row overflow-hidden rounded-lg sm:rounded-xl border border-border/40",
          "bg-white dark:bg-gray-900",
          "transition-all duration-300 cursor-pointer",
          !isMobile && "hover:-translate-y-1 hover:shadow-md hover:border-primary/20 dark:hover:border-primary/30"
        )}
        onClick={handleModuleClick}
      >
        {/* Image section - Updated to use static images */}
        <div className="relative w-full sm:w-[300px] h-[180px] xs:h-[200px] sm:h-auto bg-gradient-to-r from-primary/5 to-primary/10 dark:from-primary/10 dark:to-primary/20">
          <div className="absolute inset-0">
            <OptimizedImage
              src={getModuleImage()}
              alt={title || 'Module'}
              className="w-full h-full transition-transform duration-500 hover:scale-105"
              fallbackSrc={getModuleFallbackImage()}
              priority={false}
            />
          </div>

          {/* Video badge - Adjusted size for mobile */}
          {hasVideo && (
            <div className="absolute top-2 right-2 sm:top-3 sm:right-3 bg-primary text-primary-foreground p-1 sm:p-1.5 rounded-full shadow-md">
              <Video className="w-3 h-3 sm:w-4 sm:h-4" />
            </div>
          )}

          {/* Gradient overlay */}
          <div className="absolute bottom-0 left-0 right-0 h-1/3 bg-gradient-to-t from-black/20 to-transparent" />
        </div>

        {/* Content section - Enhanced mobile responsiveness */}
        <div className="flex flex-col flex-1 p-2 sm:p-3 md:p-4"> {/* Reduced padding */}
          <div className="flex justify-between items-start gap-2 sm:gap-3 mb-1 sm:mb-2"> {/* Reduced margin */}
            <h3 className="text-2xl sm:text-3xl md:text-4xl font-bold line-clamp-2 font-poppins bg-gradient-to-r from-red-500 to-red-700 bg-clip-text text-transparent">{title || 'Untitled Module'}</h3>
            <span className={cn(
              "px-2 sm:px-2.5 py-0.5 sm:py-1 text-xs font-medium rounded-full whitespace-nowrap",
              getBadgeStyle()
            )}>{getStatusText()}</span>
          </div>

          <p className="text-xs sm:text-sm text-muted-foreground line-clamp-2 sm:line-clamp-3 mb-2 sm:mb-3 flex-1"> {/* Reduced margin */}
            {description || 'No description available'}
          </p>

          {/* Progress section - Adjusted spacing */}
          <div className="space-y-1 sm:space-y-2 mb-2 sm:mb-3"> {/* Reduced spacing and margin */}
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Progress</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <div className="h-1 sm:h-1.5 w-full bg-primary/10 dark:bg-primary/20 rounded-full overflow-hidden">
              <div 
                className="h-full bg-primary dark:bg-primary rounded-full transition-all duration-300"
                style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
              />
            </div>
          </div>

          {/* Footer - Adjusted for better mobile layout */}
          <div className="flex items-center justify-between pt-1 sm:pt-2 border-t border-border/60"> {/* Reduced padding */}
            <div className="flex items-center text-xs sm:text-sm text-muted-foreground">
              <FileText className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 text-primary dark:text-primary/80" />
              <span>{completedLessons} / {totalLessons} lessons</span>
              {status === 'completed' && (
                <span className="ml-2 px-1.5 sm:px-2 py-0.5 text-xs rounded-full bg-primary/20 text-primary dark:bg-primary/30 dark:text-primary-foreground">
                  Completed
                </span>
              )}
            </div>

            <button
              onClick={(e) => {
                e.stopPropagation();
                if (!id || !courseId) {
                  console.error('Module ID or Course ID is missing');
                  toast.error('Unable to open module: ID is missing');
                  return;
                }
                navigate(`/course/${courseId}/module/${id}`);
              }}
              className="flex items-center justify-center gap-2 sm:gap-3 px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base font-semibold rounded-full bg-red-500 hover:bg-red-600 text-white/90 hover:text-white shadow-sm hover:shadow-md transition-all duration-300 group min-w-[120px] sm:min-w-[140px]"
            >
              <span className="font-mono tracking-wide">{getActionText()}</span>
              <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 group-hover:translate-x-1 transition-transform duration-300" />
            </button>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    // Log the error
    console.error('Error rendering ModuleCard:', error);

    // Set the error state so we can show a fallback UI
    if (error instanceof Error && !renderError) {
      setRenderError(error);
    }

    // Return a fallback UI
    return (
      <div className="modern-card p-5">
        <div className="text-primary">
          <h3 className="font-medium">Error displaying module</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">There was an error displaying this module card.</p>
          {id && courseId && (
            <button
              onClick={() => navigate(`/course/${courseId}/module/${id}`)}
              className="mt-3 text-sm text-primary hover:text-primary/80 dark:text-primary-foreground dark:hover:text-primary-foreground/90 hover:underline flex items-center"
            >
              View Module <ArrowRight className="ml-1 h-4 w-4" />
            </button>
          )}
        </div>
      </div>
    );
  }
};

export default ModuleCard; 