
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Loader2, X, Trash2, AlertTriangle, Upload, Image as ImageIcon, Check } from 'lucide-react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';
import { deleteCourse } from '@/services/course/courseManagementApi';
import { useRealtimeCourseSync } from '@/services/course';
import { createCourseAsAdmin } from '@/services/course/adminApi';
import { uploadToStorage } from '@/lib/storage-utils';
import { directUploadToStorage } from '@/lib/direct-upload';
import { uploadCourseImage } from '@/lib/enhanced-upload';
import { processImageForStorage } from '@/lib/local-storage';
import { ImageUploadGuide } from './ImageUploadGuide';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from '@/components/ui/alert-dialog';

interface CourseFormValues {
  title: string;
  slug: string;
  description: string;
  instructor: string;
  image_url?: string;
}

interface CourseEditorProps {
  courseId: string | null;
  onClose: () => void;
}

const CourseEditor: React.FC<CourseEditorProps> = ({ courseId, onClose }) => {
  const isNewCourse = !courseId;
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [useLocalStorage, setUseLocalStorage] = useState(true); // Default to local storage method

  // Set up real-time sync for the course
  useRealtimeCourseSync(courseId === 'new' ? null : courseId);

  const form = useForm<CourseFormValues>({
    defaultValues: {
      title: '',
      slug: '',
      description: '',
      instructor: '',
      image_url: '',
    }
  });

  // Fetch course data if editing an existing course
  const { isLoading: isLoadingCourse } = useQuery({
    queryKey: ['course-editor', courseId],
    queryFn: async () => {
      if (!courseId || courseId === 'new') return null;

      const { data, error } = await supabase
        .from('courses')
        .select('*')
        .eq('id', courseId)
        .single();

      if (error) throw error;

      // Set form values
      form.reset({
        title: data.title,
        slug: data.slug,
        description: data.description,
        instructor: data.instructor,
        image_url: data.image_url,
      });

      // Set image preview if available
      if (data.image_url) {
        setImagePreview(data.image_url);
      }

      return data;
    },
    enabled: !!courseId && courseId !== 'new',
  });

  // Handle slug generation
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const title = e.target.value;
    // Only auto-generate slug if it's empty or if we're creating a new course
    if (isNewCourse || !form.getValues('slug')) {
      const slug = title.toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-');
      form.setValue('slug', slug);
    }
  };

  // Handle image selection
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageFile(file);
      const previewUrl = URL.createObjectURL(file);
      setImagePreview(previewUrl);
    }
  };

  // Handle image removal
  const handleRemoveImage = () => {
    setImageFile(null);
    setImagePreview(null);
    form.setValue('image_url', '');

    toast({
      title: 'Image Removed',
      description: 'The image has been removed. Save the course to confirm this change.',
      variant: 'default'
    });
  };

  // Save or update course
  const mutation = useMutation({
    mutationFn: async (values: CourseFormValues) => {
      // Generate a unique slug if needed
      const finalSlug = values.slug || values.title.toLowerCase().replace(/\s+/g, '-');

      // Handle image upload if there's a new image
      let finalImageUrl = values.image_url || '';

      if (imageFile) {
        setIsUploading(true);
        try {
          // Always use local storage approach (data URL) for reliability
          console.log('Using local storage approach (data URL)');
          console.log(`Processing image: ${imageFile.name} (${(imageFile.size / 1024).toFixed(2)}KB)`);

          try {
            // Process the image with size limits
            finalImageUrl = await processImageForStorage(imageFile);
            console.log('Image processed successfully as data URL');
          } catch (processingError: any) {
            // If the error is about image size, show a specific message
            if (processingError.message && processingError.message.includes('too large')) {
              console.error('Image size error:', processingError.message);
              toast({
                title: 'Image Too Large',
                description: processingError.message,
                variant: 'destructive'
              });
              setIsUploading(false);
              throw new Error(`Image too large: ${processingError.message}`);
            }

            // For other errors, try a more aggressive compression as last resort
            console.warn('Standard processing failed, trying minimal quality as last resort');

            // Create a minimal quality version
            const img = new Image();
            img.src = URL.createObjectURL(imageFile);

            await new Promise<void>((resolve) => {
              img.onload = () => resolve();
              img.onerror = () => resolve(); // Continue even if error
            });

            const canvas = document.createElement('canvas');
            // Limit dimensions to very small size
            const maxDimension = 400;
            let width = img.width;
            let height = img.height;

            if (width > height) {
              if (width > maxDimension) {
                height = Math.round(height * (maxDimension / width));
                width = maxDimension;
              }
            } else {
              if (height > maxDimension) {
                width = Math.round(width * (maxDimension / height));
                height = maxDimension;
              }
            }

            canvas.width = width;
            canvas.height = height;

            const ctx = canvas.getContext('2d');
            if (!ctx) throw new Error('Could not get canvas context');

            ctx.drawImage(img, 0, 0, width, height);

            // Use lowest quality
            finalImageUrl = canvas.toDataURL('image/jpeg', 0.3);

            const sizeKB = Math.round(finalImageUrl.length / 1024);
            console.log(`Last resort compression result: ${sizeKB}KB`);

            if (sizeKB > 800) {
              toast({
                title: 'Image Still Too Large',
                description: 'Please use a smaller image or reduce its dimensions before uploading.',
                variant: 'destructive'
              });
              setIsUploading(false);
              throw new Error('Image is still too large even after maximum compression');
            }

            toast({
              title: 'Image Compressed',
              description: 'The image was very large and has been compressed to a lower quality.',
              variant: 'default'
            });
          }
        } catch (error: any) {
          console.error('Image processing error:', error);

          // Create a more detailed error message
          let errorMessage = 'Failed to process image. ';
          errorMessage += error.message || 'Please try again with a smaller image.';

          if (!error.message || !error.message.includes('too large')) {
            toast({
              title: 'Image Processing Failed',
              description: errorMessage,
              variant: 'destructive'
            });
          }

          setIsUploading(false);
          throw new Error(`Image processing failed: ${error.message}`);
        }
        setIsUploading(false);
      }

      try {
        // First try with regular Supabase client
        if (isNewCourse) {
          // Create new course
          const { data, error } = await supabase
            .from('courses')
            .insert([{
              title: values.title,
              slug: finalSlug,
              description: values.description,
              instructor: values.instructor,
              image_url: finalImageUrl,
              total_modules: 0,
              completed_modules: 0
            }])
            .select();

          if (error) {
            console.log('Error with regular client, trying admin API:', error);
            throw error;
          }
          return data[0];
        } else {
          if (!courseId) {
            throw new Error('Course ID is required for updates');
          }

          // Update existing course
          const { data, error } = await supabase
            .from('courses')
            .update({
              title: values.title,
              slug: finalSlug,
              description: values.description,
              instructor: values.instructor,
              image_url: finalImageUrl,
              updated_at: new Date().toISOString()
            })
            .eq('id', courseId)
            .select();

          if (error) {
            console.log('Error with regular client, trying admin API:', error);
            throw error;
          }
          return data[0];
        }
      } catch (error) {
        // If regular client fails, try with admin API
        console.log('Using admin API as fallback');

        if (isNewCourse) {
          const result = await createCourseAsAdmin({
            title: values.title,
            slug: finalSlug,
            description: values.description,
            instructor: values.instructor,
            image_url: finalImageUrl
          });

          if (!result) {
            throw new Error('Failed to create course using admin API');
          }

          return result;
        } else {
          // For now, we don't have updateCourseAsAdmin, so we'll throw an error
          throw new Error('Failed to update course. Please try again or contact support.');
        }
      }
    },
    onSuccess: (data) => {
      toast({
        title: isNewCourse ? "Course created" : "Course updated",
        description: `Successfully ${isNewCourse ? 'created' : 'updated'} the course.`,
      });

      // Refresh all relevant queries
      queryClient.invalidateQueries({
        queryKey: ['admin-courses'],
      });

      // Invalidate admin modules and lessons queries since course changes affect their display
      queryClient.invalidateQueries({
        queryKey: ['admin-modules'],
      });

      queryClient.invalidateQueries({
        queryKey: ['admin-lessons'],
      });

      // Invalidate course-related queries
      queryClient.invalidateQueries({
        queryKey: ['course'],
      });

      // Invalidate specific course query if updating
      if (!isNewCourse && courseId) {
        queryClient.invalidateQueries({
          queryKey: ['course', courseId],
        });
      }

      // Optionally navigate to the updated course
      if (isNewCourse) {
        navigate(`/course/${data.slug}`);
      } else {
        onClose();
      }
    },
    onError: (error: any) => {
      console.error('Error saving course:', error);
      console.log('Detailed error:', JSON.stringify(error, null, 2));

      // Create a more user-friendly error message
      let errorMessage = `Failed to ${isNewCourse ? 'create' : 'update'} course. `;

      // Check for specific error types
      if (error.message?.includes('image')) {
        errorMessage += 'There was an issue with the course image. Please try using a smaller image or a different format.';
      } else if (error.message?.includes('permission')) {
        errorMessage += 'You do not have permission to perform this action. Please contact an administrator.';
      } else if (error.message?.includes('network')) {
        errorMessage += 'There was a network error. Please check your internet connection and try again.';
      } else if (error.message?.includes('too large')) {
        errorMessage += 'The image is too large. Please use a smaller image (under 2MB) or compress it before uploading.';
      } else {
        // Generic error message
        errorMessage += error.message || 'Please try again or contact support.';
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  // Delete course mutation
  const deleteMutation = useMutation({
    mutationFn: async () => {
      if (!courseId || courseId === 'new') {
        throw new Error('Cannot delete a course that does not exist');
      }
      return await deleteCourse(courseId);
    },
    onSuccess: (success) => {
      setIsDeleteDialogOpen(false);
      if (success) {
        toast({
          title: "Course deleted",
          description: "The course and all its content have been permanently deleted.",
        });

        // Refresh course lists
        queryClient.invalidateQueries({
          queryKey: ['admin-courses'],
        });

        onClose();
      } else {
        toast({
          title: "Error",
          description: "Failed to delete the course. Please try again.",
          variant: "destructive",
        });
      }
    },
    onError: (error) => {
      setIsDeleteDialogOpen(false);
      console.error('Error deleting course:', error);
      toast({
        title: "Error",
        description: "Failed to delete the course. Please try again.",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (values: CourseFormValues) => {
    mutation.mutate(values);
  };

  if (isLoadingCourse && !isNewCourse) {
    return (
      <div className="flex justify-center p-8">
        <Loader2 className="w-6 h-6 animate-spin text-blue-500" />
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border p-6 relative">
      <Button
        variant="ghost"
        size="icon"
        className="absolute top-2 right-2"
        onClick={onClose}
      >
        <X className="h-4 w-4" />
      </Button>

      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">
          {isNewCourse ? 'Create New Course' : 'Edit Course'}
        </h2>

        {!isNewCourse && (
          <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
            <AlertDialogTrigger asChild>
              <Button variant="destructive" size="sm" className="flex items-center">
                <Trash2 className="h-4 w-4 mr-1" /> Delete
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                <AlertDialogDescription>
                  This will permanently delete the course and all its content including modules, lessons, quizzes, and student progress data. This action cannot be undone.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={(e) => {
                    e.preventDefault();
                    deleteMutation.mutate();
                  }}
                  className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
                >
                  {deleteMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Deleting...
                    </>
                  ) : (
                    <>
                      <AlertTriangle className="mr-2 h-4 w-4" />
                      Delete Everything
                    </>
                  )}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        )}
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Course Image */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <FormLabel htmlFor="image">Course Image</FormLabel>
              <ImageUploadGuide />
            </div>
            <div className="flex flex-col space-y-4">
              {imagePreview ? (
                <div className="relative w-full max-w-md aspect-video rounded-lg overflow-hidden border border-border">
                  <img
                    src={imagePreview}
                    alt="Course preview"
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute top-2 right-2 flex space-x-2">
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      className="opacity-90"
                      onClick={handleRemoveImage}
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      Remove
                    </Button>
                    <Button
                      type="button"
                      variant="secondary"
                      size="sm"
                      className="opacity-90 bg-white"
                      onClick={() => {
                        document.getElementById('image')?.click();
                      }}
                    >
                      <Upload className="h-4 w-4 mr-1" />
                      Replace
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="w-full max-w-md aspect-video rounded-lg bg-muted flex flex-col items-center justify-center border border-dashed border-border p-4">
                  <ImageIcon className="h-10 w-10 text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground">No image selected</p>
                  <p className="text-xs text-muted-foreground mt-1">Recommended size: 1280x720px (16:9 ratio)</p>
                </div>
              )}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Input
                    id="image"
                    type="file"
                    accept="image/*"
                    onChange={handleImageChange}
                    className="max-w-md"
                  />
                  {isUploading && <Loader2 className="h-4 w-4 animate-spin text-primary" />}
                </div>

                <div className="flex items-center">
                  <div className="flex items-center space-x-2 text-sm bg-red-50 text-red-800 px-3 py-1 rounded-md">
                    <Check className="h-4 w-4 text-red-600" />
                    <span>Using local storage (images embedded directly)</span>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="ml-2 h-8 px-2 text-xs"
                    onClick={() => {
                      toast({
                        title: 'Local Storage Method',
                        description: 'Images are stored directly in the database for reliability. This ensures teachers can easily update and delete images without permission issues.',
                        variant: 'default'
                      });
                    }}
                  >
                    <AlertTriangle className="h-3 w-3 mr-1" />
                    Info
                  </Button>
                </div>
              </div>
            </div>
          </div>

          <FormField
            control={form.control}
            name="title"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Course Title</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter course title"
                    {...field}
                    onChange={(e) => {
                      field.onChange(e);
                      handleTitleChange(e);
                    }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="slug"
            render={({ field }) => (
              <FormItem>
                <FormLabel>URL Slug</FormLabel>
                <FormControl>
                  <Input
                    placeholder="course-url-slug"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Describe your course"
                    rows={4}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="instructor"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Instructor</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Instructor name"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={mutation.isPending}
            >
              {mutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isNewCourse ? 'Create Course' : 'Save Changes'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default CourseEditor;
