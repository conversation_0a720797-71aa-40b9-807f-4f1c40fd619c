import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Form } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { createLessonAsAdmin, updateLessonAsAdmin } from '@/services/course/adminApi';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import BasicInfoForm from './editors/BasicInfoForm';
import ContentForm from './editors/ContentForm';


// Helper function to generate a slug from a title
const generateSlug = (title: string): string => {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with a single one
    .trim();
};

const formSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  slug: z.string().min(1, 'Slug is required'),
  duration: z.string(),
  type: z.enum(['lesson', 'assignment', 'quiz', 'video']),
  content: z.string().optional(),
  requirement: z.string().optional(),
  videoUrl: z.string().optional(),
  imageUrl: z.string().optional()
});

type LessonFormValues = z.infer<typeof formSchema>;

interface LessonEditorProps {
  moduleId: string;
  lessonId?: string;
  defaultType?: 'lesson' | 'video' | 'assignment' | 'quiz';
  restrictType?: boolean;
  onClose: () => void;
}

const LessonEditor: React.FC<LessonEditorProps> = ({
  moduleId,
  lessonId,
  defaultType = 'lesson',
  restrictType = false,
  onClose,
}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState('basic');
  const [hasVideo, setHasVideo] = useState(false);
  const [hasImage, setHasImage] = useState(false);
  const [quizQuestions, setQuizQuestions] = useState<any[]>([]);
  const [courseId, setCourseId] = useState<string | undefined>(undefined);

  const form = useForm<LessonFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: '',
      slug: '',
      duration: '15:00',
      type: defaultType,
      content: '',
      requirement: '',
      videoUrl: '',
      imageUrl: ''
    }
  });

  // Fetch module data to get courseId
  const { data: moduleData } = useQuery({
    queryKey: ['module', moduleId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('modules')
        .select('course_id')
        .eq('id', moduleId)
        .single();

      if (error) {
        console.error('Error fetching module:', error);
        return null;
      }

      setCourseId(data.course_id);
      return data;
    },
    enabled: !!moduleId
  });

  // Auto-generate slug when title changes
  const title = form.watch('title');
  useEffect(() => {
    if (title && !lessonId) {
      const slug = generateSlug(title);
      form.setValue('slug', slug);
    }
  }, [title, form, lessonId]);

  // Fetch existing lesson data if editing
  const { isLoading: isLoadingLesson } = useQuery({
    queryKey: ['lesson-editor', lessonId],
    queryFn: async () => {
      if (!lessonId) return null;

      const { data, error } = await supabase
        .from('lessons')
        .select('*')
        .eq('id', lessonId)
        .single();

      if (error) {
        console.error('Error fetching lesson:', error);
        toast({
          title: "Error",
          description: "Failed to load lesson data",
          variant: "destructive"
        });
        return null;
      }

      // Set form values with type assertion to handle database field naming
      const lessonData = data as any;
      form.reset({
        title: lessonData.title,
        slug: lessonData.slug,
        duration: lessonData.duration || '15:00',
        type: lessonData.type as 'lesson' | 'assignment' | 'quiz' | 'video',
        content: lessonData.content || '',
        requirement: lessonData.requirement || '',
        videoUrl: lessonData.video_url || lessonData.videoUrl || '',
        imageUrl: lessonData.image_url || lessonData.imageUrl || ''
      });

      // Check for video/image URL using multiple possible field names
      setHasVideo(!!(lessonData.video_url || lessonData.videoUrl));
      setHasImage(!!(lessonData.image_url || lessonData.imageUrl));

      // Parse quiz questions if this is a quiz
      if (lessonData.type === 'quiz' && lessonData.content) {
        try {
          const parsed = JSON.parse(lessonData.content);
          if (parsed.questions) {
            setQuizQuestions(parsed.questions);
          }
        } catch (e) {
          console.error('Error parsing quiz questions:', e);
        }
      }

      return data;
    },
    enabled: !!lessonId
  });

  const createLesson = useMutation({
    mutationFn: async (values: LessonFormValues) => {
      let content = values.content;

      // If this is a quiz, serialize the quiz questions
      if (values.type === 'quiz' && quizQuestions.length > 0) {
        content = JSON.stringify({
          questions: quizQuestions
        });
      }

      // If video/image URLs are present, create rich content
      if (hasVideo || hasImage) {
        content = JSON.stringify({
          content: values.content,
          videoUrl: values.videoUrl,
          imageUrl: values.imageUrl
        });
      }

      const lessonData = {
        module_id: moduleId,
        title: values.title,
        slug: values.slug,
        duration: values.duration,
        type: values.type,
        content: content,
        requirement: values.requirement
      };

      const { data, error } = await supabase
        .from('lessons')
        .insert([lessonData])
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Lesson created successfully",
      });

      // Invalidate queries
      queryClient.invalidateQueries({ queryKey: ['module-lessons'] });
      queryClient.invalidateQueries({ queryKey: ['lessons'] });
      onClose();
    },
    onError: (error: any) => {
      console.error('Error creating lesson:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to create lesson",
        variant: "destructive"
      });
    }
  });

  const updateLesson = useMutation({
    mutationFn: async (values: LessonFormValues) => {
      if (!lessonId) throw new Error('Lesson ID is required for update');

      let content = values.content;

      // If this is a quiz, serialize the quiz questions
      if (values.type === 'quiz' && quizQuestions.length > 0) {
        content = JSON.stringify({
          questions: quizQuestions
        });
      }

      // If video/image URLs are present, create rich content
      if (hasVideo || hasImage) {
        content = JSON.stringify({
          content: values.content,
          videoUrl: values.videoUrl,
          imageUrl: values.imageUrl
        });
      }

      const lessonData = {
        title: values.title,
        slug: values.slug,
        duration: values.duration || '15:00',
        type: values.type,
        content: content,
        requirement: values.requirement || null,
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('lessons')
        .update(lessonData)
        .eq('id', lessonId)
        .select()
        .single();

      if (error) {
        console.error('Lesson update error:', error);
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Lesson updated successfully",
      });

      // Invalidate queries
      queryClient.invalidateQueries({ queryKey: ['module-lessons'] });
      queryClient.invalidateQueries({ queryKey: ['lessons'] });
      queryClient.invalidateQueries({ queryKey: ['lesson-editor', lessonId] });
      onClose();
    },
    onError: (error: any) => {
      console.error('Error updating lesson:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update lesson",
        variant: "destructive"
      });
    }
  });

  const onSubmit = (data: LessonFormValues) => {
    // Validate required fields
    if (!data.title?.trim()) {
      toast({
        title: "Validation Error",
        description: "Title is required",
        variant: "destructive"
      });
      return;
    }

    if (!data.slug?.trim()) {
      toast({
        title: "Validation Error",
        description: "Slug is required",
        variant: "destructive"
      });
      return;
    }

    if (lessonId) {
      updateLesson.mutate(data);
    } else {
      createLesson.mutate(data);
    }
  };

  if (isLoadingLesson) {
    return (
      <div className="flex justify-center py-8">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  // Rest of your component rendering code...
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="basic">Basic Info</TabsTrigger>
            <TabsTrigger value="content">Content</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <BasicInfoForm
              form={form}
              hasVideo={hasVideo}
              setHasVideo={setHasVideo}
              hasImage={hasImage}
              setHasImage={setHasImage}
              restrictType={restrictType}
            />
          </TabsContent>

          <TabsContent value="content" className="space-y-4">
            <ContentForm
              form={form}
              lessonType={form.watch('type')}
              content={form.watch('content')}
              quizQuestions={quizQuestions}
              setQuizQuestions={setQuizQuestions}
              onSubmit={() => form.handleSubmit(onSubmit)()}
              courseId={courseId}
              moduleId={moduleId}
            />
          </TabsContent>
        </Tabs>

        <div className="flex justify-end gap-2 mt-4 pt-4 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={createLesson.isPending || updateLesson.isPending}
          >
            {createLesson.isPending || updateLesson.isPending ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                {lessonId ? 'Updating...' : 'Creating...'}
              </>
            ) : (
              lessonId ? 'Update Lesson' : 'Create Lesson'
            )}
          </Button>
        </div>
      </form>


    </Form>
  );
};

export default LessonEditor;
