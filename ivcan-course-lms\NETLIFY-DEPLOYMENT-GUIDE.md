# Netlify Deployment Guide for IVCAN Course LMS

This guide will help you deploy the IVCAN Course LMS to Netlify and fix any MIME type issues that may occur.

## Deployment Steps

### 1. Build the Application

First, build the application with the production optimizations:

```bash
npm run build:prod
```

This will:
- Build the application with production optimizations
- Apply compression (gzip and brotli)
- Create the necessary Netlify configuration files
- Fix MIME type issues

### 2. Deploy to Netlify

You have two options for deploying to Netlify:

#### Option 1: Drag and Drop (Manual Deploy)

1. Log in to your Netlify account
2. Go to the Sites page
3. Drag and drop the `dist` folder onto the Netlify dashboard
4. Wait for the deployment to complete

#### Option 2: Connect to Git Repository

1. Log in to your Netlify account
2. Click "New site from Git"
3. Connect to your Git provider (GitHub, GitLab, or Bitbucket)
4. Select the repository
5. Configure the build settings:
   - Build command: `npm run build:prod`
   - Publish directory: `dist`
6. Click "Deploy site"

### 3. Fix MIME Type Issues (If Needed)

If you still see MIME type errors in the browser console after deployment, follow these steps:

1. Log in to your Netlify dashboard
2. Go to the site settings
3. Navigate to "Build & deploy" > "Post processing"
4. Disable "Asset optimization"
5. Trigger a new deployment

## Environment Variables

Make sure the following environment variables are set in your Netlify site settings:

| Variable | Description |
|----------|-------------|
| `VITE_SUPABASE_URL` | Your Supabase project URL |
| `VITE_SUPABASE_ANON_KEY` | Your Supabase anonymous key |
| `VITE_SUPABASE_SERVICE_ROLE_KEY` | Your Supabase service role key (optional) |
| `VITE_APP_ENVIRONMENT` | Set to `production` |

## Troubleshooting

### MIME Type Errors

If you see errors like:

```
Refused to apply style from '...' because its MIME type ('text/html') is not a supported stylesheet MIME type
```

or

```
Failed to load module script: Expected a JavaScript module script but the server responded with a MIME type of "text/html"
```

Follow these steps:

1. Make sure the `_headers` file is present in your deployment
2. Check that "Asset optimization" is disabled in Netlify settings
3. Run the MIME type fix script again and redeploy:

```bash
npm run fix:netlify-mime
```

### Redirects Not Working

If your SPA routing is not working (404 errors when refreshing pages), make sure:

1. The `_redirects` file is present in your deployment
2. It contains the correct redirects:

```
/auth/callback/* /index.html 200
/* /index.html 200
```

## Additional Resources

- [Netlify Documentation](https://docs.netlify.com/)
- [Netlify Headers Documentation](https://docs.netlify.com/routing/headers/)
- [Netlify Redirects Documentation](https://docs.netlify.com/routing/redirects/)
