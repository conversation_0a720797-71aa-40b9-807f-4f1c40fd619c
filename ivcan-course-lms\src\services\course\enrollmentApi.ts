import { supabase } from '@/integrations/supabase/client';
import { CourseEnrollment } from './types';
import { toast } from 'sonner';

// Enroll in a course or update enrollment status
export const enrollInCourse = async (courseId: string, userId: string, status: 'not_started' | 'in_progress' | 'completed' = 'not_started') => {
  console.log(`[ENROLLMENT] Starting enrollment process for user ${userId} in course ${courseId} with status ${status}`);

  // Validate inputs
  if (!courseId || !userId) {
    const error = new Error('Missing required parameters: courseId and userId are required');
    console.error('[ENROLLMENT] Validation error:', error.message);
    throw error;
  }

  const now = new Date().toISOString();

  try {
    // Note: We don't need to check authentication here since the userId is passed from the AuthContext
    // The RLS policies will handle the authentication check at the database level
    console.log('[ENROLLMENT] Processing enrollment for user:', userId);

    // Check if enrollment already exists
    console.log('[ENROLLMENT] Checking for existing enrollment...');
    const { data: existingEnrollment, error: checkError } = await supabase
      .from('user_course_enrollment')
      .select('*')
      .eq('user_id', userId)
      .eq('course_id', courseId)
      .maybeSingle();

    if (checkError) {
      console.error('[ENROLLMENT] Error checking existing enrollment:', checkError);
      throw new Error(`Failed to check existing enrollment: ${checkError.message}`);
    }

    console.log('[ENROLLMENT] Existing enrollment check result:', existingEnrollment);

    if (existingEnrollment) {
      // Update existing enrollment - SIMPLE APPROACH
      // Just update the status field which definitely exists
      console.log('Updating enrollment status to:', status);

      const { data, error } = await supabase
        .from('user_course_enrollment')
        .update({
          status: status,
          updated_at: now
        })
        .eq('id', existingEnrollment.id)
        .select();

      if (error) {
        console.error('Error updating course enrollment:', error);
        throw error;
      }

      console.log('Enrollment updated successfully:', data);

      // If status is completed, also update the course progress
      if (status === 'completed') {
        try {
          console.log('[ENROLLMENT] Updating course progress record');
          const { error: progressError } = await supabase
            .from('user_course_progress')
            .upsert({
              user_id: userId,
              course_id: courseId,
              last_accessed_at: now,
              updated_at: now
            }, { onConflict: 'user_id,course_id' });

          if (progressError) {
            console.error('[ENROLLMENT] Error updating progress record:', progressError);
          }
        } catch (progressError) {
          console.error('[ENROLLMENT] Error updating progress (non-critical):', progressError);
          // Continue anyway as this is not critical
        }
      }

      return data;
    } else {
      // Create new enrollment - SIMPLE APPROACH
      // Just include the essential fields that definitely exist
      console.log('[ENROLLMENT] Creating new enrollment with status:', status);

      const insertData = {
        user_id: userId,
        course_id: courseId,
        status: status,
        enrolled_at: now,
        updated_at: now
      };

      console.log('[ENROLLMENT] Insert data:', insertData);

      const { data, error } = await supabase
        .from('user_course_enrollment')
        .insert(insertData)
        .select();

      if (error) {
        console.error('[ENROLLMENT] Error creating course enrollment:', error);
        console.error('[ENROLLMENT] Error details:', {
          message: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint
        });
        throw new Error(`Failed to create enrollment: ${error.message} (Code: ${error.code})`);
      }

      console.log('[ENROLLMENT] Enrollment created successfully:', data);

      // Always create a progress record for the user
      try {
        console.log('[ENROLLMENT] Creating initial progress record');
        const { error: progressError } = await supabase
          .from('user_course_progress')
          .upsert({
            user_id: userId,
            course_id: courseId,
            hours_spent: 0,
            last_accessed_at: now,
            updated_at: now
          }, { onConflict: 'user_id,course_id' });

        if (progressError) {
          console.error('[ENROLLMENT] Error creating progress record:', progressError);
        } else {
          console.log('[ENROLLMENT] Progress record created successfully');
        }
      } catch (progressError) {
        console.error('[ENROLLMENT] Unexpected error creating progress record:', progressError);
        // Continue anyway as this is not critical
      }

      return data;
    }
  } catch (error: any) {
    console.error('[ENROLLMENT] Error in enrollInCourse:', error);

    // Provide more specific error messages
    let errorMessage = 'Failed to update course enrollment status';
    if (error.message) {
      errorMessage = error.message;
    }

    toast.error(errorMessage);
    throw error;
  }
};

// Get enrollment status for a course (returns the full enrollment object)
export const getEnrollmentStatus = async (courseId: string, userId: string): Promise<CourseEnrollment | null> => {
  if (!userId) return null;

  const { data, error } = await supabase
    .from('user_course_enrollment')
    .select('*')
    .eq('user_id', userId)
    .eq('course_id', courseId)
    .maybeSingle();

  if (error) {
    console.error('Error fetching course enrollment:', error);
    return null;
  }

  // Ensure the status is one of the expected types
  if (data) {
    const enrollment = {
      ...data,
      status: data.status as 'not_started' | 'in_progress' | 'completed'
    };
    return enrollment as CourseEnrollment;
  }

  return data as CourseEnrollment;
};

// Get all enrollments for a user
export const getUserEnrollments = async (userId: string): Promise<CourseEnrollment[]> => {
  if (!userId) return [];

  const { data, error } = await supabase
    .from('user_course_enrollment')
    .select('*')
    .eq('user_id', userId);

  if (error) {
    console.error('Error fetching user enrollments:', error);
    return [];
  }

  // Convert raw data to CourseEnrollment type
  return (data || []).map(item => ({
    ...item,
    status: item.status as 'not_started' | 'in_progress' | 'completed'
  })) as CourseEnrollment[];
};

// Get all enrollments for a course
export const getCourseEnrollments = async (courseId: string): Promise<CourseEnrollment[]> => {
  if (!courseId) return [];

  const { data, error } = await supabase
    .from('user_course_enrollment')
    .select('*, profiles:user_id(id, full_name, avatar_url)')
    .eq('course_id', courseId);

  if (error) {
    console.error('Error fetching course enrollments:', error);
    return [];
  }

  // Convert raw data to CourseEnrollment type with proper status
  return (data || []).map(item => ({
    ...item,
    status: item.status as 'not_started' | 'in_progress' | 'completed'
  })) as CourseEnrollment[];
};

// Mark a course as completed
export const markCourseAsCompleted = async (courseId: string, userId: string): Promise<boolean> => {
  try {
    console.log(`Marking course ${courseId} as completed for user ${userId}`);
    console.log('Course ID type:', typeof courseId);
    console.log('User ID type:', typeof userId);

    // Ensure courseId is a string and not undefined or null
    if (!courseId || typeof courseId !== 'string') {
      console.error('Invalid course ID:', courseId);
      toast.error('Invalid course ID. Please try again.');
      return false;
    }

    // Ensure userId is a string and not undefined or null
    if (!userId || typeof userId !== 'string') {
      console.error('Invalid user ID:', userId);
      toast.error('Invalid user ID. Please try again.');
      return false;
    }

    const now = new Date().toISOString();

    // SIMPLIFIED APPROACH: Direct update/insert without any RPC calls
    console.log('Using simplified direct database operations...');

    // Step 1: Try to update existing enrollment
    const { data: updateResult, error: updateError } = await supabase
      .from('user_course_enrollment')
      .update({
        status: 'completed',
        updated_at: now
      })
      .eq('user_id', userId)
      .eq('course_id', courseId)
      .select('id');

    // Check if update worked
    if (!updateError && updateResult && updateResult.length > 0) {
      console.log('Successfully updated enrollment to completed status');
      
      // Update progress record
      await supabase
        .from('user_course_progress')
        .upsert({
          user_id: userId,
          course_id: courseId,
          completed_modules: 100,
          updated_at: now
        }, { onConflict: 'user_id,course_id' });
        
      return true;
    } else {
      console.log('No existing enrollment found or update failed, attempting insert...');
      
      // Step 2: If update didn't work, try to insert a new enrollment
      const { error: insertError } = await supabase
        .from('user_course_enrollment')
        .insert({
          user_id: userId,
          course_id: courseId,
          status: 'completed',
          enrolled_at: now,
          updated_at: now
        });
        
      if (!insertError) {
        console.log('Successfully inserted new completed enrollment');
        
        // Create progress record
        await supabase
          .from('user_course_progress')
          .upsert({
            user_id: userId,
            course_id: courseId,
            completed_modules: 100,
            updated_at: now
          }, { onConflict: 'user_id,course_id' });
          
        return true;
      } else {
        console.error('Error inserting enrollment:', insertError);
        
        // Try one last approach - upsert
        console.log('Trying upsert as final approach...');
        const { error: upsertError } = await supabase
          .from('user_course_enrollment')
          .upsert({
            user_id: userId,
            course_id: courseId,
            status: 'completed',
            enrolled_at: now,
            updated_at: now
          });
          
        if (!upsertError) {
          console.log('Successfully upserted enrollment');
          return true;
        } else {
          console.error('All direct database approaches failed');
          toast.error('Failed to mark course as completed. Please try again.');
          return false;
        }
      }
    }
  } catch (error) {
    console.error('Error in markCourseAsCompleted:', error);
    toast.error('Failed to update course enrollment status');
    return false;
  }
};

// Get enrollment status as a string
export const getEnrollmentStatusString = async (courseId: string, userId: string): Promise<string | null> => {
  try {
    if (!userId || !courseId) return null;

    const { data, error } = await supabase
      .from('user_course_enrollment')
      .select('status')
      .eq('user_id', userId)
      .eq('course_id', courseId)
      .maybeSingle();

    if (error) {
      console.error('Error fetching enrollment status:', error);
      return null;
    }

    return data?.status || null;
  } catch (error) {
    console.error('Unexpected error fetching enrollment status:', error);
    return null;
  }
};

// Get detailed enrollment information
export const getEnrollmentDetails = async (courseId: string, userId: string) => {
  try {
    if (!userId || !courseId) return null;

    console.log(`Fetching enrollment details for user ${userId} and course ${courseId}`);

    const { data, error } = await supabase
      .from('user_course_enrollment')
      .select('*')
      .eq('user_id', userId)
      .eq('course_id', courseId)
      .maybeSingle();

    if (error) {
      console.error('Error fetching enrollment details:', error);
      return null;
    }

    console.log('Enrollment details:', data);

    // If completed_at is null but status is completed, set a default date
    if (data && data.status === 'completed' && !data.completed_at) {
      console.log('Fixing missing completed_at date');
      data.completed_at = data.updated_at || new Date().toISOString();

      // Try to update the record with the correct completed_at date
      try {
        await supabase
          .from('user_course_enrollment')
          .update({ completed_at: data.completed_at })
          .eq('id', data.id);
      } catch (updateError) {
        console.error('Error updating completed_at date:', updateError);
        // Continue anyway, we'll use the local value
      }
    }

    return data;
  } catch (error) {
    console.error('Unexpected error fetching enrollment details:', error);
    return null;
  }
};

// Unenroll from a course
export const unenrollFromCourse = async (courseId: string, userId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('user_course_enrollment')
      .delete()
      .eq('user_id', userId)
      .eq('course_id', courseId);

    if (error) {
      console.error('Error unenrolling from course:', error);
      toast.error('Failed to unenroll from course');
      return false;
    }

    toast.success('Successfully unenrolled from course');
    return true;
  } catch (error: any) {
    console.error('Unexpected error unenrolling from course:', error);
    toast.error(`Failed to unenroll from course: ${error.message}`);
    return false;
  }
};

// Get the number of students enrolled in a course
export const getCourseEnrollmentCount = async (courseId: string): Promise<number> => {
  if (!courseId) return 0;

  try {
    const { count, error } = await supabase
      .from('user_course_enrollment')
      .select('id', { count: 'exact', head: true })
      .eq('course_id', courseId);

    if (error) {
      console.error('Error fetching course enrollment count:', error);
      return 0;
    }

    return count || 0;
  } catch (error) {
    console.error('Unexpected error fetching enrollment count:', error);
    return 0;
  }
};
