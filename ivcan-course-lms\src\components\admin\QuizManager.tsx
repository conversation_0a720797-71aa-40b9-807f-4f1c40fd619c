import React, { useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Plus, Edit, Trash2, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { QuizForm } from './QuizForm';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";

export function QuizManager() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [searchQuery, setSearchQuery] = useState('');
  const [isAddingQuiz, setIsAddingQuiz] = useState(false);
  const [editingQuizId, setEditingQuizId] = useState<string | null>(null);
  const [quizToDelete, setQuizToDelete] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Fetch all quizzes
  const { data: quizzes, isLoading } = useQuery({
    queryKey: ['quizzes'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('quizzes')
        .select('*, courses(title)')
        .order('created_at', { ascending: false });

      if (error) {
        toast({
          title: "Error fetching quizzes",
          description: error.message,
          variant: "destructive",
        });
        throw error;
      }
      return data;
    },
  });

  // Fetch quiz details for editing
  const { data: quizDetails, isLoading: isLoadingQuizDetails } = useQuery({
    queryKey: ['quiz', editingQuizId],
    queryFn: async () => {
      if (!editingQuizId) return null;

      const { data, error } = await supabase
        .from('quizzes')
        .select('*')
        .eq('id', editingQuizId)
        .single();

      if (error) {
        toast({
          title: "Error fetching quiz details",
          description: error.message,
          variant: "destructive",
        });
        throw error;
      }

      // Fetch quiz questions
      const { data: questionsData, error: questionsError } = await supabase
        .from('quiz_questions')
        .select('*')
        .eq('quiz_id', editingQuizId)
        .order('question_order', { ascending: true });

      if (questionsError) {
        toast({
          title: "Error fetching quiz questions",
          description: questionsError.message,
          variant: "destructive",
        });
        throw questionsError;
      }

      // Format the data for the form
      return {
        id: data.id,
        title: data.title,
        description: data.description,
        courseId: data.course_id,
        lessonId: data.lesson_id || '',
        quizType: data.quiz_type || 'standard',
        passingScore: data.passing_score,
        timeLimit: data.time_limit,
        randomizeQuestions: data.randomize_questions,
        showExplanations: data.show_explanations,
        scaleDescription: data.scale_description || '',
        sectionTitle: data.section_title || '',
        questions: questionsData.map(q => {
          // Convert database format to form format
          const options = q.options ? 
            Object.entries(q.options).map(([key, value]) => ({
              text: value as string,
              isCorrect: q.correct_answer && q.correct_answer[key] ? true : false
            })) : 
            [{ text: '', isCorrect: false }, { text: '', isCorrect: false }];

          return {
            text: q.question,
            type: q.question_type === 'multiple-choice' ? 'multiple_choice' : 
                  q.question_type === 'true-false' ? 'true_false' : 
                  q.question_type === 'rating-scale' ? 'rating_scale' : 'single_choice',
            options,
            explanation: q.explanation || '',
            minRating: q.min_rating || 1,
            maxRating: q.max_rating || 5,
            minLabel: q.min_label || 'Poor',
            maxLabel: q.max_label || 'Excellent',
          };
        })
      };
    },
    enabled: !!editingQuizId,
  });

  // Handle quiz deletion
  const handleDeleteQuiz = async () => {
    if (!quizToDelete) return;

    try {
      const { error } = await supabase
        .from('quizzes')
        .delete()
        .eq('id', quizToDelete);

      if (error) throw error;

      toast({
        title: "Quiz deleted",
        description: "The quiz has been successfully deleted.",
      });

      // Refresh the quizzes list
      queryClient.invalidateQueries({ queryKey: ['quizzes'] });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "An error occurred while deleting the quiz.",
        variant: "destructive",
      });
    } finally {
      setQuizToDelete(null);
    }
  };

  // Handle quiz form success
  const handleQuizSaved = () => {
    setIsAddingQuiz(false);
    setEditingQuizId(null);
    setIsDialogOpen(false);
  };

  // Handle closing the quiz editor
  const handleCloseEditor = () => {
    setIsAddingQuiz(false);
    setEditingQuizId(null);
    setIsDialogOpen(false);
  };

  // Filter quizzes based on search query
  const filteredQuizzes = quizzes?.filter(quiz => 
    quiz.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    quiz.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Quiz Management</h2>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => {
              setIsAddingQuiz(true);
              setEditingQuizId(null);
              setIsDialogOpen(true);
            }}>
              <Plus className="h-4 w-4 mr-2" /> Create New Quiz
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>{editingQuizId ? 'Edit Quiz' : 'Create New Quiz'}</DialogTitle>
              <DialogDescription>
                {editingQuizId 
                  ? 'Update the quiz details and questions below.' 
                  : 'Fill in the quiz details and add questions below.'}
              </DialogDescription>
            </DialogHeader>
            {isLoadingQuizDetails && editingQuizId ? (
              <div className="flex justify-center p-8">
                <Loader2 className="w-6 h-6 animate-spin text-primary" />
              </div>
            ) : (
              <QuizForm
                initialData={quizDetails}
                onSuccess={handleQuizSaved}
                onCancel={handleCloseEditor}
              />
            )}
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex items-center space-x-2">
        <Search className="h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search quizzes..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="max-w-sm"
        />
      </div>

      {isLoading ? (
        <div className="flex justify-center p-8">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
        </div>
      ) : filteredQuizzes && filteredQuizzes.length > 0 ? (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Title</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Course</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredQuizzes.map((quiz) => (
              <TableRow key={quiz.id}>
                <TableCell className="font-medium">{quiz.title}</TableCell>
                <TableCell>
                  {quiz.quiz_type === 'standard' ? 'Standard Quiz' : 'Questionnaire'}
                </TableCell>
                <TableCell>{quiz.courses?.title || 'Unknown Course'}</TableCell>
                <TableCell>{new Date(quiz.created_at).toLocaleDateString()}</TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setEditingQuizId(quiz.id);
                        setIsDialogOpen(true);
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setQuizToDelete(quiz.id)}
                    >
                      <Trash2 className="h-4 w-4 text-destructive" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : (
        <div className="text-center p-8 border rounded-md bg-muted/20">
          <p className="text-muted-foreground mb-4">No quizzes found</p>
          <Button
            variant="outline"
            onClick={() => {
              setIsAddingQuiz(true);
              setIsDialogOpen(true);
            }}
          >
            <Plus className="h-4 w-4 mr-2" /> Create your first quiz
          </Button>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!quizToDelete} onOpenChange={(open) => !open && setQuizToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the quiz and all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteQuiz} className="bg-destructive text-destructive-foreground">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
