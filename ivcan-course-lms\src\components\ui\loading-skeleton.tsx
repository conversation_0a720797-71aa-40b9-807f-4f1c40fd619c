import React from 'react';
import { cn } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';

interface LoadingSkeletonProps {
  className?: string;
}

// Course card skeleton
export const CourseCardSkeleton: React.FC<LoadingSkeletonProps> = ({ className }) => (
  <div className={cn('flex flex-col sm:flex-row gap-4 p-4 sm:p-5 bg-white dark:bg-gray-800/50 border border-border/40 rounded-xl', className)}>
    <div className="w-full sm:w-[300px] aspect-video sm:aspect-square bg-muted animate-pulse rounded-xl" />
    <div className="flex-1 space-y-4">
      <Skeleton className="h-4 w-3/4" />
      <div className="space-y-2">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-5/6" />
      </div>
      <div className="pt-4 border-t border-border/40">
        <Skeleton className="h-4 w-1/2" />
      </div>
    </div>
  </div>
);

// Module content skeleton
export const ModuleContentSkeleton: React.FC<LoadingSkeletonProps> = ({ className }) => (
  <div className={cn('space-y-4 md:space-y-6', className)}>
    <div className="space-y-2 mb-4 md:mb-8">
      <Skeleton className="h-6 md:h-8 w-3/4" />
      <Skeleton className="h-3 md:h-4 w-full" />
    </div>
    <Skeleton className="h-[300px] md:h-[400px] w-full rounded-xl" />
  </div>
);

// Lesson content skeleton
export const LessonContentSkeleton: React.FC<LoadingSkeletonProps> = ({ className }) => (
  <div className={cn('flex flex-col gap-8', className)}>
    <Skeleton className="h-8 w-2/3" />
    <Skeleton className="h-64 w-full rounded-lg" />
    <Skeleton className="h-8 w-1/2" />
  </div>
);

// Profile skeleton
export const ProfileSkeleton: React.FC<LoadingSkeletonProps> = ({ className }) => (
  <div className={cn('space-y-6', className)}>
    <div className="flex items-center gap-4">
      <Skeleton className="h-16 w-16 rounded-full" />
      <div className="space-y-2">
        <Skeleton className="h-4 w-32" />
        <Skeleton className="h-3 w-24" />
      </div>
    </div>
    <div className="space-y-4">
      <Skeleton className="h-4 w-full" />
      <Skeleton className="h-4 w-3/4" />
      <Skeleton className="h-4 w-1/2" />
    </div>
  </div>
);

// Dashboard stats skeleton
export const DashboardStatsSkeleton: React.FC<LoadingSkeletonProps> = ({ className }) => (
  <div className={cn('grid grid-cols-1 md:grid-cols-3 gap-4', className)}>
    {Array.from({ length: 3 }).map((_, i) => (
      <div key={i} className="p-6 bg-white dark:bg-gray-800/50 border border-border/40 rounded-xl">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-8 w-16" />
          </div>
          <Skeleton className="h-8 w-8 rounded" />
        </div>
      </div>
    ))}
  </div>
);

// Table skeleton
export const TableSkeleton: React.FC<LoadingSkeletonProps & { rows?: number; columns?: number }> = ({ 
  className, 
  rows = 5, 
  columns = 4 
}) => (
  <div className={cn('space-y-3', className)}>
    {/* Header */}
    <div className="flex gap-4 p-4 border-b">
      {Array.from({ length: columns }).map((_, i) => (
        <Skeleton key={i} className="h-4 flex-1" />
      ))}
    </div>
    {/* Rows */}
    {Array.from({ length: rows }).map((_, rowIndex) => (
      <div key={rowIndex} className="flex gap-4 p-4">
        {Array.from({ length: columns }).map((_, colIndex) => (
          <Skeleton key={colIndex} className="h-4 flex-1" />
        ))}
      </div>
    ))}
  </div>
);

// Form skeleton
export const FormSkeleton: React.FC<LoadingSkeletonProps> = ({ className }) => (
  <div className={cn('space-y-6', className)}>
    <div className="space-y-2">
      <Skeleton className="h-4 w-24" />
      <Skeleton className="h-10 w-full" />
    </div>
    <div className="space-y-2">
      <Skeleton className="h-4 w-32" />
      <Skeleton className="h-24 w-full" />
    </div>
    <div className="space-y-2">
      <Skeleton className="h-4 w-20" />
      <Skeleton className="h-10 w-full" />
    </div>
    <Skeleton className="h-10 w-32" />
  </div>
);

// Generic content skeleton
export const ContentSkeleton: React.FC<LoadingSkeletonProps & { lines?: number }> = ({ 
  className, 
  lines = 3 
}) => (
  <div className={cn('space-y-3', className)}>
    {Array.from({ length: lines }).map((_, i) => (
      <Skeleton 
        key={i} 
        className={cn(
          'h-4',
          i === lines - 1 ? 'w-3/4' : 'w-full'
        )} 
      />
    ))}
  </div>
);
