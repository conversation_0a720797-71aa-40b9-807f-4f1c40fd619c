import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { ChevronLeft, ChevronRight, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { UseMutationResult } from '@tanstack/react-query';
import { findNextLessonUnified } from '@/services/course/courseApi';
import { useAuth } from '@/context/AuthContext';

export interface LessonNavigationProps {
  courseId: string;
  currentLessonSlug: string;
  previousLesson?: string;
  nextLesson?: string;
  isCompleted?: boolean;
  completionMutation?: UseMutationResult<any, any, any>;
}

const LessonNavigation: React.FC<LessonNavigationProps> = ({
  courseId,
  currentLessonSlug,
  previousLesson,
  nextLesson,
  isCompleted = false,
  completionMutation
}) => {
  const [isNavigating, setIsNavigating] = useState(false);
  const navigate = useNavigate();
  const { user } = useAuth();

  // Handle next item click - complete lesson first, then navigate to next item (lesson or test)
  const handleNextClick = async (e: React.MouseEvent) => {
    e.preventDefault();

    if (isNavigating || completionMutation?.isPending || !user) return;

    setIsNavigating(true);

    try {
      console.log('[LESSON NAVIGATION] Starting next lesson navigation from:', currentLessonSlug);

      // If lesson is not completed and we have a completion mutation, mark it as completed first
      if (!isCompleted && completionMutation) {
        console.log('[LESSON NAVIGATION] Completing lesson before navigation');
        await completionMutation.mutateAsync();
      }

      // Find the next lesson using unified navigation
      console.log('[LESSON NAVIGATION] Finding next lesson using unified navigation');
      const { nextLessonSlug, isLastLesson } = await findNextLessonUnified(currentLessonSlug);

      console.log('[LESSON NAVIGATION] Navigation result:', { nextLessonSlug, isLastLesson });

      if (nextLessonSlug) {
        // Navigate to next lesson
        console.log('[LESSON NAVIGATION] Navigating to next lesson:', nextLessonSlug);
        navigate(`/course/${courseId}/lesson/${nextLessonSlug}`);
      } else if (isLastLesson) {
        // This is the last lesson, go to modules page
        console.log('[LESSON NAVIGATION] Last lesson reached, going to modules page');
        navigate(`/course/${courseId}/modules`);
      } else if (nextLesson) {
        // Fallback to original next lesson navigation
        console.log('[LESSON NAVIGATION] Using fallback navigation to:', nextLesson);
        navigate(`/course/${courseId}/lesson/${nextLesson}`);
      } else {
        // No next item, go to modules page
        console.log('[LESSON NAVIGATION] No next lesson found, going to modules page');
        navigate(`/course/${courseId}/modules`);
      }
    } catch (error) {
      console.error('[LESSON NAVIGATION] Error during navigation:', error);
      console.error('[LESSON NAVIGATION] Error details:', {
        currentLessonSlug,
        courseId,
        isCompleted,
        nextLesson,
        error: error instanceof Error ? error.message : String(error)
      });

      // Fallback navigation - go to modules page to be safe
      console.log('[LESSON NAVIGATION] Using emergency fallback - going to modules page');
      navigate(`/course/${courseId}/modules`);
    } finally {
      setIsNavigating(false);
    }
  };
  return (
    <div className="flex justify-between items-center py-2 mb-6 border-y">
      {previousLesson ? (
        <Button
          variant="ghost"
          asChild
          className="flex items-center gap-1"
        >
          <Link to={`/course/${courseId}/lesson/${previousLesson}`}>
            <ChevronLeft className="h-4 w-4" />
            Previous
          </Link>
        </Button>
      ) : (
        <Button variant="ghost" disabled className="invisible">
          <ChevronLeft className="h-4 w-4" />
          Previous
        </Button>
      )}

      {/* Removed "Back to Modules" button */}
      <div className="flex-1"></div>

      <Button
        variant="ghost"
        onClick={handleNextClick}
        disabled={isNavigating || completionMutation?.isPending || !user}
        className="flex items-center gap-1"
      >
        {isNavigating ? (
          <>
            <Loader2 className="h-4 w-4 animate-spin" />
            {isCompleted ? 'Loading...' : 'Completing...'}
          </>
        ) : (
          <>
            Next
            <ChevronRight className="h-4 w-4" />
          </>
        )}
      </Button>
    </div>
  );
};

export default LessonNavigation;
