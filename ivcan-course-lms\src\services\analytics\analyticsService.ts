/**
 * Analytics Service
 * 
 * This service provides functions for logging events and analytics data.
 * It can be used to track user actions, errors, and other events.
 */

import { supabase } from '@/integrations/supabase/client';

/**
 * Log an event to the analytics system
 * @param eventName The name of the event
 * @param eventData Additional data about the event
 */
export async function logEvent(eventName: string, eventData: Record<string, any> = {}): Promise<void> {
  try {
    // Log to console in development
    if (import.meta.env.DEV) {
      console.log(`[ANALYTICS] ${eventName}:`, eventData);
    }
    
    // Log to database if we're in production
    if (import.meta.env.PROD) {
      const { error } = await supabase
        .from('analytics_events')
        .insert({
          event_name: eventName,
          event_data: eventData,
          created_at: new Date().toISOString()
        });
      
      if (error) {
        console.error('Error logging analytics event:', error);
      }
    }
  } catch (error) {
    // Fail silently in production, log in development
    if (import.meta.env.DEV) {
      console.error('Error in analytics service:', error);
    }
  }
}
