var W=Object.defineProperty;var E=Object.getOwnPropertySymbols;var G=Object.prototype.hasOwnProperty,J=Object.prototype.propertyIsEnumerable;var A=(l,s,r)=>s in l?W(l,s,{enumerable:!0,configurable:!0,writable:!0,value:r}):l[s]=r,R=(l,s)=>{for(var r in s||(s={}))G.call(s,r)&&A(l,r,s[r]);if(E)for(var r of E(s))J.call(s,r)&&A(l,r,s[r]);return l};var z=(l,s,r)=>A(l,typeof s!="symbol"?s+"":s,r);import{r as c,bv as Q,j as e,bQ as V,aq as Y,bw as K,bx as X,by as Z,bA as $,bB as ee,bC as se,bD as te,bE as re,bF as ae,bG as ie,bH as le,bI as ne,bJ as oe,b0 as ce,bK as de,b2 as M,bU as S,bM as ue,bN as he,bR as me}from"./vendor-react.BcAa1DKr.js";import{E as ge}from"./enhanced-markdown-preview.Du81wch8.js";import{ak as xe,al as pe,am as be,an as fe,ao as we,ap as ke,aq as je,ar as Ce,as as ve,at as Te,au as Ne,av as ye,aw as Le,ay as Ae,az as He}from"./vendor.DQpuTRuB.js";import{T as P,a as O,b,c as f}from"./tabs.B0SF6qIv.js";import{c as H,T as Ee,d as Re,e as ze,B as y,f as $e}from"./index.BLDhDn0D.js";import{M as Me}from"./markdown-preview.Bw0U-NJA.js";import{m as B}from"./content-converter.L-GziWIP.js";import{C,a as v,b as T,d as N}from"./card.B9V6b2DK.js";import"./vendor-supabase.sufZ44-y.js";class Se{constructor(s={}){z(this,"options");this.options=R({tightLists:!1},s)}serialize(s){return this.serializeNode(s).trim()}serializeNode(s){switch(s.type.name){case"doc":return this.serializeChildren(s);case"paragraph":const r=this.serializeChildren(s);return r?r+`

`:"";case"heading":const a=s.attrs.level||1,i=this.serializeChildren(s);return"#".repeat(a)+" "+i+`

`;case"bulletList":return this.serializeList(s,"-");case"orderedList":return this.serializeList(s,"1.");case"listItem":return this.serializeChildren(s);case"taskList":return this.serializeTaskList(s);case"taskItem":const o=s.attrs.checked,u=this.serializeChildren(s);return`- [${o?"x":" "}] ${u}`;case"codeBlock":case"codeBlockLowlight":const h=s.attrs.language||"",m=s.textContent;return"```"+h+`
`+m+"\n```\n\n";case"blockquote":return this.serializeChildren(s).split(`
`).map(L=>"> "+L).join(`
`)+`

`;case"horizontalRule":return`---

`;case"hardBreak":return`  
`;case"image":const t=s.attrs.src||"",w=s.attrs.alt||"",k=s.attrs.title;return k?`![${w}](${t} "${k}")`:`![${w}](${t})`;case"table":return this.serializeTable(s);case"tableRow":return this.serializeTableRow(s);case"tableCell":case"tableHeader":return this.serializeTableCell(s);case"text":return this.serializeText(s);default:return this.serializeChildren(s)}}serializeChildren(s){let r="";return s.forEach(a=>{r+=this.serializeNode(a)}),r}serializeText(s){let r=s.text||"";if(s.marks)for(const a of s.marks)switch(a.type.name){case"bold":case"strong":r=`**${r}**`;break;case"italic":case"em":r=`*${r}*`;break;case"code":r=`\`${r}\``;break;case"link":const i=a.attrs.href||"",o=a.attrs.title;r=o?`[${r}](${i} "${o}")`:`[${r}](${i})`;break;case"underline":r=`<u>${r}</u>`;break;case"highlight":r=`==${r}==`;break}return r}serializeList(s,r){let a="",i=1;return s.forEach(o=>{const u=this.serializeChildren(o).trim(),h=r==="1."?`${i}.`:r;a+=`${h} ${u}
`,i++}),a+`
`}serializeTaskList(s){let r="";return s.forEach(a=>{r+=this.serializeNode(a)+`
`}),r+`
`}serializeTable(s){let r="",a=!0;return s.forEach(i=>{if(i.type.name==="tableRow"){const o=this.serializeTableRow(i);if(r+=o,a){const u=i.childCount,h="|"+" --- |".repeat(u)+`
`;r+=h,a=!1}}}),r+`
`}serializeTableRow(s){let r="|";return s.forEach(a=>{if(a.type.name==="tableCell"||a.type.name==="tableHeader"){const i=this.serializeTableCell(a);r+=` ${i} |`}}),r+`
`}serializeTableCell(s){return this.serializeChildren(s).trim().replace(/\|/g,"\\|").replace(/\n/g," ")}}function Be(l,s){return new Se(s).serialize(l)}function Ie(l){if(!l)return"";let s=l;return s=s.replace(/<h([1-6])>(.*?)<\/h[1-6]>/gi,(r,a,i)=>"#".repeat(parseInt(a))+" "+i+`

`),s=s.replace(/<(strong|b)>(.*?)<\/(strong|b)>/gi,"**$2**"),s=s.replace(/<(em|i)>(.*?)<\/(em|i)>/gi,"*$2*"),s=s.replace(/<code>(.*?)<\/code>/gi,"`$1`"),s=s.replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gi,"[$2]($1)"),s=s.replace(/<img[^>]*src="([^"]*)"[^>]*alt="([^"]*)"[^>]*>/gi,"![$2]($1)"),s=s.replace(/<p>(.*?)<\/p>/gi,`$1

`),s=s.replace(/<br\s*\/?>/gi,`  
`),s=s.replace(new RegExp("<ul>(.*?)<\\/ul>","gis"),(r,a)=>a.replace(/<li>(.*?)<\/li>/gi,`- $1
`)+`
`),s=s.replace(new RegExp("<ol>(.*?)<\\/ol>","gis"),(r,a)=>{let i=1;return a.replace(/<li>(.*?)<\/li>/gi,()=>`${i++}. $1
`)+`
`}),s=s.replace(new RegExp("<blockquote>(.*?)<\\/blockquote>","gis"),(r,a)=>a.split(`
`).map(i=>"> "+i.trim()).join(`
`)+`

`),s=s.replace(new RegExp('<pre><code(?:\\s+class="language-(\\w+)")?>(.*?)<\\/code><\\/pre>',"gis"),(r,a,i)=>"```"+(a||"")+`
`+i+"\n```\n\n"),s=s.replace(new RegExp("<table[^>]*>(.*?)<\\/table>","gis"),(r,a)=>{let i="",o=!0;const u=a.match(new RegExp("<tr[^>]*>(.*?)<\\/tr>","gis"));return u&&u.forEach(h=>{const m=h.match(new RegExp("<t[hd][^>]*>(.*?)<\\/t[hd]>","gis"));if(m){const g=m.map(t=>t.replace(/<[^>]*>/g,"").trim().replace(/\|/g,"\\|"));i+="| "+g.join(" | ")+` |
`,o&&(i+="|"+" --- |".repeat(g.length)+`
`,o=!1)}}),i+`
`}),s=s.replace(/<[^>]*>/g,""),s=s.replace(/\n{3,}/g,`

`),s.trim()}const Pe=xe(He);function Oe({initialContent:l="",onChange:s,placeholder:r="Start writing your lesson content...",className:a="",minHeight:i=400,autoFocus:o=!1}){const[u,h]=c.useState("editor"),[m,g]=c.useState(l),t=Q({extensions:[pe.configure({codeBlock:!1}),be.configure({lowlight:Pe,defaultLanguage:"plaintext",HTMLAttributes:{class:"rounded-md bg-muted/70 p-4 overflow-x-auto"}}),fe.configure({HTMLAttributes:{class:"rounded-lg max-w-full h-auto"}}),we.configure({openOnClick:!1,HTMLAttributes:{rel:"noopener noreferrer",target:"_blank",class:"text-blue-600 hover:text-blue-800 underline"}}),ke,je.configure({HTMLAttributes:{class:"bg-yellow-200 dark:bg-yellow-800"}}),Ce,ve.configure({nested:!0}),Te.configure({resizable:!0,lastColumnResizable:!1,allowTableNodeSelection:!0,HTMLAttributes:{class:"border-collapse border border-border w-full my-4"}}),Ne.configure({HTMLAttributes:{class:"border-b border-border"}}),ye.configure({HTMLAttributes:{class:"bg-muted font-semibold text-left p-3 border border-border"}}),Le.configure({HTMLAttributes:{class:"p-3 border border-border"}}),Ae.configure({placeholder:r})],content:B(l),onUpdate:({editor:d})=>{try{const p=d.state.doc,x=Be(p);g(x),s==null||s(x)}catch(p){console.error("Error converting to markdown:",p);const x=d.getHTML(),j=Ie(x);g(j),s==null||s(j)}},editorProps:{attributes:{class:H("prose prose-sm max-w-none focus:outline-none dark:prose-invert","min-h-[400px] p-4"),style:`min-height: ${i}px`}},autofocus:o});c.useEffect(()=>{if(t&&l!==m){const d=B(l);t.commands.setContent(d),g(l)}},[l,t,m]);const w=c.useCallback(()=>{const d=prompt("Enter image URL:");d&&t&&t.chain().focus().setImage({src:d}).run()},[t]),k=c.useCallback(()=>{const d=window.prompt("Enter the URL:");d&&t&&t.chain().focus().setLink({href:d}).run()},[t]),L=c.useCallback(()=>{t&&t.chain().focus().insertTable({rows:3,cols:3,withHeaderRow:!0}).run()},[t]),U=c.useCallback(()=>{t&&t.chain().focus().addColumnBefore().run()},[t]);c.useCallback(()=>{t&&t.chain().focus().addColumnAfter().run()},[t]);const q=c.useCallback(()=>{t&&t.chain().focus().deleteColumn().run()},[t]);c.useCallback(()=>{t&&t.chain().focus().addRowBefore().run()},[t]);const F=c.useCallback(()=>{t&&t.chain().focus().addRowAfter().run()},[t]),_=c.useCallback(()=>{t&&t.chain().focus().deleteRow().run()},[t]);if(c.useCallback(()=>{t&&t.chain().focus().deleteTable().run()},[t]),!t)return null;const n=({onClick:d,isActive:p=!1,disabled:x=!1,title:j,children:D})=>e.jsx(Ee,{children:e.jsxs(Re,{children:[e.jsx(ze,{asChild:!0,children:e.jsx(y,{type:"button",variant:"ghost",size:"sm",onClick:d,disabled:x,className:H("h-8 w-8 p-0",p?"bg-muted dark:bg-muted/50":""),children:D})}),e.jsx($e,{children:e.jsx("p",{children:j})})]})});return e.jsx("div",{className:H("border rounded-md bg-card text-card-foreground",a),children:e.jsxs(P,{value:u,onValueChange:d=>h(d),children:[e.jsx("div",{className:"flex items-center justify-between border-b bg-muted/30 dark:bg-muted/10",children:e.jsxs(O,{className:"bg-transparent border-none",children:[e.jsxs(b,{value:"editor",className:"flex items-center gap-2",children:[e.jsx(V,{className:"h-4 w-4"}),"Editor"]}),e.jsxs(b,{value:"preview",className:"flex items-center gap-2",children:[e.jsx(Y,{className:"h-4 w-4"}),"Preview"]})]})}),e.jsxs(f,{value:"editor",className:"p-0 m-0",children:[e.jsxs("div",{className:"flex flex-wrap gap-1 p-2 border-b border-border bg-muted/30 dark:bg-muted/10",children:[e.jsx(n,{onClick:()=>t.chain().focus().toggleBold().run(),isActive:t.isActive("bold"),title:"Bold",children:e.jsx(K,{className:"h-4 w-4"})}),e.jsx(n,{onClick:()=>t.chain().focus().toggleItalic().run(),isActive:t.isActive("italic"),title:"Italic",children:e.jsx(X,{className:"h-4 w-4"})}),e.jsx(n,{onClick:()=>t.chain().focus().toggleUnderline().run(),isActive:t.isActive("underline"),title:"Underline",children:e.jsx(Z,{className:"h-4 w-4"})}),e.jsx(n,{onClick:()=>t.chain().focus().toggleCode().run(),isActive:t.isActive("code"),title:"Inline Code",children:e.jsx($,{className:"h-4 w-4"})}),e.jsx(n,{onClick:()=>t.chain().focus().toggleHighlight().run(),isActive:t.isActive("highlight"),title:"Highlight",children:e.jsx(ee,{className:"h-4 w-4"})}),e.jsx("div",{className:"w-px h-8 bg-gray-300 mx-1"}),e.jsx(n,{onClick:()=>t.chain().focus().toggleHeading({level:1}).run(),isActive:t.isActive("heading",{level:1}),title:"Heading 1",children:e.jsx(se,{className:"h-4 w-4"})}),e.jsx(n,{onClick:()=>t.chain().focus().toggleHeading({level:2}).run(),isActive:t.isActive("heading",{level:2}),title:"Heading 2",children:e.jsx(te,{className:"h-4 w-4"})}),e.jsx(n,{onClick:()=>t.chain().focus().toggleHeading({level:3}).run(),isActive:t.isActive("heading",{level:3}),title:"Heading 3",children:e.jsx(re,{className:"h-4 w-4"})}),e.jsx("div",{className:"w-px h-8 bg-gray-300 mx-1"}),e.jsx(n,{onClick:()=>t.chain().focus().toggleBulletList().run(),isActive:t.isActive("bulletList"),title:"Bullet List",children:e.jsx(ae,{className:"h-4 w-4"})}),e.jsx(n,{onClick:()=>t.chain().focus().toggleOrderedList().run(),isActive:t.isActive("orderedList"),title:"Numbered List",children:e.jsx(ie,{className:"h-4 w-4"})}),e.jsx(n,{onClick:()=>t.chain().focus().toggleTaskList().run(),isActive:t.isActive("taskList"),title:"Task List",children:e.jsx(le,{className:"h-4 w-4"})}),e.jsx("div",{className:"w-px h-8 bg-gray-300 mx-1"}),e.jsx(n,{onClick:()=>t.chain().focus().toggleBlockquote().run(),isActive:t.isActive("blockquote"),title:"Quote",children:e.jsx(ne,{className:"h-4 w-4"})}),e.jsx(n,{onClick:()=>t.chain().focus().toggleCodeBlock().run(),isActive:t.isActive("codeBlock")||t.isActive("codeBlockLowlight"),title:"Code Block",children:e.jsx($,{className:"h-4 w-4"})}),e.jsx("div",{className:"w-px h-8 bg-gray-300 mx-1"}),e.jsx(n,{onClick:k,isActive:t.isActive("link"),title:"Add Link",children:e.jsx(oe,{className:"h-4 w-4"})}),e.jsx(n,{onClick:w,title:"Add Image",children:e.jsx(ce,{className:"h-4 w-4"})}),e.jsx("div",{className:"w-px h-8 bg-gray-300 mx-1"}),e.jsx(n,{onClick:L,title:"Insert Table",children:e.jsx(de,{className:"h-4 w-4"})}),t.isActive("table")&&e.jsxs(e.Fragment,{children:[e.jsx(n,{onClick:U,title:"Add Column Before",children:e.jsx(M,{className:"h-3 w-3"})}),e.jsx(n,{onClick:F,title:"Add Row After",children:e.jsx(M,{className:"h-3 w-3"})}),e.jsx(n,{onClick:q,title:"Delete Column",children:e.jsx(S,{className:"h-3 w-3"})}),e.jsx(n,{onClick:_,title:"Delete Row",children:e.jsx(S,{className:"h-3 w-3"})})]}),e.jsx("div",{className:"w-px h-8 bg-gray-300 mx-1"}),e.jsx(n,{onClick:()=>t.chain().focus().undo().run(),disabled:!t.can().undo(),title:"Undo",children:e.jsx(ue,{className:"h-4 w-4"})}),e.jsx(n,{onClick:()=>t.chain().focus().redo().run(),disabled:!t.can().redo(),title:"Redo",children:e.jsx(he,{className:"h-4 w-4"})})]}),e.jsx(me,{editor:t})]}),e.jsx(f,{value:"preview",className:"p-6 m-0",style:{minHeight:`${i}px`},children:e.jsx("div",{className:"github-markdown-preview",children:e.jsx(Me,{content:m,className:"prose prose-gray max-w-none dark:prose-invert",allowHtml:!0})})})]})})}const I=`# Markdown Debug Test

## Tables Test

| Feature | Status | Priority | Notes |
|---------|--------|----------|-------|
| Tables | ✅ Working | High | Should display with borders and styling |
| Code Blocks | ✅ Working | High | Syntax highlighting should work |
| Task Lists | ✅ Working | Medium | Checkboxes should be styled |
| Inline Code | \`working\` | Low | Should have background color |

## Code Blocks Test

### JavaScript Example
\`\`\`javascript
function greetUser(name) {
  console.log(\`Hello, \${name}!\`);
  return \`Welcome to the course, \${name}\`;
}

const user = "Student";
greetUser(user);
\`\`\`

### Python Example
\`\`\`python
def calculate_grade(score, total):
    percentage = (score / total) * 100
    if percentage >= 90:
        return "A"
    elif percentage >= 80:
        return "B"
    elif percentage >= 70:
        return "C"
    else:
        return "F"

print(calculate_grade(85, 100))
\`\`\`

### SQL Example
\`\`\`sql
SELECT
    c.course_name,
    COUNT(e.user_id) as enrolled_students,
    AVG(p.completion_percentage) as avg_completion
FROM courses c
LEFT JOIN enrollments e ON c.id = e.course_id
LEFT JOIN progress p ON e.id = p.enrollment_id
GROUP BY c.id, c.course_name
ORDER BY enrolled_students DESC;
\`\`\`

## Accordion Test

<details>
<summary>📚 Course Content Structure</summary>

This section explains how course content is organized:

1. **Courses** - Top level containers
2. **Modules** - Logical groupings of lessons
3. **Lessons** - Individual learning units

### Code Example
\`\`\`typescript
interface Course {
  id: string;
  title: string;
  modules: Module[];
}

interface Module {
  id: string;
  title: string;
  lessons: Lesson[];
}
\`\`\`

</details>

<details>
<summary>🔧 Technical Implementation</summary>

The markdown editor uses several technologies:

- **TipTap** for rich text editing
- **Prism.js** for syntax highlighting
- **DOMPurify** for security
- **Tailwind CSS** for styling

| Component | Purpose | Status |
|-----------|---------|--------|
| TipTap Editor | Rich text editing | ✅ |
| Markdown Preview | Content display | ✅ |
| Table Support | Data presentation | ✅ |
| Code Highlighting | Syntax coloring | ✅ |

</details>

## Task Lists Test

### Course Progress
- [x] Set up development environment
- [x] Install dependencies
- [x] Configure TipTap editor
- [x] Add table support
- [x] Fix code block rendering
- [ ] Add more advanced features
- [ ] Write comprehensive tests
- [ ] Deploy to production

### Feature Checklist
- [x] **Tables** - Properly styled with borders
- [x] **Code Blocks** - Syntax highlighting works
- [x] **Inline Code** - Background styling applied
- [x] **Task Lists** - Checkboxes render correctly
- [ ] **Math Equations** - LaTeX support (future)
- [ ] **Diagrams** - Mermaid integration (future)

## Inline Elements Test

Here's some text with \`inline code\`, **bold text**, *italic text*, and ~~strikethrough~~.

You can also have [links](https://example.com) and images (if uploaded).

## Callouts Test

> [!INFO]
> This is an informational callout. It should have a blue color scheme and an info icon.

> [!WARNING]
> This is a warning callout. It should have a yellow/orange color scheme and a warning icon.

> [!ERROR]
> This is an error callout. It should have a red color scheme and an error icon.

> [!SUCCESS]
> This is a success callout. It should have a green color scheme and a success icon.
`;function Ye(){const[l,s]=c.useState(!1),[r,a]=c.useState(""),[i,o]=c.useState(I),u=()=>{const h=document.querySelector(".professional-lesson-preview");h&&(a(h.innerHTML),s(!0))};return e.jsxs("div",{className:"container mx-auto py-8 px-4 max-w-6xl",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold mb-4",children:"Markdown Debug Tool"}),e.jsx("p",{className:"text-muted-foreground mb-4",children:"This tool helps debug the markdown rendering to ensure tables, code blocks, and other features work correctly in both the editor and preview modes."}),e.jsxs("div",{className:"flex gap-2 mb-4",children:[e.jsx(y,{onClick:u,children:"Show Raw HTML"}),e.jsx(y,{variant:"outline",onClick:()=>s(!1),children:"Hide Raw HTML"}),e.jsx(y,{variant:"outline",onClick:()=>o(I),children:"Reset Content"})]})]}),e.jsxs(P,{defaultValue:"preview",className:"w-full",children:[e.jsxs(O,{className:"grid w-full grid-cols-3",children:[e.jsx(b,{value:"preview",children:"Preview Only"}),e.jsx(b,{value:"editor",children:"TipTap Editor"}),e.jsx(b,{value:"source",children:"Source Code"})]}),e.jsx(f,{value:"preview",className:"space-y-6",children:e.jsxs(C,{children:[e.jsx(v,{children:e.jsx(T,{children:"Enhanced Markdown Preview"})}),e.jsx(N,{children:e.jsx(ge,{content:i,enableImageZoom:!0,enableCodeCopy:!0,enableExport:!1})})]})}),e.jsx(f,{value:"editor",className:"space-y-6",children:e.jsxs(C,{children:[e.jsx(v,{children:e.jsx(T,{children:"TipTap Markdown Editor"})}),e.jsx(N,{children:e.jsx(Oe,{initialContent:i,onChange:o,placeholder:"Start typing to test the editor...",minHeight:600,autoFocus:!1})})]})}),e.jsxs(f,{value:"source",className:"space-y-6",children:[e.jsxs(C,{children:[e.jsx(v,{children:e.jsx(T,{children:"Current Markdown Source"})}),e.jsx(N,{children:e.jsx("pre",{className:"bg-muted p-4 rounded-lg text-sm overflow-auto max-h-96",children:e.jsx("code",{children:i})})})]}),l&&e.jsxs(C,{children:[e.jsx(v,{children:e.jsx(T,{children:"Raw HTML Output"})}),e.jsx(N,{children:e.jsx("pre",{className:"bg-muted p-4 rounded-lg text-sm overflow-auto max-h-96",children:e.jsx("code",{children:r})})})]})]})]})]})}export{Ye as default};
