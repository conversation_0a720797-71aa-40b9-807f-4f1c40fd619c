
import React from 'react';
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Video, Image, Link } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import FileDropZone from './FileDropZone';
import { processVideoUrl } from '@/lib/file-upload';

interface MediaOptionsFormProps {
  form: UseFormReturn<any>;
  hasVideo: boolean;
  setHasVideo: React.Dispatch<React.SetStateAction<boolean>>;
  hasImage: boolean;
  setHasImage: React.Dispatch<React.SetStateAction<boolean>>;
  videoUrl: string;
  imageUrl: string;
}

const MediaOptionsForm: React.FC<MediaOptionsFormProps> = ({
  form,
  hasVideo,
  setHasVideo,
  hasImage,
  setHasImage,
  videoUrl,
  imageUrl
}) => {
  const [videoSourceType, setVideoSourceType] = React.useState<'url' | 'upload'>(
    videoUrl?.startsWith('data:') ? 'upload' : 'url'
  );

  const handleVideoFileSelected = (dataUrl: string, file: File) => {
    form.setValue('videoUrl', dataUrl);
    setVideoSourceType('upload');
  };

  const handleImageFileSelected = (dataUrl: string, file: File) => {
    form.setValue('imageUrl', dataUrl);
  };

  const handleClearVideo = () => {
    form.setValue('videoUrl', '');
  };

  const handleClearImage = () => {
    form.setValue('imageUrl', '');
  };

  return (
    <>
      <div className="flex flex-col gap-4 border-b pb-4">
        <h4 className="font-medium">Media Options</h4>

        <div className="flex items-center space-x-2">
          <Switch
            id="video-toggle"
            checked={hasVideo}
            onCheckedChange={setHasVideo}
          />
          <Label htmlFor="video-toggle" className="flex items-center gap-1">
            <Video className="w-4 h-4" /> Include Video
          </Label>
        </div>

        {hasVideo && (
          <div className="space-y-3 ml-7 mt-2">
            <Tabs value={videoSourceType} onValueChange={setVideoSourceType as any}>
              <TabsList className="grid w-full grid-cols-2 mb-2">
                <TabsTrigger value="upload">Upload Video</TabsTrigger>
                <TabsTrigger value="url">Video URL</TabsTrigger>
              </TabsList>

              <TabsContent value="upload" className="mt-0">
                <FileDropZone
                  fileType="video"
                  accept="video/*"
                  onFileSelected={handleVideoFileSelected}
                  currentUrl={videoSourceType === 'upload' ? videoUrl : ''}
                  onClear={handleClearVideo}
                />
                <p className="text-xs text-muted-foreground mt-2">
                  Upload a video file directly from your computer
                </p>
              </TabsContent>

              <TabsContent value="url" className="mt-0">
                <FormField
                  control={form.control}
                  name="videoUrl"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center gap-2">
                        <FormControl>
                          <Input
                            placeholder="https://www.youtube.com/watch?v=VIDEO_ID"
                            {...field}
                            onChange={(e) => {
                              const processedUrl = processVideoUrl(e.target.value);
                              field.onChange(processedUrl);
                            }}
                          />
                        </FormControl>
                        <Link className="w-4 h-4 text-muted-foreground" />
                      </div>
                      <FormMessage />
                      <p className="text-xs text-muted-foreground mt-1">
                        YouTube, Vimeo, or any embedded video URL
                      </p>
                    </FormItem>
                  )}
                />
              </TabsContent>
            </Tabs>
          </div>
        )}

        <div className="flex items-center space-x-2">
          <Switch
            id="image-toggle"
            checked={hasImage}
            onCheckedChange={setHasImage}
          />
          <Label htmlFor="image-toggle" className="flex items-center gap-1">
            <Image className="w-4 h-4" /> Include Image
          </Label>
        </div>

        {hasImage && (
          <div className="space-y-2 ml-7 mt-2">
            <FileDropZone
              fileType="image"
              accept="image/*"
              onFileSelected={handleImageFileSelected}
              currentUrl={imageUrl}
              onClear={handleClearImage}
            />
          </div>
        )}
      </div>

      {/* Preview section */}
      {(videoUrl || imageUrl) && videoSourceType === 'url' && (
        <div className="border border-border rounded-md p-4 bg-muted/30 dark:bg-muted/10">
          <h4 className="font-medium mb-4">Media Preview</h4>

          {videoUrl && videoSourceType === 'url' && (
            <div className="mb-4 aspect-video">
              <iframe
                src={videoUrl}
                className="w-full h-full rounded-md border border-border"
                title="Video preview"
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              />
            </div>
          )}
        </div>
      )}
    </>
  );
};

export default MediaOptionsForm;
