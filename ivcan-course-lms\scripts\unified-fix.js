/**
 * Unified Fix Script
 * 
 * This script provides a unified interface to run various fixes for the application.
 * It replaces multiple individual fix scripts with a single, more maintainable solution.
 * 
 * Usage:
 * node scripts/unified-fix.js [fix-type] [options]
 * 
 * Examples:
 * node scripts/unified-fix.js --help
 * node scripts/unified-fix.js all
 * node scripts/unified-fix.js course-completion --user=123
 * node scripts/unified-fix.js module-completion --reset --all-users
 * node scripts/unified-fix.js storage --create-buckets
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const { SUPABASE_URL, SUPABASE_ANON_KEY, SUPABASE_SERVICE_ROLE_KEY } = require('./config');

// Create Supabase client with anon key (limited permissions)
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Create service role client if available
let serviceRoleClient = null;
if (SUPABASE_SERVICE_ROLE_KEY) {
  serviceRoleClient = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
}

// Available fix types
const FIX_TYPES = {
  ALL: 'all',
  COURSE_COMPLETION: 'course-completion',
  MODULE_COMPLETION: 'module-completion',
  LESSON_PROGRESS: 'lesson-progress',
  CERTIFICATES: 'certificates',
  STORAGE: 'storage',
  AUTO_COMPLETION: 'auto-completion',
  SECURITY: 'security',
  PROFILE: 'profile',
  HELP: 'help'
};

// Parse command line arguments
function parseArgs() {
  const args = process.argv.slice(2);
  const fixType = args[0] || FIX_TYPES.HELP;
  const options = {};

  // Parse options (format: --key=value or --flag)
  for (let i = 1; i < args.length; i++) {
    const arg = args[i];
    if (arg.startsWith('--')) {
      const parts = arg.substring(2).split('=');
      if (parts.length === 1) {
        // It's a flag
        options[parts[0]] = true;
      } else {
        // It's a key-value pair
        options[parts[0]] = parts[1];
      }
    }
  }

  return { fixType, options };
}

// Display help information
function showHelp() {
  console.log(`
Unified Fix Script
------------------

Usage: node scripts/unified-fix.js [fix-type] [options]

Available fix types:
  all                 Run all fixes
  course-completion   Fix course completion issues
  module-completion   Fix module completion issues
  lesson-progress     Fix lesson progress issues
  certificates        Fix certificate issues
  storage             Fix storage issues
  auto-completion     Manage auto-completion settings
  security            Apply security fixes
  profile             Fix profile page issues
  help                Show this help message

Common options:
  --user=<user_id>    Specify a user ID
  --course=<course_id> Specify a course ID
  --all-users         Apply to all users
  --reset             Reset instead of fix
  --create-buckets    Create storage buckets
  --verbose           Show detailed output

Examples:
  node scripts/unified-fix.js all
  node scripts/unified-fix.js course-completion --user=123 --course=456
  node scripts/unified-fix.js module-completion --reset --all-users
  node scripts/unified-fix.js storage --create-buckets
  `);
}

// Fix course completion issues
async function fixCourseCompletion(options) {
  console.log('Fixing course completion issues...');
  
  const userId = options.user;
  const courseId = options.course;
  const allCourses = options.all || options['all-courses'];
  
  if (!userId) {
    console.error('Error: User ID is required. Use --user=<user_id>');
    return false;
  }
  
  try {
    const client = serviceRoleClient || supabase;
    
    if (allCourses) {
      console.log(`Fixing all courses for user ${userId}...`);
      
      // Get all courses
      const { data: courses, error: coursesError } = await client
        .from('courses')
        .select('id');
      
      if (coursesError) {
        console.error('Error fetching courses:', coursesError);
        return false;
      }
      
      // Fix each course
      for (const course of courses) {
        await fixSingleCourseCompletion(userId, course.id, client);
      }
      
      console.log('All courses fixed successfully.');
      return true;
    } else if (courseId) {
      console.log(`Fixing course ${courseId} for user ${userId}...`);
      return await fixSingleCourseCompletion(userId, courseId, client);
    } else {
      console.error('Error: Course ID is required unless --all-courses is specified.');
      return false;
    }
  } catch (error) {
    console.error('Error fixing course completion:', error);
    return false;
  }
}

// Fix a single course completion
async function fixSingleCourseCompletion(userId, courseId, client) {
  try {
    // Check if the user is enrolled in the course
    const { data: enrollment, error: enrollmentError } = await client
      .from('user_course_enrollment')
      .select('*')
      .eq('user_id', userId)
      .eq('course_id', courseId)
      .maybeSingle();
    
    if (enrollmentError) {
      console.error(`Error checking enrollment for course ${courseId}:`, enrollmentError);
      return false;
    }
    
    if (!enrollment) {
      console.log(`User ${userId} is not enrolled in course ${courseId}. Enrolling now...`);
      
      // Enroll the user
      const { error: insertError } = await client
        .from('user_course_enrollment')
        .insert({
          user_id: userId,
          course_id: courseId,
          status: 'in_progress',
          enrolled_at: new Date().toISOString()
        });
      
      if (insertError) {
        console.error(`Error enrolling user in course ${courseId}:`, insertError);
        return false;
      }
    }
    
    // Get all modules for the course
    const { data: modules, error: modulesError } = await client
      .from('modules')
      .select('id')
      .eq('course_id', courseId);
    
    if (modulesError) {
      console.error(`Error fetching modules for course ${courseId}:`, modulesError);
      return false;
    }
    
    // Mark all modules as completed
    for (const module of modules) {
      // Check if module progress exists
      const { data: moduleProgress, error: progressError } = await client
        .from('user_module_progress')
        .select('*')
        .eq('user_id', userId)
        .eq('module_id', module.id)
        .maybeSingle();
      
      if (progressError) {
        console.error(`Error checking module progress for module ${module.id}:`, progressError);
        continue;
      }
      
      if (moduleProgress) {
        // Update existing progress
        const { error: updateError } = await client
          .from('user_module_progress')
          .update({ is_completed: true, updated_at: new Date().toISOString() })
          .eq('id', moduleProgress.id);
        
        if (updateError) {
          console.error(`Error updating module progress for module ${module.id}:`, updateError);
        }
      } else {
        // Create new progress
        const { error: insertError } = await client
          .from('user_module_progress')
          .insert({
            user_id: userId,
            module_id: module.id,
            is_completed: true
          });
        
        if (insertError) {
          console.error(`Error creating module progress for module ${module.id}:`, insertError);
        }
      }
    }
    
    // Mark the course as completed
    const { error: updateError } = await client
      .from('user_course_enrollment')
      .update({
        status: 'completed',
        completed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .eq('course_id', courseId);
    
    if (updateError) {
      console.error(`Error marking course ${courseId} as completed:`, updateError);
      return false;
    }
    
    console.log(`Course ${courseId} fixed successfully for user ${userId}.`);
    return true;
  } catch (error) {
    console.error(`Error fixing course ${courseId} for user ${userId}:`, error);
    return false;
  }
}

// Fix module completion issues
async function fixModuleCompletion(options) {
  console.log('Fixing module completion issues...');
  
  const userId = options.user;
  const moduleId = options.module;
  const allUsers = options['all-users'];
  const reset = options.reset;
  
  try {
    const client = serviceRoleClient || supabase;
    
    if (reset) {
      if (allUsers) {
        console.log('Resetting all module completion statuses for all users...');
        
        // Reset all module completion statuses in the modules table
        const { error: moduleError } = await client
          .from('modules')
          .update({ is_completed: false })
          .neq('id', 'dummy'); // This will update all records
        
        if (moduleError) {
          console.error('Error resetting module completion statuses:', moduleError);
          return false;
        }
        
        // Reset all user module progress records
        const { error: progressError } = await client
          .from('user_module_progress')
          .update({ is_completed: false, updated_at: new Date().toISOString() })
          .neq('id', 'dummy'); // This will update all records
        
        if (progressError) {
          console.error('Error resetting user module progress records:', progressError);
          return false;
        }
        
        console.log('All module completion statuses reset successfully.');
        return true;
      } else if (userId) {
        console.log(`Resetting module completion statuses for user ${userId}...`);
        
        // Reset user module progress records for the specified user
        const { error: progressError } = await client
          .from('user_module_progress')
          .update({ is_completed: false, updated_at: new Date().toISOString() })
          .eq('user_id', userId);
        
        if (progressError) {
          console.error(`Error resetting module progress for user ${userId}:`, progressError);
          return false;
        }
        
        console.log(`Module completion statuses reset successfully for user ${userId}.`);
        return true;
      } else {
        console.error('Error: User ID is required unless --all-users is specified.');
        return false;
      }
    } else {
      // Fix module completion (not reset)
      console.log('Fixing module completion statuses...');
      
      // This would involve more complex logic to determine which modules should be completed
      // based on lesson progress, etc.
      console.log('Module completion fixing not implemented yet.');
      return false;
    }
  } catch (error) {
    console.error('Error fixing module completion:', error);
    return false;
  }
}

// Fix storage issues
async function fixStorage(options) {
  console.log('Fixing storage issues...');
  
  const createBuckets = options['create-buckets'];
  const fixRls = options['fix-rls'] || options.rls;
  
  try {
    if (createBuckets) {
      console.log('Creating storage buckets...');
      
      const buckets = ['course-images', 'avatars', 'app-uploads'];
      const client = serviceRoleClient || supabase;
      
      for (const bucket of buckets) {
        console.log(`Creating bucket: ${bucket}`);
        
        const { data, error } = await client.storage.createBucket(bucket, {
          public: true
        });
        
        if (error) {
          if (error.message.includes('already exists')) {
            console.log(`Bucket ${bucket} already exists.`);
          } else {
            console.error(`Error creating bucket ${bucket}:`, error);
          }
        } else {
          console.log(`Bucket ${bucket} created successfully.`);
        }
      }
    }
    
    if (fixRls) {
      console.log('Fixing storage RLS policies...');
      
      // Read the SQL file
      const sqlPath = path.join(__dirname, '..', 'supabase', 'migrations', '20250501001_fix_storage_rls.sql');
      
      if (!fs.existsSync(sqlPath)) {
        console.error('Storage RLS SQL file not found:', sqlPath);
        return false;
      }
      
      const sql = fs.readFileSync(sqlPath, 'utf8');
      
      // Split SQL into statements
      const statements = sql
        .split(';')
        .filter(stmt => stmt.trim().length > 0)
        .map(stmt => stmt.trim() + ';');
      
      // Execute each statement
      const client = serviceRoleClient || supabase;
      
      for (const statement of statements) {
        try {
          const { error } = await client.rpc('exec_sql', { sql: statement });
          
          if (error) {
            console.error('Error executing SQL statement:', error);
          }
        } catch (error) {
          console.error('Error executing SQL statement:', error);
        }
      }
      
      console.log('Storage RLS policies fixed successfully.');
    }
    
    return true;
  } catch (error) {
    console.error('Error fixing storage:', error);
    return false;
  }
}

// Run all fixes
async function runAllFixes() {
  console.log('Running all fixes...');
  
  const results = {
    courseCompletion: await fixCourseCompletion({ all: true }),
    moduleCompletion: await fixModuleCompletion({ 'all-users': true }),
    storage: await fixStorage({ 'create-buckets': true, 'fix-rls': true }),
    // Add other fixes here
  };
  
  console.log('\n--- Fix Results ---');
  for (const [fix, result] of Object.entries(results)) {
    console.log(`${fix}: ${result ? 'Success' : 'Failed'}`);
  }
  
  if (Object.values(results).every(result => result)) {
    console.log('\nAll fixes applied successfully!');
    return true;
  } else {
    console.log('\nSome fixes failed. Please check the logs above for details.');
    return false;
  }
}

// Main function
async function main() {
  const { fixType, options } = parseArgs();
  
  switch (fixType) {
    case FIX_TYPES.ALL:
      await runAllFixes();
      break;
    case FIX_TYPES.COURSE_COMPLETION:
      await fixCourseCompletion(options);
      break;
    case FIX_TYPES.MODULE_COMPLETION:
      await fixModuleCompletion(options);
      break;
    case FIX_TYPES.STORAGE:
      await fixStorage(options);
      break;
    case FIX_TYPES.HELP:
    default:
      showHelp();
      break;
  }
}

// Run the script
main().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
