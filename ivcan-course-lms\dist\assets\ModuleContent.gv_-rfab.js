var k=(s,t,o)=>new Promise((g,h)=>{var r=i=>{try{l(o.next(i))}catch(u){h(u)}},x=i=>{try{l(o.throw(i))}catch(u){h(u)}},l=i=>i.done?g(i.value):Promise.resolve(i.value).then(r,x);l((o=o.apply(s,t)).next())});import{u as D,j as e,aC as z,ag as P,af as M,aD as T,aE as I,E as q,aF as U,a9 as A,r as w,aw as R,av as H,aG as O,aH as V,az as S,R as G,ax as J}from"./vendor-react.BcAa1DKr.js";import{L}from"./Layout.DRjmVYQG.js";import{a as K,b as W,c as n,B as f,s as $,T as X,d as Z,e as ee,f as re,u as te}from"./index.BLDhDn0D.js";import{R as se,g as _,a as ae}from"./responsive-image.BMcUeCRG.js";import{P as le}from"./progress.BMuwHUJX.js";import{M as oe}from"./markdown-preview.Bw0U-NJA.js";import{a4 as ie,a2 as v}from"./vendor.DQpuTRuB.js";import{C as de}from"./CourseCompletionCelebration.BJZAxw59.js";import{finishCourse as ne}from"./completionService.BUb78ZaE.js";import{f as ce,a as me}from"./courseApi.BQX5-7u-.js";import{M as xe}from"./loading-skeleton.DxUGnZ26.js";import{P as ge,C as he}from"./floating-sidebar-container.BxlLgzat.js";import{g as ue,e as pe}from"./enrollmentApi.CstnsQi_.js";import"./vendor-supabase.sufZ44-y.js";import"./skeleton.C0Lb8Xms.js";import"./content-converter.L-GziWIP.js";import"./confetti.ShHySCrk.js";import"./achievementService.Vkx_BHml.js";import"./utils.Qa9QlCj_.js";const fe=({course:s,modules:t,isEnrolled:o,isCourseCompleted:g=!1,onStartCourse:h,isStarting:r})=>{const x=D(),l=K();W();const i=t.flatMap(a=>a.lessons||[]);i.filter(a=>a.type==="lesson"),i.filter(a=>a.type==="quiz");const u=()=>{try{const a=s.image_url||s.image;return ae(a,s.title)}catch(a){return console.error("Error getting image source:",a),_(s.title)}},N=()=>i.reduce((a,p)=>{var E;const m=parseInt(((E=p.duration)==null?void 0:E.split(":")[0])||"0");return a+(isNaN(m)?0:m)},0),y=()=>i.length,b=()=>{if(!t.length)return 0;const a=t.filter(p=>p.is_completed).length;return Math.round(a/t.length*100)},d=()=>t.filter(a=>a.is_completed).length;return e.jsxs("div",{className:n("course-detail-container animate-in fade-in duration-500",l?"px-0 pt-0 pb-1":"px-4 sm:px-6 pt-6 pb-8"),children:[e.jsx("div",{className:"relative w-full overflow-hidden rounded-t-xl",children:e.jsx("div",{className:"course-image-container",children:u()?e.jsx(se,{src:u()||"",alt:s.title,className:"course-image",objectFit:"cover",aspectRatio:"21/9",loadingStrategy:"eager",placeholderColor:"rgba(0,0,0,0.05)",containerClassName:"w-full h-full",fallback:_(s.title),overlayText:s.title,overlayTextClassName:"course-header-title",onError:a=>{console.error(`Image failed to load for course "${s.title||"Untitled"}":`);const p=a.target;p.src=_(s.title)||""}}):e.jsx("div",{className:"course-image-fallback",children:e.jsxs("div",{className:"course-header-content",children:[e.jsx("h1",{className:"course-header-title",children:s.title}),s.description&&e.jsx("p",{className:"course-header-description",children:s.description})]})})})}),e.jsx("div",{className:n("course-content-section",l?"mt-1 px-1":"mt-6"),children:e.jsxs("div",{className:n("grid",l?"grid-cols-1 gap-2":"grid-cols-1 lg:grid-cols-3 xl:grid-cols-4 gap-6"),children:[!l&&e.jsx("div",{className:"xl:col-span-1 lg:col-span-1 space-y-6 lg:sticky lg:top-20 self-start",children:e.jsxs("div",{className:n("course-stats-container bg-white dark:bg-gray-800/90 rounded-xl border border-gray-100 dark:border-gray-800/50 shadow-sm overflow-hidden transition-all duration-300 hover:shadow-md hover:border-primary/20"),children:[e.jsx("div",{className:"bg-gradient-to-r from-red-50 to-red-100/50 dark:from-red-950/20 dark:to-red-900/10 px-6 py-4 border-b border-gray-100/80 dark:border-gray-800/50",children:e.jsxs("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white flex items-center",children:[e.jsx(z,{className:"w-5 h-5 mr-2 text-red-500 dark:text-red-400"}),"Course Details"]})}),e.jsxs("div",{className:"p-5 space-y-4",children:[e.jsxs("div",{className:"flex items-center p-3 bg-gray-50/80 dark:bg-gray-800/80 rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700/80",children:[e.jsx(P,{className:"w-5 h-5 text-red-500 dark:text-red-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium dark:text-gray-200",children:"Total Duration"}),e.jsxs("p",{className:"text-xs text-gray-600 dark:text-gray-400 mt-0.5",children:[N()," minutes"]})]})]}),e.jsxs("div",{className:"flex items-center p-3 bg-gray-50/80 dark:bg-gray-800/80 rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700/80",children:[e.jsx(M,{className:"w-5 h-5 text-red-500 dark:text-red-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium dark:text-gray-200",children:"Course Modules"}),e.jsxs("p",{className:"text-xs text-gray-600 dark:text-gray-400 mt-0.5",children:[t.length," modules"]})]})]}),e.jsxs("div",{className:"flex items-center p-3 bg-gray-50/80 dark:bg-gray-800/80 rounded-lg transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-700/80",children:[e.jsx(T,{className:"w-5 h-5 text-red-500 dark:text-red-400 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium dark:text-gray-200",children:"Total Lessons"}),e.jsxs("p",{className:"text-xs text-gray-600 dark:text-gray-400 mt-0.5",children:[y()," lessons"]})]})]})]}),o&&e.jsxs("div",{className:"border-t border-gray-100/80 dark:border-gray-800/50 p-5",children:[e.jsxs("h3",{className:"text-sm font-semibold mb-3 flex items-center text-gray-900 dark:text-white",children:[e.jsx(I,{className:"w-4 h-4 text-red-500 dark:text-red-400 mr-2"}),"Your Progress"]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between text-xs",children:[e.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Completion"}),e.jsxs("span",{className:"font-medium text-gray-900 dark:text-white",children:[b(),"%"]})]}),e.jsx(le,{value:b(),className:"h-2 bg-gray-100 dark:bg-gray-700"})]}),e.jsxs("p",{className:"text-xs text-gray-600 dark:text-gray-400 mt-2",children:[d()," of ",t.length," modules completed"]})]}),e.jsx("div",{className:"border-t border-gray-100/80 dark:border-gray-800/50 p-5",children:o?g?e.jsxs("div",{className:"flex flex-col items-center space-y-2",children:[e.jsx(f,{variant:"outline",className:"w-full h-10 rounded-lg border-2 border-green-200 hover:bg-green-50 dark:hover:bg-green-900/20 text-green-700 dark:text-green-300 transition-all duration-300",onClick:()=>{var a;return x(`/course/${s.id}/lesson/${((a=i[0])==null?void 0:a.slug)||""}`)},children:"Review Course"}),e.jsxs("div",{className:"flex items-center rounded-full bg-green-50 dark:bg-green-900/20 border border-green-100 dark:border-green-800/40 text-green-600 dark:text-green-400 py-1.5 px-3 text-xs",children:[e.jsx(q,{className:"w-3.5 h-3.5 mr-1.5"}),e.jsx("span",{children:"Course Completed"})]})]}):e.jsx(f,{className:"w-full bg-red-500 hover:bg-red-600 text-white",onClick:()=>{var a;return x(`/course/${s.id}/lesson/${((a=i[0])==null?void 0:a.slug)||""}`)},children:"Continue Learning"}):e.jsx(f,{onClick:h,className:"w-full bg-red-500 hover:bg-red-600 text-white",disabled:r,children:r?"Starting...":"Start Course"})})]})}),e.jsxs("div",{className:n(l?"space-y-3":"xl:col-span-3 lg:col-span-2 space-y-6"),children:[e.jsxs("div",{className:n("bg-white dark:bg-gray-800/90 rounded-xl border border-gray-100/80 dark:border-gray-800/50 overflow-hidden",l?"p-2 shadow-none":"shadow-sm transition-all duration-300 hover:shadow-md hover:border-primary/20"),children:[e.jsx("div",{className:"bg-gradient-to-r from-red-50 to-red-100/50 dark:from-red-950/20 dark:to-red-900/10 px-6 py-4 border-b border-gray-100/80 dark:border-gray-800/50",children:e.jsxs("h2",{className:n("flex items-center text-gray-900 dark:text-white font-semibold",l?"text-sm":"text-lg"),children:[e.jsx(U,{className:n("mr-2 text-red-500 dark:text-red-400",l?"w-3.5 h-3.5":"w-5 h-5")}),"About this course"]})}),e.jsx("div",{className:n("px-6 py-5",l&&"text-sm"),children:s.description?e.jsx("div",{className:"prose prose-sm sm:prose-base prose-gray dark:prose-invert max-w-none",children:e.jsx(oe,{content:s.description})}):e.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"No description available for this course."})})]}),l&&e.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[e.jsxs("div",{className:"p-3 bg-white dark:bg-gray-800/90 rounded-lg border border-gray-100/80 dark:border-gray-800/50 flex flex-col items-center justify-center text-center",children:[e.jsx(P,{className:"w-4 h-4 text-red-500 dark:text-red-400 mb-1"}),e.jsx("div",{className:"text-[10px] text-gray-500 dark:text-gray-400",children:"Duration"}),e.jsxs("div",{className:"text-xs font-medium text-gray-900 dark:text-white",children:[N()," min"]})]}),e.jsxs("div",{className:"p-3 bg-white dark:bg-gray-800/90 rounded-lg border border-gray-100/80 dark:border-gray-800/50 flex flex-col items-center justify-center text-center",children:[e.jsx(M,{className:"w-4 h-4 text-red-500 dark:text-red-400 mb-1"}),e.jsx("div",{className:"text-[10px] text-gray-500 dark:text-gray-400",children:"Modules"}),e.jsx("div",{className:"text-xs font-medium text-gray-900 dark:text-white",children:t.length})]}),e.jsxs("div",{className:"p-3 bg-white dark:bg-gray-800/90 rounded-lg border border-gray-100/80 dark:border-gray-800/50 flex flex-col items-center justify-center text-center",children:[e.jsx(T,{className:"w-4 h-4 text-red-500 dark:text-red-400 mb-1"}),e.jsx("div",{className:"text-[10px] text-gray-500 dark:text-gray-400",children:"Lessons"}),e.jsx("div",{className:"text-xs font-medium text-gray-900 dark:text-white",children:y()})]})]}),l&&e.jsx("div",{className:"p-3 bg-white dark:bg-gray-800/90 rounded-lg border border-gray-100/80 dark:border-gray-800/50",children:o?g?e.jsxs("div",{className:"flex flex-col space-y-2",children:[e.jsx(f,{variant:"outline",className:"w-full h-10 rounded-lg border-2 border-green-200 hover:bg-green-50 dark:hover:bg-green-900/20 text-green-700 dark:text-green-300 transition-all duration-300",onClick:()=>{var a;return x(`/course/${s.id}/lesson/${((a=i[0])==null?void 0:a.slug)||""}`)},children:"Review Course"}),e.jsxs("div",{className:"flex items-center justify-center rounded-full bg-green-50 dark:bg-green-900/20 border border-green-100 dark:border-green-800/40 text-green-600 dark:text-green-400 py-1.5 px-3 text-xs",children:[e.jsx(q,{className:"w-3.5 h-3.5 mr-1.5"}),e.jsx("span",{children:"Course Completed"})]})]}):e.jsx(f,{className:"w-full bg-red-500 hover:bg-red-600 text-white",onClick:()=>{var a;return x(`/course/${s.id}/lesson/${((a=i[0])==null?void 0:a.slug)||""}`)},children:"Continue Learning"}):e.jsx(f,{onClick:h,className:"w-full bg-red-500 hover:bg-red-600 text-white",disabled:r,children:r?"Starting...":"Start Course"})}),e.jsx(f,{onClick:()=>x(`/course/${s.id}/modules`),className:n("w-full md:w-auto bg-red-500 hover:bg-red-600 text-white","px-4 sm:px-6 py-2 sm:py-3 rounded-lg flex items-center justify-center"),children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(M,{className:"w-5 h-5"}),e.jsxs("div",{className:"flex flex-col items-start",children:[e.jsx("span",{className:"text-xs sm:text-sm font-normal opacity-90",children:"Start Learning"}),e.jsx("span",{className:"text-sm sm:text-base font-semibold",children:"View Course Modules"})]}),e.jsx(A,{className:"w-5 h-5 ml-2"})]})})]})]})})]})},ye=({courseId:s,userId:t,modules:o,isEnrolled:g,courseName:h="this course"})=>{const[r,x]=w.useState(!1),[l,i]=w.useState(!1),[u,N]=w.useState(!1),[y,b]=w.useState(!1),d=K(),a=R(),p=D();w.useEffect(()=>{k(void 0,null,function*(){try{if(!o||o.length===0||!t)return;const{data:j,error:F}=yield $.from("user_module_progress").select("module_id").eq("user_id",t).eq("is_completed",!0).in("module_id",o.map(Y=>Y.id));if(F){console.error("Error checking module completion:",F);return}const Q=j&&j.length===o.length;i(Q);const{data:C}=yield $.from("user_course_enrollment").select("status").eq("user_id",t).eq("course_id",s).single();N((C==null?void 0:C.status)==="completed"),console.log(`Course ${s} enrollment status for user ${t}:`,C==null?void 0:C.status)}catch(j){console.error("Error checking module completion:",j)}})},[o,t,s]),w.useEffect(()=>{const c=j=>{console.log("=== FINISH COURSE EVENT DETECTED ==="),console.log("Event detail:",j.detail),console.log("Component courseId:",s),j.detail&&j.detail.courseId===s?(console.log("Course IDs match, handling finish course event"),m()):console.log("Course IDs do not match or detail missing")};return document.addEventListener("finishCourse",c),()=>{document.removeEventListener("finishCourse",c)}},[s,t,g,l]);const m=()=>k(void 0,null,function*(){if(!s||!t||!g){v.error("You need to be enrolled in this course to complete it");return}if(!l&&!u){v.error("You need to complete all modules before finishing the course");return}try{x(!0);const c=yield ne(s,t);c.success?(v.success(`Congratulations! You've completed ${h}! Your certificate is ready.`),N(!0),b(!0),yield a.invalidateQueries({queryKey:["courseProgress"]}),yield a.invalidateQueries({queryKey:["courseEnrollment"]}),yield a.invalidateQueries({queryKey:["certificates"]}),yield a.invalidateQueries({queryKey:["user-achievements"]})):v.error(c.error||"Failed to complete course. Please try again.")}catch(c){console.error("Error finishing course:",c),v.error("Failed to complete course. Please try again.")}finally{x(!1)}}),E=()=>{b(!1),p(`/certificate/${s}`)},B=()=>l?e.jsx(f,{onClick:m,disabled:r,size:d?"default":"lg",className:n("relative h-auto flex items-center gap-2 rounded-full shadow-md w-full md:w-auto",d?"px-4 py-3 text-sm":"px-6 py-6 text-base","font-medium","bg-primary hover:bg-primary/90 text-primary-foreground"),children:r?e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"animate-spin border-2 border-current border-t-transparent rounded-full mr-2",style:{width:d?"1rem":"1.25rem",height:d?"1rem":"1.25rem"}}),"Completing..."]}):e.jsxs(e.Fragment,{children:[e.jsx(O,{className:d?"h-4 w-4":"h-5 w-5"}),"Finish Course"]})}):e.jsx(X,{children:e.jsxs(Z,{children:[e.jsx(ee,{asChild:!0,children:e.jsxs(f,{disabled:!0,size:d?"default":"lg",className:n("relative h-auto flex items-center gap-2 rounded-full shadow-md w-full md:w-auto",d?"px-4 py-3 text-sm":"px-6 py-6 text-base","font-medium","bg-muted/50 text-muted-foreground/60 cursor-not-allowed opacity-60 hover:bg-muted/50 hover:text-muted-foreground/60"),children:[e.jsx(H,{className:d?"h-4 w-4 mr-1":"h-5 w-5 mr-1"}),e.jsx("span",{className:"line-through decoration-1",children:"Finish Course"})]})}),e.jsx(re,{children:e.jsx("p",{children:"Complete all modules to unlock"})})]})});return e.jsxs(e.Fragment,{children:[e.jsx(de,{courseId:s,courseName:h,isVisible:y,onClose:E}),!u&&e.jsxs("div",{className:n("bg-card text-card-foreground rounded-xl shadow-sm border border-border mt-6 overflow-hidden relative",d?"p-4":"p-6"),children:[e.jsx("div",{className:"absolute inset-0 opacity-5 pointer-events-none",children:e.jsxs("svg",{className:"w-full h-full",viewBox:"0 0 100 100",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("defs",{children:e.jsx("pattern",{id:"grid",width:"10",height:"10",patternUnits:"userSpaceOnUse",children:e.jsx("path",{d:"M 10 0 L 0 0 0 10",fill:"none",stroke:"currentColor",strokeWidth:"0.5"})})}),e.jsx("rect",{width:"100",height:"100",fill:"url(#grid)"})]})}),e.jsx("div",{className:"relative z-10",children:e.jsxs("div",{className:"flex flex-col md:flex-row items-center justify-between gap-4 md:gap-6",children:[e.jsxs("div",{className:"text-left flex-1",children:[e.jsx("h2",{className:n("font-semibold mb-2",d?"text-lg":"text-xl"),children:l?"Ready to complete this course?":"Complete all modules to finish the course"}),e.jsx("p",{className:n("text-muted-foreground mb-3 md:mb-4",d?"text-sm":"text-base"),children:l?"Congratulations! You have completed all the modules in this course. Click the button to mark the course as completed.":`You have completed ${o.filter(c=>c.is_completed).length} out of ${o.length} modules. The "Finish Course" button will be unlocked when all modules are completed.`}),e.jsx("div",{className:"w-full bg-muted rounded-full h-2 mb-2 overflow-hidden",children:e.jsx("div",{className:"h-full bg-primary transition-all duration-500 ease-out",style:{width:`${o.filter(c=>c.is_completed).length/o.length*100}%`}})}),e.jsxs("p",{className:"text-xs text-muted-foreground",children:[Math.round(o.filter(c=>c.is_completed).length/o.length*100),"% Complete"]})]}),e.jsx("div",{className:"flex-shrink-0 w-full md:w-auto mt-4 md:mt-0",children:e.jsx(ie.div,{whileHover:{scale:l&&!d?1.03:1},whileTap:{scale:l?.97:1},className:"relative",children:B()})})]})})]})]})},Be=()=>{const{courseId:s}=V(),{user:t}=te(),o=R(),[g,h]=w.useState(!1),{data:r,isLoading:x,error:l}=S({queryKey:["course",s],queryFn:()=>k(void 0,null,function*(){console.log("Fetching course by slug:",s);const m=yield me(s||"");return console.log("Course fetch result:",m),m}),enabled:!!s}),{data:i,isLoading:u,error:N}=S({queryKey:["courseModules",r==null?void 0:r.id,t==null?void 0:t.id],queryFn:()=>k(void 0,null,function*(){console.log("Fetching modules for course ID:",r==null?void 0:r.id);const m=yield ce((r==null?void 0:r.id)||"",t==null?void 0:t.id);return console.log("Modules fetch result:",m),m}),enabled:!!(r!=null&&r.id)}),{data:y}=S({queryKey:["enrollment",r==null?void 0:r.id,t==null?void 0:t.id],queryFn:()=>ue((r==null?void 0:r.id)||"",(t==null?void 0:t.id)||""),enabled:!!(r!=null&&r.id)&&!!(t!=null&&t.id)});G.useEffect(()=>{y&&y.status!=="not_started"&&h(!0)},[y]);const b=J({mutationFn:()=>pe((r==null?void 0:r.id)||"",(t==null?void 0:t.id)||"","in_progress"),onSuccess:()=>{h(!0),v.success("Successfully enrolled in course!"),o.invalidateQueries({queryKey:["enrollment",r==null?void 0:r.id,t==null?void 0:t.id]}),o.invalidateQueries({queryKey:["courseModules",r==null?void 0:r.id,t==null?void 0:t.id]})},onError:m=>{console.error("Error starting course:",m),v.error("Failed to start course. Please try again.")}}),d=()=>{if(!t){v.error("Please log in to start this course");return}b.mutate()},a=x||u,p=l||N;return p||!a&&!r?e.jsx(L,{children:e.jsx("div",{className:"container max-w-4xl mx-auto px-4 sm:px-6",children:e.jsxs("div",{className:"text-center py-6 md:py-8",children:[e.jsx("h2",{className:"text-xl md:text-2xl font-bold text-red-600",children:"Error loading course"}),e.jsx("p",{className:"mt-2 text-sm md:text-base text-gray-600 dark:text-gray-400",children:p?"There was a problem loading the course content. Please try again later.":`Course "${s}" not found. Please check the URL and try again.`}),e.jsxs("p",{className:"mt-4 text-sm text-gray-500",children:["Course ID: ",s||"Not provided"]})]})})}):e.jsx(L,{children:e.jsx(ge,{pageType:"module",className:"animate-fade-in",children:e.jsx(he,{spacing:"md",children:a?e.jsx(xe,{}):e.jsxs(e.Fragment,{children:[e.jsx(fe,{course:{id:(r==null?void 0:r.id)||s||"",slug:(r==null?void 0:r.slug)||"",title:(r==null?void 0:r.title)||"",description:(r==null?void 0:r.description)||"",instructor:(r==null?void 0:r.instructor)||"",total_modules:(r==null?void 0:r.total_modules)||0,completed_modules:(r==null?void 0:r.completed_modules)||0,image_url:(r==null?void 0:r.image_url)||(r==null?void 0:r.image)||""},modules:i||[],isEnrolled:g,onStartCourse:d,isStarting:b.isPending}),t&&g&&r&&e.jsx("div",{className:"mt-4 md:mt-6",children:e.jsx(ye,{courseId:r.id,userId:t.id,modules:i||[],isEnrolled:g,courseName:r.title})})]})})})})};export{Be as default};
