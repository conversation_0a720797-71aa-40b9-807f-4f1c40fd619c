/* Unified Typography System for i-can-iv e-learning platform */

/* Typography Variables - Single source of truth */
:root {
  /* Font families */
  --font-primary: 'Poppins', system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;

  /* Font sizes - Consistent scale */
  --text-xs: 0.75rem;     /* 12px */
  --text-sm: 0.875rem;    /* 14px */
  --text-base: 1rem;      /* 16px */
  --text-lg: 1.125rem;    /* 18px */
  --text-xl: 1.25rem;     /* 20px */
  --text-2xl: 1.5rem;     /* 24px */
  --text-3xl: 1.875rem;   /* 30px */
  --text-4xl: 2.25rem;    /* 36px */

  /* Font weights */
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;

  /* Line heights */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;

  /* Letter spacing */
  --tracking-tight: -0.025em;
  --tracking-normal: 0em;
  --tracking-wide: 0.025em;
}

/* Base typography */
html {
  font-family: var(--font-primary);
  font-size: 16px;
  line-height: var(--leading-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  font-family: var(--font-primary);
  font-size: var(--text-base);
  font-weight: var(--font-normal);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
}

/* Headings - Consistent hierarchy */
h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
  font-family: var(--font-primary);
  font-weight: var(--font-semibold);
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-tight);
  margin: 0;
}

h1, .h1 {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
}

h2, .h2 {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
}

h3, .h3 {
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
}

h4, .h4 {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
}

h5, .h5 {
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
}

h6, .h6 {
  font-size: var(--text-base);
  font-weight: var(--font-medium);
}

/* Responsive headings for mobile */
@media (max-width: 640px) {
  h1, .h1 {
    font-size: var(--text-3xl);
  }
  
  h2, .h2 {
    font-size: var(--text-2xl);
  }
  
  h3, .h3 {
    font-size: var(--text-xl);
  }
}

/* Body text */
p, .text-body {
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  margin: 0;
}

/* Text size utilities */
.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-3xl { font-size: var(--text-3xl); }
.text-4xl { font-size: var(--text-4xl); }

/* Font weight utilities */
.font-normal { font-weight: var(--font-normal); }
.font-medium { font-weight: var(--font-medium); }
.font-semibold { font-weight: var(--font-semibold); }
.font-bold { font-weight: var(--font-bold); }

/* Line height utilities */
.leading-tight { line-height: var(--leading-tight); }
.leading-relaxed { line-height: var(--leading-relaxed); }

/* Letter spacing utilities */
.tracking-tight { letter-spacing: var(--tracking-tight); }
.tracking-wide { letter-spacing: var(--tracking-wide); }

/* Navigation typography */

/* Button typography */

/* Card title typography */

/* Card description typography */

/* Small text */
.text-small, small {
  font-size: var(--text-xs);
  line-height: var(--leading-normal);
}

/* Code and monospace */
code, pre, .code, .pre {
  font-family: var(--font-mono);
  font-size: var(--text-sm);
}

/* Mobile-optimized text sizes */

/* Professional text colors */
.text-primary-content {
  color: hsl(var(--primary-foreground));
}

.text-muted {
  color: hsl(var(--muted-foreground));
}

.text-subtle {
  opacity: 0.7;
}

/* Text alignment utilities */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

/* Text decoration utilities */
.underline { text-decoration: underline; }

/* Text transform utilities */
.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }

/* Text overflow utilities */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
