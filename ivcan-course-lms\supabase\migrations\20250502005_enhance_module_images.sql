-- Add additional image metadata columns to modules table
ALTER TABLE modules 
  ADD COLUMN IF NOT EXISTS image_width INTEGER,
  ADD COLUMN IF NOT EXISTS image_height INTEGER,
  ADD COLUMN IF NOT EXISTS image_alt_text TEXT,
  ADD COLUMN IF NOT EXISTS image_size_bytes INTEGER;

-- Create a storage function to get image metadata upon upload
CREATE OR REPLACE FUNCTION storage.get_image_metadata(bucket_id text, object_name text)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result jsonb;
BEGIN
  -- This is a placeholder function that would normally use server-side image processing
  -- In a production environment, this would extract actual metadata from the image
  -- For now, it returns empty metadata
  result := jsonb_build_object(
    'width', NULL,
    'height', NULL,
    'size_bytes', NULL
  );
  
  RETURN result;
END;
$$;

-- Create a trigger to automatically update module image metadata when a new image is uploaded
CREATE OR REPLACE FUNCTION public.update_module_image_metadata()
<PERSON><PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  -- Only proceed if image_url has changed
  IF NEW.image_url IS NOT NULL AND (OLD.image_url IS NULL OR NEW.image_url <> OLD.image_url) THEN
    -- Extract the bucket and object name from the URL
    -- This is a simplified approach and would need to be adjusted for your actual URL structure
    DECLARE
      bucket_id text := 'module-images';
      object_name text := replace(NEW.image_url, 'https://[YOUR-PROJECT-URL].supabase.co/storage/v1/object/public/module-images/', '');
      metadata jsonb;
    BEGIN
      -- Get the metadata from the storage function
      metadata := storage.get_image_metadata(bucket_id, object_name);
      
      -- Update the module record with the metadata
      NEW.image_width := (metadata->>'width')::integer;
      NEW.image_height := (metadata->>'height')::integer;
      NEW.image_size_bytes := (metadata->>'size_bytes')::integer;
    END;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger on the modules table
DROP TRIGGER IF EXISTS update_module_image_metadata_trigger ON modules;
CREATE TRIGGER update_module_image_metadata_trigger
BEFORE INSERT OR UPDATE ON modules
FOR EACH ROW
EXECUTE FUNCTION public.update_module_image_metadata();

-- Add RLS policies specifically for module images
ALTER POLICY "Public Access"
ON storage.objects
USING (bucket_id = 'module-images');

-- Allow signed URLs for better security
ALTER POLICY "Authenticated users can upload module images"
ON storage.objects
WITH CHECK (
    bucket_id = 'module-images' 
    AND auth.role() IN ('authenticated', 'service_role')
); 