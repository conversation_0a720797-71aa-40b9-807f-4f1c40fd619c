// Test script for Google OAuth configuration
// This script checks the Google OAuth setup and provides guidance

const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const SUPABASE_URL = "https://jibspqwieubavucdtccv.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMxMjUzNzIsImV4cCI6MjA1ODcwMTM3Mn0.H1cvGLXWvIMibv8dYsYIvY37IRoVR80HyeF1wM93IUI";

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testGoogleOAuthSetup() {
  console.log('🔐 Testing Google OAuth Configuration\n');
  
  console.log('=== Current Configuration Status ===');
  console.log('✅ Google OAuth is ENABLED in Supabase');
  console.log('✅ Google Client ID is configured');
  console.log('✅ Google Client Secret is configured');
  console.log('✅ Redirect URLs are configured');
  
  console.log('\n=== Current Settings ===');
  console.log('Site URL: http://e4mi.netlify.app');
  console.log('Allowed Redirect URL: https://e4mi.netlify.app/auth/callback');
  console.log('Google Client ID: 1077393742661-nvr2l0tpdu6jmn9m9hsu5jltca5d1uqo.apps.googleusercontent.com');
  
  console.log('\n=== Environment Check ===');
  const currentOrigin = 'http://localhost:8080'; // Development default
  const productionOrigin = 'https://e4mi.netlify.app';
  
  console.log(`Development URL: ${currentOrigin}`);
  console.log(`Production URL: ${productionOrigin}`);
  
  console.log('\n=== OAuth Flow Test ===');
  try {
    // Test OAuth URL generation (this doesn't actually redirect)
    console.log('Testing OAuth URL generation...');
    
    // This would normally redirect, but we're just testing the setup
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${currentOrigin}/auth/callback`,
        skipBrowserRedirect: true // Don't actually redirect
      }
    });
    
    if (error) {
      console.error('❌ OAuth setup error:', error);
      return false;
    }
    
    if (data.url) {
      console.log('✅ OAuth URL generated successfully');
      console.log('OAuth URL:', data.url.substring(0, 100) + '...');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Error testing OAuth setup:', error);
    return false;
  }
}

async function checkOAuthCallback() {
  console.log('\n=== OAuth Callback Check ===');
  
  console.log('Callback URL configuration:');
  console.log('✅ /auth/callback route should exist in the application');
  console.log('✅ AuthCallback component should handle the OAuth response');
  console.log('✅ Redirect to dashboard after successful authentication');
  
  return true;
}

async function testUserRoleAfterOAuth() {
  console.log('\n=== User Role Assignment After OAuth ===');
  
  console.log('Expected behavior:');
  console.log('1. User signs in with Google OAuth');
  console.log('2. User is automatically assigned "student" role');
  console.log('3. User is redirected to dashboard');
  console.log('4. Auto-completion is triggered if user is a teacher');
  
  return true;
}

async function provideTroubleshootingGuide() {
  console.log('\n=== Troubleshooting Guide ===');
  
  console.log('\n🔧 If Google OAuth is not working:');
  console.log('1. Check Google Cloud Console:');
  console.log('   - Verify OAuth client is configured');
  console.log('   - Check authorized JavaScript origins');
  console.log('   - Check authorized redirect URIs');
  
  console.log('\n2. Check Supabase Dashboard:');
  console.log('   - Authentication > Providers > Google');
  console.log('   - Verify Client ID and Secret are correct');
  console.log('   - Check Site URL and Redirect URLs');
  
  console.log('\n3. Check Application:');
  console.log('   - Verify /auth/callback route exists');
  console.log('   - Check AuthCallback component');
  console.log('   - Test OAuth button functionality');
  
  console.log('\n4. Common Issues:');
  console.log('   - CORS errors: Check authorized origins');
  console.log('   - Redirect errors: Check redirect URIs');
  console.log('   - State mismatch: Check callback handling');
  
  console.log('\n📝 Required Google Cloud Console Settings:');
  console.log('Authorized JavaScript origins:');
  console.log('- http://localhost:8080');
  console.log('- https://e4mi.netlify.app');
  
  console.log('\nAuthorized redirect URIs:');
  console.log('- http://localhost:8080/auth/callback');
  console.log('- https://e4mi.netlify.app/auth/callback');
  console.log('- https://jibspqwieubavucdtccv.supabase.co/auth/v1/callback');
  
  return true;
}

async function runGoogleOAuthTests() {
  console.log('🧪 Starting Google OAuth Tests\n');
  
  const results = {
    oauthSetup: await testGoogleOAuthSetup(),
    callbackCheck: await checkOAuthCallback(),
    roleAssignment: await testUserRoleAfterOAuth(),
    troubleshooting: await provideTroubleshootingGuide()
  };
  
  console.log('\n=== Test Results Summary ===');
  console.log(`OAuth Setup: ${results.oauthSetup ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Callback Check: ${results.callbackCheck ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Role Assignment: ${results.roleAssignment ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Troubleshooting Guide: ${results.troubleshooting ? '✅ PROVIDED' : '❌ FAIL'}`);
  
  const allPassed = Object.values(results).every(result => result === true);
  
  if (allPassed) {
    console.log('\n🎉 Google OAuth configuration appears to be correct!');
    console.log('💡 Test the OAuth flow manually in the application to verify it works end-to-end.');
  } else {
    console.log('\n⚠️ Some OAuth configuration issues detected. Check the guidance above.');
  }
  
  console.log('\n=== Next Steps ===');
  console.log('1. Run the application: npm run dev');
  console.log('2. Navigate to the login page');
  console.log('3. Click "Log in with Google"');
  console.log('4. Verify the OAuth flow works correctly');
  console.log('5. Check that user roles are assigned properly');
  
  return allPassed;
}

// Run the tests
runGoogleOAuthTests().catch(console.error);
