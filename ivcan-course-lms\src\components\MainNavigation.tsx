import React from 'react';
import { Link } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { LayoutDashboard, Home, Menu, X } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { useScreenSize } from '@/hooks/use-mobile';

interface MainNavigationProps {
  sidebarOpen?: boolean;
  toggleSidebar?: () => void;
  setSidebarOpen?: (open: boolean) => void;
}

const MainNavigation: React.FC<MainNavigationProps> = ({
  sidebarOpen,
  toggleSidebar,
  setSidebarOpen
}) => {
  // Define active state based on current path
  const isActive = (path: string) => window.location.pathname === path;
  const isMobile = useIsMobile(768); // Ensure consistent breakpoint
  const screenSize = useScreenSize();
  const isLargeScreen = screenSize === 'lg' || screenSize === 'xl' || screenSize === '2xl';

  // Use toggleSidebar if available, otherwise fallback to setSidebarOpen
  const handleToggleSidebar = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log("Toggle sidebar clicked");

    if (toggleSidebar) {
      toggleSidebar();
    } else if (setSidebarOpen) {
      setSidebarOpen(!sidebarOpen);
    }
  };

  // Handle navigation link clicks
  const handleNavClick = (e: React.MouseEvent, path: string) => {
    if (window.location.pathname === path) {
      e.preventDefault();
    }
  };

  return (
    <div className="flex items-center gap-2 w-full justify-between md:justify-center relative z-50">
      {/* Mobile menu toggle button - Only show on mobile */}
      {isMobile && (toggleSidebar || setSidebarOpen) && (
        <button
          onClick={handleToggleSidebar}
          className="rounded-full bg-[#e63946]/10 text-[#e63946] hover:bg-[#e63946]/20 transition-all duration-200 p-2 h-10 w-10 flex items-center justify-center"
          aria-label="Toggle menu"
          style={{touchAction: 'manipulation'}}
        >
          {sidebarOpen ? (
            <X className="h-4 w-4" />
          ) : (
            <Menu className="h-4 w-4" />
          )}
        </button>
      )}

      {/* Navigation links - Only show on desktop, mobile uses floating sidebar */}
      {!isMobile && (
        <nav
          className="flex items-center gap-2"
          role="navigation"
          aria-label="Main navigation"
        >
          <Button
            asChild
            variant={isActive('/') ? 'default' : 'ghost'}
            size="sm"
            className="rounded-full font-medium relative z-50"
          >
            <Link
              to="/"
              onClick={(e) => handleNavClick(e, '/')}
              aria-label="Go to home page"
              aria-current={isActive('/') ? 'page' : undefined}
            >
              <Home className="h-4 w-4 mr-1.5" aria-hidden="true" />
              <span>Home</span>
            </Link>
          </Button>

          <Button
            asChild
            variant={isActive('/dashboard') ? 'default' : 'ghost'}
            size="sm"
            className="rounded-full font-medium relative z-50"
          >
            <Link
              to="/dashboard"
              onClick={(e) => handleNavClick(e, '/dashboard')}
              aria-label="Go to programmes dashboard"
              aria-current={isActive('/dashboard') ? 'page' : undefined}
            >
              <LayoutDashboard className="h-4 w-4 mr-1.5" aria-hidden="true" />
              <span>Programmes</span>
            </Link>
          </Button>
        </nav>
      )}
    </div>
  );
};

export default MainNavigation;
