var v=(a,N,t)=>new Promise((u,i)=>{var x=l=>{try{p(t.next(l))}catch(n){i(n)}},f=l=>{try{p(t.throw(l))}catch(n){i(n)}},p=l=>l.done?u(l.value):Promise.resolve(l.value).then(x,f);p((t=t.apply(a,N)).next())});import{aw as J,R as D,j as e,E as q,aG as y,b_ as W,ah as C,b$ as X,aB as ee,bT as se,c0 as ae,c1 as re,u as te,r as M,az as R,b6 as ie,Z as le,$ as A,aD as de,bs as ce}from"./vendor-react.BcAa1DKr.js";import{u as ne,L as oe}from"./Layout.DRjmVYQG.js";import{g as Q,c as _,B as m,u as me,a as ue,s as S,I as xe}from"./index.BLDhDn0D.js";import{C as T,a as z,d as I,e as P,b as he,c as fe}from"./card.B9V6b2DK.js";import{T as pe,a as ge,b as U,c as Y}from"./tabs.B0SF6qIv.js";import{B as V}from"./badge.C87ZuIxl.js";import{c as be,g as je}from"./achievementService.Vkx_BHml.js";import{C as ye}from"./confetti.ShHySCrk.js";import{a4 as j,aF as K}from"./vendor.DQpuTRuB.js";import{P as Ne,C as we}from"./floating-sidebar-container.BxlLgzat.js";import"./vendor-supabase.sufZ44-y.js";const ve=({achievement:a,userId:N,moduleInfo:t,courseInfo:u})=>{const{toast:i}=Q(),x=J(),[f,p]=D.useState(!1),[l,n]=D.useState(!1),k=()=>{switch(a.achievement.icon){case"bold-step":return e.jsx(re,{className:"h-8 w-8 text-red-500"});case"persistence":return e.jsx(ae,{className:"h-8 w-8 text-red-500"});case"consistency":return e.jsx(q,{className:"h-8 w-8 text-red-500"});case"perseverance":return e.jsx(se,{className:"h-8 w-8 text-red-500"});case"discipline":return e.jsx(ee,{className:"h-8 w-8 text-red-500"});case"last-straw":return e.jsx(X,{className:"h-8 w-8 text-red-500"});case"achiever":return e.jsx(y,{className:"h-8 w-8 text-red-500"});case"course-master":return e.jsx(C,{className:"h-8 w-8 text-primary"});default:return e.jsx(W,{className:"h-8 w-8 text-primary"})}},d=()=>{switch(a.achievement.icon){case"bold-step":return"bg-red-100 dark:bg-red-900/30 border-red-200 dark:border-red-800/50";case"persistence":return"bg-red-100 dark:bg-red-900/30 border-red-200 dark:border-red-800/50";case"consistency":return"bg-red-100 dark:bg-red-900/30 border-red-200 dark:border-red-800/50";case"perseverance":return"bg-red-100 dark:bg-red-900/30 border-red-200 dark:border-red-800/50";case"discipline":return"bg-red-100 dark:bg-red-900/30 border-red-200 dark:border-red-800/50";case"last-straw":return"bg-red-100 dark:bg-red-900/30 border-red-200 dark:border-red-800/50";case"achiever":return"bg-red-100 dark:bg-red-900/30 border-red-200 dark:border-red-800/50";case"course-master":return"bg-primary/10 dark:bg-primary/20 border-primary/20 dark:border-primary/30";default:return"bg-gray-100 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700/50"}},g=()=>v(void 0,null,function*(){if(!f){p(!0);try{(yield be(N,a.id))?(n(!0),i({title:"Badge Claimed!",description:`You've claimed the ${a.achievement.name} badge.`,variant:"default"}),x.invalidateQueries({queryKey:["user-achievements"]}),setTimeout(()=>n(!1),3e3)):i({title:"Failed to claim badge",description:"Please try again later.",variant:"destructive"})}catch(b){console.error("Error claiming badge:",b),i({title:"Error",description:"An unexpected error occurred.",variant:"destructive"})}finally{p(!1)}}});return e.jsx(j.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:"h-full",children:e.jsxs(T,{className:_("h-full flex flex-col overflow-hidden transition-all duration-300","border hover:shadow-md",a.is_claimed?"border-primary/20":"border-muted"),children:[l&&e.jsx(ye,{count:100,duration:3,active:!0}),e.jsxs(z,{className:"pb-2 relative",children:[e.jsx("div",{className:"absolute top-2 right-2",children:e.jsxs(V,{variant:"outline",className:"bg-primary/10 text-primary border-primary/20 text-xs",children:[a.achievement.points," pts"]})}),e.jsxs("div",{className:"flex flex-col items-center pt-4",children:[e.jsx("div",{className:_("w-20 h-20 rounded-full flex items-center justify-center mb-3","border-2",d()),children:k()}),e.jsx("h3",{className:"font-bold text-center",children:a.achievement.name}),e.jsx("p",{className:"text-sm text-muted-foreground text-center mt-1",children:a.achievement.description})]})]}),e.jsx(I,{className:"flex-grow pb-2",children:e.jsxs("div",{className:"flex flex-col gap-1",children:[e.jsxs("div",{className:"text-xs text-muted-foreground text-center",children:["Earned on ",K(new Date(a.completed_at),"MMMM d, yyyy")]}),t&&e.jsxs("div",{className:"mt-2 text-xs text-center",children:[e.jsxs("span",{className:"text-muted-foreground",children:["Module ",t.module_number,": "]}),e.jsx("span",{className:"font-medium",children:t.title})]}),u&&e.jsxs("div",{className:"mt-1 text-xs text-center",children:[e.jsx("span",{className:"text-muted-foreground",children:"Course: "}),e.jsx("span",{className:"font-medium",children:u.title})]})]})}),e.jsx(P,{className:"pt-0",children:a.is_claimed?e.jsxs(m,{variant:"outline",className:"w-full",disabled:!0,children:[e.jsx(q,{className:"h-4 w-4 mr-2"}),"Claimed"]}):e.jsx(m,{onClick:g,className:"w-full",disabled:f,children:f?e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"animate-spin border-2 border-current border-t-transparent rounded-full h-4 w-4 mr-2"}),"Claiming..."]}):e.jsxs(e.Fragment,{children:[e.jsx(y,{className:"h-4 w-4 mr-2"}),"Claim Badge"]})})})]})})},Ae=()=>{const{user:a}=me(),{isTeacher:N}=ne(),t=ue(),{toast:u}=Q(),i=te(),[x,f]=M.useState(""),[p,l]=M.useState("certificates"),[n,k]=M.useState(null),{data:d,isLoading:g,refetch:b}=R({queryKey:["certificates",a==null?void 0:a.id],queryFn:()=>v(void 0,null,function*(){if(!a)return[];console.log(`Fetching certificates for user ${a.id}`);const{data:s,error:h}=yield S.from("user_course_enrollment").select(`
          id,
          course_id,
          user_id,
          completed_at,
          created_at,
          updated_at,
          status,
          course:courses(id, title, description, image_url)
        `).eq("user_id",a.id).eq("status","completed");return h?(console.error("Error fetching certificates from enrollments:",h),[]):(console.log("Certificates data from enrollments:",s),s.map(r=>{if(!r.completed_at){console.log(`Fixing missing completed_at for certificate ${r.id}`),r.completed_at=r.updated_at||r.created_at||new Date().toISOString();try{S.from("user_course_enrollment").update({completed_at:r.completed_at}).eq("id",r.id).then(()=>console.log(`Updated completed_at for certificate ${r.id}`)).catch(c=>console.error(`Failed to update completed_at for certificate ${r.id}:`,c))}catch(c){console.error("Error updating certificate:",c)}}return r}))}),enabled:!!a}),{data:o,isLoading:E,refetch:Z}=R({queryKey:["user-achievements",a==null?void 0:a.id],queryFn:()=>v(void 0,null,function*(){if(!a)return[];console.log(`Fetching achievements for user ${a.id}`);const s=yield je(a.id);if(s.length>0){const h=s.filter(r=>r.module_id).map(r=>r.module_id);if(h.length>0){const{data:r}=yield S.from("modules").select("id, title, module_number, course_id").in("id",h);r&&s.forEach(c=>{if(c.module_id){const w=r.find(F=>F.id===c.module_id);w&&(c.moduleInfo=w)}})}const $=s.filter(r=>r.course_id).map(r=>r.course_id);if($.length>0){const{data:r}=yield S.from("courses").select("id, title, description").in("id",$);r&&s.forEach(c=>{if(c.course_id){const w=r.find(F=>F.id===c.course_id);w&&(c.courseInfo=w)}})}}return console.log("User achievements data:",s),s}),enabled:!!a}),B=d==null?void 0:d.filter(s=>s.course.title.toLowerCase().includes(x.toLowerCase())),L=o==null?void 0:o.filter(s=>s.achievement.name.toLowerCase().includes(x.toLowerCase())||s.achievement.description.toLowerCase().includes(x.toLowerCase())),G=s=>`CERT-${s.substring(0,4).toUpperCase()}-${s.substring(s.length-4).toUpperCase()}`,H=s=>{i(`/certificate/${s}`)},O=s=>v(void 0,null,function*(){if(!navigator.share){u({title:"Sharing not supported",description:"Your browser doesn't support sharing. Try using a mobile device.",variant:"destructive"});return}k(s.id);try{yield navigator.share({title:`${s.course.title} Certificate`,text:`I've completed the ${s.course.title} course and earned a certificate!`,url:window.location.origin+`/certificate/${s.course_id}`}),u({title:"Shared successfully",description:"Your certificate has been shared."})}catch(h){h.name!=="AbortError"&&(console.error("Error sharing certificate:",h),u({title:"Sharing failed",description:"There was an error sharing your certificate.",variant:"destructive"}))}finally{k(null)}});return a?e.jsx(oe,{children:e.jsx(Ne,{pageType:"certificate",children:e.jsxs(we,{spacing:"lg",children:[e.jsxs(j.div,{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6 sm:mb-8",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.3},children:[e.jsxs("div",{children:[e.jsx("h1",{className:_("font-bold mb-1 sm:mb-2",t?"text-2xl":"text-3xl"),children:"Your Achievements"}),e.jsx("p",{className:"text-muted-foreground text-sm sm:text-base",children:"View and download your certificates and achievements"})]}),e.jsxs("div",{className:"flex gap-2 w-full md:w-auto",children:[e.jsxs("div",{className:"relative flex-1 md:w-64",children:[e.jsx(ie,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),e.jsx(xe,{placeholder:"Search achievements...",className:"pl-9 w-full text-sm",value:x,onChange:s=>f(s.target.value)})]}),e.jsx(m,{variant:"outline",size:"icon",onClick:()=>{b(),Z()},disabled:g||E,title:"Refresh achievements",className:"flex-shrink-0",children:e.jsx(le,{className:_("h-4 w-4",(g||E)&&"animate-spin")})})]})]}),!g&&!E&&e.jsxs(j.div,{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},children:[e.jsx(T,{className:"bg-primary/5 border-primary/10",children:e.jsx(I,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Badges Earned"}),e.jsxs("p",{className:"text-sm text-muted-foreground",children:[(o==null?void 0:o.filter(s=>s.is_claimed).length)||0," of ",(o==null?void 0:o.length)||0," claimed"]})]}),e.jsx("div",{className:"h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center",children:e.jsx(y,{className:"h-6 w-6 text-primary"})})]})})}),e.jsx(T,{className:"bg-primary/5 border-primary/10",children:e.jsx(I,{className:"pt-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold",children:"Certificates Earned"}),e.jsxs("p",{className:"text-sm text-muted-foreground",children:[(d==null?void 0:d.length)||0," course",(d==null?void 0:d.length)!==1?"s":""," completed"]})]}),e.jsx("div",{className:"h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center",children:e.jsx(C,{className:"h-6 w-6 text-primary"})})]})})})]}),e.jsxs(pe,{defaultValue:"certificates",className:"space-y-6 sm:space-y-8",onValueChange:l,children:[e.jsxs(ge,{className:"w-full sm:w-auto",children:[e.jsxs(U,{value:"certificates",className:"flex items-center gap-2 flex-1 sm:flex-initial",children:[e.jsx(C,{className:"h-4 w-4"}),e.jsx("span",{className:t?"text-sm":"",children:"Certificates"})]}),e.jsxs(U,{value:"badges",className:"flex items-center gap-2 flex-1 sm:flex-initial",children:[e.jsx(y,{className:"h-4 w-4"}),e.jsx("span",{className:t?"text-sm":"",children:"Badges"})]})]}),e.jsx(Y,{value:"certificates",className:"space-y-6",children:g?e.jsx("div",{className:"flex justify-center py-12",children:e.jsx(A,{className:"h-8 w-8 animate-spin text-primary"})}):B&&B.length>0?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6",children:B.map(s=>e.jsx(j.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:"h-full",children:e.jsxs(T,{className:"h-full flex flex-col overflow-hidden hover:shadow-md transition-shadow duration-200 border-primary/10",children:[e.jsxs(z,{className:"pb-2 sm:pb-3",children:[e.jsxs("div",{className:"flex justify-between items-start gap-2",children:[e.jsx(he,{className:_("font-bold",t?"text-base":"text-lg"),children:s.course.title}),e.jsx(V,{variant:"outline",className:"bg-primary/10 text-primary border-primary/20 whitespace-nowrap text-xs",children:G(s.id)})]}),e.jsx(fe,{className:"line-clamp-2 text-xs sm:text-sm mt-1",children:s.course.description})]}),e.jsx(I,{className:"flex-grow pb-2",children:e.jsxs("div",{className:"flex flex-col gap-1",children:[e.jsxs("div",{className:"flex items-center text-xs sm:text-sm text-muted-foreground",children:[e.jsx(de,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 flex-shrink-0"}),e.jsxs("span",{className:"truncate",children:["Completed on ",K(new Date(s.completed_at||s.created_at),"MMMM d, yyyy")]})]}),e.jsxs("div",{className:"flex items-center text-xs text-muted-foreground",children:[e.jsx(y,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 flex-shrink-0"}),e.jsx("span",{children:"Course Certificate"})]})]})}),e.jsxs(P,{className:"flex flex-col sm:flex-row gap-2 pt-2",children:[e.jsxs(m,{variant:"default",size:t?"sm":"default",className:"w-full bg-primary hover:bg-primary/90 text-primary-foreground",onClick:()=>H(s.course_id),children:[e.jsx(C,{className:"h-4 w-4 mr-2"}),"View Certificate"]}),(t||typeof navigator!="undefined"&&"share"in navigator)&&e.jsxs(m,{variant:"outline",size:t?"sm":"default",className:"w-full border-primary/20 text-primary hover:bg-primary/5",onClick:()=>O(s),disabled:n===s.id,children:[n===s.id?e.jsx(A,{className:"h-4 w-4 mr-2 animate-spin"}):e.jsx(ce,{className:"h-4 w-4 mr-2"}),"Share"]})]})]})},s.id))}):e.jsxs(j.div,{className:"text-center py-8 sm:py-12 border rounded-lg bg-card",initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},children:[e.jsx(C,{className:"h-12 w-12 sm:h-16 sm:w-16 mx-auto text-muted-foreground/30 mb-3 sm:mb-4"}),e.jsx("h3",{className:"text-base sm:text-lg font-medium mb-2",children:"No Certificates Yet"}),N?e.jsxs("div",{className:"px-4",children:[e.jsx("p",{className:"text-muted-foreground text-sm sm:text-base max-w-md mx-auto mb-4 sm:mb-6",children:"As a teacher, you can view certificates for courses you've completed. Complete a course to earn a certificate that will appear here."}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-2 justify-center max-w-xs mx-auto",children:[e.jsx(m,{onClick:()=>i("/dashboard"),className:"w-full sm:w-auto",children:"Explore Courses"}),e.jsx(m,{variant:"outline",onClick:()=>i("/admin"),className:"w-full sm:w-auto",children:"Manage Courses"})]})]}):e.jsxs("div",{className:"px-4",children:[e.jsx("p",{className:"text-muted-foreground text-sm sm:text-base max-w-md mx-auto mb-4 sm:mb-6",children:"Complete courses to earn certificates that will appear here."}),e.jsx(m,{onClick:()=>i("/dashboard"),className:"w-full sm:w-auto max-w-xs mx-auto",children:"Explore Courses"})]})]})}),e.jsx(Y,{value:"badges",className:"space-y-6",children:E?e.jsx("div",{className:"flex justify-center py-12",children:e.jsx(A,{className:"h-8 w-8 animate-spin text-primary"})}):L&&L.length>0?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6",children:L.map(s=>e.jsx(ve,{achievement:s,userId:(a==null?void 0:a.id)||"",moduleInfo:s.moduleInfo,courseInfo:s.courseInfo},s.id))}):e.jsxs(j.div,{className:"text-center py-8 sm:py-12 border rounded-lg bg-card",initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},children:[e.jsx(y,{className:"h-12 w-12 sm:h-16 sm:w-16 mx-auto text-muted-foreground/30 mb-3 sm:mb-4"}),e.jsx("h3",{className:"text-base sm:text-lg font-medium mb-2",children:"No Badges Yet"}),e.jsx("p",{className:"text-muted-foreground text-sm sm:text-base max-w-md mx-auto mb-4 sm:mb-6 px-4",children:"Complete modules and courses to earn badges that will appear here."}),e.jsx(m,{onClick:()=>i("/dashboard"),className:"w-full sm:w-auto max-w-xs mx-auto",children:"Explore Courses"})]})})]})]})})}):(i("/login"),null)};export{Ae as default};
