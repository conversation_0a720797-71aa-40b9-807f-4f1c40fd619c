/**
 * Unified TipTap Markdown Editor
 * Consolidates all editor implementations with comprehensive GFM support
 */

import React, { useEffect, useState, useCallback, useRef } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import Underline from '@tiptap/extension-underline';
import Strike from '@tiptap/extension-strike';
import Highlight from '@tiptap/extension-highlight';
import Placeholder from '@tiptap/extension-placeholder';
import TaskList from '@tiptap/extension-task-list';
import TaskItem from '@tiptap/extension-task-item';
import CodeBlockLowlight from '@tiptap/extension-code-block-lowlight';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import { common, createLowlight } from 'lowlight';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { MarkdownPreview } from '@/components/ui/markdown-preview';
import { Details } from '@/lib/tiptap-extensions/details';
import { Callout } from '@/lib/tiptap-extensions/callout';
import { tiptapToUnifiedMarkdown, htmlToUnifiedMarkdown } from '@/lib/unified-markdown-serializer';
import { markdownToHtml } from '@/lib/content-converter';
import { uploadEditorImage, validateImageFile, createImagePreview } from '@/lib/tiptap-image-upload';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  Strikethrough,
  List,
  ListOrdered,
  Code,
  Quote,
  Link as LinkIcon,
  Image as ImageIcon,
  Heading1,
  Heading2,
  Heading3,
  HighlighterIcon,
  CheckSquare,
  Undo,
  Redo,
  Type,
  Eye,
  Table as TableIcon,
  Plus,
  Minus,
  Upload,
  Loader2,
  Info,
  AlertTriangle,
  ChevronDown,
  Split,
} from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './tooltip';

const lowlight = createLowlight(common);

export interface UnifiedMarkdownEditorProps {
  initialContent?: string;
  onChange?: (markdown: string) => void;
  placeholder?: string;
  className?: string;
  minHeight?: number;
  autoFocus?: boolean;
  courseId?: string;
  moduleId?: string;
  showToolbar?: boolean;
  showPreview?: boolean;
  mode?: 'editor' | 'preview' | 'split';
  theme?: 'github' | 'obsidian' | 'minimal';
}

export function UnifiedMarkdownEditor({
  initialContent = '',
  onChange,
  placeholder = 'Start writing your content...',
  className = '',
  minHeight = 400,
  autoFocus = false,
  courseId,
  moduleId,
  showToolbar = true,
  showPreview = true,
  mode = 'editor',
  theme = 'github',
}: UnifiedMarkdownEditorProps) {
  const [activeTab, setActiveTab] = useState<'editor' | 'preview' | 'split'>(mode);
  const [markdownContent, setMarkdownContent] = useState(initialContent);
  const [isImageDialogOpen, setIsImageDialogOpen] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [imageAlt, setImageAlt] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        codeBlock: false, // We'll use CodeBlockLowlight instead
      }),
      CodeBlockLowlight.configure({
        lowlight,
        defaultLanguage: 'plaintext',
        HTMLAttributes: {
          class: 'rounded-md bg-muted/70 p-4 overflow-x-auto',
        },
      }),
      Image.configure({
        HTMLAttributes: {
          class: 'rounded-lg max-w-full h-auto',
        },
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          rel: 'noopener noreferrer',
          target: '_blank',
          class: 'text-blue-600 hover:text-blue-800 underline',
        },
      }),
      Underline,
      Strike,
      Highlight.configure({
        HTMLAttributes: {
          class: 'bg-yellow-200 dark:bg-yellow-800',
        },
      }),
      TaskList,
      TaskItem.configure({
        nested: true,
      }),
      Table.configure({
        resizable: true,
        HTMLAttributes: {
          class: 'border-collapse border border-border rounded-md overflow-hidden',
        },
      }),
      TableRow,
      TableHeader.configure({
        HTMLAttributes: {
          class: 'bg-muted font-semibold',
        },
      }),
      TableCell.configure({
        HTMLAttributes: {
          class: 'border border-border px-3 py-2',
        },
      }),
      Details,
      Callout,
      Placeholder.configure({
        placeholder,
      }),
    ],
    content: markdownToHtml(initialContent),
    onUpdate: ({ editor }) => {
      // Convert editor content to markdown using unified serializer
      try {
        const doc = editor.state.doc;
        const markdown = tiptapToUnifiedMarkdown(doc);
        setMarkdownContent(markdown);
        onChange?.(markdown);
      } catch (error) {
        console.error('Error converting to markdown:', error);
        // Fallback to HTML to markdown conversion
        const html = editor.getHTML();
        const markdown = htmlToUnifiedMarkdown(html);
        setMarkdownContent(markdown);
        onChange?.(markdown);
      }
    },
    editorProps: {
      attributes: {
        class: cn(
          'prose prose-sm max-w-none focus:outline-none dark:prose-invert',
          `min-h-[${minHeight}px] p-4`,
          theme === 'github' && 'github-markdown-editor',
          theme === 'obsidian' && 'obsidian-editor',
          theme === 'minimal' && 'minimal-editor'
        ),
        style: `min-height: ${minHeight}px`,
      },
    },
    autofocus: autoFocus,
  });

  // Update editor content when initialContent changes
  useEffect(() => {
    if (editor && initialContent !== markdownContent) {
      const htmlContent = markdownToHtml(initialContent);
      editor.commands.setContent(htmlContent);
      setMarkdownContent(initialContent);
    }
  }, [initialContent, editor, markdownContent]);

  // Toolbar button component
  const ToolbarButton = ({ onClick, disabled, title, children, isActive = false }: {
    onClick: () => void;
    disabled?: boolean;
    title: string;
    children: React.ReactNode;
    isActive?: boolean;
  }) => (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant={isActive ? "default" : "ghost"}
            size="sm"
            onClick={onClick}
            disabled={disabled}
            className="h-8 w-8 p-0"
          >
            {children}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{title}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );

  // Image upload handlers
  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const validation = validateImageFile(file);
      if (validation.valid) {
        setSelectedFile(file);
        createImagePreview(file).then(setImagePreview).catch(console.error);
      } else {
        toast.error(validation.message);
      }
    }
  }, []);

  const handleImageUpload = useCallback(async () => {
    if (!selectedFile || !editor) return;

    try {
      setIsUploading(true);
      const imageUrl = await uploadEditorImage(selectedFile, {
        courseId,
        moduleId,
        onProgress: (progress) => {
          // Could show progress here if needed
        }
      });

      editor.chain().focus().setImage({
        src: imageUrl,
        alt: imageAlt || selectedFile.name
      }).run();

      // Reset state
      setIsImageDialogOpen(false);
      setSelectedFile(null);
      setImagePreview(null);
      setImageUrl('');
      setImageAlt('');

      toast.success('Image uploaded successfully!');
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error('Failed to upload image. Please try again.');
    } finally {
      setIsUploading(false);
    }
  }, [selectedFile, editor, imageAlt, courseId, moduleId]);

  const handleUrlImageInsert = useCallback(() => {
    if (!editor || !imageUrl) return;

    editor.chain().focus().setImage({
      src: imageUrl,
      alt: imageAlt || 'Image'
    }).run();

    // Reset state
    setIsImageDialogOpen(false);
    setImageUrl('');
    setImageAlt('');
  }, [editor, imageUrl, imageAlt]);

  if (!editor) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  const renderToolbar = () => {
    if (!showToolbar) return null;

    return (
      <div className="border-b border-border p-2 flex flex-wrap gap-1">
        {/* Text formatting */}
        <div className="flex gap-1 border-r border-border pr-2 mr-2">
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleBold().run()}
            disabled={!editor.can().chain().focus().toggleBold().run()}
            isActive={editor.isActive('bold')}
            title="Bold (Ctrl+B)"
          >
            <Bold className="h-4 w-4" />
          </ToolbarButton>
          
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleItalic().run()}
            disabled={!editor.can().chain().focus().toggleItalic().run()}
            isActive={editor.isActive('italic')}
            title="Italic (Ctrl+I)"
          >
            <Italic className="h-4 w-4" />
          </ToolbarButton>
          
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleUnderline().run()}
            disabled={!editor.can().chain().focus().toggleUnderline().run()}
            isActive={editor.isActive('underline')}
            title="Underline (Ctrl+U)"
          >
            <UnderlineIcon className="h-4 w-4" />
          </ToolbarButton>
          
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleStrike().run()}
            disabled={!editor.can().chain().focus().toggleStrike().run()}
            isActive={editor.isActive('strike')}
            title="Strikethrough"
          >
            <Strikethrough className="h-4 w-4" />
          </ToolbarButton>
          
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleHighlight().run()}
            disabled={!editor.can().chain().focus().toggleHighlight().run()}
            isActive={editor.isActive('highlight')}
            title="Highlight"
          >
            <HighlighterIcon className="h-4 w-4" />
          </ToolbarButton>
        </div>

        {/* Headings */}
        <div className="flex gap-1 border-r border-border pr-2 mr-2">
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
            isActive={editor.isActive('heading', { level: 1 })}
            title="Heading 1"
          >
            <Heading1 className="h-4 w-4" />
          </ToolbarButton>
          
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
            isActive={editor.isActive('heading', { level: 2 })}
            title="Heading 2"
          >
            <Heading2 className="h-4 w-4" />
          </ToolbarButton>
          
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
            isActive={editor.isActive('heading', { level: 3 })}
            title="Heading 3"
          >
            <Heading3 className="h-4 w-4" />
          </ToolbarButton>
        </div>

        {/* Lists */}
        <div className="flex gap-1 border-r border-border pr-2 mr-2">
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleBulletList().run()}
            isActive={editor.isActive('bulletList')}
            title="Bullet List"
          >
            <List className="h-4 w-4" />
          </ToolbarButton>
          
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleOrderedList().run()}
            isActive={editor.isActive('orderedList')}
            title="Numbered List"
          >
            <ListOrdered className="h-4 w-4" />
          </ToolbarButton>
          
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleTaskList().run()}
            isActive={editor.isActive('taskList')}
            title="Task List"
          >
            <CheckSquare className="h-4 w-4" />
          </ToolbarButton>
        </div>

        {/* Insert elements */}
        <div className="flex gap-1 border-r border-border pr-2 mr-2">
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleBlockquote().run()}
            isActive={editor.isActive('blockquote')}
            title="Quote"
          >
            <Quote className="h-4 w-4" />
          </ToolbarButton>
          
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleCodeBlock().run()}
            isActive={editor.isActive('codeBlock')}
            title="Code Block"
          >
            <Code className="h-4 w-4" />
          </ToolbarButton>
          
          <ToolbarButton
            onClick={() => setIsImageDialogOpen(true)}
            title="Insert Image"
          >
            <ImageIcon className="h-4 w-4" />
          </ToolbarButton>
          
          <ToolbarButton
            onClick={() => {
              const url = window.prompt('Enter link URL:');
              if (url) {
                editor.chain().focus().setLink({ href: url }).run();
              }
            }}
            isActive={editor.isActive('link')}
            title="Insert Link"
          >
            <LinkIcon className="h-4 w-4" />
          </ToolbarButton>
        </div>

        {/* Advanced elements */}
        <div className="flex gap-1 border-r border-border pr-2 mr-2">
          <ToolbarButton
            onClick={() => editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()}
            title="Insert Table"
          >
            <TableIcon className="h-4 w-4" />
          </ToolbarButton>
          
          <ToolbarButton
            onClick={() => editor.chain().focus().setDetails().run()}
            title="Insert Collapsible Section"
          >
            <ChevronDown className="h-4 w-4" />
          </ToolbarButton>
          
          <ToolbarButton
            onClick={() => editor.chain().focus().setCallout({ type: 'info' }).run()}
            title="Insert Callout"
          >
            <Info className="h-4 w-4" />
          </ToolbarButton>
        </div>

        {/* Undo/Redo */}
        <div className="flex gap-1">
          <ToolbarButton
            onClick={() => editor.chain().focus().undo().run()}
            disabled={!editor.can().undo()}
            title="Undo"
          >
            <Undo className="h-4 w-4" />
          </ToolbarButton>
          
          <ToolbarButton
            onClick={() => editor.chain().focus().redo().run()}
            disabled={!editor.can().redo()}
            title="Redo"
          >
            <Redo className="h-4 w-4" />
          </ToolbarButton>
        </div>
      </div>
    );
  };

  return (
    <div className={cn("border border-border rounded-lg overflow-hidden", className)}>
      {showPreview ? (
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="editor" className="flex items-center gap-2">
              <Type className="h-4 w-4" />
              Editor
            </TabsTrigger>
            <TabsTrigger value="split" className="flex items-center gap-2">
              <Split className="h-4 w-4" />
              Split
            </TabsTrigger>
            <TabsTrigger value="preview" className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Preview
            </TabsTrigger>
          </TabsList>

          <TabsContent value="editor" className="m-0">
            {renderToolbar()}
            <EditorContent editor={editor} />
          </TabsContent>

          <TabsContent value="split" className="m-0">
            {renderToolbar()}
            <div className="grid grid-cols-2 h-full">
              <div className="border-r border-border">
                <EditorContent editor={editor} />
              </div>
              <div className="h-full overflow-auto">
                <div className="p-6" style={{ minHeight: `${minHeight}px` }}>
                  <MarkdownPreview
                    content={markdownContent}
                    className="prose prose-gray max-w-none dark:prose-invert"
                    allowHtml={true}
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="preview" className="p-6 m-0" style={{ minHeight: `${minHeight}px` }}>
            <div className={`${theme}-markdown-preview`}>
              <MarkdownPreview
                content={markdownContent}
                className="prose prose-gray max-w-none dark:prose-invert"
                allowHtml={true}
              />
            </div>
          </TabsContent>
        </Tabs>
      ) : (
        <div>
          {renderToolbar()}
          <EditorContent editor={editor} />
        </div>
      )}

      {/* Image Upload Dialog */}
      <Dialog open={isImageDialogOpen} onOpenChange={setIsImageDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Insert Image</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <Tabs defaultValue="upload">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="upload">Upload</TabsTrigger>
                <TabsTrigger value="url">URL</TabsTrigger>
              </TabsList>
              
              <TabsContent value="upload" className="space-y-4">
                <div>
                  <label htmlFor="image-file" className="text-sm font-medium">
                    Choose Image File
                  </label>
                  <Input
                    id="image-file"
                    type="file"
                    accept="image/*"
                    ref={fileInputRef}
                    onChange={handleFileSelect}
                    className="mt-1"
                  />
                </div>
                
                {imagePreview && (
                  <div className="space-y-2">
                    <img 
                      src={imagePreview} 
                      alt="Preview" 
                      className="max-w-full h-32 object-cover rounded border"
                    />
                    <Input
                      placeholder="Alt text (optional)"
                      value={imageAlt}
                      onChange={(e) => setImageAlt(e.target.value)}
                    />
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="url" className="space-y-4">
                <div>
                  <label htmlFor="image-url" className="text-sm font-medium">
                    Image URL
                  </label>
                  <Input
                    id="image-url"
                    placeholder="https://example.com/image.jpg"
                    value={imageUrl}
                    onChange={(e) => setImageUrl(e.target.value)}
                    className="mt-1"
                  />
                </div>
                <div>
                  <label htmlFor="image-alt-url" className="text-sm font-medium">
                    Alt Text
                  </label>
                  <Input
                    id="image-alt-url"
                    placeholder="Describe the image"
                    value={imageAlt}
                    onChange={(e) => setImageAlt(e.target.value)}
                    className="mt-1"
                  />
                </div>
              </TabsContent>
            </Tabs>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsImageDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={selectedFile ? handleImageUpload : handleUrlImageInsert}
              disabled={isUploading || (!selectedFile && !imageUrl)}
            >
              {isUploading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Insert Image
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
