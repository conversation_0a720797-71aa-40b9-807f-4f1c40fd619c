-- Ensure all course-related tables exist with proper structure

-- Courses table
CREATE TABLE IF NOT EXISTS public.courses (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  title TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  description TEXT NOT NULL,
  instructor TEXT NOT NULL,
  total_modules INTEGER DEFAULT 0,
  completed_modules INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now())
);

-- Modules table
CREATE TABLE IF NOT EXISTS public.modules (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  course_id UUID REFERENCES public.courses(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  slug TEXT NOT NULL,
  module_number INTEGER NOT NULL,
  is_locked BOOLEAN DEFAULT false,
  is_completed BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(course_id, slug)
);

-- Lessons table
CREATE TABLE IF NOT EXISTS public.lessons (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  module_id UUID REFERENCES public.modules(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  slug TEXT NOT NULL,
  duration TEXT NOT NULL,
  type TEXT NOT NULL DEFAULT 'lesson',
  requirement TEXT,
  content TEXT,
  completed BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(module_id, slug)
);

-- User lesson progress table
CREATE TABLE IF NOT EXISTS public.user_lesson_progress (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  lesson_id UUID REFERENCES public.lessons(id) ON DELETE CASCADE,
  completed BOOLEAN DEFAULT false,
  last_position INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(user_id, lesson_id)
);

-- User course progress table
CREATE TABLE IF NOT EXISTS public.user_course_progress (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  course_id UUID REFERENCES public.courses(id) ON DELETE CASCADE,
  hours_spent NUMERIC(10, 2) DEFAULT 0,
  last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(user_id, course_id)
);

-- User course enrollment table
CREATE TABLE IF NOT EXISTS public.user_course_enrollment (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  course_id UUID REFERENCES public.courses(id) ON DELETE CASCADE,
  status TEXT DEFAULT 'not_started',
  enrolled_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(user_id, course_id)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_modules_course_id ON public.modules(course_id);
CREATE INDEX IF NOT EXISTS idx_lessons_module_id ON public.lessons(module_id);
CREATE INDEX IF NOT EXISTS idx_user_lesson_progress_user_id ON public.user_lesson_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_user_lesson_progress_lesson_id ON public.user_lesson_progress(lesson_id);
CREATE INDEX IF NOT EXISTS idx_user_course_progress_user_id ON public.user_course_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_user_course_progress_course_id ON public.user_course_progress(course_id);
CREATE INDEX IF NOT EXISTS idx_user_course_enrollment_user_id ON public.user_course_enrollment(user_id);
CREATE INDEX IF NOT EXISTS idx_user_course_enrollment_course_id ON public.user_course_enrollment(course_id);

-- Enable Row Level Security
ALTER TABLE public.courses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.modules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.lessons ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_lesson_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_course_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_course_enrollment ENABLE ROW LEVEL SECURITY;

-- RLS Policies for courses
CREATE POLICY "Anyone can view courses" 
ON public.courses FOR SELECT 
USING (true);

CREATE POLICY "Teachers can insert courses" 
ON public.courses FOR INSERT 
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can update courses" 
ON public.courses FOR UPDATE 
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can delete courses" 
ON public.courses FOR DELETE 
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- RLS Policies for modules
CREATE POLICY "Anyone can view modules" 
ON public.modules FOR SELECT 
USING (true);

CREATE POLICY "Teachers can insert modules" 
ON public.modules FOR INSERT 
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can update modules" 
ON public.modules FOR UPDATE 
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can delete modules" 
ON public.modules FOR DELETE 
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- RLS Policies for lessons
CREATE POLICY "Anyone can view lessons" 
ON public.lessons FOR SELECT 
USING (true);

CREATE POLICY "Teachers can insert lessons" 
ON public.lessons FOR INSERT 
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can update lessons" 
ON public.lessons FOR UPDATE 
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

CREATE POLICY "Teachers can delete lessons" 
ON public.lessons FOR DELETE 
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- RLS Policies for user_lesson_progress
CREATE POLICY "Users can view their own lesson progress" 
ON public.user_lesson_progress FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own lesson progress" 
ON public.user_lesson_progress FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own lesson progress" 
ON public.user_lesson_progress FOR UPDATE 
USING (auth.uid() = user_id);

-- RLS Policies for user_course_progress
CREATE POLICY "Users can view their own course progress" 
ON public.user_course_progress FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own course progress" 
ON public.user_course_progress FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own course progress" 
ON public.user_course_progress FOR UPDATE 
USING (auth.uid() = user_id);

-- RLS Policies for user_course_enrollment
CREATE POLICY "Users can view their own course enrollments" 
ON public.user_course_enrollment FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own course enrollments" 
ON public.user_course_enrollment FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own course enrollments" 
ON public.user_course_enrollment FOR UPDATE 
USING (auth.uid() = user_id);

-- Teachers can view all enrollments
CREATE POLICY "Teachers can view all enrollments" 
ON public.user_course_enrollment FOR SELECT 
USING (
  EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = auth.uid() AND role = 'teacher'
  )
);

-- Add these tables to the realtime publication if not already added
ALTER PUBLICATION supabase_realtime ADD TABLE public.courses;
ALTER PUBLICATION supabase_realtime ADD TABLE public.modules;
ALTER PUBLICATION supabase_realtime ADD TABLE public.lessons;
ALTER PUBLICATION supabase_realtime ADD TABLE public.user_lesson_progress;
ALTER PUBLICATION supabase_realtime ADD TABLE public.user_course_progress;
ALTER PUBLICATION supabase_realtime ADD TABLE public.user_course_enrollment;

-- Set up replica identity for realtime
ALTER TABLE public.courses REPLICA IDENTITY FULL;
ALTER TABLE public.modules REPLICA IDENTITY FULL;
ALTER TABLE public.lessons REPLICA IDENTITY FULL;
ALTER TABLE public.user_lesson_progress REPLICA IDENTITY FULL;
ALTER TABLE public.user_course_progress REPLICA IDENTITY FULL;
ALTER TABLE public.user_course_enrollment REPLICA IDENTITY FULL;

-- Create function to update module completion when all lessons are completed
CREATE OR REPLACE FUNCTION update_module_completion()
RETURNS TRIGGER AS $$
DECLARE
  all_completed BOOLEAN;
  module_id_val UUID;
BEGIN
  -- Get the module_id for this lesson
  SELECT module_id INTO module_id_val
  FROM public.lessons
  WHERE id = NEW.lesson_id;
  
  -- Check if all lessons in this module are completed by this user
  SELECT 
    CASE 
      WHEN COUNT(*) = 0 THEN FALSE
      ELSE bool_and(ulp.completed)
    END INTO all_completed
  FROM public.lessons l
  LEFT JOIN public.user_lesson_progress ulp 
    ON l.id = ulp.lesson_id AND ulp.user_id = NEW.user_id
  WHERE l.module_id = module_id_val;
  
  -- If all lessons are completed, update the module
  IF all_completed THEN
    UPDATE public.modules
    SET is_completed = TRUE
    WHERE id = module_id_val;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to update module completion when lesson progress changes
DROP TRIGGER IF EXISTS on_lesson_completion ON public.user_lesson_progress;
CREATE TRIGGER on_lesson_completion
  AFTER INSERT OR UPDATE ON public.user_lesson_progress
  FOR EACH ROW
  WHEN (NEW.completed = TRUE)
  EXECUTE FUNCTION update_module_completion();

-- Create function to update course completion when all modules are completed
CREATE OR REPLACE FUNCTION update_course_completion()
RETURNS TRIGGER AS $$
DECLARE
  course_id_val UUID;
  completed_count INTEGER;
  total_count INTEGER;
BEGIN
  -- Get the course_id for this module
  SELECT course_id INTO course_id_val
  FROM public.modules
  WHERE id = NEW.id;
  
  -- Count completed and total modules
  SELECT 
    COUNT(*) FILTER (WHERE is_completed = TRUE),
    COUNT(*)
  INTO 
    completed_count,
    total_count
  FROM public.modules
  WHERE course_id = course_id_val;
  
  -- Update the course with the new counts
  UPDATE public.courses
  SET 
    completed_modules = completed_count,
    total_modules = total_count
  WHERE id = course_id_val;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to update course completion when module completion changes
DROP TRIGGER IF EXISTS on_module_completion ON public.modules;
CREATE TRIGGER on_module_completion
  AFTER INSERT OR UPDATE ON public.modules
  FOR EACH ROW
  EXECUTE FUNCTION update_course_completion();

-- Make sure user_lesson_progress table has last_position column
ALTER TABLE IF EXISTS public.user_lesson_progress 
ADD COLUMN IF NOT EXISTS last_position INTEGER DEFAULT 0;
