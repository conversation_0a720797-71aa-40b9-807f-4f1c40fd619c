import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testDemographicFlow() {
  try {
    console.log('🧪 Testing Demographic Questionnaire Flow...\n');

    // 1. Test if questionnaire exists
    console.log('1. Checking if questionnaire exists...');
    const { data: questionnaires, error: qError } = await supabase
      .from('demographic_questionnaires')
      .select('*')
      .eq('is_active', true);

    if (qError) {
      console.log('❌ Error fetching questionnaires:', qError.message);
      return;
    }

    if (!questionnaires || questionnaires.length === 0) {
      console.log('❌ No active questionnaires found');
      return;
    }

    console.log('✅ Found active questionnaire:', questionnaires[0].title);
    console.log('   Questions count:', questionnaires[0].questions.length);

    // 2. Test questionnaire structure
    console.log('\n2. Validating questionnaire structure...');
    const questionnaire = questionnaires[0];
    const questions = questionnaire.questions;

    const requiredQuestions = ['consent', 'country', 'gender', 'age', 'formal_training', 'role_type'];
    const foundQuestions = questions.map(q => q.id);
    
    console.log('   Required questions:', requiredQuestions.join(', '));
    console.log('   Found questions:', foundQuestions.join(', '));

    const missingQuestions = requiredQuestions.filter(q => !foundQuestions.includes(q));
    if (missingQuestions.length > 0) {
      console.log('❌ Missing required questions:', missingQuestions.join(', '));
    } else {
      console.log('✅ All required questions present');
    }

    // 3. Test conditional logic
    console.log('\n3. Testing conditional logic...');
    const conditionalQuestions = questions.filter(q => q.conditional);
    console.log('   Conditional questions found:', conditionalQuestions.length);
    
    conditionalQuestions.forEach(q => {
      console.log(`   - ${q.id} shows when ${q.conditional.field} = "${q.conditional.value}"`);
    });

    // 4. Test response table structure
    console.log('\n4. Testing response table...');
    const { data: responses, error: rError } = await supabase
      .from('user_demographic_responses')
      .select('*')
      .limit(5);

    if (rError) {
      console.log('❌ Error accessing responses table:', rError.message);
    } else {
      console.log('✅ Response table accessible');
      console.log('   Current responses count:', responses?.length || 0);
      
      if (responses && responses.length > 0) {
        console.log('   Sample response keys:', Object.keys(responses[0].responses || {}));
      }
    }

    // 5. Test analytics function
    console.log('\n5. Testing analytics function...');
    try {
      const { data: analytics, error: aError } = await supabase.rpc('get_demographic_analytics');
      
      if (aError) {
        console.log('❌ Analytics function error:', aError.message);
      } else {
        console.log('✅ Analytics function working');
        if (analytics && analytics.length > 0) {
          const result = analytics[0];
          console.log('   Total responses:', result.total_responses);
          console.log('   Completion rate:', result.completion_rate + '%');
        }
      }
    } catch (err) {
      console.log('❌ Analytics function failed:', err.message);
    }

    console.log('\n🎉 Demographic questionnaire system test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testDemographicFlow();
