/**
 * TipTap to Markdown serializer
 * Converts TipTap JSON/HTML content to Markdown format
 */

import { Node } from '@tiptap/pm/model';

export interface SerializerOptions {
  tightLists?: boolean;
}

export class MarkdownSerializer {
  private options: SerializerOptions;

  constructor(options: SerializerOptions = {}) {
    this.options = {
      tightLists: false,
      ...options,
    };
  }

  serialize(doc: Node): string {
    return this.serializeNode(doc).trim();
  }

  private serializeNode(node: Node): string {
    switch (node.type.name) {
      case 'doc':
        return this.serializeChildren(node);
      
      case 'paragraph':
        const content = this.serializeChildren(node);
        return content ? content + '\n\n' : '';
      
      case 'heading':
        const level = node.attrs.level || 1;
        const headingContent = this.serializeChildren(node);
        return '#'.repeat(level) + ' ' + headingContent + '\n\n';
      
      case 'bulletList':
        return this.serializeList(node, '-');
      
      case 'orderedList':
        return this.serializeList(node, '1.');
      
      case 'listItem':
        return this.serializeChildren(node);
      
      case 'taskList':
        return this.serializeTaskList(node);
      
      case 'taskItem':
        const checked = node.attrs.checked;
        const taskContent = this.serializeChildren(node);
        return `- [${checked ? 'x' : ' '}] ${taskContent}`;
      
      case 'codeBlock':
      case 'codeBlockLowlight':
        const language = node.attrs.language || '';
        const codeContent = node.textContent;
        return '```' + language + '\n' + codeContent + '\n```\n\n';
      
      case 'blockquote':
        const quoteContent = this.serializeChildren(node);
        return quoteContent.split('\n').map(line => '> ' + line).join('\n') + '\n\n';
      
      case 'horizontalRule':
        return '---\n\n';

      case 'hardBreak':
        return '  \n';

      case 'image':
        const src = node.attrs.src || '';
        const alt = node.attrs.alt || '';
        const title = node.attrs.title;
        return title ? `![${alt}](${src} "${title}")` : `![${alt}](${src})`;

      case 'table':
        return this.serializeTable(node);

      case 'tableRow':
        return this.serializeTableRow(node);

      case 'tableCell':
      case 'tableHeader':
        return this.serializeTableCell(node);

      case 'text':
        return this.serializeText(node);
      
      default:
        return this.serializeChildren(node);
    }
  }

  private serializeChildren(node: Node): string {
    let result = '';
    node.forEach(child => {
      result += this.serializeNode(child);
    });
    return result;
  }

  private serializeText(node: Node): string {
    let text = node.text || '';
    
    if (node.marks) {
      for (const mark of node.marks) {
        switch (mark.type.name) {
          case 'bold':
          case 'strong':
            text = `**${text}**`;
            break;
          case 'italic':
          case 'em':
            text = `*${text}*`;
            break;
          case 'code':
            text = `\`${text}\``;
            break;
          case 'link':
            const href = mark.attrs.href || '';
            const title = mark.attrs.title;
            text = title ? `[${text}](${href} "${title}")` : `[${text}](${href})`;
            break;
          case 'underline':
            text = `<u>${text}</u>`;
            break;
          case 'highlight':
            text = `==${text}==`;
            break;
        }
      }
    }
    
    return text;
  }

  private serializeList(node: Node, marker: string): string {
    let result = '';
    let index = 1;
    
    node.forEach(child => {
      const content = this.serializeChildren(child).trim();
      const currentMarker = marker === '1.' ? `${index}.` : marker;
      result += `${currentMarker} ${content}\n`;
      index++;
    });
    
    return result + '\n';
  }

  private serializeTaskList(node: Node): string {
    let result = '';

    node.forEach(child => {
      result += this.serializeNode(child) + '\n';
    });

    return result + '\n';
  }

  private serializeTable(node: Node): string {
    let result = '';
    let isFirstRow = true;

    node.forEach(child => {
      if (child.type.name === 'tableRow') {
        const rowContent = this.serializeTableRow(child);
        result += rowContent;

        // Add header separator after first row
        if (isFirstRow) {
          const cellCount = child.childCount;
          const separator = '|' + ' --- |'.repeat(cellCount) + '\n';
          result += separator;
          isFirstRow = false;
        }
      }
    });

    return result + '\n';
  }

  private serializeTableRow(node: Node): string {
    let result = '|';

    node.forEach(child => {
      if (child.type.name === 'tableCell' || child.type.name === 'tableHeader') {
        const cellContent = this.serializeTableCell(child);
        result += ` ${cellContent} |`;
      }
    });

    return result + '\n';
  }

  private serializeTableCell(node: Node): string {
    const content = this.serializeChildren(node).trim();
    // Escape pipe characters in cell content
    return content.replace(/\|/g, '\\|').replace(/\n/g, ' ');
  }
}

/**
 * Converts TipTap editor content to Markdown
 */
export function tiptapToMarkdown(doc: Node, options?: SerializerOptions): string {
  const serializer = new MarkdownSerializer(options);
  return serializer.serialize(doc);
}

/**
 * Simple HTML to Markdown converter for basic cases
 */
export function htmlToMarkdown(html: string): string {
  if (!html) return '';
  
  let markdown = html;
  
  // Headers
  markdown = markdown.replace(/<h([1-6])>(.*?)<\/h[1-6]>/gi, (match, level, content) => {
    return '#'.repeat(parseInt(level)) + ' ' + content + '\n\n';
  });
  
  // Bold
  markdown = markdown.replace(/<(strong|b)>(.*?)<\/(strong|b)>/gi, '**$2**');
  
  // Italic
  markdown = markdown.replace(/<(em|i)>(.*?)<\/(em|i)>/gi, '*$2*');
  
  // Code
  markdown = markdown.replace(/<code>(.*?)<\/code>/gi, '`$1`');
  
  // Links
  markdown = markdown.replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gi, '[$2]($1)');
  
  // Images
  markdown = markdown.replace(/<img[^>]*src="([^"]*)"[^>]*alt="([^"]*)"[^>]*>/gi, '![$2]($1)');
  
  // Paragraphs
  markdown = markdown.replace(/<p>(.*?)<\/p>/gi, '$1\n\n');
  
  // Line breaks
  markdown = markdown.replace(/<br\s*\/?>/gi, '  \n');
  
  // Lists
  markdown = markdown.replace(/<ul>(.*?)<\/ul>/gis, (match, content) => {
    return content.replace(/<li>(.*?)<\/li>/gi, '- $1\n') + '\n';
  });
  
  markdown = markdown.replace(/<ol>(.*?)<\/ol>/gis, (match, content) => {
    let index = 1;
    return content.replace(/<li>(.*?)<\/li>/gi, () => `${index++}. $1\n`) + '\n';
  });
  
  // Blockquotes
  markdown = markdown.replace(/<blockquote>(.*?)<\/blockquote>/gis, (match, content) => {
    return content.split('\n').map(line => '> ' + line.trim()).join('\n') + '\n\n';
  });
  
  // Code blocks
  markdown = markdown.replace(/<pre><code(?:\s+class="language-(\w+)")?>(.*?)<\/code><\/pre>/gis, (match, lang, code) => {
    return '```' + (lang || '') + '\n' + code + '\n```\n\n';
  });

  // Tables
  markdown = markdown.replace(/<table[^>]*>(.*?)<\/table>/gis, (match, tableContent) => {
    let result = '';
    let isFirstRow = true;

    // Extract rows
    const rows = tableContent.match(/<tr[^>]*>(.*?)<\/tr>/gis);
    if (rows) {
      rows.forEach((row: string) => {
        const cells = row.match(/<t[hd][^>]*>(.*?)<\/t[hd]>/gis);
        if (cells) {
          const cellContents = cells.map((cell: string) => {
            return cell.replace(/<[^>]*>/g, '').trim().replace(/\|/g, '\\|');
          });
          result += '| ' + cellContents.join(' | ') + ' |\n';

          // Add header separator after first row
          if (isFirstRow) {
            result += '|' + ' --- |'.repeat(cellContents.length) + '\n';
            isFirstRow = false;
          }
        }
      });
    }

    return result + '\n';
  });

  // Clean up HTML tags
  markdown = markdown.replace(/<[^>]*>/g, '');

  // Clean up extra whitespace
  markdown = markdown.replace(/\n{3,}/g, '\n\n');

  return markdown.trim();
}
