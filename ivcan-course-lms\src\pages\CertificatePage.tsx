import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import Layout from '../components/Layout';
import Certificate from '@/components/certificate/Certificate';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Loader2, Award, Share2 } from 'lucide-react';
import { fetchCourseById } from '@/services/course/courseApi';
import { getEnrollmentStatusString, getEnrollmentDetails } from '@/services/course/enrollmentApi';
import { useAuth } from '@/context/AuthContext';
import { useIsMobile } from '@/hooks/use-mobile';
import { motion } from 'framer-motion';
import { toast } from 'sonner';
import Confetti from '@/components/ui/confetti';
import { isCourseCompleted, saveCompletionToLocalStorage } from '@/services/course/completionService';
import { supabase } from '@/integrations/supabase/client';
import { PageContainer, ContentSection } from '@/components/ui/floating-sidebar-container';

const CertificatePage = () => {
  const { courseId } = useParams<{ courseId: string }>();
  const { user } = useAuth();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const [completionDate, setCompletionDate] = useState<string>('');
  const [isEnrolled, setIsEnrolled] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  const [showCelebration, setShowCelebration] = useState(false);
  const [forceAccess, setForceAccess] = useState(false);

  // Fetch course data
  const { data: course, isLoading: courseLoading, error: courseError } = useQuery({
    queryKey: ['course', courseId],
    queryFn: () => fetchCourseById(courseId || ''),
    enabled: !!courseId
  });

  // Check completion status when component mounts
  useEffect(() => {
    if (!courseId || !user) return;

    const checkCompletionStatus = async () => {
      try {
        // Check enrollment and completion status
        const { data: enrollment, error: enrollmentError } = await supabase
          .from('user_course_enrollment')
          .select('status, completed_at')
          .eq('user_id', user.id)
          .eq('course_id', courseId)
          .single();

        if (enrollmentError) {
          console.error('Error checking enrollment:', enrollmentError);
          toast.error('Failed to verify course completion status');
          return;
        }

        if (enrollment) {
          setIsEnrolled(true);
          if (enrollment.status === 'completed' && enrollment.completed_at) {
            setIsCompleted(true);
            setCompletionDate(enrollment.completed_at);
            return;
          }
        }

        // If not completed, check if all modules are completed
        const { data: modules, error: modulesError } = await supabase
          .from('modules')
          .select('id')
          .eq('course_id', courseId);

        if (modulesError) {
          console.error('Error fetching modules:', modulesError);
          toast.error('Failed to verify module completion status');
          return;
        }

        if (modules && modules.length > 0) {
          const { data: moduleProgress, error: progressError } = await supabase
            .from('user_module_progress')
            .select('module_id')
            .eq('user_id', user.id)
            .eq('is_completed', true)
            .in('module_id', modules.map(m => m.id));

          if (progressError) {
            console.error('Error checking module progress:', progressError);
            toast.error('Failed to verify module completion status');
            return;
          }

          if (moduleProgress && moduleProgress.length === modules.length) {
            // All modules are completed, mark the course as completed
            const { completeCourse } = await import('@/services/course/completionService');
            const success = await completeCourse(courseId, user.id);
            if (success) {
              setIsCompleted(true);
              setCompletionDate(new Date().toISOString());
              toast.success('Course completion verified successfully');
            } else {
              toast.error('Failed to mark course as completed');
            }
          } else {
            toast.error('You need to complete all modules before accessing the certificate');
          }
        } else {
          toast.error('No modules found for this course');
        }
      } catch (error) {
        console.error('Error checking completion status:', error);
        toast.error('Failed to verify course completion status');
      }
    };

    checkCompletionStatus();
  }, [courseId, user]);

  // In development mode, allow access via URL query param
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const queryParams = new URLSearchParams(window.location.search);
      const force = queryParams.get('force');
      if (force === 'true' || force === '1') {
        console.log('Forcing certificate access via query param');
        setForceAccess(true);
        setIsCompleted(true);
        setIsEnrolled(true);
        setCompletionDate(new Date().toISOString());
      }
    }
  }, []);

  // Add a delay before redirecting to allow completion status to be checked
  useEffect(() => {
    if (!courseLoading && !isCompleted && !forceAccess && user) {
      const timer = setTimeout(() => {
        if (!isCompleted && !forceAccess) {
          toast.error('You need to complete this course to view the certificate');
          navigate(`/course/${courseId}`);
        }
      }, 2000); // Give more time for completion status to be checked
      
      return () => clearTimeout(timer);
    }
  }, [isCompleted, forceAccess, courseLoading, user, courseId, navigate]);

  if (courseLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </Layout>
    );
  }

  if (courseError || !course) {
    return (
      <Layout>
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold mb-4">Course not found</h1>
          <Button onClick={() => navigate('/dashboard')}>Back to Dashboard</Button>
        </div>
      </Layout>
    );
  }

  // Show celebration effect on first load
  useEffect(() => {
    if (isCompleted && !showCelebration) {
      setShowCelebration(true);
      // Hide celebration after 5 seconds
      const timer = setTimeout(() => setShowCelebration(false), 5000);
      return () => clearTimeout(timer);
    }
  }, [isCompleted]);

  return (
    <Layout>
      <PageContainer pageType="certificate">
        <ContentSection spacing="lg">
        {/* Navigation */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
          <Button
            variant="ghost"
            onClick={() => navigate(`/course/${courseId}`)}
            className="w-fit"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Course
          </Button>

          <Button
            variant="outline"
            onClick={() => navigate('/achievements')}
            className="w-fit border-primary/20 text-primary hover:bg-primary/5"
          >
            <Award className="mr-2 h-4 w-4" />
            View All Achievements
          </Button>
        </div>

        {/* Celebration animation */}
        {showCelebration && (
          <motion.div
            className="fixed inset-0 pointer-events-none z-50 flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <div className="absolute inset-0 bg-primary/10 backdrop-blur-sm" />
            <Confetti count={200} duration={5} active={true} />
            <motion.div
              initial={{ scale: 0.5, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ type: "spring", damping: 12 }}
              className="bg-card p-6 rounded-xl shadow-lg border border-primary/20 text-center z-10"
            >
              <Award className="h-16 w-16 text-primary mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-2">Achievement Unlocked!</h2>
              <p className="text-muted-foreground mb-2">You've earned your certificate!</p>
              <p className="text-xs text-primary font-medium">Course completed successfully</p>
            </motion.div>
          </motion.div>
        )}

        {/* Always show confetti on the certificate */}
        {isCompleted && <Confetti count={50} duration={8} active={true} />}

        {/* Header */}
        <div className="mb-6 sm:mb-8 text-center">
          <motion.h1
            className="text-2xl sm:text-3xl font-bold mb-2"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            {course.title} - Certificate
          </motion.h1>
          <motion.p
            className="text-muted-foreground text-sm sm:text-base"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            Congratulations on completing this course! You can download or share your certificate below.
          </motion.p>
        </div>

        {/* Certificate */}
        {isCompleted && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Certificate
              courseName={course.title}
              completionDate={completionDate}
              courseId={courseId || ''}
            />
          </motion.div>
        )}
        </ContentSection>
      </PageContainer>
    </Layout>
  );
};

export default CertificatePage;
