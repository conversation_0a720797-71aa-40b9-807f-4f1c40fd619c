/**
 * User Progress Repair Script
 * 
 * This script fixes progress for the current authenticated user without requiring admin privileges:
 * 1. Ensures user_lesson_progress records use is_completed consistently
 * 2. Recalculates module completion based on lesson progress 
 * 3. Updates course progress based on module completion
 */

const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.resolve(process.cwd(), '.env') });
dotenv.config({ path: path.resolve(process.cwd(), '.env.local') });

// Configuration
const SUPABASE_URL = process.env.VITE_SUPABASE_URL || "https://jibspqwieubavucdtccv.supabase.co";
const SUPABASE_ANON_KEY = process.env.VITE_SUPABASE_ANON_KEY;

// Create client
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Console output formatting
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

// Check if we can connect to Supabase
async function checkConnection() {
  console.log(`${colors.bright}${colors.cyan}=== Checking Connection ===${colors.reset}`);
  
  try {
    const { data, error } = await supabase.from('courses').select('count');
    if (error) {
      console.error(`${colors.red}Error connecting to Supabase: ${error.message}${colors.reset}`);
      process.exit(1);
    }
    console.log(`${colors.green}✓ Connected to Supabase successfully${colors.reset}`);
    return true;
  } catch (error) {
    console.error(`${colors.red}Unexpected error connecting to Supabase: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

// Get the current authenticated user
async function getCurrentUser() {
  console.log(`${colors.bright}${colors.cyan}=== Getting Current User ===${colors.reset}`);
  
  try {
    const { data, error } = await supabase.auth.getUser();
    if (error) {
      console.error(`${colors.red}Error getting current user: ${error.message}${colors.reset}`);
      console.log(`${colors.yellow}Please log in first to use this script.${colors.reset}`);
      process.exit(1);
    }
    
    if (!data?.user) {
      console.error(`${colors.red}No authenticated user found.${colors.reset}`);
      console.log(`${colors.yellow}Please log in first to use this script.${colors.reset}`);
      process.exit(1);
    }
    
    console.log(`${colors.green}✓ Found authenticated user: ${data.user.id}${colors.reset}`);
    return data.user;
  } catch (error) {
    console.error(`${colors.red}Unexpected error getting current user: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

// Fix lesson progress records for a user
async function fixLessonProgress(userId) {
  console.log(`\n${colors.bright}${colors.cyan}=== Fixing Lesson Progress Records ===${colors.reset}`);
  
  try {
    // Get all lesson progress records for this user
    const { data: records, error } = await supabase
      .from('user_lesson_progress')
      .select('*')
      .eq('user_id', userId);
    
    if (error) {
      console.error(`${colors.red}Error fetching lesson progress: ${error.message}${colors.reset}`);
      return false;
    }
    
    if (!records || records.length === 0) {
      console.log(`${colors.yellow}No lesson progress records found.${colors.reset}`);
      return true;
    }
    
    console.log(`${colors.green}Found ${records.length} lesson progress records.${colors.reset}`);
    
    // Check for inconsistencies
    let updatedCount = 0;
    for (const record of records) {
      let needsUpdate = false;
      let updateData = {};
      
      // Check for completed vs is_completed inconsistency
      if ('completed' in record && !('is_completed' in record)) {
        updateData.is_completed = record.completed;
        needsUpdate = true;
      } else if ('completed' in record && 'is_completed' in record && record.completed !== record.is_completed) {
        // Both exist but different values - use completed as source of truth
        updateData.is_completed = record.completed;
        needsUpdate = true;
      }
      
      // Ensure completed_at is set if is_completed is true
      if (record.is_completed && !record.completed_at) {
        updateData.completed_at = new Date().toISOString();
        needsUpdate = true;
      }
      
      // Ensure progress_percent is 100 if is_completed is true
      if (record.is_completed && (!record.progress_percent || record.progress_percent < 100)) {
        updateData.progress_percent = 100;
        needsUpdate = true;
      }
      
      if (needsUpdate) {
        const { error: updateError } = await supabase
          .from('user_lesson_progress')
          .update(updateData)
          .eq('id', record.id);
        
        if (updateError) {
          console.error(`${colors.red}Error updating lesson progress record ${record.id}: ${updateError.message}${colors.reset}`);
        } else {
          updatedCount++;
        }
      }
    }
    
    console.log(`${colors.green}✓ Updated ${updatedCount} lesson progress records${colors.reset}`);
    return true;
  } catch (error) {
    console.error(`${colors.red}Unexpected error fixing lesson progress: ${error.message}${colors.reset}`);
    return false;
  }
}

// Update module progress based on lesson completion
async function updateModuleProgress(userId) {
  console.log(`\n${colors.bright}${colors.cyan}=== Updating Module Progress ===${colors.reset}`);
  
  try {
    // Get all modules this user has lessons for
    const { data: lessonProgress, error: lessonError } = await supabase
      .from('user_lesson_progress')
      .select('lesson_id')
      .eq('user_id', userId);
    
    if (lessonError) {
      console.error(`${colors.red}Error fetching lesson progress: ${lessonError.message}${colors.reset}`);
      return false;
    }
    
    if (!lessonProgress || lessonProgress.length === 0) {
      console.log(`${colors.yellow}No lesson progress found.${colors.reset}`);
      return true;
    }
    
    // Get lessons to find modules
    const lessonIds = lessonProgress.map(p => p.lesson_id);
    const { data: lessons, error: lessonsError } = await supabase
      .from('lessons')
      .select('id, module_id')
      .in('id', lessonIds);
    
    if (lessonsError) {
      console.error(`${colors.red}Error fetching lessons: ${lessonsError.message}${colors.reset}`);
      return false;
    }
    
    // Get unique modules
    const moduleMap = {};
    lessons.forEach(lesson => {
      if (lesson.module_id) {
        moduleMap[lesson.module_id] = true;
      }
    });
    
    const moduleIds = Object.keys(moduleMap);
    console.log(`${colors.green}Found ${moduleIds.length} modules to update.${colors.reset}`);
    
    // For each module, calculate completion
    let updatedCount = 0;
    for (const moduleId of moduleIds) {
      // Get all lessons for this module
      const { data: moduleLessons, error: moduleLessonsError } = await supabase
        .from('lessons')
        .select('id')
        .eq('module_id', moduleId);
      
      if (moduleLessonsError) {
        console.error(`${colors.red}Error fetching lessons for module ${moduleId}: ${moduleLessonsError.message}${colors.reset}`);
        continue;
      }
      
      if (!moduleLessons || moduleLessons.length === 0) {
        console.log(`${colors.yellow}No lessons found for module ${moduleId}.${colors.reset}`);
        continue;
      }
      
      // Get completed lessons for this module
      const { data: completedLessons, error: completedError } = await supabase
        .from('user_lesson_progress')
        .select('lesson_id')
        .eq('user_id', userId)
        .eq('is_completed', true)
        .in('lesson_id', moduleLessons.map(l => l.id));
      
      if (completedError) {
        console.error(`${colors.red}Error fetching completed lessons: ${completedError.message}${colors.reset}`);
        continue;
      }
      
      const totalLessons = moduleLessons.length;
      const completedCount = completedLessons ? completedLessons.length : 0;
      const isCompleted = completedCount === totalLessons;
      const now = new Date().toISOString();
      
      console.log(`  Module ${moduleId}: ${completedCount}/${totalLessons} lessons completed`);
      
      // Get existing module progress
      const { data: existingProgress, error: progressError } = await supabase
        .from('user_module_progress')
        .select('*')
        .eq('user_id', userId)
        .eq('module_id', moduleId)
        .maybeSingle();
      
      if (progressError) {
        console.error(`${colors.red}Error checking module progress: ${progressError.message}${colors.reset}`);
        continue;
      }
      
      if (existingProgress) {
        // Only update if status changed
        if (existingProgress.is_completed !== isCompleted) {
          const { error: updateError } = await supabase
            .from('user_module_progress')
            .update({
              is_completed: isCompleted,
              completed_at: isCompleted ? now : null,
              updated_at: now
            })
            .eq('id', existingProgress.id);
          
          if (updateError) {
            console.error(`${colors.red}Error updating module progress: ${updateError.message}${colors.reset}`);
          } else {
            updatedCount++;
          }
        }
      } else {
        // Create new record
        const { error: insertError } = await supabase
          .from('user_module_progress')
          .insert({
            user_id: userId,
            module_id: moduleId,
            is_completed: isCompleted,
            completed_at: isCompleted ? now : null,
            created_at: now,
            updated_at: now
          });
        
        if (insertError) {
          console.error(`${colors.red}Error creating module progress: ${insertError.message}${colors.reset}`);
        } else {
          updatedCount++;
        }
      }
    }
    
    console.log(`${colors.green}✓ Updated ${updatedCount} module progress records${colors.reset}`);
    return true;
  } catch (error) {
    console.error(`${colors.red}Unexpected error updating module progress: ${error.message}${colors.reset}`);
    return false;
  }
}

// Update course progress based on module completion
async function updateCourseProgress(userId) {
  console.log(`\n${colors.bright}${colors.cyan}=== Updating Course Progress ===${colors.reset}`);
  
  try {
    // Get all modules this user has progress for
    const { data: moduleProgress, error: moduleError } = await supabase
      .from('user_module_progress')
      .select('module_id, is_completed')
      .eq('user_id', userId);
    
    if (moduleError) {
      console.error(`${colors.red}Error fetching module progress: ${moduleError.message}${colors.reset}`);
      return false;
    }
    
    if (!moduleProgress || moduleProgress.length === 0) {
      console.log(`${colors.yellow}No module progress found.${colors.reset}`);
      return true;
    }
    
    // Get modules to find courses
    const moduleIds = moduleProgress.map(p => p.module_id);
    const { data: modules, error: modulesError } = await supabase
      .from('modules')
      .select('id, course_id')
      .in('id', moduleIds);
    
    if (modulesError) {
      console.error(`${colors.red}Error fetching modules: ${modulesError.message}${colors.reset}`);
      return false;
    }
    
    // Group modules by course
    const courseModules = {};
    modules.forEach(module => {
      if (!module.course_id) return;
      
      if (!courseModules[module.course_id]) {
        courseModules[module.course_id] = { modules: [], completed: 0 };
      }
      
      courseModules[module.course_id].modules.push(module.id);
      
      // Check if this module is completed
      const progress = moduleProgress.find(p => p.module_id === module.id);
      if (progress && progress.is_completed) {
        courseModules[module.course_id].completed++;
      }
    });
    
    const courseIds = Object.keys(courseModules);
    console.log(`${colors.green}Found ${courseIds.length} courses to update.${colors.reset}`);
    
    // For each course, update enrollment
    let updatedCount = 0;
    for (const courseId of courseIds) {
      const course = courseModules[courseId];
      const totalModules = course.modules.length;
      const completedModules = course.completed;
      const isCompleted = completedModules === totalModules;
      const status = isCompleted ? 'completed' : (completedModules > 0 ? 'in_progress' : 'not_started');
      const now = new Date().toISOString();
      
      console.log(`  Course ${courseId}: ${completedModules}/${totalModules} modules completed (${status})`);
      
      // Get existing enrollment
      const { data: existingEnrollment, error: enrollmentError } = await supabase
        .from('user_course_enrollment')
        .select('*')
        .eq('user_id', userId)
        .eq('course_id', courseId)
        .maybeSingle();
      
      if (enrollmentError) {
        console.error(`${colors.red}Error checking course enrollment: ${enrollmentError.message}${colors.reset}`);
        continue;
      }
      
      if (existingEnrollment) {
        // Only update if status changed
        if (existingEnrollment.status !== status) {
          const { error: updateError } = await supabase
            .from('user_course_enrollment')
            .update({
              status: status,
              completed_at: isCompleted ? now : null,
              updated_at: now
            })
            .eq('id', existingEnrollment.id);
          
          if (updateError) {
            console.error(`${colors.red}Error updating course enrollment: ${updateError.message}${colors.reset}`);
          } else {
            updatedCount++;
          }
        }
      } else {
        // Create new enrollment
        const { error: insertError } = await supabase
          .from('user_course_enrollment')
          .insert({
            user_id: userId,
            course_id: courseId,
            status: status,
            enrolled_at: now,
            completed_at: isCompleted ? now : null,
            created_at: now,
            updated_at: now
          });
        
        if (insertError) {
          console.error(`${colors.red}Error creating course enrollment: ${insertError.message}${colors.reset}`);
        } else {
          updatedCount++;
        }
      }
      
      // Also update course progress
      const { data: existingProgress, error: progressError } = await supabase
        .from('user_course_progress')
        .select('id')
        .eq('user_id', userId)
        .eq('course_id', courseId)
        .maybeSingle();
      
      if (!progressError) {
        if (existingProgress) {
          // Update existing progress
          await supabase
            .from('user_course_progress')
            .update({
              last_accessed_at: now,
              updated_at: now
            })
            .eq('id', existingProgress.id);
        } else {
          // Create new progress
          await supabase
            .from('user_course_progress')
            .insert({
              user_id: userId,
              course_id: courseId,
              hours_spent: 0,
              last_accessed_at: now,
              created_at: now,
              updated_at: now
            });
        }
      }
    }
    
    console.log(`${colors.green}✓ Updated ${updatedCount} course enrollment records${colors.reset}`);
    return true;
  } catch (error) {
    console.error(`${colors.red}Unexpected error updating course progress: ${error.message}${colors.reset}`);
    return false;
  }
}

// Main function
async function main() {
  console.log(`${colors.bright}${colors.cyan}====================================${colors.reset}`);
  console.log(`${colors.bright}${colors.cyan}= USER PROGRESS REPAIR UTILITY =${colors.reset}`);
  console.log(`${colors.bright}${colors.cyan}====================================${colors.reset}`);
  
  try {
    // Check connection
    await checkConnection();
    
    // Get current user
    const user = await getCurrentUser();
    
    if (!user || !user.id) {
      console.error(`${colors.red}Could not determine user ID.${colors.reset}`);
      process.exit(1);
    }
    
    const userId = user.id;
    
    // Step 1: Fix lesson progress
    await fixLessonProgress(userId);
    
    // Step 2: Update module progress
    await updateModuleProgress(userId);
    
    // Step 3: Update course progress
    await updateCourseProgress(userId);
    
    console.log(`\n${colors.bright}${colors.green}✓ All fixes completed successfully!${colors.reset}`);
    console.log(`\nRun the health check script to verify: npm run check:supabase`);
  } catch (error) {
    console.error(`\n${colors.red}Unexpected error: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

// Run the script
main(); 