// scripts/setup-google-oauth.js
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Get the site URL
function getSiteUrl() {
  // Check if we're in a Netlify environment
  if (process.env.NETLIFY === 'true') {
    return process.env.URL || 'https://ivcan-course-lms.netlify.app';
  }
  
  // Default to localhost for development
  return 'http://localhost:8080';
}

// Get the Supabase project URL
function getSupabaseUrl() {
  // Try to get from environment variables
  const envPath = path.join(__dirname, '..', '.env');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const match = envContent.match(/VITE_SUPABASE_URL=(.+)/);
    if (match && match[1]) {
      return match[1].trim();
    }
  }
  
  // Fallback to hardcoded value
  return 'https://jibspqwieubavucdtccv.supabase.co';
}

// Main function
async function setupGoogleOAuth() {
  console.log('Google OAuth Setup Helper');
  console.log('========================\n');
  
  const siteUrl = getSiteUrl();
  const supabaseUrl = getSupabaseUrl();
  
  console.log('Site URL:', siteUrl);
  console.log('Supabase URL:', supabaseUrl);
  console.log('\n');
  
  console.log('Step 1: Google Cloud Console Setup');
  console.log('----------------------------------');
  console.log('1. Go to https://console.cloud.google.com/');
  console.log('2. Create a new project or select an existing one');
  console.log('3. Navigate to "APIs & Services" > "Credentials"');
  console.log('4. Click "Create Credentials" and select "OAuth client ID"');
  console.log('5. Select "Web application" as the application type');
  console.log('6. Add a name for your OAuth client (e.g., "IVCAN Course LMS")');
  console.log('\n');
  
  console.log('Step 2: Configure Authorized JavaScript Origins');
  console.log('---------------------------------------------');
  console.log('Add the following JavaScript origins:');
  console.log(`- ${siteUrl}`);
  console.log(`- http://localhost:8080`);
  console.log('\n');
  
  console.log('Step 3: Configure Authorized Redirect URIs');
  console.log('----------------------------------------');
  console.log('Add the following redirect URIs:');
  console.log(`- ${siteUrl}/auth/callback`);
  console.log(`- http://localhost:8080/auth/callback`);
  console.log(`- ${supabaseUrl}/auth/v1/callback`);
  console.log('\n');
  
  // Prompt for Google Client ID and Secret
  rl.question('Enter your Google Client ID: ', (clientId) => {
    rl.question('Enter your Google Client Secret: ', (clientSecret) => {
      console.log('\n');
      console.log('Step 4: Configure Supabase Auth Settings');
      console.log('--------------------------------------');
      console.log('1. Go to your Supabase Dashboard');
      console.log('2. Select your project');
      console.log('3. Navigate to "Authentication" > "Providers"');
      console.log('4. Find "Google" in the list of providers and click "Edit"');
      console.log('5. Enable the provider by toggling the switch');
      console.log('6. Enter the following credentials:');
      console.log(`   - Client ID: ${clientId}`);
      console.log(`   - Client Secret: ${clientSecret}`);
      console.log('7. Save the changes');
      console.log('\n');
      
      console.log('Step 5: Configure Site URL and Redirect URLs in Supabase');
      console.log('----------------------------------------------------');
      console.log('1. In your Supabase dashboard, go to "Authentication" > "URL Configuration"');
      console.log('2. Set the Site URL:');
      console.log(`   - ${siteUrl}`);
      console.log('3. Add Redirect URLs:');
      console.log(`   - ${siteUrl}/auth/callback`);
      console.log(`   - http://localhost:8080/auth/callback`);
      console.log('4. Save the changes');
      console.log('\n');
      
      console.log('Step 6: Test the Integration');
      console.log('-------------------------');
      console.log('1. Run your application');
      console.log('2. Navigate to the login page');
      console.log('3. Click the "Log in with Google" button');
      console.log('4. You should be redirected to Google\'s authentication page');
      console.log('5. After successful authentication, you should be redirected back to your application');
      console.log('\n');
      
      rl.close();
    });
  });
}

// Run the setup
setupGoogleOAuth();
