export interface TestQuestion {
  id: string;
  question: string;
  questionNumber: number;
}

export interface RatingQuestion extends TestQuestion {
  type: 'rating';
  minRating: number;
  maxRating: number;
  minLabel: string;
  maxLabel: string;
}

export interface ModuleTest {
  id: string;
  moduleId: string;
  title: string;
  type: 'pre_test' | 'post_test';
  description?: string;
  questions: RatingQuestion[];
  createdAt: string;
  updatedAt: string;
}

export interface TestResponse {
  questionId: string;
  rating: number;
}

export interface ModuleTestResponse {
  id: string;
  testId: string;
  userId: string;
  responses: TestResponse[];
  createdAt: string;
  updatedAt: string;
}

export const createDefaultModuleTest = (
  moduleId: string,
  type: 'pre_test' | 'post_test'
): Partial<ModuleTest> => {
  return {
    moduleId,
    type,
    title: type === 'pre_test' ? 'Pre-Intervention Questionnaire' : 'Post-Intervention Questionnaire',
    description: 'Using a scale of 1 to 4, please rate your degree of understanding/familiarity with the following statement about intravenous (IV) cannulation in medical imaging.',
    questions: []
  };
};

export const createDefaultRatingQuestion = (questionNumber: number): RatingQuestion => {
  return {
    id: crypto.randomUUID(),
    type: 'rating',
    question: '',
    questionNumber,
    minRating: 1,
    maxRating: 4,
    minLabel: 'do not understand/not familiar',
    maxLabel: 'very understanding/very familiar'
  };
};

export const ratingDescriptions = [
  { value: 1, label: 'do not understand/not familiar' },
  { value: 2, label: 'slight understanding/slightly familiar' },
  { value: 3, label: 'somewhat understanding/somewhat familiar' },
  { value: 4, label: 'very understanding/very familiar' }
]; 