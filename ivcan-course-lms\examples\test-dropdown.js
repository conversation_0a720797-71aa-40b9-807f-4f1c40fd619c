// Simple test script to check dropdown rendering
const fs = require('fs');
const path = require('path');
const { markdownToHtml } = require('../src/lib/content-converter');

// Read our test markdown file
const markdownPath = path.join(__dirname, 'dropdown-example.md');
const markdown = fs.readFileSync(markdownPath, 'utf-8');

// Convert to HTML
const html = markdownToHtml(markdown);

// Output the result
console.log("=== CONVERTED HTML ===");
console.log(html);

// Save to a file for inspection
const outputPath = path.join(__dirname, 'dropdown-result.html');
fs.writeFileSync(outputPath, `
<!DOCTYPE html>
<html>
<head>
  <title>Dropdown Test Result</title>
  <style>
    body { font-family: system-ui, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
    
    /* Minimal dropdown styling */
    .dropdown { 
      margin: 0; 
      border: none;
      box-shadow: none;
      outline: none;
    }
    .dropdown summary { 
      cursor: pointer;
      padding: 0;
      display: flex;
      align-items: center;
      background: transparent;
      border: none;
      box-shadow: none;
      outline: none;
    }
    .dropdown summary::-webkit-details-marker {
      display: none;
    }
    .dropdown-icon {
      position: relative;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background-color: #0070f3;
      opacity: 0.8;
    }
    .dropdown-icon::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 8px;
      height: 8px;
      border-right: 2px solid white;
      border-bottom: 2px solid white;
      transform: translate(-50%, -70%) rotate(45deg);
    }
    .dropdown[open] .dropdown-icon {
      transform: rotate(180deg);
    }
    .dropdown > div {
      padding: 0.25rem 0 0 0.75rem;
    }
  </style>
</head>
<body>
  ${html}
</body>
</html>
`);

console.log(`\nResult saved to ${outputPath}`); 