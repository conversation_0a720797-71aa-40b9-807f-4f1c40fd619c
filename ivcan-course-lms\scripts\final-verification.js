import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function finalVerification() {
  console.log('🔍 FINAL VERIFICATION - Demographic Questionnaire System\n');
  
  let allTestsPassed = true;
  const results = [];

  // Test 1: Database Tables
  try {
    console.log('1. Testing database tables...');
    
    const { data: questionnaires, error: qError } = await supabase
      .from('demographic_questionnaires')
      .select('*')
      .limit(1);

    const { data: responses, error: rError } = await supabase
      .from('user_demographic_responses')
      .select('*')
      .limit(1);

    if (qError || rError) {
      results.push('❌ Database tables: FAILED');
      allTestsPassed = false;
    } else {
      results.push('✅ Database tables: WORKING');
    }
  } catch (error) {
    results.push('❌ Database tables: ERROR');
    allTestsPassed = false;
  }

  // Test 2: Active Questionnaire
  try {
    console.log('2. Testing active questionnaire...');
    
    const { data: questionnaire, error } = await supabase
      .from('demographic_questionnaires')
      .select('*')
      .eq('is_active', true)
      .single();

    if (error || !questionnaire) {
      results.push('❌ Active questionnaire: NOT FOUND');
      allTestsPassed = false;
    } else {
      const questionCount = questionnaire.questions?.length || 0;
      if (questionCount >= 15) {
        results.push(`✅ Active questionnaire: WORKING (${questionCount} questions)`);
      } else {
        results.push(`⚠️ Active questionnaire: INCOMPLETE (${questionCount} questions)`);
      }
    }
  } catch (error) {
    results.push('❌ Active questionnaire: ERROR');
    allTestsPassed = false;
  }

  // Test 3: Question Structure
  try {
    console.log('3. Testing question structure...');
    
    const { data: questionnaire } = await supabase
      .from('demographic_questionnaires')
      .select('questions')
      .eq('is_active', true)
      .single();

    if (questionnaire?.questions) {
      const questions = questionnaire.questions;
      const requiredFields = ['consent', 'country', 'gender', 'age', 'formal_training', 'role_type'];
      const questionIds = questions.map(q => q.id);
      const hasAllRequired = requiredFields.every(field => questionIds.includes(field));
      
      if (hasAllRequired) {
        results.push('✅ Question structure: COMPLETE');
      } else {
        results.push('⚠️ Question structure: MISSING REQUIRED FIELDS');
      }
    } else {
      results.push('❌ Question structure: INVALID');
      allTestsPassed = false;
    }
  } catch (error) {
    results.push('❌ Question structure: ERROR');
    allTestsPassed = false;
  }

  // Test 4: Conditional Logic
  try {
    console.log('4. Testing conditional logic...');
    
    const { data: questionnaire } = await supabase
      .from('demographic_questionnaires')
      .select('questions')
      .eq('is_active', true)
      .single();

    if (questionnaire?.questions) {
      const conditionalQuestions = questionnaire.questions.filter(q => q.conditional);
      if (conditionalQuestions.length >= 8) {
        results.push(`✅ Conditional logic: WORKING (${conditionalQuestions.length} conditional questions)`);
      } else {
        results.push(`⚠️ Conditional logic: LIMITED (${conditionalQuestions.length} conditional questions)`);
      }
    } else {
      results.push('❌ Conditional logic: ERROR');
      allTestsPassed = false;
    }
  } catch (error) {
    results.push('❌ Conditional logic: ERROR');
    allTestsPassed = false;
  }

  // Test 5: RLS Policies
  try {
    console.log('5. Testing RLS policies...');
    
    // Try to access tables (should work with anon key due to policies)
    const { error: qError } = await supabase
      .from('demographic_questionnaires')
      .select('*')
      .eq('is_active', true)
      .limit(1);

    const { error: rError } = await supabase
      .from('user_demographic_responses')
      .select('*')
      .limit(1);

    if (!qError && !rError) {
      results.push('✅ RLS policies: WORKING');
    } else {
      results.push('⚠️ RLS policies: RESTRICTIVE (expected for security)');
    }
  } catch (error) {
    results.push('❌ RLS policies: ERROR');
    allTestsPassed = false;
  }

  // Display Results
  console.log('\n📊 VERIFICATION RESULTS:\n');
  results.forEach(result => console.log('   ' + result));

  console.log('\n' + '='.repeat(60));
  
  if (allTestsPassed) {
    console.log('🎉 ALL SYSTEMS GO! Demographic questionnaire is ready for production.');
    console.log('\n📋 Next Steps:');
    console.log('   1. Open http://localhost:8081 in your browser');
    console.log('   2. Create a new user account to test the questionnaire');
    console.log('   3. Login as admin to view demographic analytics');
    console.log('   4. Verify the complete user flow works as expected');
  } else {
    console.log('⚠️ Some issues detected. Please review the failed tests above.');
  }

  console.log('\n🔗 System Components:');
  console.log('   • Database: Supabase tables with RLS');
  console.log('   • Frontend: React components with TypeScript');
  console.log('   • API: Supabase client with error handling');
  console.log('   • Admin: Analytics dashboard in admin panel');
  console.log('   • Security: Row-level security policies');

  console.log('\n✨ Features Available:');
  console.log('   • 17-question demographic questionnaire');
  console.log('   • Conditional logic based on user responses');
  console.log('   • Mandatory completion before dashboard access');
  console.log('   • Real-time analytics for administrators');
  console.log('   • Secure data storage and access controls');

  return allTestsPassed;
}

finalVerification().then(success => {
  process.exit(success ? 0 : 1);
});
