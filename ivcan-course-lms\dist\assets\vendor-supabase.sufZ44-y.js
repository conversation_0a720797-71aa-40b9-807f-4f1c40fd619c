const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/vendor.DQpuTRuB.js","assets/vendor-react.BcAa1DKr.js"])))=>i.map(i=>d[i]);
var u=(i,e,t)=>new Promise((s,r)=>{var n=l=>{try{a(t.next(l))}catch(h){r(h)}},o=l=>{try{a(t.throw(l))}catch(h){r(h)}},a=l=>l.done?s(l.value):Promise.resolve(l.value).then(n,o);a((t=t.apply(i,e)).next())});import{W,X as kt,Y as O}from"./vendor.DQpuTRuB.js";const St=i=>{let e;return i?e=i:typeof fetch=="undefined"?e=(...t)=>W(()=>u(void 0,null,function*(){const{default:s}=yield Promise.resolve().then(()=>X);return{default:s}}),void 0).then(({default:s})=>s(...t)):e=fetch,(...t)=>e(...t)};class xe extends Error{constructor(e,t="FunctionsError",s){super(e),this.name=t,this.context=s}}class Tt extends xe{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class Et extends xe{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class Ot extends xe{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}var Ee;(function(i){i.Any="any",i.ApNortheast1="ap-northeast-1",i.ApNortheast2="ap-northeast-2",i.ApSouth1="ap-south-1",i.ApSoutheast1="ap-southeast-1",i.ApSoutheast2="ap-southeast-2",i.CaCentral1="ca-central-1",i.EuCentral1="eu-central-1",i.EuWest1="eu-west-1",i.EuWest2="eu-west-2",i.EuWest3="eu-west-3",i.SaEast1="sa-east-1",i.UsEast1="us-east-1",i.UsWest1="us-west-1",i.UsWest2="us-west-2"})(Ee||(Ee={}));var $t=function(i,e,t,s){function r(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(c){try{h(s.next(c))}catch(d){o(d)}}function l(c){try{h(s.throw(c))}catch(d){o(d)}}function h(c){c.done?n(c.value):r(c.value).then(a,l)}h((s=s.apply(i,e||[])).next())})};class jt{constructor(e,{headers:t={},customFetch:s,region:r=Ee.Any}={}){this.url=e,this.headers=t,this.region=r,this.fetch=St(s)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var s;return $t(this,void 0,void 0,function*(){try{const{headers:r,method:n,body:o}=t;let a={},{region:l}=t;l||(l=this.region),l&&l!=="any"&&(a["x-region"]=l);let h;o&&(r&&!Object.prototype.hasOwnProperty.call(r,"Content-Type")||!r)&&(typeof Blob!="undefined"&&o instanceof Blob||o instanceof ArrayBuffer?(a["Content-Type"]="application/octet-stream",h=o):typeof o=="string"?(a["Content-Type"]="text/plain",h=o):typeof FormData!="undefined"&&o instanceof FormData?h=o:(a["Content-Type"]="application/json",h=JSON.stringify(o)));const c=yield this.fetch(`${this.url}/${e}`,{method:n||"POST",headers:Object.assign(Object.assign(Object.assign({},a),this.headers),r),body:h}).catch(v=>{throw new Tt(v)}),d=c.headers.get("x-relay-error");if(d&&d==="true")throw new Et(c);if(!c.ok)throw new Ot(c);let f=((s=c.headers.get("Content-Type"))!==null&&s!==void 0?s:"text/plain").split(";")[0].trim(),g;return f==="application/json"?g=yield c.json():f==="application/octet-stream"?g=yield c.blob():f==="text/event-stream"?g=c:f==="multipart/form-data"?g=yield c.formData():g=yield c.text(),{data:g,error:null}}catch(r){return{data:null,error:r}}})}}var T={},Ie={},de={},ne={},fe={},ge={},Pt=function(){if(typeof self!="undefined")return self;if(typeof window!="undefined")return window;if(typeof global!="undefined")return global;throw new Error("unable to locate global object")},Q=Pt();const At=Q.fetch,Ye=Q.fetch.bind(Q),Ze=Q.Headers,Rt=Q.Request,Ct=Q.Response,X=Object.freeze(Object.defineProperty({__proto__:null,Headers:Ze,Request:Rt,Response:Ct,default:Ye,fetch:At},Symbol.toStringTag,{value:"Module"})),xt=kt(X);var pe={};Object.defineProperty(pe,"__esModule",{value:!0});let It=class extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}};pe.default=It;var et=O&&O.__importDefault||function(i){return i&&i.__esModule?i:{default:i}};Object.defineProperty(ge,"__esModule",{value:!0});const Lt=et(xt),Ut=et(pe);let Dt=class{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:typeof fetch=="undefined"?this.fetch=Lt.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),this.method!=="GET"&&this.method!=="HEAD"&&(this.headers["Content-Type"]="application/json");const s=this.fetch;let r=s(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(n=>u(this,null,function*(){var o,a,l;let h=null,c=null,d=null,f=n.status,g=n.statusText;if(n.ok){if(this.method!=="HEAD"){const k=yield n.text();k===""||(this.headers.Accept==="text/csv"||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?c=k:c=JSON.parse(k))}const w=(o=this.headers.Prefer)===null||o===void 0?void 0:o.match(/count=(exact|planned|estimated)/),y=(a=n.headers.get("content-range"))===null||a===void 0?void 0:a.split("/");w&&y&&y.length>1&&(d=parseInt(y[1])),this.isMaybeSingle&&this.method==="GET"&&Array.isArray(c)&&(c.length>1?(h={code:"PGRST116",details:`Results contain ${c.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},c=null,d=null,f=406,g="Not Acceptable"):c.length===1?c=c[0]:c=null)}else{const w=yield n.text();try{h=JSON.parse(w),Array.isArray(h)&&n.status===404&&(c=[],h=null,f=200,g="OK")}catch(y){n.status===404&&w===""?(f=204,g="No Content"):h={message:w}}if(h&&this.isMaybeSingle&&(!((l=h==null?void 0:h.details)===null||l===void 0)&&l.includes("0 rows"))&&(h=null,f=200,g="OK"),h&&this.shouldThrowOnError)throw new Ut.default(h)}return{error:h,data:c,count:d,status:f,statusText:g}}));return this.shouldThrowOnError||(r=r.catch(n=>{var o,a,l;return{error:{message:`${(o=n==null?void 0:n.name)!==null&&o!==void 0?o:"FetchError"}: ${n==null?void 0:n.message}`,details:`${(a=n==null?void 0:n.stack)!==null&&a!==void 0?a:""}`,hint:"",code:`${(l=n==null?void 0:n.code)!==null&&l!==void 0?l:""}`},data:null,count:null,status:0,statusText:""}})),r.then(e,t)}returns(){return this}overrideTypes(){return this}};ge.default=Dt;var Bt=O&&O.__importDefault||function(i){return i&&i.__esModule?i:{default:i}};Object.defineProperty(fe,"__esModule",{value:!0});const Nt=Bt(ge);let qt=class extends Nt.default{select(e){let t=!1;const s=(e!=null?e:"*").split("").map(r=>/\s/.test(r)&&!t?"":(r==='"'&&(t=!t),r)).join("");return this.url.searchParams.set("select",s),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:s,foreignTable:r,referencedTable:n=r}={}){const o=n?`${n}.order`:"order",a=this.url.searchParams.get(o);return this.url.searchParams.set(o,`${a?`${a},`:""}${e}.${t?"asc":"desc"}${s===void 0?"":s?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:s=t}={}){const r=typeof s=="undefined"?"limit":`${s}.limit`;return this.url.searchParams.set(r,`${e}`),this}range(e,t,{foreignTable:s,referencedTable:r=s}={}){const n=typeof r=="undefined"?"offset":`${r}.offset`,o=typeof r=="undefined"?"limit":`${r}.limit`;return this.url.searchParams.set(n,`${e}`),this.url.searchParams.set(o,`${t-e+1}`),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.method==="GET"?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:s=!1,buffers:r=!1,wal:n=!1,format:o="text"}={}){var a;const l=[e?"analyze":null,t?"verbose":null,s?"settings":null,r?"buffers":null,n?"wal":null].filter(Boolean).join("|"),h=(a=this.headers.Accept)!==null&&a!==void 0?a:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${o}; for="${h}"; options=${l};`,o==="json"?this:this}rollback(){var e;return((e=this.headers.Prefer)!==null&&e!==void 0?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}};fe.default=qt;var Ft=O&&O.__importDefault||function(i){return i&&i.__esModule?i:{default:i}};Object.defineProperty(ne,"__esModule",{value:!0});const Mt=Ft(fe);let Jt=class extends Mt.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){const s=Array.from(new Set(t)).map(r=>typeof r=="string"&&new RegExp("[,()]").test(r)?`"${r}"`:`${r}`).join(",");return this.url.searchParams.append(e,`in.(${s})`),this}contains(e,t){return typeof t=="string"?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return typeof t=="string"?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return typeof t=="string"?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:s,type:r}={}){let n="";r==="plain"?n="pl":r==="phrase"?n="ph":r==="websearch"&&(n="w");const o=s===void 0?"":`(${s})`;return this.url.searchParams.append(e,`${n}fts${o}.${t}`),this}match(e){return Object.entries(e).forEach(([t,s])=>{this.url.searchParams.append(t,`eq.${s}`)}),this}not(e,t,s){return this.url.searchParams.append(e,`not.${t}.${s}`),this}or(e,{foreignTable:t,referencedTable:s=t}={}){const r=s?`${s}.or`:"or";return this.url.searchParams.append(r,`(${e})`),this}filter(e,t,s){return this.url.searchParams.append(e,`${t}.${s}`),this}};ne.default=Jt;var zt=O&&O.__importDefault||function(i){return i&&i.__esModule?i:{default:i}};Object.defineProperty(de,"__esModule",{value:!0});const Z=zt(ne);let Ht=class{constructor(e,{headers:t={},schema:s,fetch:r}){this.url=e,this.headers=t,this.schema=s,this.fetch=r}select(e,{head:t=!1,count:s}={}){const r=t?"HEAD":"GET";let n=!1;const o=(e!=null?e:"*").split("").map(a=>/\s/.test(a)&&!n?"":(a==='"'&&(n=!n),a)).join("");return this.url.searchParams.set("select",o),s&&(this.headers.Prefer=`count=${s}`),new Z.default({method:r,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t,defaultToNull:s=!0}={}){const r="POST",n=[];if(this.headers.Prefer&&n.push(this.headers.Prefer),t&&n.push(`count=${t}`),s||n.push("missing=default"),this.headers.Prefer=n.join(","),Array.isArray(e)){const o=e.reduce((a,l)=>a.concat(Object.keys(l)),[]);if(o.length>0){const a=[...new Set(o)].map(l=>`"${l}"`);this.url.searchParams.set("columns",a.join(","))}}return new Z.default({method:r,url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:s=!1,count:r,defaultToNull:n=!0}={}){const o="POST",a=[`resolution=${s?"ignore":"merge"}-duplicates`];if(t!==void 0&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&a.push(this.headers.Prefer),r&&a.push(`count=${r}`),n||a.push("missing=default"),this.headers.Prefer=a.join(","),Array.isArray(e)){const l=e.reduce((h,c)=>h.concat(Object.keys(c)),[]);if(l.length>0){const h=[...new Set(l)].map(c=>`"${c}"`);this.url.searchParams.set("columns",h.join(","))}}return new Z.default({method:o,url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){const s="PATCH",r=[];return this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push(`count=${t}`),this.headers.Prefer=r.join(","),new Z.default({method:s,url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){const t="DELETE",s=[];return e&&s.push(`count=${e}`),this.headers.Prefer&&s.unshift(this.headers.Prefer),this.headers.Prefer=s.join(","),new Z.default({method:t,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}};de.default=Ht;var _e={},ve={};Object.defineProperty(ve,"__esModule",{value:!0});ve.version=void 0;ve.version="0.0.0-automated";Object.defineProperty(_e,"__esModule",{value:!0});_e.DEFAULT_HEADERS=void 0;const Kt=ve;_e.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${Kt.version}`};var tt=O&&O.__importDefault||function(i){return i&&i.__esModule?i:{default:i}};Object.defineProperty(Ie,"__esModule",{value:!0});const Gt=tt(de),Vt=tt(ne),Wt=_e;let Qt=class st{constructor(e,{headers:t={},schema:s,fetch:r}={}){this.url=e,this.headers=Object.assign(Object.assign({},Wt.DEFAULT_HEADERS),t),this.schemaName=s,this.fetch=r}from(e){const t=new URL(`${this.url}/${e}`);return new Gt.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(e){return new st(this.url,{headers:this.headers,schema:e,fetch:this.fetch})}rpc(e,t={},{head:s=!1,get:r=!1,count:n}={}){let o;const a=new URL(`${this.url}/rpc/${e}`);let l;s||r?(o=s?"HEAD":"GET",Object.entries(t).filter(([c,d])=>d!==void 0).map(([c,d])=>[c,Array.isArray(d)?`{${d.join(",")}}`:`${d}`]).forEach(([c,d])=>{a.searchParams.append(c,d)})):(o="POST",l=t);const h=Object.assign({},this.headers);return n&&(h.Prefer=`count=${n}`),new Vt.default({method:o,url:a,headers:h,schema:this.schemaName,body:l,fetch:this.fetch,allowEmpty:!1})}};Ie.default=Qt;var Y=O&&O.__importDefault||function(i){return i&&i.__esModule?i:{default:i}};Object.defineProperty(T,"__esModule",{value:!0});T.PostgrestError=T.PostgrestBuilder=T.PostgrestTransformBuilder=T.PostgrestFilterBuilder=T.PostgrestQueryBuilder=T.PostgrestClient=void 0;const rt=Y(Ie);T.PostgrestClient=rt.default;const it=Y(de);T.PostgrestQueryBuilder=it.default;const nt=Y(ne);T.PostgrestFilterBuilder=nt.default;const ot=Y(fe);T.PostgrestTransformBuilder=ot.default;const at=Y(ge);T.PostgrestBuilder=at.default;const lt=Y(pe);T.PostgrestError=lt.default;var Xt=T.default={PostgrestClient:rt.default,PostgrestQueryBuilder:it.default,PostgrestFilterBuilder:nt.default,PostgrestTransformBuilder:ot.default,PostgrestBuilder:at.default,PostgrestError:lt.default};const{PostgrestClient:Yt,PostgrestQueryBuilder:Mr,PostgrestFilterBuilder:Jr,PostgrestTransformBuilder:zr,PostgrestBuilder:Hr,PostgrestError:Kr}=Xt,Zt="2.11.2",es={"X-Client-Info":`realtime-js/${Zt}`},ts="1.0.0",ct=1e4,ss=1e3;var V;(function(i){i[i.connecting=0]="connecting",i[i.open=1]="open",i[i.closing=2]="closing",i[i.closed=3]="closed"})(V||(V={}));var E;(function(i){i.closed="closed",i.errored="errored",i.joined="joined",i.joining="joining",i.leaving="leaving"})(E||(E={}));var j;(function(i){i.close="phx_close",i.error="phx_error",i.join="phx_join",i.reply="phx_reply",i.leave="phx_leave",i.access_token="access_token"})(j||(j={}));var Oe;(function(i){i.websocket="websocket"})(Oe||(Oe={}));var q;(function(i){i.Connecting="connecting",i.Open="open",i.Closing="closing",i.Closed="closed"})(q||(q={}));class rs{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):t(typeof e=="string"?JSON.parse(e):{})}_binaryDecode(e){const t=new DataView(e),s=new TextDecoder;return this._decodeBroadcast(e,t,s)}_decodeBroadcast(e,t,s){const r=t.getUint8(1),n=t.getUint8(2);let o=this.HEADER_LENGTH+2;const a=s.decode(e.slice(o,o+r));o=o+r;const l=s.decode(e.slice(o,o+n));o=o+n;const h=JSON.parse(s.decode(e.slice(o,e.byteLength)));return{ref:null,topic:a,event:l,payload:h}}}class ht{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}var m;(function(i){i.abstime="abstime",i.bool="bool",i.date="date",i.daterange="daterange",i.float4="float4",i.float8="float8",i.int2="int2",i.int4="int4",i.int4range="int4range",i.int8="int8",i.int8range="int8range",i.json="json",i.jsonb="jsonb",i.money="money",i.numeric="numeric",i.oid="oid",i.reltime="reltime",i.text="text",i.time="time",i.timestamp="timestamp",i.timestamptz="timestamptz",i.timetz="timetz",i.tsrange="tsrange",i.tstzrange="tstzrange"})(m||(m={}));const Be=(i,e,t={})=>{var s;const r=(s=t.skipTypes)!==null&&s!==void 0?s:[];return Object.keys(e).reduce((n,o)=>(n[o]=is(o,i,e,r),n),{})},is=(i,e,t,s)=>{const r=e.find(a=>a.name===i),n=r==null?void 0:r.type,o=t[i];return n&&!s.includes(n)?ut(n,o):$e(o)},ut=(i,e)=>{if(i.charAt(0)==="_"){const t=i.slice(1,i.length);return ls(e,t)}switch(i){case m.bool:return ns(e);case m.float4:case m.float8:case m.int2:case m.int4:case m.int8:case m.numeric:case m.oid:return os(e);case m.json:case m.jsonb:return as(e);case m.timestamp:return cs(e);case m.abstime:case m.date:case m.daterange:case m.int4range:case m.int8range:case m.money:case m.reltime:case m.text:case m.time:case m.timestamptz:case m.timetz:case m.tsrange:case m.tstzrange:return $e(e);default:return $e(e)}},$e=i=>i,ns=i=>{switch(i){case"t":return!0;case"f":return!1;default:return i}},os=i=>{if(typeof i=="string"){const e=parseFloat(i);if(!Number.isNaN(e))return e}return i},as=i=>{if(typeof i=="string")try{return JSON.parse(i)}catch(e){return console.log(`JSON parse error: ${e}`),i}return i},ls=(i,e)=>{if(typeof i!="string")return i;const t=i.length-1,s=i[t];if(i[0]==="{"&&s==="}"){let n;const o=i.slice(1,t);try{n=JSON.parse("["+o+"]")}catch(a){n=o?o.split(","):[]}return n.map(a=>ut(e,a))}return i},cs=i=>typeof i=="string"?i.replace(" ","T"):i,dt=i=>{let e=i;return e=e.replace(/^ws/i,"http"),e=e.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),e.replace(/\/+$/,"")};class we{constructor(e,t,s={},r=ct){this.channel=e,this.event=t,this.payload=s,this.timeout=r,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var s;return this._hasReceived(e)&&t((s=this.receivedResp)===null||s===void 0?void 0:s.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);const e=t=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=t,this._matchReceive(t)};this.channel._on(this.refEvent,{},e),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(s=>s.status===e).forEach(s=>s.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}var Ne;(function(i){i.SYNC="sync",i.JOIN="join",i.LEAVE="leave"})(Ne||(Ne={}));class te{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const s=(t==null?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(s.state,{},r=>{const{onJoin:n,onLeave:o,onSync:a}=this.caller;this.joinRef=this.channel._joinRef(),this.state=te.syncState(this.state,r,n,o),this.pendingDiffs.forEach(l=>{this.state=te.syncDiff(this.state,l,n,o)}),this.pendingDiffs=[],a()}),this.channel._on(s.diff,{},r=>{const{onJoin:n,onLeave:o,onSync:a}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(r):(this.state=te.syncDiff(this.state,r,n,o),a())}),this.onJoin((r,n,o)=>{this.channel._trigger("presence",{event:"join",key:r,currentPresences:n,newPresences:o})}),this.onLeave((r,n,o)=>{this.channel._trigger("presence",{event:"leave",key:r,currentPresences:n,leftPresences:o})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,s,r){const n=this.cloneDeep(e),o=this.transformState(t),a={},l={};return this.map(n,(h,c)=>{o[h]||(l[h]=c)}),this.map(o,(h,c)=>{const d=n[h];if(d){const f=c.map(y=>y.presence_ref),g=d.map(y=>y.presence_ref),v=c.filter(y=>g.indexOf(y.presence_ref)<0),w=d.filter(y=>f.indexOf(y.presence_ref)<0);v.length>0&&(a[h]=v),w.length>0&&(l[h]=w)}else a[h]=c}),this.syncDiff(n,{joins:a,leaves:l},s,r)}static syncDiff(e,t,s,r){const{joins:n,leaves:o}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return s||(s=()=>{}),r||(r=()=>{}),this.map(n,(a,l)=>{var h;const c=(h=e[a])!==null&&h!==void 0?h:[];if(e[a]=this.cloneDeep(l),c.length>0){const d=e[a].map(g=>g.presence_ref),f=c.filter(g=>d.indexOf(g.presence_ref)<0);e[a].unshift(...f)}s(a,c,l)}),this.map(o,(a,l)=>{let h=e[a];if(!h)return;const c=l.map(d=>d.presence_ref);h=h.filter(d=>c.indexOf(d.presence_ref)<0),e[a]=h,r(a,h,l),h.length===0&&delete e[a]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(s=>t(s,e[s]))}static transformState(e){return e=this.cloneDeep(e),Object.getOwnPropertyNames(e).reduce((t,s)=>{const r=e[s];return"metas"in r?t[s]=r.metas.map(n=>(n.presence_ref=n.phx_ref,delete n.phx_ref,delete n.phx_ref_prev,n)):t[s]=r,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}var qe;(function(i){i.ALL="*",i.INSERT="INSERT",i.UPDATE="UPDATE",i.DELETE="DELETE"})(qe||(qe={}));var Fe;(function(i){i.BROADCAST="broadcast",i.PRESENCE="presence",i.POSTGRES_CHANGES="postgres_changes",i.SYSTEM="system"})(Fe||(Fe={}));var C;(function(i){i.SUBSCRIBED="SUBSCRIBED",i.TIMED_OUT="TIMED_OUT",i.CLOSED="CLOSED",i.CHANNEL_ERROR="CHANNEL_ERROR"})(C||(C={}));class Le{constructor(e,t={config:{}},s){this.topic=e,this.params=t,this.socket=s,this.bindings={},this.state=E.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new we(this,j.join,this.params,this.timeout),this.rejoinTimer=new ht(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=E.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(r=>r.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=E.closed,this.socket._remove(this)}),this._onError(r=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,r),this.state=E.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=E.errored,this.rejoinTimer.scheduleTimeout())}),this._on(j.reply,{},(r,n)=>{this._trigger(this._replyEventName(n),r)}),this.presence=new te(this),this.broadcastEndpointURL=dt(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var s,r;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{const{config:{broadcast:n,presence:o,private:a}}=this.params;this._onError(c=>e==null?void 0:e(C.CHANNEL_ERROR,c)),this._onClose(()=>e==null?void 0:e(C.CLOSED));const l={},h={broadcast:n,presence:o,postgres_changes:(r=(s=this.bindings.postgres_changes)===null||s===void 0?void 0:s.map(c=>c.filter))!==null&&r!==void 0?r:[],private:a};this.socket.accessTokenValue&&(l.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:h},l)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",d=>u(this,[d],function*({postgres_changes:c}){var f;if(this.socket.setAuth(),c===void 0){e==null||e(C.SUBSCRIBED);return}else{const g=this.bindings.postgres_changes,v=(f=g==null?void 0:g.length)!==null&&f!==void 0?f:0,w=[];for(let y=0;y<v;y++){const k=g[y],{filter:{event:R,schema:P,table:$,filter:M}}=k,J=c&&c[y];if(J&&J.event===R&&J.schema===P&&J.table===$&&J.filter===M)w.push(Object.assign(Object.assign({},k),{id:J.id}));else{this.unsubscribe(),e==null||e(C.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=w,e&&e(C.SUBSCRIBED);return}})).receive("error",c=>{e==null||e(C.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(c).join(", ")||"error")))}).receive("timeout",()=>{e==null||e(C.TIMED_OUT)})}return this}presenceState(){return this.presence.state}track(s){return u(this,arguments,function*(e,t={}){return yield this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)})}untrack(){return u(this,arguments,function*(e={}){return yield this.send({type:"presence",event:"untrack"},e)})}on(e,t,s){return this._on(e,t,s)}send(s){return u(this,arguments,function*(e,t={}){var r,n;if(!this._canPush()&&e.type==="broadcast"){const{event:o,payload:a}=e,h={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:o,payload:a,private:this.private}]})};try{const c=yield this._fetchWithTimeout(this.broadcastEndpointURL,h,(r=t.timeout)!==null&&r!==void 0?r:this.timeout);return yield(n=c.body)===null||n===void 0?void 0:n.cancel(),c.ok?"ok":"error"}catch(c){return c.name==="AbortError"?"timed out":"error"}}else return new Promise(o=>{var a,l,h;const c=this._push(e.type,e,t.timeout||this.timeout);e.type==="broadcast"&&!(!((h=(l=(a=this.params)===null||a===void 0?void 0:a.config)===null||l===void 0?void 0:l.broadcast)===null||h===void 0)&&h.ack)&&o("ok"),c.receive("ok",()=>o("ok")),c.receive("error",()=>o("error")),c.receive("timeout",()=>o("timed out"))})})}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=E.leaving;const t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(j.close,"leave",this._joinRef())};return this.rejoinTimer.reset(),this.joinPush.destroy(),new Promise(s=>{const r=new we(this,j.leave,{},e);r.receive("ok",()=>{t(),s("ok")}).receive("timeout",()=>{t(),s("timed out")}).receive("error",()=>{s("error")}),r.send(),this._canPush()||r.trigger("ok",{})})}_fetchWithTimeout(e,t,s){return u(this,null,function*(){const r=new AbortController,n=setTimeout(()=>r.abort(),s),o=yield this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:r.signal}));return clearTimeout(n),o})}_push(e,t,s=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let r=new we(this,e,t,s);return this._canPush()?r.send():(r.startTimeout(),this.pushBuffer.push(r)),r}_onMessage(e,t,s){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,s){var r,n;const o=e.toLocaleLowerCase(),{close:a,error:l,leave:h,join:c}=j;if(s&&[a,l,h,c].indexOf(o)>=0&&s!==this._joinRef())return;let f=this._onMessage(o,t,s);if(t&&!f)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(o)?(r=this.bindings.postgres_changes)===null||r===void 0||r.filter(g=>{var v,w,y;return((v=g.filter)===null||v===void 0?void 0:v.event)==="*"||((y=(w=g.filter)===null||w===void 0?void 0:w.event)===null||y===void 0?void 0:y.toLocaleLowerCase())===o}).map(g=>g.callback(f,s)):(n=this.bindings[o])===null||n===void 0||n.filter(g=>{var v,w,y,k,R,P;if(["broadcast","presence","postgres_changes"].includes(o))if("id"in g){const $=g.id,M=(v=g.filter)===null||v===void 0?void 0:v.event;return $&&((w=t.ids)===null||w===void 0?void 0:w.includes($))&&(M==="*"||(M==null?void 0:M.toLocaleLowerCase())===((y=t.data)===null||y===void 0?void 0:y.type.toLocaleLowerCase()))}else{const $=(R=(k=g==null?void 0:g.filter)===null||k===void 0?void 0:k.event)===null||R===void 0?void 0:R.toLocaleLowerCase();return $==="*"||$===((P=t==null?void 0:t.event)===null||P===void 0?void 0:P.toLocaleLowerCase())}else return g.type.toLocaleLowerCase()===o}).map(g=>{if(typeof f=="object"&&"ids"in f){const v=f.data,{schema:w,table:y,commit_timestamp:k,type:R,errors:P}=v;f=Object.assign(Object.assign({},{schema:w,table:y,commit_timestamp:k,eventType:R,new:{},old:{},errors:P}),this._getPayloadRecords(v))}g.callback(f,s)})}_isClosed(){return this.state===E.closed}_isJoined(){return this.state===E.joined}_isJoining(){return this.state===E.joining}_isLeaving(){return this.state===E.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,s){const r=e.toLocaleLowerCase(),n={type:r,filter:t,callback:s};return this.bindings[r]?this.bindings[r].push(n):this.bindings[r]=[n],this}_off(e,t){const s=e.toLocaleLowerCase();return this.bindings[s]=this.bindings[s].filter(r=>{var n;return!(((n=r.type)===null||n===void 0?void 0:n.toLocaleLowerCase())===s&&Le.isEqual(r.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(e[s]!==t[s])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(j.close,{},e)}_onError(e){this._on(j.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=E.joining,this.joinPush.resend(e))}_getPayloadRecords(e){const t={new:{},old:{}};return(e.type==="INSERT"||e.type==="UPDATE")&&(t.new=Be(e.columns,e.record)),(e.type==="UPDATE"||e.type==="DELETE")&&(t.old=Be(e.columns,e.old_record)),t}}const hs=()=>{},us=typeof WebSocket!="undefined",ds=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class fs{constructor(e,t){var s;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=es,this.params={},this.timeout=ct,this.heartbeatIntervalMs=3e4,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.ref=0,this.logger=hs,this.conn=null,this.sendBuffer=[],this.serializer=new rs,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=n=>{let o;return n?o=n:typeof fetch=="undefined"?o=(...a)=>W(()=>u(this,null,function*(){const{default:l}=yield Promise.resolve().then(()=>X);return{default:l}}),void 0).then(({default:l})=>l(...a)):o=fetch,(...a)=>o(...a)},this.endPoint=`${e}/${Oe.websocket}`,this.httpEndpoint=dt(e),t!=null&&t.transport?this.transport=t.transport:this.transport=null,t!=null&&t.params&&(this.params=t.params),t!=null&&t.headers&&(this.headers=Object.assign(Object.assign({},this.headers),t.headers)),t!=null&&t.timeout&&(this.timeout=t.timeout),t!=null&&t.logger&&(this.logger=t.logger),t!=null&&t.heartbeatIntervalMs&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);const r=(s=t==null?void 0:t.params)===null||s===void 0?void 0:s.apikey;if(r&&(this.accessTokenValue=r,this.apiKey=r),this.reconnectAfterMs=t!=null&&t.reconnectAfterMs?t.reconnectAfterMs:n=>[1e3,2e3,5e3,1e4][n-1]||1e4,this.encode=t!=null&&t.encode?t.encode:(n,o)=>o(JSON.stringify(n)),this.decode=t!=null&&t.decode?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new ht(()=>u(this,null,function*(){this.disconnect(),this.connect()}),this.reconnectAfterMs),this.fetch=this._resolveFetch(t==null?void 0:t.fetch),t!=null&&t.worker){if(typeof window!="undefined"&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(t==null?void 0:t.worker)||!1,this.workerUrl=t==null?void 0:t.workerUrl}this.accessToken=(t==null?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport){this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers});return}if(us){this.conn=new WebSocket(this.endpointURL()),this.setupConnection();return}this.conn=new gs(this.endpointURL(),void 0,{close:()=>{this.conn=null}}),W(()=>u(this,null,function*(){const{default:e}=yield import("./vendor.DQpuTRuB.js").then(t=>t.b);return{default:e}}),__vite__mapDeps([0,1])).then(({default:e})=>{this.conn=new e(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection()})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:ts}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,t!=null?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset())}getChannels(){return this.channels}removeChannel(e){return u(this,null,function*(){const t=yield e.unsubscribe();return this.channels.length===0&&this.disconnect(),t})}removeAllChannels(){return u(this,null,function*(){const e=yield Promise.all(this.channels.map(t=>t.unsubscribe()));return this.disconnect(),e})}log(e,t,s){this.logger(e,t,s)}connectionState(){switch(this.conn&&this.conn.readyState){case V.connecting:return q.Connecting;case V.open:return q.Open;case V.closing:return q.Closing;default:return q.Closed}}isConnected(){return this.connectionState()===q.Open}channel(e,t={config:{}}){const s=new Le(`realtime:${e}`,t,this);return this.channels.push(s),s}push(e){const{topic:t,event:s,payload:r,ref:n}=e,o=()=>{this.encode(e,a=>{var l;(l=this.conn)===null||l===void 0||l.send(a)})};this.log("push",`${t} ${s} (${n})`,r),this.isConnected()?o():this.sendBuffer.push(o)}setAuth(e=null){return u(this,null,function*(){let t=e||this.accessToken&&(yield this.accessToken())||this.accessTokenValue;if(t){let s=null;try{s=JSON.parse(atob(t.split(".")[1]))}catch(r){}if(s&&s.exp&&!(Math.floor(Date.now()/1e3)-s.exp<0))return this.log("auth",`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${s.exp}`),Promise.reject(`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${s.exp}`);this.accessTokenValue=t,this.channels.forEach(r=>{t&&r.updateJoinPayload({access_token:t}),r.joinedOnce&&r._isJoined()&&r._push(j.access_token,{access_token:t})})}})}sendHeartbeat(){return u(this,null,function*(){var e;if(this.isConnected()){if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),(e=this.conn)===null||e===void 0||e.close(ss,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.setAuth()}})}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(s=>s.topic===e&&(s._isJoined()||s._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t._joinRef()!==e._joinRef())}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,t=>{let{topic:s,event:r,payload:n,ref:o}=t;o&&o===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${n.status||""} ${s} ${r} ${o&&"("+o+")"||""}`,n),this.channels.filter(a=>a._isMember(s)).forEach(a=>a._trigger(r,n,o)),this.stateChangeCallbacks.message.forEach(a=>a(t))})}_onConnOpen(){return u(this,null,function*(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),!this.worker)this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs);else{this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=t=>{this.log("worker","worker error",t.message),this.workerRef.terminate()},this.workerRef.onmessage=t=>{t.data.event==="keepAlive"&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}this.stateChangeCallbacks.open.forEach(e=>e())})}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",e.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(j.error))}_appendParams(e,t){if(Object.keys(t).length===0)return e;const s=e.match(/\?/)?"&":"?",r=new URLSearchParams(t);return`${e}${s}${r}`}_workerObjectUrl(e){let t;if(e)t=e;else{const s=new Blob([ds],{type:"application/javascript"});t=URL.createObjectURL(s)}return t}}class gs{constructor(e,t,s){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=V.connecting,this.send=()=>{},this.url=null,this.url=e,this.close=s.close}}class Ue extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function b(i){return typeof i=="object"&&i!==null&&"__isStorageError"in i}class ps extends Ue{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class je extends Ue{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}var _s=function(i,e,t,s){function r(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(c){try{h(s.next(c))}catch(d){o(d)}}function l(c){try{h(s.throw(c))}catch(d){o(d)}}function h(c){c.done?n(c.value):r(c.value).then(a,l)}h((s=s.apply(i,e||[])).next())})};const ft=i=>{let e;return i?e=i:typeof fetch=="undefined"?e=(...t)=>W(()=>u(void 0,null,function*(){const{default:s}=yield Promise.resolve().then(()=>X);return{default:s}}),void 0).then(({default:s})=>s(...t)):e=fetch,(...t)=>e(...t)},vs=()=>_s(void 0,void 0,void 0,function*(){return typeof Response=="undefined"?(yield W(()=>Promise.resolve().then(()=>X),void 0)).Response:Response}),Pe=i=>{if(Array.isArray(i))return i.map(t=>Pe(t));if(typeof i=="function"||i!==Object(i))return i;const e={};return Object.entries(i).forEach(([t,s])=>{const r=t.replace(/([-_][a-z])/gi,n=>n.toUpperCase().replace(/[-_]/g,""));e[r]=Pe(s)}),e};var F=function(i,e,t,s){function r(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(c){try{h(s.next(c))}catch(d){o(d)}}function l(c){try{h(s.throw(c))}catch(d){o(d)}}function h(c){c.done?n(c.value):r(c.value).then(a,l)}h((s=s.apply(i,e||[])).next())})};const me=i=>i.msg||i.message||i.error_description||i.error||JSON.stringify(i),ys=(i,e,t)=>F(void 0,void 0,void 0,function*(){const s=yield vs();i instanceof s&&!(t!=null&&t.noResolveJson)?i.json().then(r=>{e(new ps(me(r),i.status||500))}).catch(r=>{e(new je(me(r),r))}):e(new je(me(i),i))}),ws=(i,e,t,s)=>{const r={method:i,headers:(e==null?void 0:e.headers)||{}};return i==="GET"?r:(r.headers=Object.assign({"Content-Type":"application/json"},e==null?void 0:e.headers),s&&(r.body=JSON.stringify(s)),Object.assign(Object.assign({},r),t))};function oe(i,e,t,s,r,n){return F(this,void 0,void 0,function*(){return new Promise((o,a)=>{i(t,ws(e,s,r,n)).then(l=>{if(!l.ok)throw l;return s!=null&&s.noResolveJson?l:l.json()}).then(l=>o(l)).catch(l=>ys(l,a,s))})})}function ue(i,e,t,s){return F(this,void 0,void 0,function*(){return oe(i,"GET",e,t,s)})}function L(i,e,t,s,r){return F(this,void 0,void 0,function*(){return oe(i,"POST",e,s,r,t)})}function ms(i,e,t,s,r){return F(this,void 0,void 0,function*(){return oe(i,"PUT",e,s,r,t)})}function bs(i,e,t,s){return F(this,void 0,void 0,function*(){return oe(i,"HEAD",e,Object.assign(Object.assign({},t),{noResolveJson:!0}),s)})}function gt(i,e,t,s,r){return F(this,void 0,void 0,function*(){return oe(i,"DELETE",e,s,r,t)})}var S=function(i,e,t,s){function r(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(c){try{h(s.next(c))}catch(d){o(d)}}function l(c){try{h(s.throw(c))}catch(d){o(d)}}function h(c){c.done?n(c.value):r(c.value).then(a,l)}h((s=s.apply(i,e||[])).next())})};const ks={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},Me={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class Ss{constructor(e,t={},s,r){this.url=e,this.headers=t,this.bucketId=s,this.fetch=ft(r)}uploadOrUpdate(e,t,s,r){return S(this,void 0,void 0,function*(){try{let n;const o=Object.assign(Object.assign({},Me),r);let a=Object.assign(Object.assign({},this.headers),e==="POST"&&{"x-upsert":String(o.upsert)});const l=o.metadata;typeof Blob!="undefined"&&s instanceof Blob?(n=new FormData,n.append("cacheControl",o.cacheControl),l&&n.append("metadata",this.encodeMetadata(l)),n.append("",s)):typeof FormData!="undefined"&&s instanceof FormData?(n=s,n.append("cacheControl",o.cacheControl),l&&n.append("metadata",this.encodeMetadata(l))):(n=s,a["cache-control"]=`max-age=${o.cacheControl}`,a["content-type"]=o.contentType,l&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(l)))),r!=null&&r.headers&&(a=Object.assign(Object.assign({},a),r.headers));const h=this._removeEmptyFolders(t),c=this._getFinalPath(h),d=yield this.fetch(`${this.url}/object/${c}`,Object.assign({method:e,body:n,headers:a},o!=null&&o.duplex?{duplex:o.duplex}:{})),f=yield d.json();return d.ok?{data:{path:h,id:f.Id,fullPath:f.Key},error:null}:{data:null,error:f}}catch(n){if(b(n))return{data:null,error:n};throw n}})}upload(e,t,s){return S(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,s)})}uploadToSignedUrl(e,t,s,r){return S(this,void 0,void 0,function*(){const n=this._removeEmptyFolders(e),o=this._getFinalPath(n),a=new URL(this.url+`/object/upload/sign/${o}`);a.searchParams.set("token",t);try{let l;const h=Object.assign({upsert:Me.upsert},r),c=Object.assign(Object.assign({},this.headers),{"x-upsert":String(h.upsert)});typeof Blob!="undefined"&&s instanceof Blob?(l=new FormData,l.append("cacheControl",h.cacheControl),l.append("",s)):typeof FormData!="undefined"&&s instanceof FormData?(l=s,l.append("cacheControl",h.cacheControl)):(l=s,c["cache-control"]=`max-age=${h.cacheControl}`,c["content-type"]=h.contentType);const d=yield this.fetch(a.toString(),{method:"PUT",body:l,headers:c}),f=yield d.json();return d.ok?{data:{path:n,fullPath:f.Key},error:null}:{data:null,error:f}}catch(l){if(b(l))return{data:null,error:l};throw l}})}createSignedUploadUrl(e,t){return S(this,void 0,void 0,function*(){try{let s=this._getFinalPath(e);const r=Object.assign({},this.headers);t!=null&&t.upsert&&(r["x-upsert"]="true");const n=yield L(this.fetch,`${this.url}/object/upload/sign/${s}`,{},{headers:r}),o=new URL(this.url+n.url),a=o.searchParams.get("token");if(!a)throw new Ue("No token returned by API");return{data:{signedUrl:o.toString(),path:e,token:a},error:null}}catch(s){if(b(s))return{data:null,error:s};throw s}})}update(e,t,s){return S(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,s)})}move(e,t,s){return S(this,void 0,void 0,function*(){try{return{data:yield L(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:s==null?void 0:s.destinationBucket},{headers:this.headers}),error:null}}catch(r){if(b(r))return{data:null,error:r};throw r}})}copy(e,t,s){return S(this,void 0,void 0,function*(){try{return{data:{path:(yield L(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:s==null?void 0:s.destinationBucket},{headers:this.headers})).Key},error:null}}catch(r){if(b(r))return{data:null,error:r};throw r}})}createSignedUrl(e,t,s){return S(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e),n=yield L(this.fetch,`${this.url}/object/sign/${r}`,Object.assign({expiresIn:t},s!=null&&s.transform?{transform:s.transform}:{}),{headers:this.headers});const o=s!=null&&s.download?`&download=${s.download===!0?"":s.download}`:"";return n={signedUrl:encodeURI(`${this.url}${n.signedURL}${o}`)},{data:n,error:null}}catch(r){if(b(r))return{data:null,error:r};throw r}})}createSignedUrls(e,t,s){return S(this,void 0,void 0,function*(){try{const r=yield L(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),n=s!=null&&s.download?`&download=${s.download===!0?"":s.download}`:"";return{data:r.map(o=>Object.assign(Object.assign({},o),{signedUrl:o.signedURL?encodeURI(`${this.url}${o.signedURL}${n}`):null})),error:null}}catch(r){if(b(r))return{data:null,error:r};throw r}})}download(e,t){return S(this,void 0,void 0,function*(){const r=typeof(t==null?void 0:t.transform)!="undefined"?"render/image/authenticated":"object",n=this.transformOptsToQueryString((t==null?void 0:t.transform)||{}),o=n?`?${n}`:"";try{const a=this._getFinalPath(e);return{data:yield(yield ue(this.fetch,`${this.url}/${r}/${a}${o}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(a){if(b(a))return{data:null,error:a};throw a}})}info(e){return S(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{const s=yield ue(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:Pe(s),error:null}}catch(s){if(b(s))return{data:null,error:s};throw s}})}exists(e){return S(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{return yield bs(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(s){if(b(s)&&s instanceof je){const r=s.originalError;if([400,404].includes(r==null?void 0:r.status))return{data:!1,error:s}}throw s}})}getPublicUrl(e,t){const s=this._getFinalPath(e),r=[],n=t!=null&&t.download?`download=${t.download===!0?"":t.download}`:"";n!==""&&r.push(n);const a=typeof(t==null?void 0:t.transform)!="undefined"?"render/image":"object",l=this.transformOptsToQueryString((t==null?void 0:t.transform)||{});l!==""&&r.push(l);let h=r.join("&");return h!==""&&(h=`?${h}`),{data:{publicUrl:encodeURI(`${this.url}/${a}/public/${s}${h}`)}}}remove(e){return S(this,void 0,void 0,function*(){try{return{data:yield gt(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(t){if(b(t))return{data:null,error:t};throw t}})}list(e,t,s){return S(this,void 0,void 0,function*(){try{const r=Object.assign(Object.assign(Object.assign({},ks),t),{prefix:e||""});return{data:yield L(this.fetch,`${this.url}/object/list/${this.bucketId}`,r,{headers:this.headers},s),error:null}}catch(r){if(b(r))return{data:null,error:r};throw r}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return typeof Buffer!="undefined"?Buffer.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){const t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}const Ts="2.7.1",Es={"X-Client-Info":`storage-js/${Ts}`};var z=function(i,e,t,s){function r(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(c){try{h(s.next(c))}catch(d){o(d)}}function l(c){try{h(s.throw(c))}catch(d){o(d)}}function h(c){c.done?n(c.value):r(c.value).then(a,l)}h((s=s.apply(i,e||[])).next())})};class Os{constructor(e,t={},s){this.url=e,this.headers=Object.assign(Object.assign({},Es),t),this.fetch=ft(s)}listBuckets(){return z(this,void 0,void 0,function*(){try{return{data:yield ue(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(b(e))return{data:null,error:e};throw e}})}getBucket(e){return z(this,void 0,void 0,function*(){try{return{data:yield ue(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(t){if(b(t))return{data:null,error:t};throw t}})}createBucket(e,t={public:!1}){return z(this,void 0,void 0,function*(){try{return{data:yield L(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(s){if(b(s))return{data:null,error:s};throw s}})}updateBucket(e,t){return z(this,void 0,void 0,function*(){try{return{data:yield ms(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(s){if(b(s))return{data:null,error:s};throw s}})}emptyBucket(e){return z(this,void 0,void 0,function*(){try{return{data:yield L(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(t){if(b(t))return{data:null,error:t};throw t}})}deleteBucket(e){return z(this,void 0,void 0,function*(){try{return{data:yield gt(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(t){if(b(t))return{data:null,error:t};throw t}})}}class $s extends Os{constructor(e,t={},s){super(e,t,s)}from(e){return new Ss(this.url,this.headers,e,this.fetch)}}const js="2.49.4";let ee="";typeof Deno!="undefined"?ee="deno":typeof document!="undefined"?ee="web":typeof navigator!="undefined"&&navigator.product==="ReactNative"?ee="react-native":ee="node";const Ps={"X-Client-Info":`supabase-js-${ee}/${js}`},As={headers:Ps},Rs={schema:"public"},Cs={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},xs={};var Is=function(i,e,t,s){function r(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(c){try{h(s.next(c))}catch(d){o(d)}}function l(c){try{h(s.throw(c))}catch(d){o(d)}}function h(c){c.done?n(c.value):r(c.value).then(a,l)}h((s=s.apply(i,e||[])).next())})};const Ls=i=>{let e;return i?e=i:typeof fetch=="undefined"?e=Ye:e=fetch,(...t)=>e(...t)},Us=()=>typeof Headers=="undefined"?Ze:Headers,Ds=(i,e,t)=>{const s=Ls(t),r=Us();return(n,o)=>Is(void 0,void 0,void 0,function*(){var a;const l=(a=yield e())!==null&&a!==void 0?a:i;let h=new r(o==null?void 0:o.headers);return h.has("apikey")||h.set("apikey",i),h.has("Authorization")||h.set("Authorization",`Bearer ${l}`),s(n,Object.assign(Object.assign({},o),{headers:h}))})};var Bs=function(i,e,t,s){function r(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(c){try{h(s.next(c))}catch(d){o(d)}}function l(c){try{h(s.throw(c))}catch(d){o(d)}}function h(c){c.done?n(c.value):r(c.value).then(a,l)}h((s=s.apply(i,e||[])).next())})};function Ns(i){return i.replace(/\/$/,"")}function qs(i,e){const{db:t,auth:s,realtime:r,global:n}=i,{db:o,auth:a,realtime:l,global:h}=e,c={db:Object.assign(Object.assign({},o),t),auth:Object.assign(Object.assign({},a),s),realtime:Object.assign(Object.assign({},l),r),global:Object.assign(Object.assign({},h),n),accessToken:()=>Bs(this,void 0,void 0,function*(){return""})};return i.accessToken?c.accessToken=i.accessToken:delete c.accessToken,c}const pt="2.69.1",G=30*1e3,Ae=3,be=Ae*G,Fs="http://localhost:9999",Ms="supabase.auth.token",Js={"X-Client-Info":`gotrue-js/${pt}`},Re="X-Supabase-Api-Version",_t={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},zs=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i,Hs=6e5;class De extends Error{constructor(e,t,s){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=s}}function p(i){return typeof i=="object"&&i!==null&&"__isAuthError"in i}class Ks extends De{constructor(e,t,s){super(e,t,s),this.name="AuthApiError",this.status=t,this.code=s}}function Gs(i){return p(i)&&i.name==="AuthApiError"}class vt extends De{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class D extends De{constructor(e,t,s,r){super(e,s,r),this.name=t,this.status=s}}class x extends D{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function Vs(i){return p(i)&&i.name==="AuthSessionMissingError"}class ke extends D{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class ae extends D{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class le extends D{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function Ws(i){return p(i)&&i.name==="AuthImplicitGrantRedirectError"}class Je extends D{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Ce extends D{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function Se(i){return p(i)&&i.name==="AuthRetryableFetchError"}class ze extends D{constructor(e,t,s){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=s}}class se extends D{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}const He="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),Ke=` 	
\r=`.split(""),Qs=(()=>{const i=new Array(128);for(let e=0;e<i.length;e+=1)i[e]=-1;for(let e=0;e<Ke.length;e+=1)i[Ke[e].charCodeAt(0)]=-2;for(let e=0;e<He.length;e+=1)i[He[e].charCodeAt(0)]=e;return i})();function yt(i,e,t){const s=Qs[i];if(s>-1)for(e.queue=e.queue<<6|s,e.queuedBits+=6;e.queuedBits>=8;)t(e.queue>>e.queuedBits-8&255),e.queuedBits-=8;else{if(s===-2)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(i)}"`)}}function Ge(i){const e=[],t=o=>{e.push(String.fromCodePoint(o))},s={utf8seq:0,codepoint:0},r={queue:0,queuedBits:0},n=o=>{Zs(o,s,t)};for(let o=0;o<i.length;o+=1)yt(i.charCodeAt(o),r,n);return e.join("")}function Xs(i,e){if(i<=127){e(i);return}else if(i<=2047){e(192|i>>6),e(128|i&63);return}else if(i<=65535){e(224|i>>12),e(128|i>>6&63),e(128|i&63);return}else if(i<=1114111){e(240|i>>18),e(128|i>>12&63),e(128|i>>6&63),e(128|i&63);return}throw new Error(`Unrecognized Unicode codepoint: ${i.toString(16)}`)}function Ys(i,e){for(let t=0;t<i.length;t+=1){let s=i.charCodeAt(t);if(s>55295&&s<=56319){const r=(s-55296)*1024&65535;s=(i.charCodeAt(t+1)-56320&65535|r)+65536,t+=1}Xs(s,e)}}function Zs(i,e,t){if(e.utf8seq===0){if(i<=127){t(i);return}for(let s=1;s<6;s+=1)if(!(i>>7-s&1)){e.utf8seq=s;break}if(e.utf8seq===2)e.codepoint=i&31;else if(e.utf8seq===3)e.codepoint=i&15;else if(e.utf8seq===4)e.codepoint=i&7;else throw new Error("Invalid UTF-8 sequence");e.utf8seq-=1}else if(e.utf8seq>0){if(i<=127)throw new Error("Invalid UTF-8 sequence");e.codepoint=e.codepoint<<6|i&63,e.utf8seq-=1,e.utf8seq===0&&t(e.codepoint)}}function er(i){const e=[],t={queue:0,queuedBits:0},s=r=>{e.push(r)};for(let r=0;r<i.length;r+=1)yt(i.charCodeAt(r),t,s);return new Uint8Array(e)}function tr(i){const e=[];return Ys(i,t=>e.push(t)),new Uint8Array(e)}function sr(i){return Math.round(Date.now()/1e3)+i}function rr(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(i){const e=Math.random()*16|0;return(i=="x"?e:e&3|8).toString(16)})}const A=()=>typeof window!="undefined"&&typeof document!="undefined",B={tested:!1,writable:!1},re=()=>{if(!A())return!1;try{if(typeof globalThis.localStorage!="object")return!1}catch(e){return!1}if(B.tested)return B.writable;const i=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(i,i),globalThis.localStorage.removeItem(i),B.tested=!0,B.writable=!0}catch(e){B.tested=!0,B.writable=!1}return B.writable};function ir(i){const e={},t=new URL(i);if(t.hash&&t.hash[0]==="#")try{new URLSearchParams(t.hash.substring(1)).forEach((r,n)=>{e[n]=r})}catch(s){}return t.searchParams.forEach((s,r)=>{e[r]=s}),e}const wt=i=>{let e;return i?e=i:typeof fetch=="undefined"?e=(...t)=>W(()=>u(void 0,null,function*(){const{default:s}=yield Promise.resolve().then(()=>X);return{default:s}}),void 0).then(({default:s})=>s(...t)):e=fetch,(...t)=>e(...t)},nr=i=>typeof i=="object"&&i!==null&&"status"in i&&"ok"in i&&"json"in i&&typeof i.json=="function",mt=(i,e,t)=>u(void 0,null,function*(){yield i.setItem(e,JSON.stringify(t))}),ce=(i,e)=>u(void 0,null,function*(){const t=yield i.getItem(e);if(!t)return null;try{return JSON.parse(t)}catch(s){return t}}),he=(i,e)=>u(void 0,null,function*(){yield i.removeItem(e)});class ye{constructor(){this.promise=new ye.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}ye.promiseConstructor=Promise;function Te(i){const e=i.split(".");if(e.length!==3)throw new se("Invalid JWT structure");for(let s=0;s<e.length;s++)if(!zs.test(e[s]))throw new se("JWT not in base64url format");return{header:JSON.parse(Ge(e[0])),payload:JSON.parse(Ge(e[1])),signature:er(e[2]),raw:{header:e[0],payload:e[1]}}}function or(i){return u(this,null,function*(){return yield new Promise(e=>{setTimeout(()=>e(null),i)})})}function ar(i,e){return new Promise((s,r)=>{u(this,null,function*(){for(let n=0;n<1/0;n++)try{const o=yield i(n);if(!e(n,null,o)){s(o);return}}catch(o){if(!e(n,o)){r(o);return}}})})}function lr(i){return("0"+i.toString(16)).substr(-2)}function cr(){const e=new Uint32Array(56);if(typeof crypto=="undefined"){const t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",s=t.length;let r="";for(let n=0;n<56;n++)r+=t.charAt(Math.floor(Math.random()*s));return r}return crypto.getRandomValues(e),Array.from(e,lr).join("")}function hr(i){return u(this,null,function*(){const t=new TextEncoder().encode(i),s=yield crypto.subtle.digest("SHA-256",t),r=new Uint8Array(s);return Array.from(r).map(n=>String.fromCharCode(n)).join("")})}function ur(i){return u(this,null,function*(){if(!(typeof crypto!="undefined"&&typeof crypto.subtle!="undefined"&&typeof TextEncoder!="undefined"))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),i;const t=yield hr(i);return btoa(t).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")})}function H(i,e,t=!1){return u(this,null,function*(){const s=cr();let r=s;t&&(r+="/PASSWORD_RECOVERY"),yield mt(i,`${e}-code-verifier`,r);const n=yield ur(s);return[n,s===n?"plain":"s256"]})}const dr=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function fr(i){const e=i.headers.get(Re);if(!e||!e.match(dr))return null;try{return new Date(`${e}T00:00:00.0Z`)}catch(t){return null}}function gr(i){if(!i)throw new Error("Missing exp claim");const e=Math.floor(Date.now()/1e3);if(i<=e)throw new Error("JWT has expired")}function pr(i){switch(i){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}var _r=function(i,e){var t={};for(var s in i)Object.prototype.hasOwnProperty.call(i,s)&&e.indexOf(s)<0&&(t[s]=i[s]);if(i!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,s=Object.getOwnPropertySymbols(i);r<s.length;r++)e.indexOf(s[r])<0&&Object.prototype.propertyIsEnumerable.call(i,s[r])&&(t[s[r]]=i[s[r]]);return t};const N=i=>i.msg||i.message||i.error_description||i.error||JSON.stringify(i),vr=[502,503,504];function Ve(i){return u(this,null,function*(){var e;if(!nr(i))throw new Ce(N(i),0);if(vr.includes(i.status))throw new Ce(N(i),i.status);let t;try{t=yield i.json()}catch(n){throw new vt(N(n),n)}let s;const r=fr(i);if(r&&r.getTime()>=_t["2024-01-01"].timestamp&&typeof t=="object"&&t&&typeof t.code=="string"?s=t.code:typeof t=="object"&&t&&typeof t.error_code=="string"&&(s=t.error_code),s){if(s==="weak_password")throw new ze(N(t),i.status,((e=t.weak_password)===null||e===void 0?void 0:e.reasons)||[]);if(s==="session_not_found")throw new x}else if(typeof t=="object"&&t&&typeof t.weak_password=="object"&&t.weak_password&&Array.isArray(t.weak_password.reasons)&&t.weak_password.reasons.length&&t.weak_password.reasons.reduce((n,o)=>n&&typeof o=="string",!0))throw new ze(N(t),i.status,t.weak_password.reasons);throw new Ks(N(t),i.status||500,s)})}const yr=(i,e,t,s)=>{const r={method:i,headers:(e==null?void 0:e.headers)||{}};return i==="GET"?r:(r.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},e==null?void 0:e.headers),r.body=JSON.stringify(s),Object.assign(Object.assign({},r),t))};function _(i,e,t,s){return u(this,null,function*(){var r;const n=Object.assign({},s==null?void 0:s.headers);n[Re]||(n[Re]=_t["2024-01-01"].name),s!=null&&s.jwt&&(n.Authorization=`Bearer ${s.jwt}`);const o=(r=s==null?void 0:s.query)!==null&&r!==void 0?r:{};s!=null&&s.redirectTo&&(o.redirect_to=s.redirectTo);const a=Object.keys(o).length?"?"+new URLSearchParams(o).toString():"",l=yield wr(i,e,t+a,{headers:n,noResolveJson:s==null?void 0:s.noResolveJson},{},s==null?void 0:s.body);return s!=null&&s.xform?s==null?void 0:s.xform(l):{data:Object.assign({},l),error:null}})}function wr(i,e,t,s,r,n){return u(this,null,function*(){const o=yr(e,s,r,n);let a;try{a=yield i(t,Object.assign({},o))}catch(l){throw console.error(l),new Ce(N(l),0)}if(a.ok||(yield Ve(a)),s!=null&&s.noResolveJson)return a;try{return yield a.json()}catch(l){yield Ve(l)}})}function I(i){var e;let t=null;Sr(i)&&(t=Object.assign({},i),i.expires_at||(t.expires_at=sr(i.expires_in)));const s=(e=i.user)!==null&&e!==void 0?e:i;return{data:{session:t,user:s},error:null}}function We(i){const e=I(i);return!e.error&&i.weak_password&&typeof i.weak_password=="object"&&Array.isArray(i.weak_password.reasons)&&i.weak_password.reasons.length&&i.weak_password.message&&typeof i.weak_password.message=="string"&&i.weak_password.reasons.reduce((t,s)=>t&&typeof s=="string",!0)&&(e.data.weak_password=i.weak_password),e}function U(i){var e;return{data:{user:(e=i.user)!==null&&e!==void 0?e:i},error:null}}function mr(i){return{data:i,error:null}}function br(i){const{action_link:e,email_otp:t,hashed_token:s,redirect_to:r,verification_type:n}=i,o=_r(i,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),a={action_link:e,email_otp:t,hashed_token:s,redirect_to:r,verification_type:n},l=Object.assign({},o);return{data:{properties:a,user:l},error:null}}function kr(i){return i}function Sr(i){return i.access_token&&i.refresh_token&&i.expires_in}var Tr=function(i,e){var t={};for(var s in i)Object.prototype.hasOwnProperty.call(i,s)&&e.indexOf(s)<0&&(t[s]=i[s]);if(i!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,s=Object.getOwnPropertySymbols(i);r<s.length;r++)e.indexOf(s[r])<0&&Object.prototype.propertyIsEnumerable.call(i,s[r])&&(t[s[r]]=i[s[r]]);return t};class Er{constructor({url:e="",headers:t={},fetch:s}){this.url=e,this.headers=t,this.fetch=wt(s),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}signOut(e,t="global"){return u(this,null,function*(){try{return yield _(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(s){if(p(s))return{data:null,error:s};throw s}})}inviteUserByEmail(s){return u(this,arguments,function*(e,t={}){try{return yield _(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:U})}catch(r){if(p(r))return{data:{user:null},error:r};throw r}})}generateLink(e){return u(this,null,function*(){try{const{options:t}=e,s=Tr(e,["options"]),r=Object.assign(Object.assign({},s),t);return"newEmail"in s&&(r.new_email=s==null?void 0:s.newEmail,delete r.newEmail),yield _(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:r,headers:this.headers,xform:br,redirectTo:t==null?void 0:t.redirectTo})}catch(t){if(p(t))return{data:{properties:null,user:null},error:t};throw t}})}createUser(e){return u(this,null,function*(){try{return yield _(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:U})}catch(t){if(p(t))return{data:{user:null},error:t};throw t}})}listUsers(e){return u(this,null,function*(){var t,s,r,n,o,a,l;try{const h={nextPage:null,lastPage:0,total:0},c=yield _(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(s=(t=e==null?void 0:e.page)===null||t===void 0?void 0:t.toString())!==null&&s!==void 0?s:"",per_page:(n=(r=e==null?void 0:e.perPage)===null||r===void 0?void 0:r.toString())!==null&&n!==void 0?n:""},xform:kr});if(c.error)throw c.error;const d=yield c.json(),f=(o=c.headers.get("x-total-count"))!==null&&o!==void 0?o:0,g=(l=(a=c.headers.get("link"))===null||a===void 0?void 0:a.split(","))!==null&&l!==void 0?l:[];return g.length>0&&(g.forEach(v=>{const w=parseInt(v.split(";")[0].split("=")[1].substring(0,1)),y=JSON.parse(v.split(";")[1].split("=")[1]);h[`${y}Page`]=w}),h.total=parseInt(f)),{data:Object.assign(Object.assign({},d),h),error:null}}catch(h){if(p(h))return{data:{users:[]},error:h};throw h}})}getUserById(e){return u(this,null,function*(){try{return yield _(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:U})}catch(t){if(p(t))return{data:{user:null},error:t};throw t}})}updateUserById(e,t){return u(this,null,function*(){try{return yield _(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:U})}catch(s){if(p(s))return{data:{user:null},error:s};throw s}})}deleteUser(e,t=!1){return u(this,null,function*(){try{return yield _(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:U})}catch(s){if(p(s))return{data:{user:null},error:s};throw s}})}_listFactors(e){return u(this,null,function*(){try{const{data:t,error:s}=yield _(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:r=>({data:{factors:r},error:null})});return{data:t,error:s}}catch(t){if(p(t))return{data:null,error:t};throw t}})}_deleteFactor(e){return u(this,null,function*(){try{return{data:yield _(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(t){if(p(t))return{data:null,error:t};throw t}})}}const Or={getItem:i=>re()?globalThis.localStorage.getItem(i):null,setItem:(i,e)=>{re()&&globalThis.localStorage.setItem(i,e)},removeItem:i=>{re()&&globalThis.localStorage.removeItem(i)}};function Qe(i={}){return{getItem:e=>i[e]||null,setItem:(e,t)=>{i[e]=t},removeItem:e=>{delete i[e]}}}function $r(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(i){typeof self!="undefined"&&(self.globalThis=self)}}const K={debug:!!(globalThis&&re()&&globalThis.localStorage&&globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug")==="true")};class bt extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class jr extends bt{}function Pr(i,e,t){return u(this,null,function*(){K.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",i,e);const s=new globalThis.AbortController;return e>0&&setTimeout(()=>{s.abort(),K.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",i)},e),yield Promise.resolve().then(()=>globalThis.navigator.locks.request(i,e===0?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:s.signal},r=>u(this,null,function*(){if(r){K.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",i,r.name);try{return yield t()}finally{K.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",i,r.name)}}else{if(e===0)throw K.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",i),new jr(`Acquiring an exclusive Navigator LockManager lock "${i}" immediately failed`);if(K.debug)try{const n=yield globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(n,null,"  "))}catch(n){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",n)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),yield t()}})))})}$r();const Ar={url:Fs,storageKey:Ms,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:Js,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};function Xe(i,e,t){return u(this,null,function*(){return yield t()})}class ie{constructor(e){var t,s;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=ie.nextInstanceID,ie.nextInstanceID+=1,this.instanceID>0&&A()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const r=Object.assign(Object.assign({},Ar),e);if(this.logDebugMessages=!!r.debug,typeof r.debug=="function"&&(this.logger=r.debug),this.persistSession=r.persistSession,this.storageKey=r.storageKey,this.autoRefreshToken=r.autoRefreshToken,this.admin=new Er({url:r.url,headers:r.headers,fetch:r.fetch}),this.url=r.url,this.headers=r.headers,this.fetch=wt(r.fetch),this.lock=r.lock||Xe,this.detectSessionInUrl=r.detectSessionInUrl,this.flowType=r.flowType,this.hasCustomAuthorizationHeader=r.hasCustomAuthorizationHeader,r.lock?this.lock=r.lock:A()&&(!((t=globalThis==null?void 0:globalThis.navigator)===null||t===void 0)&&t.locks)?this.lock=Pr:this.lock=Xe,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?r.storage?this.storage=r.storage:re()?this.storage=Or:(this.memoryStorage={},this.storage=Qe(this.memoryStorage)):(this.memoryStorage={},this.storage=Qe(this.memoryStorage)),A()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(n){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",n)}(s=this.broadcastChannel)===null||s===void 0||s.addEventListener("message",n=>u(this,null,function*(){this._debug("received broadcast notification from other tab or client",n),yield this._notifyAllSubscribers(n.data.event,n.data.session,!1)}))}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${pt}) ${new Date().toISOString()}`,...e),this}initialize(){return u(this,null,function*(){return this.initializePromise?yield this.initializePromise:(this.initializePromise=u(this,null,function*(){return yield this._acquireLock(-1,()=>u(this,null,function*(){return yield this._initialize()}))}),yield this.initializePromise)})}_initialize(){return u(this,null,function*(){var e;try{const t=ir(window.location.href);let s="none";if(this._isImplicitGrantCallback(t)?s="implicit":(yield this._isPKCECallback(t))&&(s="pkce"),A()&&this.detectSessionInUrl&&s!=="none"){const{data:r,error:n}=yield this._getSessionFromURL(t,s);if(n){if(this._debug("#_initialize()","error detecting session from URL",n),Ws(n)){const l=(e=n.details)===null||e===void 0?void 0:e.code;if(l==="identity_already_exists"||l==="identity_not_found"||l==="single_identity_not_deletable")return{error:n}}return yield this._removeSession(),{error:n}}const{session:o,redirectType:a}=r;return this._debug("#_initialize()","detected session in URL",o,"redirect type",a),yield this._saveSession(o),setTimeout(()=>u(this,null,function*(){a==="recovery"?yield this._notifyAllSubscribers("PASSWORD_RECOVERY",o):yield this._notifyAllSubscribers("SIGNED_IN",o)}),0),{error:null}}return yield this._recoverAndRefresh(),{error:null}}catch(t){return p(t)?{error:t}:{error:new vt("Unexpected error during initialization",t)}}finally{yield this._handleVisibilityChange(),this._debug("#_initialize()","end")}})}signInAnonymously(e){return u(this,null,function*(){var t,s,r;try{const n=yield _(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:(s=(t=e==null?void 0:e.options)===null||t===void 0?void 0:t.data)!==null&&s!==void 0?s:{},gotrue_meta_security:{captcha_token:(r=e==null?void 0:e.options)===null||r===void 0?void 0:r.captchaToken}},xform:I}),{data:o,error:a}=n;if(a||!o)return{data:{user:null,session:null},error:a};const l=o.session,h=o.user;return o.session&&(yield this._saveSession(o.session),yield this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:h,session:l},error:null}}catch(n){if(p(n))return{data:{user:null,session:null},error:n};throw n}})}signUp(e){return u(this,null,function*(){var t,s,r;try{let n;if("email"in e){const{email:c,password:d,options:f}=e;let g=null,v=null;this.flowType==="pkce"&&([g,v]=yield H(this.storage,this.storageKey)),n=yield _(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:f==null?void 0:f.emailRedirectTo,body:{email:c,password:d,data:(t=f==null?void 0:f.data)!==null&&t!==void 0?t:{},gotrue_meta_security:{captcha_token:f==null?void 0:f.captchaToken},code_challenge:g,code_challenge_method:v},xform:I})}else if("phone"in e){const{phone:c,password:d,options:f}=e;n=yield _(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:c,password:d,data:(s=f==null?void 0:f.data)!==null&&s!==void 0?s:{},channel:(r=f==null?void 0:f.channel)!==null&&r!==void 0?r:"sms",gotrue_meta_security:{captcha_token:f==null?void 0:f.captchaToken}},xform:I})}else throw new ae("You must provide either an email or phone number and a password");const{data:o,error:a}=n;if(a||!o)return{data:{user:null,session:null},error:a};const l=o.session,h=o.user;return o.session&&(yield this._saveSession(o.session),yield this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:h,session:l},error:null}}catch(n){if(p(n))return{data:{user:null,session:null},error:n};throw n}})}signInWithPassword(e){return u(this,null,function*(){try{let t;if("email"in e){const{email:n,password:o,options:a}=e;t=yield _(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:n,password:o,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken}},xform:We})}else if("phone"in e){const{phone:n,password:o,options:a}=e;t=yield _(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:n,password:o,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken}},xform:We})}else throw new ae("You must provide either an email or phone number and a password");const{data:s,error:r}=t;return r?{data:{user:null,session:null},error:r}:!s||!s.session||!s.user?{data:{user:null,session:null},error:new ke}:(s.session&&(yield this._saveSession(s.session),yield this._notifyAllSubscribers("SIGNED_IN",s.session)),{data:Object.assign({user:s.user,session:s.session},s.weak_password?{weakPassword:s.weak_password}:null),error:r})}catch(t){if(p(t))return{data:{user:null,session:null},error:t};throw t}})}signInWithOAuth(e){return u(this,null,function*(){var t,s,r,n;return yield this._handleProviderSignIn(e.provider,{redirectTo:(t=e.options)===null||t===void 0?void 0:t.redirectTo,scopes:(s=e.options)===null||s===void 0?void 0:s.scopes,queryParams:(r=e.options)===null||r===void 0?void 0:r.queryParams,skipBrowserRedirect:(n=e.options)===null||n===void 0?void 0:n.skipBrowserRedirect})})}exchangeCodeForSession(e){return u(this,null,function*(){return yield this.initializePromise,this._acquireLock(-1,()=>u(this,null,function*(){return this._exchangeCodeForSession(e)}))})}_exchangeCodeForSession(e){return u(this,null,function*(){const t=yield ce(this.storage,`${this.storageKey}-code-verifier`),[s,r]=(t!=null?t:"").split("/");try{const{data:n,error:o}=yield _(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:s},xform:I});if(yield he(this.storage,`${this.storageKey}-code-verifier`),o)throw o;return!n||!n.session||!n.user?{data:{user:null,session:null,redirectType:null},error:new ke}:(n.session&&(yield this._saveSession(n.session),yield this._notifyAllSubscribers("SIGNED_IN",n.session)),{data:Object.assign(Object.assign({},n),{redirectType:r!=null?r:null}),error:o})}catch(n){if(p(n))return{data:{user:null,session:null,redirectType:null},error:n};throw n}})}signInWithIdToken(e){return u(this,null,function*(){try{const{options:t,provider:s,token:r,access_token:n,nonce:o}=e,a=yield _(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:s,id_token:r,access_token:n,nonce:o,gotrue_meta_security:{captcha_token:t==null?void 0:t.captchaToken}},xform:I}),{data:l,error:h}=a;return h?{data:{user:null,session:null},error:h}:!l||!l.session||!l.user?{data:{user:null,session:null},error:new ke}:(l.session&&(yield this._saveSession(l.session),yield this._notifyAllSubscribers("SIGNED_IN",l.session)),{data:l,error:h})}catch(t){if(p(t))return{data:{user:null,session:null},error:t};throw t}})}signInWithOtp(e){return u(this,null,function*(){var t,s,r,n,o;try{if("email"in e){const{email:a,options:l}=e;let h=null,c=null;this.flowType==="pkce"&&([h,c]=yield H(this.storage,this.storageKey));const{error:d}=yield _(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:a,data:(t=l==null?void 0:l.data)!==null&&t!==void 0?t:{},create_user:(s=l==null?void 0:l.shouldCreateUser)!==null&&s!==void 0?s:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},code_challenge:h,code_challenge_method:c},redirectTo:l==null?void 0:l.emailRedirectTo});return{data:{user:null,session:null},error:d}}if("phone"in e){const{phone:a,options:l}=e,{data:h,error:c}=yield _(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:a,data:(r=l==null?void 0:l.data)!==null&&r!==void 0?r:{},create_user:(n=l==null?void 0:l.shouldCreateUser)!==null&&n!==void 0?n:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},channel:(o=l==null?void 0:l.channel)!==null&&o!==void 0?o:"sms"}});return{data:{user:null,session:null,messageId:h==null?void 0:h.message_id},error:c}}throw new ae("You must provide either an email or phone number.")}catch(a){if(p(a))return{data:{user:null,session:null},error:a};throw a}})}verifyOtp(e){return u(this,null,function*(){var t,s;try{let r,n;"options"in e&&(r=(t=e.options)===null||t===void 0?void 0:t.redirectTo,n=(s=e.options)===null||s===void 0?void 0:s.captchaToken);const{data:o,error:a}=yield _(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:n}}),redirectTo:r,xform:I});if(a)throw a;if(!o)throw new Error("An error occurred on token verification.");const l=o.session,h=o.user;return l!=null&&l.access_token&&(yield this._saveSession(l),yield this._notifyAllSubscribers(e.type=="recovery"?"PASSWORD_RECOVERY":"SIGNED_IN",l)),{data:{user:h,session:l},error:null}}catch(r){if(p(r))return{data:{user:null,session:null},error:r};throw r}})}signInWithSSO(e){return u(this,null,function*(){var t,s,r;try{let n=null,o=null;return this.flowType==="pkce"&&([n,o]=yield H(this.storage,this.storageKey)),yield _(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:(s=(t=e.options)===null||t===void 0?void 0:t.redirectTo)!==null&&s!==void 0?s:void 0}),!((r=e==null?void 0:e.options)===null||r===void 0)&&r.captchaToken?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:n,code_challenge_method:o}),headers:this.headers,xform:mr})}catch(n){if(p(n))return{data:null,error:n};throw n}})}reauthenticate(){return u(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>u(this,null,function*(){return yield this._reauthenticate()}))})}_reauthenticate(){return u(this,null,function*(){try{return yield this._useSession(e=>u(this,null,function*(){const{data:{session:t},error:s}=e;if(s)throw s;if(!t)throw new x;const{error:r}=yield _(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:r}}))}catch(e){if(p(e))return{data:{user:null,session:null},error:e};throw e}})}resend(e){return u(this,null,function*(){try{const t=`${this.url}/resend`;if("email"in e){const{email:s,type:r,options:n}=e,{error:o}=yield _(this.fetch,"POST",t,{headers:this.headers,body:{email:s,type:r,gotrue_meta_security:{captcha_token:n==null?void 0:n.captchaToken}},redirectTo:n==null?void 0:n.emailRedirectTo});return{data:{user:null,session:null},error:o}}else if("phone"in e){const{phone:s,type:r,options:n}=e,{data:o,error:a}=yield _(this.fetch,"POST",t,{headers:this.headers,body:{phone:s,type:r,gotrue_meta_security:{captcha_token:n==null?void 0:n.captchaToken}}});return{data:{user:null,session:null,messageId:o==null?void 0:o.message_id},error:a}}throw new ae("You must provide either an email or phone number and a type")}catch(t){if(p(t))return{data:{user:null,session:null},error:t};throw t}})}getSession(){return u(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>u(this,null,function*(){return this._useSession(t=>u(this,null,function*(){return t}))}))})}_acquireLock(e,t){return u(this,null,function*(){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){const s=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),r=u(this,null,function*(){return yield s,yield t()});return this.pendingInLock.push(u(this,null,function*(){try{yield r}catch(n){}})),r}return yield this.lock(`lock:${this.storageKey}`,e,()=>u(this,null,function*(){this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const s=t();for(this.pendingInLock.push(u(this,null,function*(){try{yield s}catch(r){}})),yield s;this.pendingInLock.length;){const r=[...this.pendingInLock];yield Promise.all(r),this.pendingInLock.splice(0,r.length)}return yield s}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}}))}finally{this._debug("#_acquireLock","end")}})}_useSession(e){return u(this,null,function*(){this._debug("#_useSession","begin");try{const t=yield this.__loadSession();return yield e(t)}finally{this._debug("#_useSession","end")}})}__loadSession(){return u(this,null,function*(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",new Error().stack);try{let e=null;const t=yield ce(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),t!==null&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),yield this._removeSession())),!e)return{data:{session:null},error:null};const s=e.expires_at?e.expires_at*1e3-Date.now()<be:!1;if(this._debug("#__loadSession()",`session has${s?"":" not"} expired`,"expires_at",e.expires_at),!s){if(this.storage.isServer){let o=this.suppressGetSessionWarning;e=new Proxy(e,{get:(l,h,c)=>(!o&&h==="user"&&(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),o=!0,this.suppressGetSessionWarning=!0),Reflect.get(l,h,c))})}return{data:{session:e},error:null}}const{session:r,error:n}=yield this._callRefreshToken(e.refresh_token);return n?{data:{session:null},error:n}:{data:{session:r},error:null}}finally{this._debug("#__loadSession()","end")}})}getUser(e){return u(this,null,function*(){return e?yield this._getUser(e):(yield this.initializePromise,yield this._acquireLock(-1,()=>u(this,null,function*(){return yield this._getUser()})))})}_getUser(e){return u(this,null,function*(){try{return e?yield _(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:U}):yield this._useSession(t=>u(this,null,function*(){var s,r,n;const{data:o,error:a}=t;if(a)throw a;return!(!((s=o.session)===null||s===void 0)&&s.access_token)&&!this.hasCustomAuthorizationHeader?{data:{user:null},error:new x}:yield _(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:(n=(r=o.session)===null||r===void 0?void 0:r.access_token)!==null&&n!==void 0?n:void 0,xform:U})}))}catch(t){if(p(t))return Vs(t)&&(yield this._removeSession(),yield he(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:t};throw t}})}updateUser(s){return u(this,arguments,function*(e,t={}){return yield this.initializePromise,yield this._acquireLock(-1,()=>u(this,null,function*(){return yield this._updateUser(e,t)}))})}_updateUser(s){return u(this,arguments,function*(e,t={}){try{return yield this._useSession(r=>u(this,null,function*(){const{data:n,error:o}=r;if(o)throw o;if(!n.session)throw new x;const a=n.session;let l=null,h=null;this.flowType==="pkce"&&e.email!=null&&([l,h]=yield H(this.storage,this.storageKey));const{data:c,error:d}=yield _(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:t==null?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:l,code_challenge_method:h}),jwt:a.access_token,xform:U});if(d)throw d;return a.user=c.user,yield this._saveSession(a),yield this._notifyAllSubscribers("USER_UPDATED",a),{data:{user:a.user},error:null}}))}catch(r){if(p(r))return{data:{user:null},error:r};throw r}})}setSession(e){return u(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>u(this,null,function*(){return yield this._setSession(e)}))})}_setSession(e){return u(this,null,function*(){try{if(!e.access_token||!e.refresh_token)throw new x;const t=Date.now()/1e3;let s=t,r=!0,n=null;const{payload:o}=Te(e.access_token);if(o.exp&&(s=o.exp,r=s<=t),r){const{session:a,error:l}=yield this._callRefreshToken(e.refresh_token);if(l)return{data:{user:null,session:null},error:l};if(!a)return{data:{user:null,session:null},error:null};n=a}else{const{data:a,error:l}=yield this._getUser(e.access_token);if(l)throw l;n={access_token:e.access_token,refresh_token:e.refresh_token,user:a.user,token_type:"bearer",expires_in:s-t,expires_at:s},yield this._saveSession(n),yield this._notifyAllSubscribers("SIGNED_IN",n)}return{data:{user:n.user,session:n},error:null}}catch(t){if(p(t))return{data:{session:null,user:null},error:t};throw t}})}refreshSession(e){return u(this,null,function*(){return yield this.initializePromise,yield this._acquireLock(-1,()=>u(this,null,function*(){return yield this._refreshSession(e)}))})}_refreshSession(e){return u(this,null,function*(){try{return yield this._useSession(t=>u(this,null,function*(){var s;if(!e){const{data:o,error:a}=t;if(a)throw a;e=(s=o.session)!==null&&s!==void 0?s:void 0}if(!(e!=null&&e.refresh_token))throw new x;const{session:r,error:n}=yield this._callRefreshToken(e.refresh_token);return n?{data:{user:null,session:null},error:n}:r?{data:{user:r.user,session:r},error:null}:{data:{user:null,session:null},error:null}}))}catch(t){if(p(t))return{data:{user:null,session:null},error:t};throw t}})}_getSessionFromURL(e,t){return u(this,null,function*(){try{if(!A())throw new le("No browser detected.");if(e.error||e.error_description||e.error_code)throw new le(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if(this.flowType==="pkce")throw new Je("Not a valid PKCE flow url.");break;case"pkce":if(this.flowType==="implicit")throw new le("Not a valid implicit grant flow url.");break;default:}if(t==="pkce"){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new Je("No code detected.");const{data:R,error:P}=yield this._exchangeCodeForSession(e.code);if(P)throw P;const $=new URL(window.location.href);return $.searchParams.delete("code"),window.history.replaceState(window.history.state,"",$.toString()),{data:{session:R.session,redirectType:null},error:null}}const{provider_token:s,provider_refresh_token:r,access_token:n,refresh_token:o,expires_in:a,expires_at:l,token_type:h}=e;if(!n||!a||!o||!h)throw new le("No session defined in URL");const c=Math.round(Date.now()/1e3),d=parseInt(a);let f=c+d;l&&(f=parseInt(l));const g=f-c;g*1e3<=G&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${g}s, should have been closer to ${d}s`);const v=f-d;c-v>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",v,f,c):c-v<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",v,f,c);const{data:w,error:y}=yield this._getUser(n);if(y)throw y;const k={provider_token:s,provider_refresh_token:r,access_token:n,expires_in:d,expires_at:f,refresh_token:o,token_type:h,user:w.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:k,redirectType:e.type},error:null}}catch(s){if(p(s))return{data:{session:null,redirectType:null},error:s};throw s}})}_isImplicitGrantCallback(e){return!!(e.access_token||e.error_description)}_isPKCECallback(e){return u(this,null,function*(){const t=yield ce(this.storage,`${this.storageKey}-code-verifier`);return!!(e.code&&t)})}signOut(){return u(this,arguments,function*(e={scope:"global"}){return yield this.initializePromise,yield this._acquireLock(-1,()=>u(this,null,function*(){return yield this._signOut(e)}))})}_signOut(){return u(this,arguments,function*({scope:e}={scope:"global"}){return yield this._useSession(t=>u(this,null,function*(){var s;const{data:r,error:n}=t;if(n)return{error:n};const o=(s=r.session)===null||s===void 0?void 0:s.access_token;if(o){const{error:a}=yield this.admin.signOut(o,e);if(a&&!(Gs(a)&&(a.status===404||a.status===401||a.status===403)))return{error:a}}return e!=="others"&&(yield this._removeSession(),yield he(this.storage,`${this.storageKey}-code-verifier`)),{error:null}}))})}onAuthStateChange(e){const t=rr(),s={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,s),u(this,null,function*(){yield this.initializePromise,yield this._acquireLock(-1,()=>u(this,null,function*(){this._emitInitialSession(t)}))}),{data:{subscription:s}}}_emitInitialSession(e){return u(this,null,function*(){return yield this._useSession(t=>u(this,null,function*(){var s,r;try{const{data:{session:n},error:o}=t;if(o)throw o;yield(s=this.stateChangeEmitters.get(e))===null||s===void 0?void 0:s.callback("INITIAL_SESSION",n),this._debug("INITIAL_SESSION","callback id",e,"session",n)}catch(n){yield(r=this.stateChangeEmitters.get(e))===null||r===void 0?void 0:r.callback("INITIAL_SESSION",null),this._debug("INITIAL_SESSION","callback id",e,"error",n),console.error(n)}}))})}resetPasswordForEmail(s){return u(this,arguments,function*(e,t={}){let r=null,n=null;this.flowType==="pkce"&&([r,n]=yield H(this.storage,this.storageKey,!0));try{return yield _(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:r,code_challenge_method:n,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(o){if(p(o))return{data:null,error:o};throw o}})}getUserIdentities(){return u(this,null,function*(){var e;try{const{data:t,error:s}=yield this.getUser();if(s)throw s;return{data:{identities:(e=t.user.identities)!==null&&e!==void 0?e:[]},error:null}}catch(t){if(p(t))return{data:null,error:t};throw t}})}linkIdentity(e){return u(this,null,function*(){var t;try{const{data:s,error:r}=yield this._useSession(n=>u(this,null,function*(){var o,a,l,h,c;const{data:d,error:f}=n;if(f)throw f;const g=yield this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:(o=e.options)===null||o===void 0?void 0:o.redirectTo,scopes:(a=e.options)===null||a===void 0?void 0:a.scopes,queryParams:(l=e.options)===null||l===void 0?void 0:l.queryParams,skipBrowserRedirect:!0});return yield _(this.fetch,"GET",g,{headers:this.headers,jwt:(c=(h=d.session)===null||h===void 0?void 0:h.access_token)!==null&&c!==void 0?c:void 0})}));if(r)throw r;return A()&&!(!((t=e.options)===null||t===void 0)&&t.skipBrowserRedirect)&&window.location.assign(s==null?void 0:s.url),{data:{provider:e.provider,url:s==null?void 0:s.url},error:null}}catch(s){if(p(s))return{data:{provider:e.provider,url:null},error:s};throw s}})}unlinkIdentity(e){return u(this,null,function*(){try{return yield this._useSession(t=>u(this,null,function*(){var s,r;const{data:n,error:o}=t;if(o)throw o;return yield _(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:(r=(s=n.session)===null||s===void 0?void 0:s.access_token)!==null&&r!==void 0?r:void 0})}))}catch(t){if(p(t))return{data:null,error:t};throw t}})}_refreshAccessToken(e){return u(this,null,function*(){const t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{const s=Date.now();return yield ar(r=>u(this,null,function*(){return r>0&&(yield or(200*Math.pow(2,r-1))),this._debug(t,"refreshing attempt",r),yield _(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:I})}),(r,n)=>{const o=200*Math.pow(2,r);return n&&Se(n)&&Date.now()+o-s<G})}catch(s){if(this._debug(t,"error",s),p(s))return{data:{session:null,user:null},error:s};throw s}finally{this._debug(t,"end")}})}_isValidSession(e){return typeof e=="object"&&e!==null&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}_handleProviderSignIn(e,t){return u(this,null,function*(){const s=yield this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",s),A()&&!t.skipBrowserRedirect&&window.location.assign(s),{data:{provider:e,url:s},error:null}})}_recoverAndRefresh(){return u(this,null,function*(){var e;const t="#_recoverAndRefresh()";this._debug(t,"begin");try{const s=yield ce(this.storage,this.storageKey);if(this._debug(t,"session from storage",s),!this._isValidSession(s)){this._debug(t,"session is not valid"),s!==null&&(yield this._removeSession());return}const r=((e=s.expires_at)!==null&&e!==void 0?e:1/0)*1e3-Date.now()<be;if(this._debug(t,`session has${r?"":" not"} expired with margin of ${be}s`),r){if(this.autoRefreshToken&&s.refresh_token){const{error:n}=yield this._callRefreshToken(s.refresh_token);n&&(console.error(n),Se(n)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",n),yield this._removeSession()))}}else yield this._notifyAllSubscribers("SIGNED_IN",s)}catch(s){this._debug(t,"error",s),console.error(s);return}finally{this._debug(t,"end")}})}_callRefreshToken(e){return u(this,null,function*(){var t,s;if(!e)throw new x;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const r=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(r,"begin");try{this.refreshingDeferred=new ye;const{data:n,error:o}=yield this._refreshAccessToken(e);if(o)throw o;if(!n.session)throw new x;yield this._saveSession(n.session),yield this._notifyAllSubscribers("TOKEN_REFRESHED",n.session);const a={session:n.session,error:null};return this.refreshingDeferred.resolve(a),a}catch(n){if(this._debug(r,"error",n),p(n)){const o={session:null,error:n};return Se(n)||(yield this._removeSession()),(t=this.refreshingDeferred)===null||t===void 0||t.resolve(o),o}throw(s=this.refreshingDeferred)===null||s===void 0||s.reject(n),n}finally{this.refreshingDeferred=null,this._debug(r,"end")}})}_notifyAllSubscribers(e,t,s=!0){return u(this,null,function*(){const r=`#_notifyAllSubscribers(${e})`;this._debug(r,"begin",t,`broadcast = ${s}`);try{this.broadcastChannel&&s&&this.broadcastChannel.postMessage({event:e,session:t});const n=[],o=Array.from(this.stateChangeEmitters.values()).map(a=>u(this,null,function*(){try{yield a.callback(e,t)}catch(l){n.push(l)}}));if(yield Promise.all(o),n.length>0){for(let a=0;a<n.length;a+=1)console.error(n[a]);throw n[0]}}finally{this._debug(r,"end")}})}_saveSession(e){return u(this,null,function*(){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,yield mt(this.storage,this.storageKey,e)})}_removeSession(){return u(this,null,function*(){this._debug("#_removeSession()"),yield he(this.storage,this.storageKey),yield this._notifyAllSubscribers("SIGNED_OUT",null)})}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&A()&&(window!=null&&window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(t){console.error("removing visibilitychange callback failed",t)}}_startAutoRefresh(){return u(this,null,function*(){yield this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const e=setInterval(()=>this._autoRefreshTokenTick(),G);this.autoRefreshTicker=e,e&&typeof e=="object"&&typeof e.unref=="function"?e.unref():typeof Deno!="undefined"&&typeof Deno.unrefTimer=="function"&&Deno.unrefTimer(e),setTimeout(()=>u(this,null,function*(){yield this.initializePromise,yield this._autoRefreshTokenTick()}),0)})}_stopAutoRefresh(){return u(this,null,function*(){this._debug("#_stopAutoRefresh()");const e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)})}startAutoRefresh(){return u(this,null,function*(){this._removeVisibilityChangedCallback(),yield this._startAutoRefresh()})}stopAutoRefresh(){return u(this,null,function*(){this._removeVisibilityChangedCallback(),yield this._stopAutoRefresh()})}_autoRefreshTokenTick(){return u(this,null,function*(){this._debug("#_autoRefreshTokenTick()","begin");try{yield this._acquireLock(0,()=>u(this,null,function*(){try{const e=Date.now();try{return yield this._useSession(t=>u(this,null,function*(){const{data:{session:s}}=t;if(!s||!s.refresh_token||!s.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}const r=Math.floor((s.expires_at*1e3-e)/G);this._debug("#_autoRefreshTokenTick()",`access token expires in ${r} ticks, a tick lasts ${G}ms, refresh threshold is ${Ae} ticks`),r<=Ae&&(yield this._callRefreshToken(s.refresh_token))}))}catch(t){console.error("Auto refresh tick failed with error. This is likely a transient error.",t)}}finally{this._debug("#_autoRefreshTokenTick()","end")}}))}catch(e){if(e.isAcquireTimeout||e instanceof bt)this._debug("auto refresh token tick lock not available");else throw e}})}_handleVisibilityChange(){return u(this,null,function*(){if(this._debug("#_handleVisibilityChange()"),!A()||!(window!=null&&window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=()=>u(this,null,function*(){return yield this._onVisibilityChanged(!1)}),window==null||window.addEventListener("visibilitychange",this.visibilityChangedCallback),yield this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}})}_onVisibilityChanged(e){return u(this,null,function*(){const t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),document.visibilityState==="visible"?(this.autoRefreshToken&&this._startAutoRefresh(),e||(yield this.initializePromise,yield this._acquireLock(-1,()=>u(this,null,function*(){if(document.visibilityState!=="visible"){this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}yield this._recoverAndRefresh()})))):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()})}_getUrlForProvider(e,t,s){return u(this,null,function*(){const r=[`provider=${encodeURIComponent(t)}`];if(s!=null&&s.redirectTo&&r.push(`redirect_to=${encodeURIComponent(s.redirectTo)}`),s!=null&&s.scopes&&r.push(`scopes=${encodeURIComponent(s.scopes)}`),this.flowType==="pkce"){const[n,o]=yield H(this.storage,this.storageKey),a=new URLSearchParams({code_challenge:`${encodeURIComponent(n)}`,code_challenge_method:`${encodeURIComponent(o)}`});r.push(a.toString())}if(s!=null&&s.queryParams){const n=new URLSearchParams(s.queryParams);r.push(n.toString())}return s!=null&&s.skipBrowserRedirect&&r.push(`skip_http_redirect=${s.skipBrowserRedirect}`),`${e}?${r.join("&")}`})}_unenroll(e){return u(this,null,function*(){try{return yield this._useSession(t=>u(this,null,function*(){var s;const{data:r,error:n}=t;return n?{data:null,error:n}:yield _(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:(s=r==null?void 0:r.session)===null||s===void 0?void 0:s.access_token})}))}catch(t){if(p(t))return{data:null,error:t};throw t}})}_enroll(e){return u(this,null,function*(){try{return yield this._useSession(t=>u(this,null,function*(){var s,r;const{data:n,error:o}=t;if(o)return{data:null,error:o};const a=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},e.factorType==="phone"?{phone:e.phone}:{issuer:e.issuer}),{data:l,error:h}=yield _(this.fetch,"POST",`${this.url}/factors`,{body:a,headers:this.headers,jwt:(s=n==null?void 0:n.session)===null||s===void 0?void 0:s.access_token});return h?{data:null,error:h}:(e.factorType==="totp"&&(!((r=l==null?void 0:l.totp)===null||r===void 0)&&r.qr_code)&&(l.totp.qr_code=`data:image/svg+xml;utf-8,${l.totp.qr_code}`),{data:l,error:null})}))}catch(t){if(p(t))return{data:null,error:t};throw t}})}_verify(e){return u(this,null,function*(){return this._acquireLock(-1,()=>u(this,null,function*(){try{return yield this._useSession(t=>u(this,null,function*(){var s;const{data:r,error:n}=t;if(n)return{data:null,error:n};const{data:o,error:a}=yield _(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:(s=r==null?void 0:r.session)===null||s===void 0?void 0:s.access_token});return a?{data:null,error:a}:(yield this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+o.expires_in},o)),yield this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",o),{data:o,error:a})}))}catch(t){if(p(t))return{data:null,error:t};throw t}}))})}_challenge(e){return u(this,null,function*(){return this._acquireLock(-1,()=>u(this,null,function*(){try{return yield this._useSession(t=>u(this,null,function*(){var s;const{data:r,error:n}=t;return n?{data:null,error:n}:yield _(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:(s=r==null?void 0:r.session)===null||s===void 0?void 0:s.access_token})}))}catch(t){if(p(t))return{data:null,error:t};throw t}}))})}_challengeAndVerify(e){return u(this,null,function*(){const{data:t,error:s}=yield this._challenge({factorId:e.factorId});return s?{data:null,error:s}:yield this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})})}_listFactors(){return u(this,null,function*(){const{data:{user:e},error:t}=yield this.getUser();if(t)return{data:null,error:t};const s=(e==null?void 0:e.factors)||[],r=s.filter(o=>o.factor_type==="totp"&&o.status==="verified"),n=s.filter(o=>o.factor_type==="phone"&&o.status==="verified");return{data:{all:s,totp:r,phone:n},error:null}})}_getAuthenticatorAssuranceLevel(){return u(this,null,function*(){return this._acquireLock(-1,()=>u(this,null,function*(){return yield this._useSession(e=>u(this,null,function*(){var t,s;const{data:{session:r},error:n}=e;if(n)return{data:null,error:n};if(!r)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:o}=Te(r.access_token);let a=null;o.aal&&(a=o.aal);let l=a;((s=(t=r.user.factors)===null||t===void 0?void 0:t.filter(d=>d.status==="verified"))!==null&&s!==void 0?s:[]).length>0&&(l="aal2");const c=o.amr||[];return{data:{currentLevel:a,nextLevel:l,currentAuthenticationMethods:c},error:null}}))}))})}fetchJwk(s){return u(this,arguments,function*(e,t={keys:[]}){let r=t.keys.find(a=>a.kid===e);if(r||(r=this.jwks.keys.find(a=>a.kid===e),r&&this.jwks_cached_at+Hs>Date.now()))return r;const{data:n,error:o}=yield _(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(o)throw o;if(!n.keys||n.keys.length===0)throw new se("JWKS is empty");if(this.jwks=n,this.jwks_cached_at=Date.now(),r=n.keys.find(a=>a.kid===e),!r)throw new se("No matching signing key found in JWKS");return r})}getClaims(s){return u(this,arguments,function*(e,t={keys:[]}){try{let r=e;if(!r){const{data:v,error:w}=yield this.getSession();if(w||!v.session)return{data:null,error:w};r=v.session.access_token}const{header:n,payload:o,signature:a,raw:{header:l,payload:h}}=Te(r);if(gr(o.exp),!n.kid||n.alg==="HS256"||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){const{error:v}=yield this.getUser(r);if(v)throw v;return{data:{claims:o,header:n,signature:a},error:null}}const c=pr(n.alg),d=yield this.fetchJwk(n.kid,t),f=yield crypto.subtle.importKey("jwk",d,c,!0,["verify"]);if(!(yield crypto.subtle.verify(c,f,a,tr(`${l}.${h}`))))throw new se("Invalid JWT signature");return{data:{claims:o,header:n,signature:a},error:null}}catch(r){if(p(r))return{data:null,error:r};throw r}})}}ie.nextInstanceID=0;const Rr=ie;class Cr extends Rr{constructor(e){super(e)}}var xr=function(i,e,t,s){function r(n){return n instanceof t?n:new t(function(o){o(n)})}return new(t||(t=Promise))(function(n,o){function a(c){try{h(s.next(c))}catch(d){o(d)}}function l(c){try{h(s.throw(c))}catch(d){o(d)}}function h(c){c.done?n(c.value):r(c.value).then(a,l)}h((s=s.apply(i,e||[])).next())})};class Ir{constructor(e,t,s){var r,n,o;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw new Error("supabaseUrl is required.");if(!t)throw new Error("supabaseKey is required.");const a=Ns(e);this.realtimeUrl=`${a}/realtime/v1`.replace(/^http/i,"ws"),this.authUrl=`${a}/auth/v1`,this.storageUrl=`${a}/storage/v1`,this.functionsUrl=`${a}/functions/v1`;const l=`sb-${new URL(this.authUrl).hostname.split(".")[0]}-auth-token`,h={db:Rs,realtime:xs,auth:Object.assign(Object.assign({},Cs),{storageKey:l}),global:As},c=qs(s!=null?s:{},h);this.storageKey=(r=c.auth.storageKey)!==null&&r!==void 0?r:"",this.headers=(n=c.global.headers)!==null&&n!==void 0?n:{},c.accessToken?(this.accessToken=c.accessToken,this.auth=new Proxy({},{get:(d,f)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(f)} is not possible`)}})):this.auth=this._initSupabaseAuthClient((o=c.auth)!==null&&o!==void 0?o:{},this.headers,c.global.fetch),this.fetch=Ds(t,this._getAccessToken.bind(this),c.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},c.realtime)),this.rest=new Yt(`${a}/rest/v1`,{headers:this.headers,schema:c.db.schema,fetch:this.fetch}),c.accessToken||this._listenForAuthEvents()}get functions(){return new jt(this.functionsUrl,{headers:this.headers,customFetch:this.fetch})}get storage(){return new $s(this.storageUrl,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},s={}){return this.rest.rpc(e,t,s)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t;return xr(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:s}=yield this.auth.getSession();return(t=(e=s.session)===null||e===void 0?void 0:e.access_token)!==null&&t!==void 0?t:null})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:s,storage:r,storageKey:n,flowType:o,lock:a,debug:l},h,c){const d={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new Cr({url:this.authUrl,headers:Object.assign(Object.assign({},d),h),storageKey:n,autoRefreshToken:e,persistSession:t,detectSessionInUrl:s,storage:r,flowType:o,lock:a,debug:l,fetch:c,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new fs(this.realtimeUrl,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},e==null?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((t,s)=>{this._handleTokenChanged(t,"CLIENT",s==null?void 0:s.access_token)})}_handleTokenChanged(e,t,s){(e==="TOKEN_REFRESHED"||e==="SIGNED_IN")&&this.changedAccessToken!==s?this.changedAccessToken=s:e==="SIGNED_OUT"&&(this.realtime.setAuth(),t=="STORAGE"&&this.auth.signOut(),this.changedAccessToken=void 0)}}const Gr=(i,e,t)=>new Ir(i,e,t);export{Kr as P,Gr as c};
