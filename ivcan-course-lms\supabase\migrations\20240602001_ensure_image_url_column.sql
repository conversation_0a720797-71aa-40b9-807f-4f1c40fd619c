-- Ensure the image_url column exists in the courses table
DO $$
BEGIN
    -- Check if the column exists
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'courses'
        AND column_name = 'image_url'
    ) THEN
        -- Add the column if it doesn't exist
        ALTER TABLE public.courses ADD COLUMN image_url TEXT;
        
        -- Log that the column was added
        RAISE NOTICE 'Added image_url column to courses table';
    ELSE
        -- Log that the column already exists
        RAISE NOTICE 'image_url column already exists in courses table';
    END IF;
END $$;
