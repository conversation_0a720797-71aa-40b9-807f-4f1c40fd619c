import{u as l,j as e,aG as c}from"./vendor-react.BcAa1DKr.js";import{B as d}from"./index.BLDhDn0D.js";import{C as m}from"./confetti.ShHySCrk.js";import{a5 as x,a4 as t}from"./vendor.DQpuTRuB.js";const f=({courseName:i,courseId:a,onClose:s,isVisible:n=!1})=>{const o=l(),r=()=>{s(),o(`/certificate/${a}`)};return e.jsx(x,{children:n&&e.jsx(t.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:e.jsxs(t.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},transition:{type:"spring",damping:15},className:"relative w-full max-w-xs rounded-xl overflow-hidden shadow-2xl bg-gradient-to-br from-red-500 to-red-700",children:[e.jsx(m,{count:300,duration:6,active:!0}),e.jsxs("div",{className:"text-center p-6 pb-8 relative z-10",children:[e.jsx(t.h2,{initial:{y:-20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.2},className:"text-3xl font-bold mb-6 text-white tracking-wide",children:"Congratulations!"}),e.jsx(t.div,{initial:{y:-10,opacity:0},animate:{y:0,opacity:1},transition:{delay:.4},className:"mb-6",children:e.jsx("div",{className:"mx-auto w-16 h-16 bg-white/10 rounded-full flex items-center justify-center border-2 border-white/20",children:e.jsx(c,{className:"w-8 h-8 text-white"})})}),e.jsxs(t.p,{initial:{y:-10,opacity:0},animate:{y:0,opacity:1},transition:{delay:.6},className:"text-white/90 text-center mb-8",children:["You've successfully completed",e.jsx("br",{}),e.jsx("span",{className:"font-semibold",children:i})]}),e.jsx(t.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:.8},children:e.jsx(d,{onClick:r,className:"bg-white hover:bg-white/90 text-red-600 font-medium rounded-full px-8 py-2 h-auto shadow-lg",children:"View Certificate"})})]})]})})})};export{f as C};
