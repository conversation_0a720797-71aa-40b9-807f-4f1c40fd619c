var c=Object.defineProperty;var a=Object.getOwnPropertySymbols;var d=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable;var s=(r,t,e)=>t in r?c(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,u=(r,t)=>{for(var e in t||(t={}))d.call(t,e)&&s(r,e,t[e]);if(a)for(var e of a(t))i.call(t,e)&&s(r,e,t[e]);return r};var b=(r,t)=>{var e={};for(var o in r)d.call(r,o)&&t.indexOf(o)<0&&(e[o]=r[o]);if(r!=null&&a)for(var o of a(r))t.indexOf(o)<0&&i.call(r,o)&&(e[o]=r[o]);return e};import{j as m}from"./vendor-react.BcAa1DKr.js";import{a3 as f}from"./vendor.DQpuTRuB.js";import{c as p}from"./index.BLDhDn0D.js";const g=f("inline-flex items-center rounded-full border px-2.5 py-1 text-xs font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 shadow-sm",{variants:{variant:{default:"border-transparent bg-primary/90 backdrop-blur-sm text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary/90 backdrop-blur-sm text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive/90 backdrop-blur-sm text-destructive-foreground hover:bg-destructive/80",outline:"border-input/50 bg-background/50 backdrop-blur-sm text-foreground",ios:"border-transparent bg-primary/90 backdrop-blur-sm text-white font-medium"}},defaultVariants:{variant:"default"}});function k(o){var n=o,{className:r,variant:t}=n,e=b(n,["className","variant"]);return m.jsx("div",u({className:p(g({variant:t}),r)},e))}export{k as B};
