/* Mobile-specific styles for course modules */

@media (max-width: 768px) {
  /* Course container */

  /* Course modules container */

  /* Course stats grid - more consistent and professional */

  .dark .course-stats {
    background: rgba(30, 30, 30, 0.7) !important;
    border: 1px solid rgba(255, 255, 255, 0.04) !important;
  }

  .dark .course-stat-item {
    background: rgba(40, 40, 40, 0.85) !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
  }

  /* Course stat item labels - smaller */

  .dark .course-stat-item p:first-of-type {
    color: #d1d5db !important;
  }

  /* Course stat item values - smaller */

  .dark .course-stat-item p:last-of-type {
    color: #9ca3af !important;
  }

  .dark .course-stat-item svg {
    color: #f48b95 !important;
  }

  /* Module styling - more touch-friendly */

  .dark .module-container {
    background: rgba(28, 28, 28, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.08);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  /* Module header - improved touch target */

  .dark .module-header {
    background: rgba(28, 28, 28, 0.8);
  }

  .dark .module-number {
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #e0e0e0 !important;
  }

  /* Module title - improved readability */

  .dark .module-title {
    color: #f0f0f0 !important;
  }

  /* Module Info - better readability */

  .dark .module-info {
    color: #a0a0a0 !important;
  }

  .dark .module-info svg {
    color: #aaa !important;
  }

  /* Module Dropdown Icon */

  .dark .module-dropdown-icon {
    color: #aaa !important;
  }

  /* Module content - better spacing */

  .dark .module-content {
    background: rgba(28, 28, 28, 0.5);
  }

  /* Lesson items - improved touch targets and visual hierarchy */

  .dark .lesson-item {
    background: rgba(255, 255, 255, 0.04) !important;
    border: 1px solid rgba(255, 255, 255, 0.06) !important;
  }

  .dark .lesson-item:hover {
    background: rgba(255, 255, 255, 0.07) !important;
    transform: translateX(2px);
  }

  .dark .lesson-item:active {
    background: rgba(255, 255, 255, 0.08) !important;
  }

  /* Lesson title - improved readability */

  .dark .lesson-title {
    color: #e0e0e0 !important;
  }

  /* Lesson meta - better spacing and readability */

  .dark .lesson-meta {
    color: #999 !important;
  }

  .dark .lesson-meta svg {
    color: #aaa !important;
  }

  /* Icons - smaller */

  /* Buttons */
}

/* Extra small devices */
@media (max-width: 380px) {
  /* Course container */
  
  /* Stats area - even more compact */
  
  /* Module styling - even more compact */
  
  /* Module content */
  
  /* Lesson items */
  
  /* Fixed bottom navigation on very small screens */
}

/* Very small devices */
@media (max-width: 320px) {
  
  /* Fixed bottom navigation on tiny screens */
}

/* Enhanced Mobile Module Styles */

/* Module container styles */

.dark .module-container {
  background-color: rgba(31, 41, 55, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Module container hover effects (desktop only) */
@media (min-width: 768px) {
  
  .dark .module-container:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    border-color: rgba(248, 113, 113, 0.3);
  }
}

/* Module header styling */

.dark .module-header {
  background-color: rgba(31, 41, 55, 0.9);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

/* Module title */

.dark .module-title {
  color: #F3F4F6;
}

/* Module content container */

/* Lesson item styling */

.dark .lesson-item {
  background-color: rgba(31, 41, 55, 0.7);
}

.dark .lesson-item:hover {
  background-color: rgba(31, 41, 55, 0.9);
  border-left: 2px solid #f87171;
}

/* Lesson item icon */

.dark .lesson-icon {
  color: #f87171;
}

/* Lesson title */

.dark .lesson-title {
  color: #D1D5DB;
}

/* Locked module styling */

.dark .module-locked .module-header {
  background-color: rgba(31, 41, 55, 0.7);
}

.dark .module-locked .module-title {
  color: #9CA3AF;
}

/* Complete module styling */

.dark .module-completed .module-header {
  border-left: 3px solid #34D399;
}

/* Active module styling */

.dark .module-active .module-header {
  border-left: 3px solid #f87171;
}

/* Responsive styles for mobile */

/* Extra small screens (iPhone SE, etc.) */

/* Very small screens (under 320px) */

/* Desktop Enhancements */
@media (min-width: 1024px) {
  
  .dark .module-container {
    background-color: rgba(31, 41, 55, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.03);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  .dark .module-header:hover {
    background-color: rgba(31, 41, 55, 1);
  }
  
  .dark .lesson-item:hover {
    background-color: rgba(31, 41, 55, 0.9);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
    border-left: 3px solid #f87171;
  }
}
