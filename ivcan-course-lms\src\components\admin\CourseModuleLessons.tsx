import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Loader2, Edit, Trash2, BookOpen, BookText } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useNavigate } from 'react-router-dom';

interface CourseModuleLessonsProps {
  moduleId: string;
  onEditLesson?: (id: string) => void;
  onDeleteLesson?: (id: string) => void;
  onAddLesson?: () => void;
}

const CourseModuleLessons: React.FC<CourseModuleLessonsProps> = ({
  moduleId,
  onEditLesson,
  onDeleteLesson,
  onAddLesson
}) => {
  const navigate = useNavigate();

  const { data: lessons, isLoading } = useQuery({
    queryKey: ['module-lessons', moduleId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('lessons')
        .select('*')
        .eq('module_id', moduleId)
        .order('order');

      if (error) throw error;
      return data;
    }
  });

  const { data: moduleData } = useQuery({
    queryKey: ['module-data', moduleId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('modules')
        .select('course_id')
        .eq('id', moduleId)
        .single();

      if (error) {
        console.error('Error fetching module data:', error);
        return null;
      }

      return data;
    },
    enabled: !!moduleId,
  });

  const handleViewLesson = (lessonSlug: string) => {
    if (moduleData?.course_id) {
      navigate(`/course/${moduleData.course_id}/lesson/${lessonSlug}`);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center p-4">
        <Loader2 className="w-6 h-6 animate-spin text-primary" />
      </div>
    );
  }

  if (!lessons?.length) {
    return (
      <div className="text-center py-6 bg-muted/30 rounded-lg border border-dashed border-border">
        <p className="text-muted-foreground mb-4">No lessons in this module yet</p>
        <div className="flex justify-center gap-2">
          {onAddLesson && (
            <Button onClick={onAddLesson} size="sm" className="flex items-center">
              <BookText className="w-4 h-4 mr-1" /> Add Lesson
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-end mb-4 gap-2">
        {onAddLesson && (
          <Button onClick={onAddLesson} size="sm" variant="outline" className="flex items-center">
            <BookText className="w-4 h-4 mr-1" /> Add Lesson
          </Button>
        )}
      </div>
      <div className="border rounded-md overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Title</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Duration</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
        <TableBody>
          {lessons.map((lesson) => (
            <TableRow key={lesson.id}>
              <TableCell className="font-medium">{lesson.title}</TableCell>
              <TableCell>
                <span className={`px-2 py-1 rounded-full text-xs ${
                  lesson.type === 'assignment' ? 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300' :
                  'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                }`}>
                  {lesson.type.charAt(0).toUpperCase() + lesson.type.slice(1)}
                </span>
              </TableCell>
              <TableCell>{lesson.duration}</TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleViewLesson(lesson.slug)}
                    title="View lesson"
                  >
                    <BookOpen className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onEditLesson(lesson.id)}
                    title="Edit lesson"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onDeleteLesson(lesson.id)}
                    className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-950/30"
                    title="Delete lesson"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  </div>
  );
};

export default CourseModuleLessons;
