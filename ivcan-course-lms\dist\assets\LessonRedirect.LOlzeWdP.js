var l=(e,a,s)=>new Promise((u,d)=>{var n=o=>{try{i(s.next(o))}catch(c){d(c)}},r=o=>{try{i(s.throw(o))}catch(c){d(c)}},i=o=>o.done?u(o.value):Promise.resolve(o.value).then(n,r);i((s=s.apply(e,a)).next())});import{aH as x,u as f,az as p,r as g,j as t}from"./vendor-react.BcAa1DKr.js";import{P as m,s as h}from"./index.BLDhDn0D.js";import"./vendor.DQpuTRuB.js";import"./vendor-supabase.sufZ44-y.js";const w=()=>{const{lessonSlug:e}=x(),a=f(),{data:s,isLoading:u,error:d}=p({queryKey:["lesson-redirect",e],queryFn:()=>l(void 0,null,function*(){if(!e)throw new Error("No lesson slug provided");const{data:n,error:r}=yield h.from("lessons").select(`
          slug,
          modules!inner (
            courses!inner (
              id
            )
          )
        `).eq("slug",e).single();if(r)throw r;return n}),enabled:!!e});return g.useEffect(()=>{var n,r;if(s&&((r=(n=s.modules)==null?void 0:n.courses)!=null&&r.id)){const i=s.modules.courses.id;a(`/course/${i}/lesson/${e}`,{replace:!0})}},[s,e,a]),u?t.jsx(m,{text:"Redirecting to lesson..."}):d?t.jsx("div",{className:"flex items-center justify-center min-h-screen",children:t.jsxs("div",{className:"text-center",children:[t.jsx("h1",{className:"text-2xl font-bold text-destructive mb-2",children:"Lesson Not Found"}),t.jsxs("p",{className:"text-muted-foreground mb-4",children:['The lesson "',e,'" could not be found.']}),t.jsx("button",{onClick:()=>a("/dashboard"),className:"px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90",children:"Go to Dashboard"})]})}):t.jsx(m,{text:"Redirecting..."})};export{w as default};
