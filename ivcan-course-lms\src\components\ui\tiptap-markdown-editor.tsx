import React, { useEffect, useState, useCallback, useRef } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import Underline from '@tiptap/extension-underline';
import Highlight from '@tiptap/extension-highlight';
import Placeholder from '@tiptap/extension-placeholder';
import TaskList from '@tiptap/extension-task-list';
import TaskItem from '@tiptap/extension-task-item';
import CodeBlockLowlight from '@tiptap/extension-code-block-lowlight';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import { common, createLowlight } from 'lowlight';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
// Removed unused dialog imports
import { MarkdownPreview } from '@/components/ui/markdown-preview';
import { tiptapToMarkdown, htmlToMarkdown } from '@/lib/tiptap-markdown-serializer';
import { markdownToHtml } from '@/lib/content-converter';
// Removed complex image upload functionality
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  List,
  ListOrdered,
  Code,
  Quote,
  Link as LinkIcon,
  Image as ImageIcon,
  Heading1,
  Heading2,
  Heading3,
  HighlighterIcon,
  CheckSquare,
  Undo,
  Redo,
  Type,
  Eye,
  Table as TableIcon,
  Plus,
  Minus,
  // Removed unused Upload, Loader2
} from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './tooltip';

const lowlight = createLowlight(common);

interface TiptapMarkdownEditorProps {
  initialContent?: string;
  onChange?: (markdown: string) => void;
  placeholder?: string;
  className?: string;
  minHeight?: number;
  autoFocus?: boolean;
}

export function TiptapMarkdownEditor({
  initialContent = '',
  onChange,
  placeholder = 'Start writing your lesson content...',
  className = '',
  minHeight = 400,
  autoFocus = false,
}: TiptapMarkdownEditorProps) {
  const [activeTab, setActiveTab] = useState<'editor' | 'preview'>('editor');
  const [markdownContent, setMarkdownContent] = useState(initialContent);

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        codeBlock: false, // We'll use CodeBlockLowlight instead
      }),
      CodeBlockLowlight.configure({
        lowlight,
        defaultLanguage: 'plaintext',
        HTMLAttributes: {
          class: 'rounded-md bg-muted/70 p-4 overflow-x-auto',
        },
      }),
      Image.configure({
        HTMLAttributes: {
          class: 'rounded-lg max-w-full h-auto',
        },
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          rel: 'noopener noreferrer',
          target: '_blank',
          class: 'text-blue-600 hover:text-blue-800 underline',
        },
      }),
      Underline,
      Highlight.configure({
        HTMLAttributes: {
          class: 'bg-yellow-200 dark:bg-yellow-800',
        },
      }),
      TaskList,
      TaskItem.configure({
        nested: true,
      }),
      Table.configure({
        resizable: true,
        lastColumnResizable: false,
        allowTableNodeSelection: true,
        HTMLAttributes: {
          class: 'border-collapse border border-border w-full my-4',
        },
      }),
      TableRow.configure({
        HTMLAttributes: {
          class: 'border-b border-border',
        },
      }),
      TableHeader.configure({
        HTMLAttributes: {
          class: 'bg-muted font-semibold text-left p-3 border border-border',
        },
      }),
      TableCell.configure({
        HTMLAttributes: {
          class: 'p-3 border border-border',
        },
      }),
      Placeholder.configure({
        placeholder,
      }),
    ],
    content: markdownToHtml(initialContent),
    onUpdate: ({ editor }) => {
      // Convert editor content to markdown
      try {
        const doc = editor.state.doc;
        const markdown = tiptapToMarkdown(doc);
        setMarkdownContent(markdown);
        onChange?.(markdown);
      } catch (error) {
        console.error('Error converting to markdown:', error);
        // Fallback to HTML to markdown conversion
        const html = editor.getHTML();
        const markdown = htmlToMarkdown(html);
        setMarkdownContent(markdown);
        onChange?.(markdown);
      }
    },
    editorProps: {
      attributes: {
        class: cn(
          'prose prose-sm max-w-none focus:outline-none dark:prose-invert',
          'min-h-[400px] p-4'
        ),
        style: `min-height: ${minHeight}px`,
      },
    },
    autofocus: autoFocus,
  });

  // Update editor content when initialContent changes
  useEffect(() => {
    if (editor && initialContent !== markdownContent) {
      const htmlContent = markdownToHtml(initialContent);
      editor.commands.setContent(htmlContent);
      setMarkdownContent(initialContent);
    }
  }, [initialContent, editor, markdownContent]);

  // Simple image URL insertion
  const addImage = useCallback(() => {
    const url = prompt('Enter image URL:');
    if (url && editor) {
      editor.chain().focus().setImage({ src: url }).run();
    }
  }, [editor]);

  const addLink = useCallback(() => {
    const url = window.prompt('Enter the URL:');
    if (url && editor) {
      editor.chain().focus().setLink({ href: url }).run();
    }
  }, [editor]);

  // Table handlers
  const insertTable = useCallback(() => {
    if (editor) {
      editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
    }
  }, [editor]);

  const addColumnBefore = useCallback(() => {
    if (editor) {
      editor.chain().focus().addColumnBefore().run();
    }
  }, [editor]);

  const addColumnAfter = useCallback(() => {
    if (editor) {
      editor.chain().focus().addColumnAfter().run();
    }
  }, [editor]);

  const deleteColumn = useCallback(() => {
    if (editor) {
      editor.chain().focus().deleteColumn().run();
    }
  }, [editor]);

  const addRowBefore = useCallback(() => {
    if (editor) {
      editor.chain().focus().addRowBefore().run();
    }
  }, [editor]);

  const addRowAfter = useCallback(() => {
    if (editor) {
      editor.chain().focus().addRowAfter().run();
    }
  }, [editor]);

  const deleteRow = useCallback(() => {
    if (editor) {
      editor.chain().focus().deleteRow().run();
    }
  }, [editor]);

  const deleteTable = useCallback(() => {
    if (editor) {
      editor.chain().focus().deleteTable().run();
    }
  }, [editor]);

  if (!editor) {
    return null;
  }

  const ToolbarButton = ({ 
    onClick, 
    isActive = false, 
    disabled = false, 
    title, 
    children 
  }: {
    onClick: () => void;
    isActive?: boolean;
    disabled?: boolean;
    title: string;
    children: React.ReactNode;
  }) => (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={onClick}
            disabled={disabled}
            className={cn(
              "h-8 w-8 p-0",
              isActive ? 'bg-muted dark:bg-muted/50' : ''
            )}
          >
            {children}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{title}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );

  return (
    <div className={cn("border rounded-md bg-card text-card-foreground", className)}>
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'editor' | 'preview')}>
        <div className="flex items-center justify-between border-b bg-muted/30 dark:bg-muted/10">
          <TabsList className="bg-transparent border-none">
            <TabsTrigger value="editor" className="flex items-center gap-2">
              <Type className="h-4 w-4" />
              Editor
            </TabsTrigger>
            <TabsTrigger value="preview" className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Preview
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="editor" className="p-0 m-0">
          {/* Toolbar */}
          <div className="flex flex-wrap gap-1 p-2 border-b border-border bg-muted/30 dark:bg-muted/10">
            {/* Text formatting */}
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleBold().run()}
              isActive={editor.isActive('bold')}
              title="Bold"
            >
              <Bold className="h-4 w-4" />
            </ToolbarButton>
            
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleItalic().run()}
              isActive={editor.isActive('italic')}
              title="Italic"
            >
              <Italic className="h-4 w-4" />
            </ToolbarButton>
            
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleUnderline().run()}
              isActive={editor.isActive('underline')}
              title="Underline"
            >
              <UnderlineIcon className="h-4 w-4" />
            </ToolbarButton>
            
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleCode().run()}
              isActive={editor.isActive('code')}
              title="Inline Code"
            >
              <Code className="h-4 w-4" />
            </ToolbarButton>
            
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleHighlight().run()}
              isActive={editor.isActive('highlight')}
              title="Highlight"
            >
              <HighlighterIcon className="h-4 w-4" />
            </ToolbarButton>

            <div className="w-px h-8 bg-gray-300 mx-1"></div>

            {/* Headings */}
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
              isActive={editor.isActive('heading', { level: 1 })}
              title="Heading 1"
            >
              <Heading1 className="h-4 w-4" />
            </ToolbarButton>
            
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
              isActive={editor.isActive('heading', { level: 2 })}
              title="Heading 2"
            >
              <Heading2 className="h-4 w-4" />
            </ToolbarButton>
            
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
              isActive={editor.isActive('heading', { level: 3 })}
              title="Heading 3"
            >
              <Heading3 className="h-4 w-4" />
            </ToolbarButton>

            <div className="w-px h-8 bg-gray-300 mx-1"></div>

            {/* Lists */}
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleBulletList().run()}
              isActive={editor.isActive('bulletList')}
              title="Bullet List"
            >
              <List className="h-4 w-4" />
            </ToolbarButton>
            
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleOrderedList().run()}
              isActive={editor.isActive('orderedList')}
              title="Numbered List"
            >
              <ListOrdered className="h-4 w-4" />
            </ToolbarButton>
            
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleTaskList().run()}
              isActive={editor.isActive('taskList')}
              title="Task List"
            >
              <CheckSquare className="h-4 w-4" />
            </ToolbarButton>

            <div className="w-px h-8 bg-gray-300 mx-1"></div>

            {/* Block elements */}
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleBlockquote().run()}
              isActive={editor.isActive('blockquote')}
              title="Quote"
            >
              <Quote className="h-4 w-4" />
            </ToolbarButton>
            
            <ToolbarButton
              onClick={() => editor.chain().focus().toggleCodeBlock().run()}
              isActive={editor.isActive('codeBlock') || editor.isActive('codeBlockLowlight')}
              title="Code Block"
            >
              <Code className="h-4 w-4" />
            </ToolbarButton>

            <div className="w-px h-8 bg-gray-300 mx-1"></div>

            {/* Media */}
            <ToolbarButton
              onClick={addLink}
              isActive={editor.isActive('link')}
              title="Add Link"
            >
              <LinkIcon className="h-4 w-4" />
            </ToolbarButton>

            <ToolbarButton
              onClick={addImage}
              title="Add Image"
            >
              <ImageIcon className="h-4 w-4" />
            </ToolbarButton>

            <div className="w-px h-8 bg-gray-300 mx-1"></div>

            {/* Tables */}
            <ToolbarButton
              onClick={insertTable}
              title="Insert Table"
            >
              <TableIcon className="h-4 w-4" />
            </ToolbarButton>

            {editor.isActive('table') && (
              <>
                <ToolbarButton
                  onClick={addColumnBefore}
                  title="Add Column Before"
                >
                  <Plus className="h-3 w-3" />
                </ToolbarButton>

                <ToolbarButton
                  onClick={addRowAfter}
                  title="Add Row After"
                >
                  <Plus className="h-3 w-3" />
                </ToolbarButton>

                <ToolbarButton
                  onClick={deleteColumn}
                  title="Delete Column"
                >
                  <Minus className="h-3 w-3" />
                </ToolbarButton>

                <ToolbarButton
                  onClick={deleteRow}
                  title="Delete Row"
                >
                  <Minus className="h-3 w-3" />
                </ToolbarButton>
              </>
            )}

            <div className="w-px h-8 bg-gray-300 mx-1"></div>

            {/* Undo/Redo */}
            <ToolbarButton
              onClick={() => editor.chain().focus().undo().run()}
              disabled={!editor.can().undo()}
              title="Undo"
            >
              <Undo className="h-4 w-4" />
            </ToolbarButton>
            
            <ToolbarButton
              onClick={() => editor.chain().focus().redo().run()}
              disabled={!editor.can().redo()}
              title="Redo"
            >
              <Redo className="h-4 w-4" />
            </ToolbarButton>
          </div>

          <EditorContent editor={editor} />
        </TabsContent>

        <TabsContent value="preview" className="p-6 m-0" style={{ minHeight: `${minHeight}px` }}>
          <div className="github-markdown-preview">
            <MarkdownPreview
              content={markdownContent}
              className="prose prose-gray max-w-none dark:prose-invert"
              allowHtml={true}
            />
          </div>
        </TabsContent>
      </Tabs>


    </div>
  );
}
