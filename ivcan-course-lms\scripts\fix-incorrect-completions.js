// This script fixes incorrect course completion records
// Run this script with: node scripts/fix-incorrect-completions.js

const { createClient } = require('@supabase/supabase-js');

// Replace these with your Supabase URL and anon key
const SUPABASE_URL = "https://jibspqwieubavucdtccv.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImppYnNwcXdpZXViYXZ1Y2R0Y2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMxMjUzNzIsImV4cCI6MjA1ODcwMTM3Mn0.H1cvGLXWvIMibv8dYsYIvY37IRoVR80HyeF1wM93IUI";

// Create Supabase client with anon key (limited permissions)
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Try to create a service role client if environment variables are available
let serviceRoleClient = null;
if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
  console.log('Using service role client for elevated permissions');
  serviceRoleClient = createClient(SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);
}

async function fixIncorrectCompletions() {
  try {
    const client = serviceRoleClient || supabase;
    console.log('Fixing incorrect course completion records...');
    
    // Step 1: Get all user_course_enrollment records with status 'completed'
    console.log('Finding all completed course enrollments...');
    const { data: completedEnrollments, error: enrollmentError } = await client
      .from('user_course_enrollment')
      .select('id, user_id, course_id, status, enrolled_at, completed_at')
      .eq('status', 'completed');
    
    if (enrollmentError) {
      console.error('Error fetching completed enrollments:', enrollmentError);
      process.exit(1);
    }
    
    if (!completedEnrollments || completedEnrollments.length === 0) {
      console.log('No completed enrollments found');
      return;
    }
    
    console.log(`Found ${completedEnrollments.length} completed enrollments`);
    
    // Step 2: Check if any of these enrollments are missing the enrolled_at field
    const invalidEnrollments = completedEnrollments.filter(e => !e.enrolled_at);
    
    if (invalidEnrollments.length === 0) {
      console.log('No invalid enrollments found');
      return;
    }
    
    console.log(`Found ${invalidEnrollments.length} invalid enrollments (missing enrolled_at)`);
    
    // Step 3: Fix the invalid enrollments
    let fixedCount = 0;
    for (const enrollment of invalidEnrollments) {
      console.log(`Fixing enrollment for user ${enrollment.user_id} and course ${enrollment.course_id}...`);
      
      const now = new Date().toISOString();
      const { error: updateError } = await client
        .from('user_course_enrollment')
        .update({
          enrolled_at: enrollment.completed_at || now,
          updated_at: now
        })
        .eq('id', enrollment.id);
      
      if (updateError) {
        console.error(`Error fixing enrollment ${enrollment.id}:`, updateError);
      } else {
        fixedCount++;
      }
    }
    
    console.log(`Successfully fixed ${fixedCount} out of ${invalidEnrollments.length} invalid enrollments`);
    
    // Step 4: Check for enrollments with status 'completed' but no completed_at date
    const missingCompletedAt = completedEnrollments.filter(e => !e.completed_at);
    
    if (missingCompletedAt.length === 0) {
      console.log('No enrollments missing completed_at date');
      return;
    }
    
    console.log(`Found ${missingCompletedAt.length} enrollments missing completed_at date`);
    
    // Step 5: Fix the enrollments missing completed_at date
    let fixedCompletedCount = 0;
    for (const enrollment of missingCompletedAt) {
      console.log(`Fixing completed_at for enrollment ${enrollment.id}...`);
      
      const now = new Date().toISOString();
      const { error: updateError } = await client
        .from('user_course_enrollment')
        .update({
          completed_at: now,
          updated_at: now
        })
        .eq('id', enrollment.id);
      
      if (updateError) {
        console.error(`Error fixing completed_at for enrollment ${enrollment.id}:`, updateError);
      } else {
        fixedCompletedCount++;
      }
    }
    
    console.log(`Successfully fixed ${fixedCompletedCount} out of ${missingCompletedAt.length} enrollments missing completed_at date`);
    
  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  }
}

// Run the function
fixIncorrectCompletions();
