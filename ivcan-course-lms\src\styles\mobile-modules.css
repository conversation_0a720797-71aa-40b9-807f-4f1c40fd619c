/* Mobile-specific styles for course modules */

@media (max-width: 768px) {
  /* Course container */
  .course-container {
    padding: 0.75rem;
    max-width: 100vw;
    overflow-x: hidden;
  }

  /* Course modules container */
  .course-modules-container {
    background-color: transparent !important;
    backdrop-filter: none !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
    margin: 0 auto !important;
    max-width: 100% !important;
    width: 100% !important;
  }

  /* Course stats grid - more consistent and professional */
  .course-stats {
    display: grid !important;
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 0.5rem !important;
    padding: 0.5rem !important;
    margin: 0.5rem !important;
    border-radius: 0.5rem !important;
    background: rgba(255, 255, 255, 0.7) !important;
    border: 1px solid rgba(0, 0, 0, 0.04) !important;
  }

  .dark .course-stats {
    background: rgba(30, 30, 30, 0.7) !important;
    border: 1px solid rgba(255, 255, 255, 0.04) !important;
  }

  .course-stat-item {
    display: flex !important;
    flex-direction: column;
    align-items: center !important;
    justify-content: center;
    text-align: center;
    gap: 0.25rem !important;
    padding: 0.75rem 0.5rem !important;
    border-radius: 0.5rem !important;
    background: rgba(255, 255, 255, 0.85) !important;
    min-height: 52px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03) !important;
  }

  .dark .course-stat-item {
    background: rgba(40, 40, 40, 0.85) !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
  }

  /* Course stat item labels - smaller */
  .course-stat-item p:first-of-type {
    font-size: 0.65rem !important; /* Smaller font */
    font-weight: 500 !important;
    color: #333 !important;
    margin-bottom: 0.125rem !important;
  }

  .dark .course-stat-item p:first-of-type {
    color: #d1d5db !important;
  }

  /* Course stat item values - smaller */
  .course-stat-item p:last-of-type {
    font-size: 0.65rem !important; /* Smaller font */
    color: #666 !important;
  }

  .dark .course-stat-item p:last-of-type {
    color: #9ca3af !important;
  }

  .course-stat-item svg {
    margin-bottom: 0.125rem;
    width: 0.75rem !important; /* Even smaller icon */
    height: 0.75rem !important;
    color: #e63946 !important;
  }

  .dark .course-stat-item svg {
    color: #f48b95 !important;
  }

  /* Module styling - more touch-friendly */
  .module-container {
    margin: 0.75rem 0.5rem !important;
    border-radius: 0.75rem !important;
    overflow: hidden;
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.06);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
    transition: all 0.2s ease-in-out;
  }

  .dark .module-container {
    background: rgba(28, 28, 28, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.08);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  /* Module header - improved touch target */
  .module-header {
    padding: 1rem !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
    min-height: 64px !important;
    cursor: pointer;
    position: relative;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(8px);
  }

  .dark .module-header {
    background: rgba(28, 28, 28, 0.8);
  }

  .module-number {
    width: 1.5rem !important; /* Further reduced */
    height: 1.5rem !important; /* Further reduced */
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-weight: 600 !important;
    font-size: 0.7rem !important; /* Further reduced */
    flex-shrink: 0;
    background: rgba(0, 0, 0, 0.03) !important;
    box-shadow: none !important;
    border: 1px solid rgba(0, 0, 0, 0.05);
  }

  .dark .module-number {
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #e0e0e0 !important;
  }

  /* Module title - improved readability */
  .module-title {
    font-size: 0.9375rem !important;
    line-height: 1.4 !important;
    font-weight: 500 !important;
    color: #222 !important;
    flex-grow: 1;
    letter-spacing: -0.01em !important;
  }

  .dark .module-title {
    color: #f0f0f0 !important;
  }

  /* Module Info - better readability */
  .module-info {
    font-size: 0.75rem !important;
    color: #666 !important;
    margin-top: 0.25rem !important;
    gap: 0.75rem !important;
    display: flex !important;
    align-items: center !important;
    flex-wrap: wrap !important;
  }

  .dark .module-info {
    color: #a0a0a0 !important;
  }

  .module-info svg {
    width: 0.875rem !important;
    height: 0.875rem !important;
    margin-right: 0.25rem !important;
    color: #666 !important;
  }

  .dark .module-info svg {
    color: #aaa !important;
  }

  /* Module Dropdown Icon */
  .module-dropdown-icon {
    margin-left: auto;
    color: #888 !important;
    transition: transform 0.2s ease-in-out;
    width: 0.75rem !important; /* Further reduced */
    height: 0.75rem !important; /* Further reduced */
  }

  .dark .module-dropdown-icon {
    color: #aaa !important;
  }

  .module-header[aria-expanded="true"] .module-dropdown-icon {
    transform: rotate(180deg);
  }

  /* Module content - better spacing */
  .module-content {
    padding: 0.5rem !important;
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(8px);
  }

  .dark .module-content {
    background: rgba(28, 28, 28, 0.5);
  }

  /* Lesson items - improved touch targets and visual hierarchy */
  .lesson-item {
    padding: 0.875rem 1rem !important;
    margin: 0.5rem !important;
    border-radius: 0.625rem !important;
    min-height: 56px !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
    background: rgba(0, 0, 0, 0.02) !important;
    border: 1px solid rgba(0, 0, 0, 0.04) !important;
    transition: all 0.2s ease-in-out !important;
    position: relative;
  }

  .lesson-item:active {
    transform: scale(0.98);
    background: rgba(0, 0, 0, 0.04) !important;
  }

  .dark .lesson-item {
    background: rgba(255, 255, 255, 0.04) !important;
    border: 1px solid rgba(255, 255, 255, 0.06) !important;
  }

  .lesson-item:hover {
    background: rgba(0, 0, 0, 0.04) !important;
    transform: translateX(2px);
  }

  .dark .lesson-item:hover {
    background: rgba(255, 255, 255, 0.07) !important;
    transform: translateX(2px);
  }

  .dark .lesson-item:active {
    background: rgba(255, 255, 255, 0.08) !important;
  }

  /* Lesson title - improved readability */
  .lesson-title {
    font-size: 0.875rem !important;
    line-height: 1.4 !important;
    font-weight: 400 !important;
    color: #333 !important;
    flex-grow: 1 !important;
  }

  .dark .lesson-title {
    color: #e0e0e0 !important;
  }

  /* Lesson meta - better spacing and readability */
  .lesson-meta {
    font-size: 0.75rem !important;
    color: #666 !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
  }

  .dark .lesson-meta {
    color: #999 !important;
  }

  .lesson-meta svg {
    width: 0.875rem !important;
    height: 0.875rem !important;
    color: #666 !important;
  }

  .dark .lesson-meta svg {
    color: #aaa !important;
  }

  /* Icons - smaller */
  .icon-small {
    width: 0.75rem !important; /* Reduced from 0.875rem */
    height: 0.75rem !important; /* Reduced from 0.875rem */
  }

  .icon-medium {
    width: 1rem !important;
    height: 1rem !important;
  }

  /* Buttons */
  .mobile-button {
    width: 100% !important;
    height: 40px !important;
    font-size: 0.8125rem !important;
    border-radius: 0.5rem !important;
    font-weight: 500 !important;
  }
}

/* Extra small devices */
@media (max-width: 380px) {
  /* Course container */
  .course-container {
    padding: 0.625rem;
  }
  
  /* Stats area - even more compact */
  .course-stats {
    grid-template-columns: repeat(2, 1fr) !important;
  }
  
  .course-stat-item:last-child {
    grid-column: span 2;
  }
  
  .course-stat-item {
    padding: 0.375rem !important;
    min-height: 44px; /* Further reduced */
  }
  
  .course-stat-item p:first-of-type {
    font-size: 0.625rem !important;
    margin-bottom: 0 !important;
  }
  
  .course-stat-item p:last-of-type {
    font-size: 0.625rem !important;
  }
  
  .course-stat-item svg {
    width: 0.75rem !important;
    height: 0.75rem !important;
  }
  
  /* Module styling - even more compact */
  .module-container {
    margin-bottom: 0.5rem;
  }
  
  .module-header {
    padding: 0.625rem 0.75rem !important; /* Further reduced */
  }
  
  .module-number {
    width: 1.375rem !important; /* Further reduced */
    height: 1.375rem !important; /* Further reduced */
    font-size: 0.625rem !important; /* Further reduced */
  }
  
  .module-title {
    font-size: 0.75rem !important; /* Further reduced */
  }
  
  .module-info {
    font-size: 0.625rem !important; /* Further reduced */
  }
  
  .module-info svg {
    width: 0.625rem !important; /* Further reduced */
    height: 0.625rem !important; /* Further reduced */
  }
  
  /* Module content */
  .module-content {
    padding: 0 0.375rem 0.375rem 0.375rem !important;
  }
  
  /* Lesson items */
  .lesson-item {
    padding: 0.5rem 0.625rem !important;
    gap: 0.375rem !important;
  }
  
  .lesson-title {
    font-size: 0.7rem !important; /* Further reduced */
  }
  
  .lesson-meta {
    font-size: 0.625rem !important;
    gap: 0.25rem !important;
  }
  
  .lesson-meta svg {
    width: 0.625rem !important;
    height: 0.625rem !important;
  }
  
  /* Fixed bottom navigation on very small screens */
  .fixed-bottom-nav {
    padding: 0.5rem !important;
    height: 56px !important;
  }
  
  .fixed-bottom-button {
    height: 36px !important;
    font-size: 0.75rem !important;
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }
  
  .btn-text-xs {
    font-size: 0.6875rem !important;
  }
  
  .icon-small {
    width: 0.75rem !important;
    height: 0.75rem !important;
  }
  
  .icon-medium {
    width: 0.875rem !important;
    height: 0.875rem !important;
  }
}

/* Very small devices */
@media (max-width: 320px) {
  .course-stat-item {
    padding: 0.25rem !important;
    min-height: 40px; /* Further reduced */
  }
  
  .course-stat-item p:first-of-type {
    font-size: 0.6rem !important;
  }
  
  .course-stat-item p:last-of-type {
    font-size: 0.6rem !important;
  }
  
  .course-stat-item svg {
    width: 0.6875rem !important;
    height: 0.6875rem !important;
  }
  
  .module-title {
    font-size: 0.875rem !important;
  }
  
  .module-info {
    font-size: 0.6875rem !important;
  }
  
  .lesson-title {
    font-size: 0.8125rem !important;
  }
  
  .lesson-meta {
    font-size: 0.6875rem !important;
  }
  
  .module-number {
    width: 1.25rem !important; /* Further reduced */
    height: 1.25rem !important; /* Further reduced */
    font-size: 0.6rem !important; /* Further reduced */
  }
  
  /* Fixed bottom navigation on tiny screens */
  .fixed-bottom-nav {
    padding: 0.375rem !important;
    height: 50px !important;
  }
  
  .fixed-bottom-button {
    height: 32px !important;
    font-size: 0.6875rem !important;
    padding-left: 0.375rem !important;
    padding-right: 0.375rem !important;
  }
  
  .module-container {
    margin: 0.5rem 0.25rem !important;
  }
  
  .module-header {
    padding: 0.875rem !important;
    min-height: 56px !important;
  }
  
  .lesson-item {
    padding: 0.75rem !important;
    min-height: 48px !important;
  }
}

/* Enhanced Mobile Module Styles */

/* Module container styles */
.module-container {
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  background-color: white;
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
  margin-bottom: 1.25rem;
}

.dark .module-container {
  background-color: rgba(31, 41, 55, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Module container hover effects (desktop only) */
@media (min-width: 768px) {
  .module-container {
    transform: translateZ(0);
    will-change: transform, box-shadow;
  }
  
  .module-container:hover {
    transform: translateY(-3px) scale(1.01);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.06);
    border-color: rgba(230, 57, 70, 0.2);
  }
  
  .dark .module-container:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    border-color: rgba(248, 113, 113, 0.3);
  }
}

/* Module header styling */
.module-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.25rem;
  background-color: rgba(249, 250, 251, 0.9);
  border-bottom: 1px solid rgba(0, 0, 0, 0.03);
  cursor: pointer;
}

.dark .module-header {
  background-color: rgba(31, 41, 55, 0.9);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

/* Module title */
.module-title {
  font-weight: 600;
  color: #111827;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dark .module-title {
  color: #F3F4F6;
}

/* Module content container */
.module-content {
  padding: 0.75rem 1rem;
}

/* Lesson item styling */
.lesson-item {
  display: flex;
  align-items: center;
  padding: 0.875rem 1rem;
  background-color: white;
  border-radius: 10px;
  margin-bottom: 0.5rem;
  border-left: 2px solid transparent;
  transition: all 0.2s ease;
}

.dark .lesson-item {
  background-color: rgba(31, 41, 55, 0.7);
}

.lesson-item:hover {
  background-color: rgba(249, 250, 251, 1);
  border-left: 2px solid #e63946;
  transform: translateX(4px);
}

.dark .lesson-item:hover {
  background-color: rgba(31, 41, 55, 0.9);
  border-left: 2px solid #f87171;
}

/* Lesson item icon */
.lesson-icon {
  color: #e63946;
  margin-right: 0.75rem;
}

.dark .lesson-icon {
  color: #f87171;
}

/* Lesson title */
.lesson-title {
  font-weight: 500;
  font-size: 0.875rem;
  color: #374151;
  flex: 1;
}

.dark .lesson-title {
  color: #D1D5DB;
}

/* Locked module styling */
.module-locked .module-header {
  background-color: rgba(243, 244, 246, 0.7);
}

.dark .module-locked .module-header {
  background-color: rgba(31, 41, 55, 0.7);
}

.module-locked .module-title {
  color: #6B7280;
}

.dark .module-locked .module-title {
  color: #9CA3AF;
}

/* Complete module styling */
.module-completed .module-header {
  border-left: 3px solid #10B981;
}

.dark .module-completed .module-header {
  border-left: 3px solid #34D399;
}

/* Active module styling */
.module-active .module-header {
  border-left: 3px solid #e63946;
}

.dark .module-active .module-header {
  border-left: 3px solid #f87171;
}

/* Responsive styles for mobile */
@media (max-width: 767px) {
  .module-container {
    border-radius: 12px;
    margin-bottom: 0.5rem; /* Reduced margin */
  }

  .module-header {
    padding: 0.5rem 0.75rem; /* Reduced padding */
  }

  .module-title {
    font-size: 0.875rem;
  }

  .module-content {
    padding: 0.375rem 0.5rem; /* Reduced padding */
  }

  .lesson-item {
    padding: 0.5rem 0.625rem; /* Reduced padding */
    margin-bottom: 0.25rem; /* Reduced margin */
    border-radius: 8px;
  }
  
  .lesson-title {
    font-size: 0.75rem;
  }
}

/* Extra small screens (iPhone SE, etc.) */
@media (max-width: 380px) {
  .module-container {
    border-radius: 10px;
    margin-bottom: 0.625rem;
  }
  
  .module-header {
    padding: 0.625rem 0.875rem;
  }
  
  .module-title {
    font-size: 0.75rem;
  }
  
  .module-content {
    padding: 0.375rem 0.625rem;
  }
  
  .lesson-item {
    padding: 0.625rem 0.75rem;
    margin-bottom: 0.25rem;
    border-radius: 6px;
  }
  
  .lesson-title {
    font-size: 0.6875rem;
  }
  
  .lesson-icon {
    width: 0.875rem;
    height: 0.875rem;
    margin-right: 0.5rem;
  }
}

/* Very small screens (under 320px) */
@media (max-width: 320px) {
  .module-container {
    border-radius: 8px;
    margin-bottom: 0.5rem;
  }
  
  .module-header {
    padding: 0.5rem 0.75rem;
  }
  
  .module-title {
    font-size: 0.875rem !important;
  }
  
  .module-content {
    padding: 0.25rem 0.5rem;
  }
  
  .lesson-item {
    padding: 0.5rem 0.625rem;
    margin-bottom: 0.25rem;
  }
  
  .lesson-title {
    font-size: 0.8125rem !important;
  }
  
  .lesson-icon {
    width: 0.75rem;
    height: 0.75rem;
    margin-right: 0.375rem;
  }
  
  .module-info {
    font-size: 0.6875rem !important;
  }
  
  .lesson-meta {
    font-size: 0.6875rem !important;
  }
}

/* Desktop Enhancements */
@media (min-width: 1024px) {
  .module-container {
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.06);
    margin-bottom: 1.5rem;
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  }
  
  .dark .module-container {
    background-color: rgba(31, 41, 55, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.03);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  .module-header {
    padding: 1.25rem 1.5rem;
    border-radius: 16px 16px 0 0;
    transition: background-color 0.3s ease;
  }
  
  .module-header:hover {
    background-color: rgba(249, 250, 251, 1);
  }
  
  .dark .module-header:hover {
    background-color: rgba(31, 41, 55, 1);
  }
  
  .module-title {
    font-size: 1rem;
    font-weight: 600;
  }
  
  .module-content {
    padding: 1rem 1.5rem 1.25rem;
  }
  
  .lesson-item {
    padding: 1rem 1.25rem;
    margin-bottom: 0.625rem;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  }
  
  .lesson-item:hover {
    transform: translateX(8px);
    background-color: white;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.04);
    border-left: 3px solid #e63946;
  }
  
  .dark .lesson-item:hover {
    background-color: rgba(31, 41, 55, 0.9);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
    border-left: 3px solid #f87171;
  }
  
  .lesson-title {
    font-size: 0.9375rem;
  }
  
  .lesson-icon {
    width: 1.125rem;
    height: 1.125rem;
    margin-right: 0.875rem;
    transition: transform 0.3s ease;
  }
  
  .lesson-item:hover .lesson-icon {
    transform: scale(1.1);
  }
}
