import { supabase } from '@/integrations/supabase/client';

export const testEnrollment = async () => {
  try {
    console.log('=== ENROLLMENT TEST START ===');
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    console.log('Current user:', user);
    
    if (authError) {
      console.error('Auth error:', authError);
      return;
    }
    
    if (!user) {
      console.error('No authenticated user');
      return;
    }
    
    // Test course ID (from the database query we did earlier)
    const courseId = '9a528e76-f04d-4bc6-a080-5909d34d257a';
    const userId = user.id;
    
    console.log('Testing enrollment for:', { userId, courseId });
    
    // Test 1: Check if we can read from user_course_enrollment
    console.log('Test 1: Reading enrollments...');
    const { data: enrollments, error: readError } = await supabase
      .from('user_course_enrollment')
      .select('*')
      .eq('user_id', userId);
    
    console.log('Read result:', { enrollments, readError });
    
    // Test 2: Try to insert a new enrollment
    console.log('Test 2: Inserting enrollment...');
    const { data: insertData, error: insertError } = await supabase
      .from('user_course_enrollment')
      .insert({
        user_id: userId,
        course_id: courseId,
        status: 'in_progress',
        enrolled_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select();
    
    console.log('Insert result:', { insertData, insertError });
    
    // Test 3: Try to update the enrollment
    if (insertData && insertData.length > 0) {
      console.log('Test 3: Updating enrollment...');
      const { data: updateData, error: updateError } = await supabase
        .from('user_course_enrollment')
        .update({
          status: 'completed',
          updated_at: new Date().toISOString()
        })
        .eq('id', insertData[0].id)
        .select();
      
      console.log('Update result:', { updateData, updateError });
    }
    
    console.log('=== ENROLLMENT TEST END ===');
    
  } catch (error) {
    console.error('Test error:', error);
  }
};

// Make it available globally for testing
(window as any).testEnrollment = testEnrollment;
