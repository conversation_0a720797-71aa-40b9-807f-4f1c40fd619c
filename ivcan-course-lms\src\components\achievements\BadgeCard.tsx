import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, <PERSON><PERSON>ooter, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Trophy, Award, Medal, Star, Sparkles, Zap, Target, Flag, CheckCircle } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { UserAchievement, claimBadge } from '@/services/achievements/achievementService';
import { useToast } from '@/hooks/use-toast';
import { useQueryClient } from '@tanstack/react-query';
import Confetti from '@/components/ui/confetti';

interface BadgeCardProps {
  achievement: UserAchievement;
  userId: string;
  moduleInfo?: {
    id: string;
    title: string;
    module_number: number;
    course_id: string;
  };
  courseInfo?: {
    id: string;
    title: string;
    description: string;
  };
}

const BadgeCard: React.FC<BadgeCardProps> = ({ achievement, userId, moduleInfo, courseInfo }) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [claiming, setClaiming] = React.useState(false);
  const [showConfetti, setShowConfetti] = React.useState(false);

  // Get the appropriate icon based on the badge type
  const getBadgeIcon = () => {
    const iconName = achievement.achievement.icon;

    switch (iconName) {
      case 'bold-step':
        return <Flag className="h-8 w-8 text-red-500" />;
      case 'persistence':
        return <Target className="h-8 w-8 text-red-500" />;
      case 'consistency':
        return <CheckCircle className="h-8 w-8 text-red-500" />;
      case 'perseverance':
        return <Zap className="h-8 w-8 text-red-500" />;
      case 'discipline':
        return <Star className="h-8 w-8 text-red-500" />;
      case 'last-straw':
        return <Medal className="h-8 w-8 text-red-500" />;
      case 'achiever':
        return <Trophy className="h-8 w-8 text-red-500" />;
      case 'course-master':
        return <Award className="h-8 w-8 text-primary" />;
      default:
        return <Sparkles className="h-8 w-8 text-primary" />;
    }
  };

  // Get the appropriate color class based on the badge type
  const getBadgeColorClass = () => {
    const iconName = achievement.achievement.icon;

    switch (iconName) {
      case 'bold-step':
        return 'bg-red-100 dark:bg-red-900/30 border-red-200 dark:border-red-800/50';
      case 'persistence':
        return 'bg-red-100 dark:bg-red-900/30 border-red-200 dark:border-red-800/50';
      case 'consistency':
        return 'bg-red-100 dark:bg-red-900/30 border-red-200 dark:border-red-800/50';
      case 'perseverance':
        return 'bg-red-100 dark:bg-red-900/30 border-red-200 dark:border-red-800/50';
      case 'discipline':
        return 'bg-red-100 dark:bg-red-900/30 border-red-200 dark:border-red-800/50';
      case 'last-straw':
        return 'bg-red-100 dark:bg-red-900/30 border-red-200 dark:border-red-800/50';
      case 'achiever':
        return 'bg-red-100 dark:bg-red-900/30 border-red-200 dark:border-red-800/50';
      case 'course-master':
        return 'bg-primary/10 dark:bg-primary/20 border-primary/20 dark:border-primary/30';
      default:
        return 'bg-gray-100 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700/50';
    }
  };

  const handleClaimBadge = async () => {
    if (claiming) return;

    setClaiming(true);
    try {
      const success = await claimBadge(userId, achievement.id);
      if (success) {
        setShowConfetti(true);
        toast({
          title: "Badge Claimed!",
          description: `You've claimed the ${achievement.achievement.name} badge.`,
          variant: "default",
        });

        // Refresh the achievements data
        queryClient.invalidateQueries({ queryKey: ['user-achievements'] });

        // Hide confetti after 3 seconds
        setTimeout(() => setShowConfetti(false), 3000);
      } else {
        toast({
          title: "Failed to claim badge",
          description: "Please try again later.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error claiming badge:', error);
      toast({
        title: "Error",
        description: "An unexpected error occurred.",
        variant: "destructive",
      });
    } finally {
      setClaiming(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="h-full"
    >
      <Card className={cn(
        "h-full flex flex-col overflow-hidden transition-all duration-300",
        "border hover:shadow-md",
        achievement.is_claimed ? "border-primary/20" : "border-muted"
      )}>
        {showConfetti && <Confetti count={100} duration={3} active={true} />}

        <CardHeader className="pb-2 relative">
          <div className="absolute top-2 right-2">
            <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20 text-xs">
              {achievement.achievement.points} pts
            </Badge>
          </div>

          <div className="flex flex-col items-center pt-4">
            <div className={cn(
              "w-20 h-20 rounded-full flex items-center justify-center mb-3",
              "border-2",
              getBadgeColorClass()
            )}>
              {getBadgeIcon()}
            </div>
            <h3 className="font-bold text-center">{achievement.achievement.name}</h3>
            <p className="text-sm text-muted-foreground text-center mt-1">
              {achievement.achievement.description}
            </p>
          </div>
        </CardHeader>

        <CardContent className="flex-grow pb-2">
          <div className="flex flex-col gap-1">
            <div className="text-xs text-muted-foreground text-center">
              Earned on {format(new Date(achievement.completed_at), 'MMMM d, yyyy')}
            </div>

            {moduleInfo && (
              <div className="mt-2 text-xs text-center">
                <span className="text-muted-foreground">Module {moduleInfo.module_number}: </span>
                <span className="font-medium">{moduleInfo.title}</span>
              </div>
            )}

            {courseInfo && (
              <div className="mt-1 text-xs text-center">
                <span className="text-muted-foreground">Course: </span>
                <span className="font-medium">{courseInfo.title}</span>
              </div>
            )}
          </div>
        </CardContent>

        <CardFooter className="pt-0">
          {achievement.is_claimed ? (
            <Button variant="outline" className="w-full" disabled>
              <CheckCircle className="h-4 w-4 mr-2" />
              Claimed
            </Button>
          ) : (
            <Button
              onClick={handleClaimBadge}
              className="w-full"
              disabled={claiming}
            >
              {claiming ? (
                <>
                  <span className="animate-spin border-2 border-current border-t-transparent rounded-full h-4 w-4 mr-2" />
                  Claiming...
                </>
              ) : (
                <>
                  <Trophy className="h-4 w-4 mr-2" />
                  Claim Badge
                </>
              )}
            </Button>
          )}
        </CardFooter>
      </Card>
    </motion.div>
  );
};

export default BadgeCard;
