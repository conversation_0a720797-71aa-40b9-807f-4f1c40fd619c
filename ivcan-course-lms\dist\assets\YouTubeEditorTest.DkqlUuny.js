import{r as v,j as e,bL as g,bt as h,E as t,br as T}from"./vendor-react.BcAa1DKr.js";import{S as N}from"./simple-markdown-editor.DQ2eiB7n.js";import{C as n,a as l,b as r,c,d}from"./card.B9V6b2DK.js";import{B as m}from"./badge.C87ZuIxl.js";import{B as u}from"./index.BLDhDn0D.js";import{a2 as x}from"./vendor.DQpuTRuB.js";import"./tabs.B0SF6qIv.js";import"./label.D4YlnUPk.js";import"./markdown-preview.Bw0U-NJA.js";import"./content-converter.L-GziWIP.js";import"./tiptap-image-upload.B_U9gNrF.js";import"./vendor-supabase.sufZ44-y.js";const S=()=>{const[a,p]=v.useState(`# YouTube Video Test

This is a test of the YouTube functionality in the SimpleMarkdownEditor.

## Instructions

1. Click the YouTube button in the toolbar (📺)
2. Enter a YouTube URL like: https://www.youtube.com/watch?v=dQw4w9WgXcQ
3. Click "Insert Video"
4. The video should appear in the editor

## Sample Content

Here's some sample content with a YouTube video:

**Bold text** and *italic text* work normally.

- List item 1
- List item 2
- List item 3

\`\`\`javascript
// Code blocks work too
console.log("Hello, world!");
\`\`\`

> This is a blockquote

| Column 1 | Column 2 | Column 3 |
|----------|----------|----------|
| Cell 1   | Cell 2   | Cell 3   |
| Cell 4   | Cell 5   | Cell 6   |

## Test the YouTube Button

Use the YouTube button in the toolbar above to add a video!
`),w=()=>{navigator.clipboard.writeText(a),x.success("Markdown copied to clipboard!")},j=()=>{const o=new Blob([a],{type:"text/markdown"}),i=URL.createObjectURL(o),s=document.createElement("a");s.href=i,s.download="youtube-test-content.md",document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(i),x.success("Markdown file downloaded!")},b=["https://www.youtube.com/watch?v=dQw4w9WgXcQ","https://www.youtube.com/watch?v=9bZkp7q19f0","https://youtu.be/dQw4w9WgXcQ","https://www.youtube.com/embed/dQw4w9WgXcQ"];return e.jsxs("div",{className:"container mx-auto py-8 px-4 max-w-7xl",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-4xl font-bold mb-4",children:"YouTube Editor Test"}),e.jsx("p",{className:"text-lg text-muted-foreground mb-6",children:"Test the YouTube video functionality in the SimpleMarkdownEditor component."}),e.jsxs("div",{className:"flex flex-wrap gap-2 mb-6",children:[e.jsxs(m,{variant:"default",children:[e.jsx(g,{className:"h-3 w-3 mr-1"}),"YouTube Support"]}),e.jsx(m,{variant:"secondary",children:"TipTap Editor"}),e.jsx(m,{variant:"outline",children:"Live Preview"}),e.jsx(m,{variant:"outline",children:"Markdown Output"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[e.jsxs(n,{children:[e.jsxs(l,{children:[e.jsx(r,{className:"text-lg",children:"Test URLs"}),e.jsx(c,{children:"Try these YouTube URLs in the editor"})]}),e.jsx(d,{children:e.jsx("div",{className:"space-y-2",children:b.map((o,i)=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("code",{className:"text-sm bg-muted px-2 py-1 rounded flex-1",children:o}),e.jsx(u,{size:"sm",variant:"outline",onClick:()=>{navigator.clipboard.writeText(o),x.success("URL copied!")},children:e.jsx(h,{className:"h-3 w-3"})})]},i))})})]}),e.jsxs(n,{children:[e.jsxs(l,{children:[e.jsx(r,{className:"text-lg",children:"Features to Test"}),e.jsx(c,{children:"Verify these features work correctly"})]}),e.jsx(d,{children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(t,{className:"h-4 w-4 text-green-500"}),e.jsx("span",{className:"text-sm",children:"YouTube button in toolbar"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(t,{className:"h-4 w-4 text-green-500"}),e.jsx("span",{className:"text-sm",children:"Dialog opens on button click"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(t,{className:"h-4 w-4 text-green-500"}),e.jsx("span",{className:"text-sm",children:"URL input validation"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(t,{className:"h-4 w-4 text-green-500"}),e.jsx("span",{className:"text-sm",children:"Video insertion"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(t,{className:"h-4 w-4 text-green-500"}),e.jsx("span",{className:"text-sm",children:"Preview mode display"})]})]})})]})]})]}),e.jsxs(n,{children:[e.jsx(l,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx(r,{children:"YouTube-Enabled Markdown Editor"}),e.jsx(c,{children:"Test the YouTube video insertion functionality. Click the YouTube button in the toolbar and enter a YouTube URL to insert a video."})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(u,{variant:"outline",size:"sm",onClick:w,className:"flex items-center gap-2",children:[e.jsx(h,{className:"h-4 w-4"}),"Copy Markdown"]}),e.jsxs(u,{variant:"outline",size:"sm",onClick:j,className:"flex items-center gap-2",children:[e.jsx(T,{className:"h-4 w-4"}),"Download"]})]})]})}),e.jsx(d,{children:e.jsx(N,{initialContent:a,onChange:p,placeholder:"Start writing and test the YouTube functionality...",minHeight:500,courseId:"test-course",moduleId:"test-module"})})]}),e.jsxs(n,{className:"mt-6",children:[e.jsxs(l,{children:[e.jsx(r,{children:"Markdown Output"}),e.jsx(c,{children:"This is the raw markdown content that will be saved"})]}),e.jsx(d,{children:e.jsx("pre",{className:"bg-muted p-4 rounded-lg overflow-auto text-sm",children:a})})]})]})};export{S as default};
