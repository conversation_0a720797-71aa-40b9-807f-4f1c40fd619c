/**
 * Supabase admin client with service role permissions
 *
 * This client has elevated permissions and should only be used
 * for admin operations that require bypassing RLS policies.
 */
import { createClient, PostgrestError, SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/types/database.types';
import { 
  SUPABASE_URL, 
  SUPABASE_SERVICE_ROLE_KEY, 
  logSupabaseError,
  SUPABASE_CONFIG
} from '@/config/supabase';

// Type for admin operation results with proper error typing
export type AdminOperationResult<T> = {
  data: T | null;
  error: PostgrestError | Error | null;
  status: 'success' | 'error' | 'unavailable';
};

// Circuit breaker state
let consecutiveFailures = 0;
let circuitBreakerOpen = false;
let circuitResetTimer: ReturnType<typeof setTimeout> | null = null;

// Create a Supabase client with the service role key
export const supabaseAdmin = createClient<Database>(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  },
  global: {
    headers: {
      'x-client-info': 'admin-api',
    }
  }
});

// Check if service role key is available
if (!SUPABASE_SERVICE_ROLE_KEY) {
  console.warn('VITE_SUPABASE_SERVICE_ROLE_KEY is not set. Admin operations will not work.');
}

// Log environment variables for debugging (only in development)
if (import.meta.env.DEV) {
  console.log('ServiceClient - Environment check:');
  console.log('SUPABASE_URL:', SUPABASE_URL ? 'Set' : 'Not set');
  console.log('SUPABASE_SERVICE_ROLE_KEY:', SUPABASE_SERVICE_ROLE_KEY ? 'Set' : 'Not set');
}

/**
 * Check if the service role client is available
 * @returns boolean indicating if the service role client is usable
 */
export const isServiceClientAvailable = (): boolean => {
  return Boolean(SUPABASE_SERVICE_ROLE_KEY) && !circuitBreakerOpen;
};

/**
 * Reset the circuit breaker manually
 */
export const resetCircuitBreaker = (): void => {
  consecutiveFailures = 0;
  circuitBreakerOpen = false;
  
  if (circuitResetTimer) {
    clearTimeout(circuitResetTimer);
    circuitResetTimer = null;
  }
  
  console.log('Admin service circuit breaker manually reset');
};

/**
 * Checks if the database is healthy using the health_check function
 * @returns Promise resolving to a boolean indicating database health
 */
export const checkDatabaseHealth = async (): Promise<boolean> => {
  if (!isServiceClientAvailable()) {
    return false;
  }
  
  try {
    // Using any type to avoid type errors until the generated types are updated
    const { data, error } = await supabaseAdmin.rpc('health_check') as { data: any; error: any };
    return Boolean(data?.status === 'ok' && !error);
  } catch (err) {
    logSupabaseError('health check', err);
    return false;
  }
};

/**
 * Performs a safe admin operation with proper error handling and circuit breaking
 * @param operation - Function that performs the admin operation
 * @returns Result of the operation with standardized error format
 */
export const safeAdminOperation = async <T>(
  operation: (client: SupabaseClient<Database>) => Promise<{ data: T | null; error: PostgrestError | null }>
): Promise<AdminOperationResult<T>> => {
  // Check if service role key is available
  if (!SUPABASE_SERVICE_ROLE_KEY) {
    return {
      data: null,
      error: new Error('Service role key is not configured. Admin operations cannot be performed.'),
      status: 'unavailable'
    };
  }
  
  // Check if circuit breaker is open
  if (circuitBreakerOpen) {
    return {
      data: null,
      error: new Error('Service temporarily unavailable. Too many errors occurred recently.'),
      status: 'unavailable'
    };
  }
  
  try {
    const result = await operation(supabaseAdmin);
    
    if (result.error) {
      // Count failures for circuit breaker
      consecutiveFailures++;
      
      // Check if circuit breaker threshold reached
      if (consecutiveFailures >= SUPABASE_CONFIG.CONNECTION.CIRCUIT_BREAKER_THRESHOLD) {
        circuitBreakerOpen = true;
        
        // Set timer to reset circuit breaker
        circuitResetTimer = setTimeout(() => {
          circuitBreakerOpen = false;
          consecutiveFailures = 0;
          circuitResetTimer = null;
          console.log('Admin service circuit breaker reset after timeout');
        }, SUPABASE_CONFIG.CONNECTION.CIRCUIT_BREAKER_RESET_TIMEOUT_MS);
        
        console.warn(`Admin service circuit breaker opened after ${consecutiveFailures} consecutive failures`);
      }
      
      logSupabaseError('admin operation', result.error);
      
      return {
        data: null,
        error: result.error,
        status: 'error'
      };
    }
    
    // Reset failure count on success
    consecutiveFailures = 0;
    
    return {
      data: result.data,
      error: null,
      status: 'success'
    };
  } catch (err) {
    // Count failures for circuit breaker
    consecutiveFailures++;
    
    // Check if circuit breaker threshold reached
    if (consecutiveFailures >= SUPABASE_CONFIG.CONNECTION.CIRCUIT_BREAKER_THRESHOLD) {
      circuitBreakerOpen = true;
      
      // Set timer to reset circuit breaker
      circuitResetTimer = setTimeout(() => {
        circuitBreakerOpen = false;
        consecutiveFailures = 0;
        circuitResetTimer = null;
        console.log('Admin service circuit breaker reset after timeout');
      }, SUPABASE_CONFIG.CONNECTION.CIRCUIT_BREAKER_RESET_TIMEOUT_MS);
      
      console.warn(`Admin service circuit breaker opened after ${consecutiveFailures} consecutive failures`);
    }
    
    logSupabaseError('admin operation', err);
    
    return {
      data: null,
      error: err instanceof Error ? err : new Error(String(err)),
      status: 'error'
    };
  }
};

export default supabaseAdmin;
