var d=Object.defineProperty;var r=Object.getOwnPropertySymbols;var n=Object.prototype.hasOwnProperty,a=Object.prototype.propertyIsEnumerable;var s=(e,t,m)=>t in e?d(e,t,{enumerable:!0,configurable:!0,writable:!0,value:m}):e[t]=m,i=(e,t)=>{for(var m in t||(t={}))n.call(t,m)&&s(e,m,t[m]);if(r)for(var m of r(t))a.call(t,m)&&s(e,m,t[m]);return e};var u=(e,t)=>{var m={};for(var o in e)n.call(e,o)&&t.indexOf(o)<0&&(m[o]=e[o]);if(e!=null&&r)for(var o of r(e))t.indexOf(o)<0&&a.call(e,o)&&(m[o]=e[o]);return m};import{j as p}from"./vendor-react.BcAa1DKr.js";import{c}from"./index.BLDhDn0D.js";function l(m){var o=m,{className:e}=o,t=u(o,["className"]);return p.jsx("div",i({className:c("animate-pulse rounded-md bg-muted",e)},t))}export{l as S};
