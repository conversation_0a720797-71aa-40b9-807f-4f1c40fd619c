import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Loader2, X, Upload, Trash2 } from 'lucide-react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Switch } from '@/components/ui/switch';
import { createModuleAsAdmin } from '@/services/course/adminApi';
import CourseModuleLessons from './CourseModuleLessons';
import { processImageForStorage } from '@/lib/local-storage';
import { Module, RawModule } from '@/services/course/types';
import { getModuleImageByNumber, getModuleFallbackImage } from '@/utils/moduleImageUtils';
import { OptimizedImage } from '@/components/ui/optimized-image';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import ModuleTestsList from './ModuleTestsList';

interface ModuleFormValues {
  title: string;
  slug: string;
  module_number: number;
  image_url?: string;
}

interface ModuleEditorProps {
  courseId: string;
  moduleId?: string;
  onClose: () => void;
  onAddLesson?: (moduleId: string) => void;
}

const ModuleEditor: React.FC<ModuleEditorProps> = ({
  courseId,
  moduleId,
  onClose,
  onAddLesson
}) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedModuleImage, setSelectedModuleImage] = useState<number>(1);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('basic');
  const isNewModule = !moduleId;

  const form = useForm<ModuleFormValues>({
    defaultValues: {
      title: '',
      slug: '',
      module_number: 1,
      image_url: ''
    }
  });

  // Fetch existing module data if editing
  const { isLoading: isLoadingModule } = useQuery({
    queryKey: ['module-editor', moduleId],
    queryFn: async () => {
      if (!moduleId) return null;

      const { data, error } = await supabase
        .from('modules')
        .select('*')
        .eq('id', moduleId)
        .single();

      if (error) {
        console.error('Error fetching module:', error);
        toast({
          title: "Error",
          description: "Failed to load module data",
          variant: "destructive"
        });
        return null;
      }

      // Set form values
      form.reset({
        title: data.title,
        slug: data.slug,
        module_number: data.module_number,
        image_url: data.image_url || ''
      });

      // Set image preview if available
      if (data.image_url) {
        setImagePreview(data.image_url);
        // Try to determine the module image number from the URL
        const imageNumberMatch = data.image_url.match(/module-(\d+)/);
        if (imageNumberMatch && imageNumberMatch[1]) {
          setSelectedModuleImage(parseInt(imageNumberMatch[1]));
        }
      }

      return data;
    },
    enabled: !!moduleId
  });

  // Handle module image selection
  const handleSelectModuleImage = (moduleNumber: number) => {
    setSelectedModuleImage(moduleNumber);
    const imageUrl = getModuleImageByNumber(moduleNumber);
    setImagePreview(imageUrl);
    form.setValue('image_url', imageUrl);
  };

  // Save or update module
  const mutation = useMutation({
    mutationFn: async (values: ModuleFormValues) => {
      // Validate courseId
      if (!courseId) {
        throw new Error('Course ID is required');
      }

      // Generate a unique slug if needed
      const finalSlug = values.slug || values.title.toLowerCase().replace(/\s+/g, '-');

      // Use the selected static image URL
      const finalImageUrl = getModuleImageByNumber(selectedModuleImage);

      if (moduleId) {
        // Update existing module
        const { data, error } = await supabase
          .from('modules')
          .update({
            title: values.title,
            slug: finalSlug,
            module_number: values.module_number,
            image_url: finalImageUrl
          })
          .eq('id', moduleId)
          .select()
          .single();

        if (error) throw error;
        return data;
      } else {
        // Create new module using the admin API
        const moduleData = {
          course_id: courseId,
          title: values.title,
          slug: finalSlug,
          module_number: values.module_number,
          is_locked: false,
          image_url: finalImageUrl
        };
        
        const data = await createModuleAsAdmin(moduleData);
        if (!data) throw new Error('Failed to create module');
        return data;
      }
    },
    onSuccess: (data) => {
      // Show success message
      toast({
        title: isNewModule ? 'Module Created' : 'Module Updated',
        description: `The module has been ${isNewModule ? 'created' : 'updated'} successfully.`
      });

      // Invalidate relevant queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['modules'] });
      queryClient.invalidateQueries({ queryKey: ['course-modules'] });
      queryClient.invalidateQueries({ queryKey: ['module-editor', moduleId] });

      if (isNewModule) {
        // If this is a new module, invalidate the course modules query
        queryClient.invalidateQueries({ queryKey: ['course-modules', courseId] });
      }

      // Close the editor
      onClose();
    },
    onError: (error: any) => {
      console.error('Error saving module:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to save module',
        variant: 'destructive'
      });
    }
  });

  // Delete a lesson
  const deleteLesson = useMutation({
    mutationFn: async (lessonId: string) => {
      const { error } = await supabase
        .from('lessons')
        .delete()
        .eq('id', lessonId);

      if (error) throw error;
    },
    onSuccess: () => {
      toast({
        title: 'Lesson Deleted',
        description: 'The lesson has been deleted successfully.'
      });
      queryClient.invalidateQueries({ queryKey: ['module-lessons', moduleId] });
    },
    onError: (error: any) => {
      console.error('Error deleting lesson:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete lesson',
        variant: 'destructive'
      });
    }
  });

  const onSubmit = (data: ModuleFormValues) => {
    mutation.mutate(data);
  };

  if (isLoadingModule) {
    return (
      <div className="flex justify-center py-8">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="basic">Module Info</TabsTrigger>
          <TabsTrigger value="lessons">Lessons</TabsTrigger>
          <TabsTrigger value="tests">Tests</TabsTrigger>
        </TabsList>

        <TabsContent value="basic">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="title"
                rules={{ required: "Title is required" }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Module Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter module title" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="slug"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Module Slug</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Enter module slug or leave empty to auto-generate" 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="module_number"
                rules={{ required: "Module number is required" }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Module Order</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        min="1"
                        placeholder="Enter module order" 
                        {...field}
                        value={field.value}
                        onChange={(e) => field.onChange(parseInt(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="space-y-2">
                <FormLabel>Module Image</FormLabel>
                <div className="grid grid-cols-4 gap-4">
                  {[1, 2, 3, 4, 5, 6].map((num) => (
                    <div 
                      key={num}
                      className={`border rounded-md p-2 cursor-pointer transition-all ${
                        selectedModuleImage === num ? 'ring-2 ring-primary' : 'hover:border-primary'
                      }`}
                      onClick={() => handleSelectModuleImage(num)}
                    >
                      <OptimizedImage
                        src={getModuleImageByNumber(num)}
                        alt={`Module Image ${num}`}
                        width={100}
                        height={60}
                        className="w-full h-auto object-cover rounded"
                      />
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={onClose}
                >
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={mutation.isPending}
                >
                  {mutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    'Save Module'
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </TabsContent>

        <TabsContent value="lessons">
          {!isNewModule && moduleId && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium mb-4">Module Lessons</h3>
              <CourseModuleLessons
                moduleId={moduleId}
                onEditLesson={(lessonId) => {
                  onClose();
                }}
                onDeleteLesson={(lessonId) => {
                  deleteLesson.mutate(lessonId);
                }}
                onAddLesson={onAddLesson ? () => onAddLesson(moduleId) : undefined}
              />
            </div>
          )}
          {isNewModule && (
            <div className="p-6 text-center border border-dashed rounded-lg">
              <p className="text-muted-foreground mb-2">You must save the module before adding lessons</p>
              <Button onClick={() => setActiveTab('basic')} variant="outline" size="sm">
                Go Back to Module Info
              </Button>
            </div>
          )}
        </TabsContent>

        <TabsContent value="tests">
          {!isNewModule && moduleId ? (
            <ModuleTestsList 
              moduleId={moduleId} 
              moduleName={form.watch('title')}
            />
          ) : (
            <div className="p-6 text-center border border-dashed rounded-lg">
              <p className="text-muted-foreground mb-2">You must save the module before managing tests</p>
              <Button onClick={() => setActiveTab('basic')} variant="outline" size="sm">
                Go Back to Module Info
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ModuleEditor;
