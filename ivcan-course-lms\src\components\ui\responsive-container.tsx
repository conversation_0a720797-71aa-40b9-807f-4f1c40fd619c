import React from 'react';
import { cn } from '@/lib/utils';

interface ResponsiveContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  as?: React.ElementType;
  fluid?: boolean;
  className?: string;
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg';
}

/**
 * A responsive container component that adapts to different screen sizes
 */
export function ResponsiveContainer({
  children,
  as: Component = 'div',
  fluid = false,
  className,
  maxWidth = 'xl',
  padding = 'md',
  ...props
}: ResponsiveContainerProps) {
  return (
    <Component
      className={cn(
        // Base styles
        "w-full mx-auto",
        "safe-bottom safe-top", // Add safe area insets for mobile

        // Responsive padding with better mobile spacing
        padding === 'none' && 'px-0',
        padding === 'sm' && 'px-3 sm:px-4',
        padding === 'md' && 'px-4 sm:px-6 lg:px-8',
        padding === 'lg' && 'px-6 sm:px-8 lg:px-12',

        // Responsive max-width
        !fluid && [
          maxWidth === 'sm' && 'max-w-screen-sm',
          maxWidth === 'md' && 'max-w-screen-md',
          maxWidth === 'lg' && 'max-w-screen-lg',
          maxWidth === 'xl' && 'max-w-screen-xl',
          maxWidth === '2xl' && 'max-w-screen-2xl',
          maxWidth === 'full' && 'max-w-full',
        ],

        className
      )}
      {...props}
    >
      {children}
    </Component>
  );
}

interface ResponsiveGridProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
  cols?: {
    default: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: 'sm' | 'md' | 'lg' | 'xl';
}

/**
 * A responsive grid component that adapts to different screen sizes
 */
export function ResponsiveGrid({
  children,
  className,
  cols = { default: 1, sm: 2, md: 3, lg: 4 },
  gap = 'md',
  ...props
}: ResponsiveGridProps) {
  return (
    <div
      className={cn(
        "grid w-full",
        // Responsive grid columns
        `grid-cols-${cols.default}`,
        cols.sm && `sm:grid-cols-${cols.sm}`,
        cols.md && `md:grid-cols-${cols.md}`,
        cols.lg && `lg:grid-cols-${cols.lg}`,
        cols.xl && `xl:grid-cols-${cols.xl}`,

        // Responsive gaps with better mobile spacing
        gap === 'sm' && 'gap-3 sm:gap-4',
        gap === 'md' && 'gap-4 sm:gap-6',
        gap === 'lg' && 'gap-6 sm:gap-8',
        gap === 'xl' && 'gap-8 sm:gap-10',

        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

interface ResponsiveFlexProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
  direction?: {
    default: 'row' | 'col';
    sm?: 'row' | 'col';
    md?: 'row' | 'col';
    lg?: 'row' | 'col';
    xl?: 'row' | 'col';
  };
  align?: 'start' | 'center' | 'end' | 'stretch' | 'baseline';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  gap?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  wrap?: boolean | 'reverse' | { default?: boolean | 'reverse', sm?: boolean | 'reverse', md?: boolean | 'reverse', lg?: boolean | 'reverse' };
}

/**
 * A responsive flex component that adapts to different screen sizes
 */
export function ResponsiveFlex({
  children,
  className,
  direction = { default: 'row' },
  align = 'start',
  justify = 'start',
  gap = 'md',
  wrap = false,
  ...props
}: ResponsiveFlexProps) {
  return (
    <div
      className={cn(
        "flex w-full",

        // Responsive direction
        direction.default === 'col' ? 'flex-col' : 'flex-row',
        direction.sm && (direction.sm === 'col' ? 'sm:flex-col' : 'sm:flex-row'),
        direction.md && (direction.md === 'col' ? 'md:flex-col' : 'md:flex-row'),
        direction.lg && (direction.lg === 'col' ? 'lg:flex-col' : 'lg:flex-row'),

        // Alignment
        {
          'items-start': align === 'start',
          'items-center': align === 'center',
          'items-end': align === 'end',
          'items-stretch': align === 'stretch',
          'items-baseline': align === 'baseline',
        },

        // Justification
        {
          'justify-start': justify === 'start',
          'justify-center': justify === 'center',
          'justify-end': justify === 'end',
          'justify-between': justify === 'between',
          'justify-around': justify === 'around',
          'justify-evenly': justify === 'evenly',
        },

        // Wrap handling
        typeof wrap === 'boolean' && (wrap ? 'flex-wrap' : 'flex-nowrap'),
        wrap === 'reverse' && 'flex-wrap-reverse',

        // Responsive gaps with better mobile spacing
        gap === 'sm' && 'gap-3 sm:gap-4',
        gap === 'md' && 'gap-4 sm:gap-6',
        gap === 'lg' && 'gap-6 sm:gap-8',
        gap === 'xl' && 'gap-8 sm:gap-10',

        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}
