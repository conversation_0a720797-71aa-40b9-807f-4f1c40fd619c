-- Fix RLS policies for all tables (safe version)

-- =====================
-- COURSES TABLE
-- =====================

-- Drop existing policies (only if they exist)
DO $$
BEGIN
    BEGIN
        DROP POLICY IF EXISTS "Anyone can view courses" ON public.courses;
        EXCEPTION WHEN OTHERS THEN END;
    BEGIN
        DROP POLICY IF EXISTS "Teachers can insert courses" ON public.courses;
        EXCEPTION WHEN OTHERS THEN END;
    BEGIN
        DROP POLICY IF EXISTS "Teachers can update courses" ON public.courses;
        EXCEPTION WHEN OTHERS THEN END;
    BEGIN
        DROP POLICY IF EXISTS "Teachers can delete courses" ON public.courses;
        EXCEPTION WHEN OTHERS THEN END;
    BEGIN
        DROP POLICY IF EXISTS "Service role bypass" ON public.courses;
        EXCEPTION WHEN OTHERS THEN END;
END $$;

-- Create new policies (only if they don't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'courses' AND policyname = 'Anyone can view courses'
    ) THEN
        CREATE POLICY "Anyone can view courses" 
        ON public.courses FOR SELECT 
        USING (true);
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'courses' AND policyname = 'Anyone can insert courses'
    ) THEN
        CREATE POLICY "Anyone can insert courses" 
        ON public.courses FOR INSERT 
        WITH CHECK (true);
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'courses' AND policyname = 'Anyone can update courses'
    ) THEN
        CREATE POLICY "Anyone can update courses" 
        ON public.courses FOR UPDATE 
        USING (true);
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'courses' AND policyname = 'Anyone can delete courses'
    ) THEN
        CREATE POLICY "Anyone can delete courses" 
        ON public.courses FOR DELETE 
        USING (true);
    END IF;
END $$;

-- =====================
-- MODULES TABLE
-- =====================

-- Drop existing policies (only if they exist)
DO $$
BEGIN
    BEGIN
        DROP POLICY IF EXISTS "Anyone can view modules" ON public.modules;
        EXCEPTION WHEN OTHERS THEN END;
    BEGIN
        DROP POLICY IF EXISTS "Teachers can insert modules" ON public.modules;
        EXCEPTION WHEN OTHERS THEN END;
    BEGIN
        DROP POLICY IF EXISTS "Teachers can update modules" ON public.modules;
        EXCEPTION WHEN OTHERS THEN END;
    BEGIN
        DROP POLICY IF EXISTS "Teachers can delete modules" ON public.modules;
        EXCEPTION WHEN OTHERS THEN END;
    BEGIN
        DROP POLICY IF EXISTS "Service role bypass" ON public.modules;
        EXCEPTION WHEN OTHERS THEN END;
END $$;

-- Create new policies (only if they don't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'modules' AND policyname = 'Anyone can view modules'
    ) THEN
        CREATE POLICY "Anyone can view modules" 
        ON public.modules FOR SELECT 
        USING (true);
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'modules' AND policyname = 'Anyone can insert modules'
    ) THEN
        CREATE POLICY "Anyone can insert modules" 
        ON public.modules FOR INSERT 
        WITH CHECK (true);
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'modules' AND policyname = 'Anyone can update modules'
    ) THEN
        CREATE POLICY "Anyone can update modules" 
        ON public.modules FOR UPDATE 
        USING (true);
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'modules' AND policyname = 'Anyone can delete modules'
    ) THEN
        CREATE POLICY "Anyone can delete modules" 
        ON public.modules FOR DELETE 
        USING (true);
    END IF;
END $$;

-- =====================
-- LESSONS TABLE
-- =====================

-- Drop existing policies (only if they exist)
DO $$
BEGIN
    BEGIN
        DROP POLICY IF EXISTS "Anyone can view lessons" ON public.lessons;
        EXCEPTION WHEN OTHERS THEN END;
    BEGIN
        DROP POLICY IF EXISTS "Teachers can insert lessons" ON public.lessons;
        EXCEPTION WHEN OTHERS THEN END;
    BEGIN
        DROP POLICY IF EXISTS "Teachers can update lessons" ON public.lessons;
        EXCEPTION WHEN OTHERS THEN END;
    BEGIN
        DROP POLICY IF EXISTS "Teachers can delete lessons" ON public.lessons;
        EXCEPTION WHEN OTHERS THEN END;
    BEGIN
        DROP POLICY IF EXISTS "Service role bypass" ON public.lessons;
        EXCEPTION WHEN OTHERS THEN END;
END $$;

-- Create new policies (only if they don't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'lessons' AND policyname = 'Anyone can view lessons'
    ) THEN
        CREATE POLICY "Anyone can view lessons" 
        ON public.lessons FOR SELECT 
        USING (true);
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'lessons' AND policyname = 'Anyone can insert lessons'
    ) THEN
        CREATE POLICY "Anyone can insert lessons" 
        ON public.lessons FOR INSERT 
        WITH CHECK (true);
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'lessons' AND policyname = 'Anyone can update lessons'
    ) THEN
        CREATE POLICY "Anyone can update lessons" 
        ON public.lessons FOR UPDATE 
        USING (true);
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'lessons' AND policyname = 'Anyone can delete lessons'
    ) THEN
        CREATE POLICY "Anyone can delete lessons" 
        ON public.lessons FOR DELETE 
        USING (true);
    END IF;
END $$;

-- =====================
-- USER_ROLES TABLE
-- =====================

-- Drop existing policies (only if they exist)
DO $$
BEGIN
    BEGIN
        DROP POLICY IF EXISTS "Anyone can view user_roles" ON public.user_roles;
        EXCEPTION WHEN OTHERS THEN END;
    BEGIN
        DROP POLICY IF EXISTS "Teachers can insert user_roles" ON public.user_roles;
        EXCEPTION WHEN OTHERS THEN END;
    BEGIN
        DROP POLICY IF EXISTS "Teachers can update user_roles" ON public.user_roles;
        EXCEPTION WHEN OTHERS THEN END;
    BEGIN
        DROP POLICY IF EXISTS "Teachers can delete user_roles" ON public.user_roles;
        EXCEPTION WHEN OTHERS THEN END;
    BEGIN
        DROP POLICY IF EXISTS "Service role bypass" ON public.user_roles;
        EXCEPTION WHEN OTHERS THEN END;
END $$;

-- Create new policies (only if they don't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'user_roles' AND policyname = 'Anyone can view user_roles'
    ) THEN
        CREATE POLICY "Anyone can view user_roles" 
        ON public.user_roles FOR SELECT 
        USING (true);
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'user_roles' AND policyname = 'Anyone can insert user_roles'
    ) THEN
        CREATE POLICY "Anyone can insert user_roles" 
        ON public.user_roles FOR INSERT 
        WITH CHECK (true);
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'user_roles' AND policyname = 'Anyone can update user_roles'
    ) THEN
        CREATE POLICY "Anyone can update user_roles" 
        ON public.user_roles FOR UPDATE 
        USING (true);
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'user_roles' AND policyname = 'Anyone can delete user_roles'
    ) THEN
        CREATE POLICY "Anyone can delete user_roles" 
        ON public.user_roles FOR DELETE 
        USING (true);
    END IF;
END $$;
