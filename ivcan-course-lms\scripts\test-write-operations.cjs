/**
 * Test Write Operations
 * This script tests if write operations are working properly
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const SUPABASE_URL = process.env.VITE_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.VITE_SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

// Create Supabase client with service role key for admin operations
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function testWriteOperations() {
  console.log('🔧 Testing write operations...');
  
  try {
    // Test 1: Insert into user_module_progress table
    const testUserId = '00000000-0000-0000-0000-000000000000'; // Valid UUID format
    const testModuleId = '11111111-1111-1111-1111-111111111111'; // Valid UUID format
    
    const { data: insertData, error: insertError } = await supabase
      .from('user_module_progress')
      .insert({
        user_id: testUserId,
        module_id: testModuleId,
        is_completed: false
      })
      .select();
    
    if (insertError) {
      console.error(`❌ Insert test failed: ${insertError.message}`);
      return false;
    }
    
    console.log('✅ Insert operation successful');
    
    // Test 2: Update the record
    const { data: updateData, error: updateError } = await supabase
      .from('user_module_progress')
      .update({ is_completed: true })
      .eq('user_id', testUserId)
      .eq('module_id', testModuleId)
      .select();
    
    if (updateError) {
      console.error(`❌ Update test failed: ${updateError.message}`);
      return false;
    }
    
    console.log('✅ Update operation successful');
    
    // Test 3: Delete the test record
    const { error: deleteError } = await supabase
      .from('user_module_progress')
      .delete()
      .eq('user_id', testUserId)
      .eq('module_id', testModuleId);
    
    if (deleteError) {
      console.error(`❌ Delete test failed: ${deleteError.message}`);
      return false;
    }
    
    console.log('✅ Delete operation successful');
    
    return true;
    
  } catch (err) {
    console.error(`❌ Write test error: ${err.message}`);
    return false;
  }
}

async function testStorageBuckets() {
  console.log('\n🔧 Testing storage buckets...');
  
  try {
    const { data: buckets, error } = await supabase.storage.listBuckets();
    
    if (error) {
      console.error(`❌ Storage test failed: ${error.message}`);
      return false;
    }
    
    const requiredBuckets = ['course-images', 'avatars', 'app-uploads'];
    const existingBuckets = buckets.map(b => b.name);
    
    console.log('📦 Found buckets:', existingBuckets);
    
    const missingBuckets = requiredBuckets.filter(bucket => !existingBuckets.includes(bucket));
    
    if (missingBuckets.length > 0) {
      console.error(`❌ Missing buckets: ${missingBuckets.join(', ')}`);
      return false;
    }
    
    console.log('✅ All required storage buckets exist');
    return true;
    
  } catch (err) {
    console.error(`❌ Storage test error: ${err.message}`);
    return false;
  }
}

async function main() {
  console.log('🚀 Testing database operations...');
  console.log(`📍 Supabase URL: ${SUPABASE_URL}`);
  
  let allSuccess = true;

  // Test write operations
  if (!await testWriteOperations()) {
    allSuccess = false;
  }

  // Test storage buckets
  if (!await testStorageBuckets()) {
    allSuccess = false;
  }

  console.log('\n' + '='.repeat(50));
  if (allSuccess) {
    console.log('🎉 All database operations are working correctly!');
    console.log('✅ Task 5: Database Schema Issues - COMPLETED');
  } else {
    console.log('⚠️  Some operations failed. Check the errors above.');
  }
  console.log('='.repeat(50));
}

main().catch(console.error);
