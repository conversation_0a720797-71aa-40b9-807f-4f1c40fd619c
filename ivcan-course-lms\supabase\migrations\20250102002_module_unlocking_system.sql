-- Migration: Module Unlocking System
-- Created at: 2025-01-02
-- Description: Implements progressive module unlocking based on completion

-- =============================================
-- MODULE UNLOCKING FUNCTIONS
-- =============================================

-- Function to check if a user should have access to a module
CREATE OR REPLACE FUNCTION public.check_module_access(
  p_user_id UUID,
  p_module_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  user_role TEXT;
  module_number INTEGER;
  previous_module_id UUID;
  previous_module_completed BOOLEAN := FALSE;
BEGIN
  -- Get user role
  SELECT role INTO user_role
  FROM public.user_roles
  WHERE user_id = p_user_id;

  -- Teachers have access to all modules
  IF user_role = 'teacher' THEN
    RETURN TRUE;
  END IF;

  -- Get the module number
  SELECT m.module_number INTO module_number
  FROM public.modules m
  WHERE m.id = p_module_id;

  -- Module 1 is always accessible
  IF module_number = 1 THEN
    RETURN TRUE;
  END IF;

  -- Find the previous module in the same course
  SELECT m.id INTO previous_module_id
  FROM public.modules m
  WHERE m.course_id = (SELECT course_id FROM public.modules WHERE id = p_module_id)
    AND m.module_number = (module_number - 1);

  -- Check if previous module is completed
  IF previous_module_id IS NOT NULL THEN
    SELECT COALESCE(ump.is_completed, FALSE) INTO previous_module_completed
    FROM public.user_module_progress ump
    WHERE ump.user_id = p_user_id
      AND ump.module_id = previous_module_id;
  END IF;

  RETURN previous_module_completed;
END;
$$;

-- Function to check if a user should have access to a lesson
CREATE OR REPLACE FUNCTION public.check_lesson_access(
  p_user_id UUID,
  p_lesson_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  user_role TEXT;
  module_id UUID;
  lesson_number INTEGER;
  previous_lesson_id UUID;
  previous_lesson_completed BOOLEAN := FALSE;
  module_accessible BOOLEAN := FALSE;
BEGIN
  -- Get user role
  SELECT role INTO user_role
  FROM public.user_roles
  WHERE user_id = p_user_id;

  -- Teachers have access to all lessons
  IF user_role = 'teacher' THEN
    RETURN TRUE;
  END IF;

  -- Get lesson details
  SELECT l.module_id, l.lesson_number INTO module_id, lesson_number
  FROM public.lessons l
  WHERE l.id = p_lesson_id;

  -- Check if the module is accessible
  SELECT public.check_module_access(p_user_id, module_id) INTO module_accessible;

  IF NOT module_accessible THEN
    RETURN FALSE;
  END IF;

  -- First lesson in module is accessible if module is accessible
  IF lesson_number = 1 THEN
    RETURN TRUE;
  END IF;

  -- Find the previous lesson in the same module
  SELECT l.id INTO previous_lesson_id
  FROM public.lessons l
  WHERE l.module_id = module_id
    AND l.lesson_number = (lesson_number - 1);

  -- Check if previous lesson is completed
  IF previous_lesson_id IS NOT NULL THEN
    SELECT COALESCE(ulp.is_completed, FALSE) INTO previous_lesson_completed
    FROM public.user_lesson_progress ulp
    WHERE ulp.user_id = p_user_id
      AND ulp.lesson_id = previous_lesson_id;
  END IF;

  RETURN previous_lesson_completed;
END;
$$;

-- Function to update module lock status based on completion
CREATE OR REPLACE FUNCTION public.update_module_locks_for_user(
  p_user_id UUID,
  p_course_id UUID DEFAULT NULL
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  module_record RECORD;
  should_be_accessible BOOLEAN;
BEGIN
  -- Loop through all modules (optionally filtered by course)
  FOR module_record IN
    SELECT m.id, m.course_id, m.module_number
    FROM public.modules m
    WHERE (p_course_id IS NULL OR m.course_id = p_course_id)
    ORDER BY m.course_id, m.module_number
  LOOP
    -- Check if this module should be accessible
    SELECT public.check_module_access(p_user_id, module_record.id) INTO should_be_accessible;

    -- Update the module's lock status in the modules table
    -- Note: We're not using a user-specific lock field, but the global is_locked field
    -- This approach assumes single-user courses or that we handle locking at the application level
    -- For multi-user scenarios, we'd need a user_module_access table

    -- For now, we'll create/update a user_module_access table to track individual access
    INSERT INTO public.user_module_access (user_id, module_id, has_access, updated_at)
    VALUES (p_user_id, module_record.id, should_be_accessible, NOW())
    ON CONFLICT (user_id, module_id)
    DO UPDATE SET
      has_access = should_be_accessible,
      updated_at = NOW();
  END LOOP;
END;
$$;

-- Create user_module_access table to track individual user access to modules
CREATE TABLE IF NOT EXISTS public.user_module_access (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  module_id UUID REFERENCES public.modules(id) ON DELETE CASCADE,
  has_access BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(user_id, module_id)
);

-- Create user_lesson_access table to track individual user access to lessons
CREATE TABLE IF NOT EXISTS public.user_lesson_access (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  lesson_id UUID REFERENCES public.lessons(id) ON DELETE CASCADE,
  has_access BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()),
  UNIQUE(user_id, lesson_id)
);

-- Enable RLS on the new tables
ALTER TABLE public.user_module_access ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_lesson_access ENABLE ROW LEVEL SECURITY;

-- RLS policies for user_module_access
CREATE POLICY "Users can view their own module access"
  ON public.user_module_access
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own module access"
  ON public.user_module_access
  FOR ALL
  USING (auth.uid() = user_id);

-- RLS policies for user_lesson_access
CREATE POLICY "Users can view their own lesson access"
  ON public.user_lesson_access
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own lesson access"
  ON public.user_lesson_access
  FOR ALL
  USING (auth.uid() = user_id);

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.check_module_access(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.check_lesson_access(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_module_locks_for_user(UUID, UUID) TO authenticated;

-- =============================================
-- TRIGGER FUNCTIONS FOR AUTOMATIC ACCESS UPDATES
-- =============================================

-- Trigger function to update access when lesson progress changes
CREATE OR REPLACE FUNCTION public.update_access_on_lesson_progress()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  course_id_val UUID;
BEGIN
  -- Get the course ID for this lesson
  SELECT m.course_id INTO course_id_val
  FROM public.lessons l
  JOIN public.modules m ON l.module_id = m.id
  WHERE l.id = NEW.lesson_id;

  -- Update module locks for this user and course
  PERFORM public.update_module_locks_for_user(NEW.user_id, course_id_val);

  -- Update lesson access for all lessons in this course
  INSERT INTO public.user_lesson_access (user_id, lesson_id, has_access, updated_at)
  SELECT
    NEW.user_id,
    l.id,
    public.check_lesson_access(NEW.user_id, l.id),
    NOW()
  FROM public.lessons l
  JOIN public.modules m ON l.module_id = m.id
  WHERE m.course_id = course_id_val
  ON CONFLICT (user_id, lesson_id)
  DO UPDATE SET
    has_access = EXCLUDED.has_access,
    updated_at = NOW();

  RETURN NEW;
END;
$$;

-- Trigger function to update access when module progress changes
CREATE OR REPLACE FUNCTION public.update_access_on_module_progress()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  course_id_val UUID;
BEGIN
  -- Get the course ID for this module
  SELECT course_id INTO course_id_val
  FROM public.modules
  WHERE id = NEW.module_id;

  -- Update module locks for this user and course
  PERFORM public.update_module_locks_for_user(NEW.user_id, course_id_val);

  RETURN NEW;
END;
$$;

-- Create triggers
DROP TRIGGER IF EXISTS on_lesson_progress_update_access ON public.user_lesson_progress;
CREATE TRIGGER on_lesson_progress_update_access
  AFTER INSERT OR UPDATE OF is_completed ON public.user_lesson_progress
  FOR EACH ROW
  WHEN (NEW.is_completed = TRUE)
  EXECUTE FUNCTION public.update_access_on_lesson_progress();

DROP TRIGGER IF EXISTS on_module_progress_update_access ON public.user_module_progress;
CREATE TRIGGER on_module_progress_update_access
  AFTER INSERT OR UPDATE OF is_completed ON public.user_module_progress
  FOR EACH ROW
  WHEN (NEW.is_completed = TRUE)
  EXECUTE FUNCTION public.update_access_on_module_progress();

-- Grant permissions for trigger functions
GRANT EXECUTE ON FUNCTION public.update_access_on_lesson_progress() TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_access_on_module_progress() TO authenticated;

-- Add comments
COMMENT ON FUNCTION public.check_module_access(UUID, UUID) IS 'Check if a user should have access to a specific module based on completion rules';
COMMENT ON FUNCTION public.check_lesson_access(UUID, UUID) IS 'Check if a user should have access to a specific lesson based on completion rules';
COMMENT ON FUNCTION public.update_module_locks_for_user(UUID, UUID) IS 'Update module lock status for a user based on their progress';
COMMENT ON FUNCTION public.update_access_on_lesson_progress() IS 'Trigger function to update access when lesson progress changes';
COMMENT ON FUNCTION public.update_access_on_module_progress() IS 'Trigger function to update access when module progress changes';
COMMENT ON TABLE public.user_module_access IS 'Tracks individual user access to modules';
COMMENT ON TABLE public.user_lesson_access IS 'Tracks individual user access to lessons';
