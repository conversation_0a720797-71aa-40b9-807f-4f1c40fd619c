@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;500;600;700&display=swap');
@import './styles/unified-typography.css';
@import './styles/prism.css';
@import './styles/ios-theme.css';
@import './styles/modern-sidebar.css';
@import './styles/floating-sidebar-layout.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --font-sans: 'Poppins', system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;

    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 346.8 77.2% 49.8%;
    --primary-foreground: 355.7 100% 97.3%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --warning: 38 92% 50%;
    --warning-foreground: 48 96% 89%;

    --success: 142 76% 36%;
    --success-foreground: 355.7 100% 97.3%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 346.8 77.2% 49.8%;
    --primary-foreground: 355.7 100% 97.3%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --warning: 38 92% 50%;
    --warning-foreground: 48 96% 89%;

    --success: 142 76% 36%;
    --success-foreground: 355.7 100% 97.3%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
  .theme {
    --animate-accordion-down: accordion-down 0.2s ease-out;
    --animate-accordion-up: accordion-up 0.2s ease-out;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    scroll-behavior: smooth;
    -webkit-tap-highlight-color: transparent;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-sans font-semibold tracking-tight;
  }

  /* Improving focus ring visibility for accessibility */
  :focus-visible {
    @apply outline-none ring-2 ring-primary ring-offset-2 ring-offset-background;
  }

  /* Improved links */
  a {
    @apply transition-colors duration-200;
  }

  /* iOS-style form elements */
  input, textarea, select {
    @apply transition-all duration-200 text-foreground bg-background/80 dark:bg-card/80 backdrop-blur-sm rounded-xl border-input/50;
  }
}

@layer components {
  /* Red theme specific styles */
  .red-theme .text-primary {
    color: #E63946 !important;
  }

  .red-theme .bg-primary {
    background-color: #E63946 !important;
  }

  .red-theme .border-primary {
    border-color: #E63946 !important;
  }

  .red-theme .ring-primary {
    --tw-ring-color: #E63946 !important;
  }

  /* Layout fixes */
  .modern-layout {
    min-height: 100vh;
    display: flex;
    width: 100%;
    position: relative;
  }

  /* Ensure main content is visible */
  main {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
  }

  .glass-card {
    @apply bg-white/80 dark:bg-black/20 backdrop-blur-sm border border-white/20 dark:border-white/10 rounded-xl shadow-sm transition-all duration-200 hover:shadow-md;
  }

  .sidebar-icon {
    @apply w-5 h-5 opacity-90 group-hover:opacity-100 transition-all duration-200;
  }

  .progress-animation {
    @apply transition-all duration-500 ease-in-out;
  }

  .hover-lift {
    @apply transition-all duration-200 hover:-translate-y-1 hover:shadow-md dark:hover:shadow-primary/10;
  }

  .pill {
    @apply px-3 py-1 rounded-full text-xs font-medium;
  }

  /* iOS card styling */
  .card-modern {
    @apply bg-white/80 backdrop-blur-sm border-none rounded-xl shadow-sm hover:shadow-md transition-all duration-300;
  }

  /* Content wrapper for lesson pages */
  .content-wrapper {
    @apply max-w-3xl mx-auto px-4 py-8;
  }

  /* iOS quiz option styling */
  .quiz-option {
    @apply relative border-none rounded-xl p-4 cursor-pointer transition-all duration-200
           bg-background/80 backdrop-blur-sm shadow-sm
           hover:bg-primary/5 focus:outline-none focus:ring-1 focus:ring-primary
           focus:ring-offset-1 focus:ring-offset-background active:scale-[0.98];
  }

  .quiz-option-selected {
    @apply bg-primary/10 shadow-md;
  }

  /* iOS loading spinner */
  .spinner-gradient {
    @apply w-8 h-8 rounded-full animate-spin border-2 border-primary/20;
    border-top-color: hsl(var(--primary));
  }
}

/* iOS-style scrollbar - only apply on non-touch devices */
@media (hover: hover) {
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-primary/20 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-primary/30;
  }
}

/* Module animation effects */
.module-enter {
  opacity: 0;
  transform: translateY(10px);
}

.module-enter-active {
  opacity: 1;
  transform: translateY(0px);
  transition: opacity 300ms, transform 300ms;
}

.module-exit {
  opacity: 1;
}

.module-exit-active {
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 300ms, transform 300ms;
}

/* Add text selection styling */
::selection {
  @apply bg-primary/20 text-foreground;
}

/* Page transitions */
.page-transition-enter {
  opacity: 0;
  transform: translateY(8px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

.page-transition-exit {
  opacity: 1;
}

.page-transition-exit-active {
  opacity: 0;
  transition: opacity 200ms;
}

/* Performance optimizations */
img, video {
  content-visibility: auto;
}

/* iOS focus styles for form elements */
input:focus,
textarea:focus,
select:focus,
button:focus-visible {
  @apply outline-none ring-1 ring-primary ring-offset-1 border-primary;
}

/* iOS button transitions */
button {
  @apply transition-all duration-200 active:scale-[0.98];
}

/* Animation for dropdowns */
@keyframes slide-down {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-down {
  animation: slide-down 0.2s ease-out forwards;
}

/* Smooth shadow transitions */
.shadow-transition {
  @apply transition-shadow duration-300;
}

/* iOS-specific utility classes */
.ios-blur-bg {
  @apply bg-background/80 backdrop-blur-sm;
}

.ios-card {
  @apply bg-card/80 backdrop-blur-sm border-none rounded-xl shadow-sm transition-all duration-200;
}

.ios-button {
  @apply rounded-full font-medium transition-all duration-200 active:scale-[0.98] bg-primary text-white;
}

.ios-input {
  @apply rounded-xl border border-input/50 bg-background/80 backdrop-blur-sm px-4 py-3 transition-all duration-200;
}

.ios-nav {
  @apply bg-background/80 backdrop-blur-sm border-b border-border/50 sticky top-0 z-50;
}

.ios-tab-bar {
  @apply bg-background/80 backdrop-blur-sm border-t border-border/50 fixed bottom-0 left-0 right-0 z-50 flex justify-around items-center py-2;
}

/* Text shadow utilities */
.text-shadow-sm {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
}

.text-shadow-lg {
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
}

/* Certificate signature font */
.font-signature {
  font-family: 'Dancing Script', cursive;
  font-weight: 600;
}

@theme inline {
  @keyframes accordion-down {
  from {
    height: 0;
    }
  to {
    height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
    }
  to {
    height: 0;
    }
  }
}