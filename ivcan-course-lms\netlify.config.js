// netlify.config.js
module.exports = {
  // Build settings
  build: {
    publish: 'dist',
    command: 'npm run build:prod',
    environment: {
      NODE_VERSION: '18'
    }
  },

  // Headers for proper MIME types and caching
  headers: [
    {
      for: '/*',
      values: {
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'X-Content-Type-Options': 'nosniff',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'Cache-Control': 'public, max-age=0, must-revalidate',
        'Access-Control-Allow-Origin': '*'
      }
    },
    {
      for: '/index.html',
      values: {
        'Content-Type': 'text/html; charset=utf-8',
        'Cache-Control': 'no-cache'
      }
    },
    {
      for: '/*.js',
      values: {
        'Content-Type': 'application/javascript; charset=utf-8'
      }
    },
    {
      for: '*.js',
      values: {
        'Content-Type': 'application/javascript; charset=utf-8'
      }
    },
    {
      for: '/*.css',
      values: {
        'Content-Type': 'text/css; charset=utf-8'
      }
    },
    {
      for: '*.css',
      values: {
        'Content-Type': 'text/css; charset=utf-8'
      }
    },
    {
      for: '/assets/*',
      values: {
        'Cache-Control': 'public, max-age=31536000, immutable'
      }
    },
    {
      for: '/assets/*.js',
      values: {
        'Content-Type': 'application/javascript; charset=utf-8'
      }
    },
    {
      for: '/assets/**.js',
      values: {
        'Content-Type': 'application/javascript; charset=utf-8'
      }
    },
    {
      for: '/assets/*.css',
      values: {
        'Content-Type': 'text/css; charset=utf-8'
      }
    },
    {
      for: '/assets/**.css',
      values: {
        'Content-Type': 'text/css; charset=utf-8'
      }
    }
  ],

  // Redirects for SPA routing
  redirects: [
    {
      from: '/*',
      to: '/index.html',
      status: 200
    }
  ]
};