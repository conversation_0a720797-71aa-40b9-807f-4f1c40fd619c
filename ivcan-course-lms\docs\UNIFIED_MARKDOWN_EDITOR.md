# Unified Markdown Editor Documentation

## Overview

The Unified Markdown Editor is a comprehensive, professional-grade TipTap-based editor that consolidates all previous editor implementations into a single, feature-rich component with full GitHub Flavored Markdown support.

## Features

### ✅ Core Features
- **GitHub Flavored Markdown**: Complete GFM support including tables, task lists, strikethrough
- **Live Preview**: Real-time preview with editor, split-pane, and preview-only modes
- **Syntax Highlighting**: Code blocks with syntax highlighting for 20+ languages
- **Image Upload**: Drag & drop image upload with Supabase storage integration
- **Custom Extensions**: Details/summary collapsible sections and callout blocks
- **Multiple Themes**: GitHub, Obsidian, and Minimal themes
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Dark Mode**: Full dark mode support across all themes
- **Accessibility**: ARIA labels, keyboard navigation, and screen reader support

### 🔄 Planned Features
- **LaTeX Math**: Mathematical expressions with KaTeX
- **Mermaid Diagrams**: Flowcharts and diagrams
- **Collaborative Editing**: Real-time collaboration features

## Installation & Usage

### Basic Usage

```tsx
import { UnifiedMarkdownEditor } from '@/components/ui/unified-markdown-editor';

function MyComponent() {
  const [content, setContent] = useState('# Hello World\n\nStart writing...');

  return (
    <UnifiedMarkdownEditor
      initialContent={content}
      onChange={setContent}
      placeholder="Start writing your content..."
      minHeight={400}
      theme="github"
      showToolbar={true}
      showPreview={true}
      mode="editor"
    />
  );
}
```

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `initialContent` | `string` | `''` | Initial markdown content |
| `onChange` | `(markdown: string) => void` | - | Callback when content changes |
| `placeholder` | `string` | `'Start writing...'` | Placeholder text |
| `className` | `string` | `''` | Additional CSS classes |
| `minHeight` | `number` | `400` | Minimum editor height in pixels |
| `autoFocus` | `boolean` | `false` | Auto-focus editor on mount |
| `courseId` | `string` | - | Course ID for image uploads |
| `moduleId` | `string` | - | Module ID for image uploads |
| `showToolbar` | `boolean` | `true` | Show/hide toolbar |
| `showPreview` | `boolean` | `true` | Show/hide preview tabs |
| `mode` | `'editor' \| 'preview' \| 'split'` | `'editor'` | Initial view mode |
| `theme` | `'github' \| 'obsidian' \| 'minimal'` | `'github'` | Editor theme |

## Supported Markdown Features

### Text Formatting
- **Bold**: `**text**` or `__text__`
- *Italic*: `*text*` or `_text_`
- ~~Strikethrough~~: `~~text~~`
- ==Highlight==: `==text==`
- <u>Underline</u>: `<u>text</u>`
- `Inline code`: `` `code` ``

### Headers
```markdown
# H1 Header
## H2 Header
### H3 Header
#### H4 Header
##### H5 Header
###### H6 Header
```

### Lists

#### Bullet Lists
```markdown
- Item 1
- Item 2
  - Nested item
  - Another nested item
- Item 3
```

#### Numbered Lists
```markdown
1. First item
2. Second item
   1. Nested item
   2. Another nested item
3. Third item
```

#### Task Lists
```markdown
- [x] Completed task
- [ ] Incomplete task
- [x] Another completed task
  - [x] Nested completed task
  - [ ] Nested incomplete task
```

### Code Blocks

#### Inline Code
```markdown
Use `console.log()` to debug your code.
```

#### Code Blocks with Syntax Highlighting
````markdown
```javascript
function greetUser(name) {
  console.log(`Hello, ${name}!`);
  return `Welcome!`;
}
```

```python
def calculate_fibonacci(n):
    if n <= 1:
        return n
    return calculate_fibonacci(n-1) + calculate_fibonacci(n-2)
```
````

### Tables
```markdown
| Feature | Status | Notes |
|---------|--------|-------|
| Tables | ✅ Working | Fully responsive |
| Task Lists | ✅ Working | Interactive checkboxes |
| Code Blocks | ✅ Working | Syntax highlighting |
```

### Links and Images
```markdown
[Link text](https://example.com)
![Alt text](https://example.com/image.jpg)
![Alt text with title](https://example.com/image.jpg "Image title")
```

### Blockquotes
```markdown
> This is a blockquote.
> 
> It can span multiple lines and contain **formatted text**.
```

### Horizontal Rules
```markdown
---
```

### Custom Extensions

#### Collapsible Sections (Details/Summary)
```markdown
<details>
<summary>Click to expand</summary>

This content is hidden by default and can be expanded by clicking the summary.

You can include any markdown content here:
- Lists
- **Formatted text**
- `Code`
- And more!

</details>
```

#### Callouts
```markdown
> [!INFO] Information
> This is an informational callout.

> [!WARNING] Warning
> This is a warning callout.

> [!SUCCESS] Success
> This indicates success.

> [!ERROR] Error
> This indicates an error.

> [!TIP] Tip
> This is a helpful tip.
```

## Themes

### GitHub Theme
- Clean, professional GitHub-style editor
- Familiar typography and spacing
- Excellent for documentation and technical writing

### Obsidian Theme
- Obsidian-inspired theme for note-taking
- Enhanced readability with modern styling
- Perfect for knowledge management and research

### Minimal Theme
- Clean, distraction-free minimal theme
- Serif typography for focused writing
- Ideal for long-form content and articles

## Architecture

### File Structure
```
src/
├── components/ui/
│   └── unified-markdown-editor.tsx     # Main editor component
├── lib/
│   ├── tiptap-extensions/
│   │   ├── details.ts                  # Collapsible sections
│   │   └── callout.ts                  # Callout blocks
│   ├── unified-markdown-serializer.ts  # Markdown conversion
│   └── tiptap-image-upload.ts          # Image upload utilities
└── styles/
    └── unified-markdown.css             # Consolidated styles
```

### Key Components

#### UnifiedMarkdownEditor
The main editor component that provides:
- TipTap editor with all extensions
- Toolbar with formatting controls
- Tabbed interface for editor/preview/split modes
- Image upload dialog
- Theme switching

#### Custom Extensions
- **Details**: Collapsible sections with summary/content
- **Callout**: Styled callout blocks for different message types

#### Unified Serializer
- Converts TipTap content to clean Markdown
- Handles all GFM features properly
- Maintains formatting consistency

## Migration Guide

### From Existing Editors

To migrate from existing editor implementations:

1. **Replace imports**:
```tsx
// Old
import { TiptapMarkdownEditor } from '@/components/ui/tiptap-markdown-editor';
import { SimpleMarkdownEditor } from '@/components/ui/simple-markdown-editor';

// New
import { UnifiedMarkdownEditor } from '@/components/ui/unified-markdown-editor';
```

2. **Update props**:
```tsx
// Old TiptapMarkdownEditor props
<TiptapMarkdownEditor
  initialContent={content}
  onChange={setContent}
  placeholder="Start writing..."
  minHeight={400}
  courseId="course-1"
  moduleId="module-1"
/>

// New UnifiedMarkdownEditor (same props work!)
<UnifiedMarkdownEditor
  initialContent={content}
  onChange={setContent}
  placeholder="Start writing..."
  minHeight={400}
  courseId="course-1"
  moduleId="module-1"
  theme="github"
  showToolbar={true}
  showPreview={true}
/>
```

3. **Update CSS imports** (if using custom styles):
```css
/* Remove old imports */
@import './styles/github-markdown.css';
@import './styles/obsidian-markdown.css';
@import './styles/table-fixes.css';
@import './styles/tiptap.css';

/* Use new unified styles */
@import './styles/unified-markdown.css';
```

## Testing

A comprehensive test page is available at `/markdown-editor-test` that demonstrates:
- All markdown features
- Theme switching
- Image upload functionality
- Performance with large documents
- Accessibility features

## Performance

The unified editor is optimized for:
- **Fast rendering**: Efficient DOM updates and minimal re-renders
- **Memory usage**: Proper cleanup and garbage collection
- **Large documents**: Handles documents with thousands of lines
- **Mobile performance**: Optimized for touch devices

## Accessibility

The editor includes:
- **ARIA labels**: Proper labeling for screen readers
- **Keyboard navigation**: Full keyboard support
- **Focus management**: Logical tab order and focus indicators
- **High contrast**: Works with high contrast modes
- **Screen reader support**: Compatible with NVDA, JAWS, and VoiceOver

## Browser Support

- **Chrome**: 90+
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+
- **Mobile browsers**: iOS Safari 14+, Chrome Mobile 90+

## Contributing

When contributing to the unified editor:

1. **Test thoroughly**: Use the test page to verify all features
2. **Check accessibility**: Ensure keyboard navigation and screen reader support
3. **Test themes**: Verify changes work across all themes
4. **Update documentation**: Keep this documentation current
5. **Performance**: Profile changes for performance impact

## Troubleshooting

### Common Issues

1. **Images not uploading**: Check Supabase storage configuration
2. **Styles not applying**: Ensure unified-markdown.css is imported
3. **Preview not updating**: Check onChange callback implementation
4. **Performance issues**: Use React DevTools to profile re-renders

### Debug Mode

Enable debug mode by setting:
```tsx
<UnifiedMarkdownEditor
  // ... other props
  className="debug-mode"
/>
```

This will add visual indicators for debugging layout and styling issues.
