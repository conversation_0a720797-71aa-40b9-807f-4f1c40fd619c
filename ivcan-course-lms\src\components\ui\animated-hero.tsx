import { useEffect, useMemo, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { MoveRight, Users, ArrowRight, GraduationCap, BookOpen, FlaskConical } from "lucide-react";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";

function Hero(): JSX.Element {
  const [titleNumber, setTitleNumber] = useState(0);
  const titles = useMemo(
    () => ["innovative", "evidence-based", "interactive"],
    []
  );

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (titleNumber === titles.length - 1) {
        setTitleNumber(0);
      } else {
        setTitleNumber(titleNumber + 1);
      }
    }, 2000);
    return () => clearTimeout(timeoutId);
  }, [titleNumber, titles]);

  const stats = [
    { icon: Users, value: "Research", label: "Backed Learning" },
    { icon: BookO<PERSON>, value: "Interactive", label: "Course Content" },
    { icon: GraduationCap, value: "Specialized", label: "Medical Training" },
  ];

  return (
    <div className="relative w-full overflow-hidden bg-gradient-to-b from-red-50 via-white to-white dark:from-red-950/20 dark:via-background dark:to-background">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          animate={{
            y: [0, 15, 0],
            opacity: [0.5, 0.7, 0.5]
          }}
          transition={{
            repeat: Infinity,
            duration: 8,
            ease: "easeInOut"
          }}
          className="absolute top-20 right-[10%] w-64 h-64 rounded-full bg-primary/5 blur-3xl"
        />
        <motion.div
          animate={{
            y: [0, -20, 0],
            opacity: [0.3, 0.5, 0.3]
          }}
          transition={{
            repeat: Infinity,
            duration: 10,
            ease: "easeInOut",
            delay: 1
          }}
          className="absolute bottom-20 left-[15%] w-72 h-72 rounded-full bg-red-300/5 blur-3xl"
        />
      </div>
      {/* Background Circles */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -right-5 -top-5 h-72 w-72 rounded-full bg-red-100/50 dark:bg-red-900/20" />
        <div className="absolute -left-10 top-32 h-96 w-96 rounded-full bg-red-50/50 dark:bg-red-900/10" />
      </div>

      <div className="container relative mx-auto px-4">
        <div className="flex min-h-[80vh] flex-col items-center justify-center gap-8 py-20">
          {/* Badge */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="rounded-full bg-red-500/10 px-4 py-2 text-sm font-medium text-red-500 dark:bg-red-500/20 dark:text-red-400 inline-flex items-center">
              <FlaskConical className="mr-2 h-4 w-4" /> Medical Skills Training
            </div>
          </motion.div>

          {/* Main Title */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="flex flex-col gap-6 text-center"
          >
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 dark:text-gray-100 sm:text-6xl md:text-7xl">
              Welcome to
              <br />
              <span className="text-red-500 dark:text-red-400">
                Intravenous Cannulation
              </span>
              <br />
              E-learning Platform
            </h1>

            <p className="mx-auto max-w-2xl text-lg text-gray-600 dark:text-gray-300 sm:text-xl">
              Elevate your intravenous cannulation and contrast media
              administration skills through our specialized e-learning platform for
              medical imaging students.
            </p>

            <p className="mx-auto max-w-2xl text-sm text-gray-500 dark:text-gray-400">
              Part of a pre and post interventional research study by KNUST Diagnostic Radiography
            </p>
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="flex flex-col gap-4 sm:flex-row"
          >
            <Link to="/login">
              <Button
                size="lg"
                className="gap-2 group relative overflow-hidden w-full sm:w-auto rounded-full"
              >
                <span className="relative z-10 flex items-center">Start Learning Now <ArrowRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" /></span>
                <motion.div
                  className="absolute inset-0 bg-white/10"
                  initial={{ opacity: 0 }}
                  whileHover={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                />
              </Button>
            </Link>
            <Link to="/login">
              <Button
                size="lg"
                variant="outline"
                className="gap-2 border-primary/30 hover:bg-primary/10 group w-full sm:w-auto rounded-full"
              >
                <span className="flex items-center">Explore the Platform <MoveRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" /></span>
              </Button>
            </Link>
          </motion.div>

          {/* Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
            className="mt-8 grid grid-cols-1 gap-4 sm:gap-8 sm:grid-cols-3"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                whileHover={{ scale: 1.02, y: -2 }}
                whileTap={{ scale: 0.98 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
                className="flex flex-col items-center gap-2 rounded-lg bg-white dark:bg-gray-800/90 p-5 sm:p-6 shadow-sm hover:shadow-md border border-gray-100 dark:border-gray-700 backdrop-blur-sm transition-all duration-300"
              >
                <stat.icon className="h-8 w-8 text-red-500 dark:text-red-400" />
                <div className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-gray-100">{stat.value}</div>
                <div className="text-sm text-gray-600 dark:text-gray-300 text-center">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </div>
  );
}

export { Hero };
