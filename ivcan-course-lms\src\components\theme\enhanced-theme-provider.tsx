import React, { useEffect } from 'react';
import { createContext, useContext } from 'react';

type Theme = 'dark' | 'light' | 'system';

type ThemeProviderProps = {
  children: React.ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
};

type ThemeProviderState = {
  theme: Theme;
  setTheme: (theme: Theme) => void;
};

const ThemeContext = createContext<ThemeProviderState | undefined>(undefined);

export function useTheme() {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

interface EnhancedThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: 'light' | 'dark' | 'system';
  storageKey?: string;
}

/**
 * Enhanced Theme Provider that ensures consistent theme application
 * across the entire application and handles system preference changes
 *
 * Now prioritizes the red color scheme
 */
export function EnhancedThemeProvider({
  children,
  defaultTheme = 'system',
  storageKey = 'vite-ui-theme',
}: EnhancedThemeProviderProps) {
  const [theme, setTheme] = React.useState<Theme>(
    () => (localStorage.getItem(storageKey) as Theme) || defaultTheme
  );

  React.useEffect(() => {
    const root = window.document.documentElement;

    // Remove initial theme to prevent flash
    root.classList.remove('light', 'dark');

    if (theme === 'system') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)')
        .matches
        ? 'dark'
        : 'light';

      root.classList.add(systemTheme);

      // Store the actual theme in a data attribute for other components to reference
      root.dataset.actualTheme = systemTheme;

      // Force update CSS variables based on the actual theme
      if (systemTheme === 'dark') {
        document.body.classList.add('dark-mode');
      } else {
        document.body.classList.remove('dark-mode');
      }

      return;
    }

    root.classList.add(theme);
    root.dataset.actualTheme = theme;

    // Force update CSS variables based on the theme
    if (theme === 'dark') {
      document.body.classList.add('dark-mode');
    } else {
      document.body.classList.remove('dark-mode');
    }
  }, [theme]);

  React.useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleChange = () => {
      if (theme === 'system') {
        const root = window.document.documentElement;
        const newTheme = mediaQuery.matches ? 'dark' : 'light';

        root.classList.remove('light', 'dark');
        root.classList.add(newTheme);

        // Update data attribute
        root.dataset.actualTheme = newTheme;

        // Force update CSS variables
        if (newTheme === 'dark') {
          document.body.classList.add('dark-mode');
        } else {
          document.body.classList.remove('dark-mode');
        }
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [theme]);

  // Enhanced mobile theme handling
  React.useEffect(() => {
    const root = document.documentElement;
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    const isDark = root.classList.contains('dark');

    // Update theme color for mobile browsers
    if (metaThemeColor) {
      metaThemeColor.setAttribute(
        'content',
        isDark ? '#0C0C0C' : '#FFFFFF'
      );
    }

    // Apply smoother transitions
    root.style.setProperty('--theme-transition-duration', '0.2s');

    // Disable transitions during initial load
    const disableTransitions = () => {
      root.style.setProperty('--theme-transition-duration', '0s');
    };

    // Re-enable transitions after a short delay
    const enableTransitions = () => {
      root.style.setProperty('--theme-transition-duration', '0.2s');
    };

    window.addEventListener('pageshow', disableTransitions);
    const timeoutId = setTimeout(enableTransitions, 100);

    return () => {
      window.removeEventListener('pageshow', disableTransitions);
      clearTimeout(timeoutId);
    };
  }, [theme]);

  const value = React.useMemo(
    () => ({
      theme,
      setTheme: (theme: Theme) => {
        localStorage.setItem(storageKey, theme);
        setTheme(theme);
      },
    }),
    [theme, storageKey]
  );

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>;
}

/**
 * Internal component that handles theme application and system preference changes
 */
function ThemeApplier({ children }: { children: React.ReactNode }) {
  const { theme, setTheme } = useTheme();

  // Apply theme to document element and handle system preference changes
  useEffect(() => {
    const root = document.documentElement;

    // Remove any existing theme classes
    root.classList.remove('light', 'dark');

    // Apply current theme
    if (theme === 'system') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches
        ? 'dark'
        : 'light';
      root.classList.add(systemTheme);

      // Listen for system preference changes
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = (e: MediaQueryListEvent) => {
        const newTheme = e.matches ? 'dark' : 'light';
        root.classList.remove('light', 'dark');
        root.classList.add(newTheme);
      };

      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    } else {
      root.classList.add(theme);
    }
  }, [theme]);

  // Apply additional theme-specific styles
  useEffect(() => {
    const applyThemeStyles = () => {
      const root = document.documentElement;
      const isDark = root.classList.contains('dark');

      // Apply theme-specific meta theme color for mobile browsers
      const metaThemeColor = document.querySelector('meta[name="theme-color"]');
      if (metaThemeColor) {
        metaThemeColor.setAttribute(
          'content',
          isDark ? '#1C1C1C' : '#FFFFFF'
        );
      }

      // Apply smooth transitions when theme changes
      root.style.setProperty('--theme-transition', 'background-color 0.2s ease, color 0.2s ease');

      // Remove transition after theme change to prevent transition on page load
      const timeout = setTimeout(() => {
        root.style.removeProperty('--theme-transition');
      }, 200);

      return () => clearTimeout(timeout);
    };

    applyThemeStyles();
  }, [theme]);

  return <>{children}</>;
}
