var p=Object.defineProperty;var i=Object.getOwnPropertySymbols;var m=Object.prototype.hasOwnProperty,g=Object.prototype.propertyIsEnumerable;var f=(t,r,e)=>r in t?p(t,r,{enumerable:!0,configurable:!0,writable:!0,value:e}):t[r]=e,o=(t,r)=>{for(var e in r||(r={}))m.call(r,e)&&f(t,e,r[e]);if(i)for(var e of i(r))g.call(r,e)&&f(t,e,r[e]);return t};var l=(t,r)=>{var e={};for(var a in t)m.call(t,a)&&r.indexOf(a)<0&&(e[a]=t[a]);if(t!=null&&i)for(var a of i(t))r.indexOf(a)<0&&g.call(t,a)&&(e[a]=t[a]);return e};import{r as d,j as n}from"./vendor-react.BcAa1DKr.js";import{a3 as u}from"./vendor.DQpuTRuB.js";import{c}from"./index.BLDhDn0D.js";const x=u("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),b=d.forwardRef((s,a)=>{var v=s,{className:t,variant:r}=v,e=l(v,["className","variant"]);return n.jsx("div",o({ref:a,role:"alert",className:c(x({variant:r}),t)},e))});b.displayName="Alert";const A=d.forwardRef((a,e)=>{var s=a,{className:t}=s,r=l(s,["className"]);return n.jsx("h5",o({ref:e,className:c("mb-1 font-medium leading-none tracking-tight",t)},r))});A.displayName="AlertTitle";const N=d.forwardRef((a,e)=>{var s=a,{className:t}=s,r=l(s,["className"]);return n.jsx("div",o({ref:e,className:c("text-sm [&_p]:leading-relaxed",t)},r))});N.displayName="AlertDescription";export{b as A,A as a,N as b};
