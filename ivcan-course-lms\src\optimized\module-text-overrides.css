/* Module Text Overrides - Making text smaller and more professional */

/* Module title - smaller */

.dark .module-title {
  color: #e0e0e0 !important;
}

/* Module info text (lessons count and time) - much smaller */

.text-gray-500,
.flex.items-center.gap-3,
.flex.items-center.gap-4.text-gray-500 {
  font-size: 0.65rem !important;
  line-height: 1.3 !important;
  color: #666 !important;
}

.dark .module-info,
.dark .text-gray-500,
.dark .flex.items-center.gap-3,
.dark .flex.items-center.gap-4.text-gray-500 {
  color: #999 !important;
}

/* Module icons - smaller */

.text-gray-500 svg,
.flex.items-center.gap-3 svg,
.flex.items-center.gap-4.text-gray-500 svg {
  width: 0.65rem !important;
  height: 0.65rem !important;
  margin-right: 0.125rem !important;
  color: #888 !important;
}

.dark .module-info svg,
.dark .text-gray-500 svg,
.dark .flex.items-center.gap-3 svg,
.dark .flex.items-center.gap-4.text-gray-500 svg {
  color: #aaa !important;
}

/* Module dropdown icon - smaller */

/* Lesson title - smaller */

.dark .lesson-title {
  color: #d1d5db !important;
}

/* Lesson meta - smaller */

.flex.items-center.gap-3.mt-1,
.flex.items-center.gap-3.text-gray-500 {
  font-size: 0.65rem !important;
  line-height: 1.3 !important;
  color: #777 !important;
}

.dark .lesson-meta,
.dark .flex.items-center.gap-1\.5,
.dark .flex.items-center.gap-3.mt-1,
.dark .flex.items-center.gap-3.text-gray-500 {
  color: #9ca3af !important;
}

/* Lesson meta icons - smaller */

.flex.items-center.gap-3.mt-1 svg,
.flex.items-center.gap-3.text-gray-500 svg {
  width: 0.65rem !important;
  height: 0.65rem !important;
  margin-right: 0.125rem !important;
  color: #888 !important;
}

.dark .lesson-meta svg,
.dark .flex.items-center.gap-1\.5 svg,
.dark .flex.items-center.gap-3.mt-1 svg,
.dark .flex.items-center.gap-3.text-gray-500 svg {
  color: #aaa !important;
}

/* Module number - smaller */

/* Extra small devices */
@media (max-width: 380px) {
  
  
  .text-gray-500,
  .flex.items-center.gap-3,
  .flex.items-center.gap-4.text-gray-500 {
    font-size: 0.625rem !important;
  }
  
  
  .flex.items-center.gap-3.mt-1,
  .flex.items-center.gap-3.text-gray-500 {
    font-size: 0.625rem !important;
  }
}

/* Very small devices */
@media (max-width: 320px) {
  
  
  .text-gray-500,
  .flex.items-center.gap-3,
  .flex.items-center.gap-4.text-gray-500 {
    font-size: 0.6rem !important;
  }
  
  
  .flex.items-center.gap-3.mt-1,
  .flex.items-center.gap-3.text-gray-500 {
    font-size: 0.6rem !important;
  }
} 