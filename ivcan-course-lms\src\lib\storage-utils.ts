import { supabase } from '@/integrations/supabase/client';
import { fileToDataUrl } from './file-upload';

/**
 * Ensures that a storage bucket exists, creating it if necessary
 * @param bucketName The name of the bucket to ensure exists
 * @param isPublic Whether the bucket should be public (default: true)
 * @returns The name of the bucket that can be used (may fall back to a default bucket)
 */
export async function ensureBucketExists(
  bucketName: string,
  isPublic: boolean = true
): Promise<string> {
  try {
    // Check if the bucket already exists
    console.log(`Checking if bucket '${bucketName}' exists...`);
    const { data: buckets, error } = await supabase.storage.listBuckets();

    if (error) {
      console.error('Error listing buckets:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));

      // Check if this is a permissions issue
      if (error.message?.includes('permission') || error.code === '42501') {
        console.warn('Permission denied when listing buckets. This might be an issue with your Supabase configuration.');
        // Try to use the bucket directly without checking if it exists
        return bucketName;
      }

      return 'course-images'; // Fall back to default bucket
    }

    console.log('Available buckets:', buckets?.map(b => b.name).join(', ') || 'none');

    // If the bucket already exists, return its name
    if (buckets && buckets.some(b => b.name === bucketName)) {
      console.log(`Bucket '${bucketName}' already exists.`);
      return bucketName;
    }

    // If 'course-images' exists, use that as a fallback
    if (buckets && buckets.some(b => b.name === 'course-images')) {
      console.log(`Using existing 'course-images' bucket as fallback.`);
      return 'course-images';
    }

    // Try to create the bucket
    console.log(`Attempting to create bucket '${bucketName}'...`);
    const { data: newBucket, error: createError } = await supabase.storage.createBucket(bucketName, {
      public: isPublic
    });

    if (createError) {
      console.error(`Error creating bucket '${bucketName}':`, createError);
      console.error('Error details:', JSON.stringify(createError, null, 2));

      // Check if this is a permissions issue
      if (createError.message?.includes('permission') || createError.code === '42501') {
        console.warn('Permission denied when creating bucket. This might be an issue with your Supabase configuration.');
        // Try to use the bucket directly without creating it
        // It might already exist but the user doesn't have permission to list or create buckets
        return bucketName;
      }

      // If we can't create the requested bucket, try to create a default one
      if (bucketName !== 'app-uploads') {
        console.log(`Attempting to create fallback bucket 'app-uploads'...`);
        const { error: defaultCreateError } = await supabase.storage.createBucket('app-uploads', {
          public: isPublic
        });

        if (!defaultCreateError) {
          console.log(`Successfully created fallback bucket 'app-uploads'.`);
          return 'app-uploads';
        } else {
          console.error(`Error creating fallback bucket 'app-uploads':`, defaultCreateError);
        }
      }

      // If all else fails, return the original bucket name and let the caller handle the error
      console.log(`Using original bucket name '${bucketName}' despite creation failure.`);
      return bucketName;
    }

    // Successfully created the bucket
    console.log(`Successfully created bucket '${bucketName}'.`);
    return bucketName;
  } catch (error) {
    console.error('Unexpected error ensuring bucket exists:', error);
    console.error('Error details:', error instanceof Error ? error.stack : JSON.stringify(error, null, 2));
    return bucketName; // Return the original bucket name instead of falling back
  }
}

/**
 * Uploads a file to Supabase storage, ensuring the bucket exists first
 * @param bucketName The name of the bucket to upload to
 * @param filePath The path within the bucket where the file should be stored
 * @param file The file to upload
 * @returns The public URL of the uploaded file
 */
export async function uploadToStorage(
  bucketName: string,
  filePath: string,
  file: File
): Promise<string> {
  try {
    console.log(`Starting upload to bucket '${bucketName}', file: '${filePath}'`);

    // Ensure the bucket exists
    let actualBucketName;
    try {
      actualBucketName = await ensureBucketExists(bucketName);
      console.log(`Using bucket '${actualBucketName}' for upload`);
    } catch (error) {
      console.error('Error ensuring bucket exists:', error);
      // Use the original bucket name as fallback
      actualBucketName = bucketName;
      console.log(`Falling back to original bucket name '${actualBucketName}'`);
    }

    // Upload the file
    console.log(`Uploading file '${filePath}' to bucket '${actualBucketName}'...`);

    try {
      // First attempt with standard options
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from(actualBucketName)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: true // Overwrite if file exists
        });

      if (uploadError) {
        console.error(`Error uploading file to '${actualBucketName}':`, uploadError);
        console.error('Upload error details:', JSON.stringify(uploadError, null, 2));

        // Check if this is a "bucket not found" error
        if (uploadError.message?.includes('bucket') && uploadError.message?.includes('not found')) {
          console.log('Bucket not found error detected. Trying to create the bucket...');

          // Try to create the bucket explicitly
          const { error: createBucketError } = await supabase.storage.createBucket(actualBucketName, {
            public: true
          });

          if (createBucketError) {
            console.error(`Error creating bucket '${actualBucketName}':`, createBucketError);

            // Try with a different bucket
            const fallbackBucket = 'app-uploads';
            console.log(`Trying fallback bucket '${fallbackBucket}'...`);

            // Try to create the fallback bucket
            await supabase.storage.createBucket(fallbackBucket, { public: true }).catch(e => {
              console.log(`Error creating fallback bucket: ${e.message}`);
            });

            // Try uploading to the fallback bucket
            const { data: fallbackData, error: fallbackError } = await supabase.storage
              .from(fallbackBucket)
              .upload(filePath, file, { upsert: true });

            if (fallbackError) {
              console.error(`Error uploading to fallback bucket:`, fallbackError);

              // Convert to data URL as last resort
              return await fileToDataUrl(file);
            } else {
              // Get the public URL from the fallback bucket
              const { data: { publicUrl } } = supabase.storage
                .from(fallbackBucket)
                .getPublicUrl(filePath);

              console.log(`Generated public URL from fallback bucket: ${publicUrl}`);
              return publicUrl;
            }
          } else {
            console.log(`Successfully created bucket '${actualBucketName}'. Retrying upload...`);

            // Retry the upload with the newly created bucket
            const { data: retryData, error: retryError } = await supabase.storage
              .from(actualBucketName)
              .upload(filePath, file, { upsert: true });

            if (retryError) {
              console.error(`Error on retry upload:`, retryError);
              // Convert to data URL as last resort
              return await fileToDataUrl(file);
            } else {
              // Get the public URL
              const { data: { publicUrl } } = supabase.storage
                .from(actualBucketName)
                .getPublicUrl(filePath);

              console.log(`Generated public URL after bucket creation: ${publicUrl}`);
              return publicUrl;
            }
          }
        } else {
          // For other types of errors, try a simpler upload approach
          console.log('Trying simplified upload approach...');

          const { data: simpleData, error: simpleError } = await supabase.storage
            .from(actualBucketName)
            .upload(filePath, file, { upsert: true });

          if (simpleError) {
            console.error(`Simplified upload also failed:`, simpleError);
            // Convert to data URL as last resort
            return await fileToDataUrl(file);
          } else {
            // Get the public URL
            const { data: { publicUrl } } = supabase.storage
              .from(actualBucketName)
              .getPublicUrl(filePath);

            console.log(`Generated public URL with simplified approach: ${publicUrl}`);
            return publicUrl;
          }
        }
      }

      console.log(`File uploaded successfully:`, uploadData);

      // Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from(actualBucketName)
        .getPublicUrl(filePath);

      console.log(`Generated public URL: ${publicUrl}`);
      return publicUrl;
    } catch (uploadError) {
      console.error('Error during upload process:', uploadError);
      // Convert to data URL as last resort
      return await fileToDataUrl(file);
    }
  } catch (error) {
    console.error('Unexpected error in uploadToStorage:', error);
    console.error('Error details:', error instanceof Error ? error.stack : JSON.stringify(error, null, 2));
    throw error;
  }
}
