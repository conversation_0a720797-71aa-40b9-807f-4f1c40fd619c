import React, { useState } from 'react';
import { SimpleMarkdownEditor } from '@/components/ui/simple-markdown-editor';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Copy, Download } from 'lucide-react';
import { toast } from 'sonner';

const EditorDemo: React.FC = () => {
  const [markdownContent, setMarkdownContent] = useState(`# Simple Markdown Editor

This is a **clean** and *simple* markdown editor that focuses on essential features.

## What You Can Do

### Text Formatting
- **Bold text** - Use the B button or **text**
- *Italic text* - Use the I button or *text*
- \`Inline code\` - Use the code button or \`code\`

### Lists
1. Numbered lists
2. Easy to create
3. Just click the numbered list button

- Bullet points
- Also simple
- Click the list button

### Links and Images

Add links easily: [Example Link](https://example.com)

Click the image button to upload or add image URLs:

![Sample Image](https://via.placeholder.com/400x200/3B82F6/FFFFFF?text=Simple+Editor)

### Tables

Click the table button to create tables:

| Feature | Simple | Functional |
| --- | --- | --- |
| **Bold/Italic** | ✅ | ✅ |
| **Lists** | ✅ | ✅ |
| **Images** | ✅ | ✅ |
| **Tables** | ✅ | ✅ |
| **Preview** | ✅ | ✅ |

## Why Simple is Better

1. **Easy to Use** - Only essential buttons
2. **Fast** - No complex features to slow you down
3. **Clean** - Focused interface
4. **Functional** - Everything you need for lessons

Try editing this content - it's that simple!`);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(markdownContent);
      toast.success('Markdown copied to clipboard!');
    } catch (err) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const downloadMarkdown = () => {
    const blob = new Blob([markdownContent], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'lesson-content.md';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success('Markdown file downloaded!');
  };

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-foreground">
            Simple Markdown Editor
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            A clean, simple, and functional markdown editor for creating lesson content.
            Easy to use with essential features only.
          </p>
          <div className="flex flex-wrap justify-center gap-2">
            <Badge variant="secondary">Simple & Clean</Badge>
            <Badge variant="secondary">Essential Features</Badge>
            <Badge variant="secondary">Image Upload</Badge>
            <Badge variant="secondary">Table Support</Badge>
            <Badge variant="secondary">Live Preview</Badge>
            <Badge variant="secondary">Markdown Output</Badge>
          </div>
        </div>

        {/* Editor Section */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Lesson Content Editor</CardTitle>
                <CardDescription>
                  Edit your lesson content using the rich text editor. The content is automatically 
                  converted to Markdown format for storage and portability.
                </CardDescription>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={copyToClipboard}
                  className="flex items-center gap-2"
                >
                  <Copy className="h-4 w-4" />
                  Copy Markdown
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={downloadMarkdown}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  Download
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <SimpleMarkdownEditor
              initialContent={markdownContent}
              onChange={setMarkdownContent}
              placeholder="Start writing your lesson content here..."
              minHeight={500}
              courseId="demo-course"
              moduleId="demo-module"
            />
          </CardContent>
        </Card>

        {/* Features Section */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Simple & Clean</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Only essential formatting buttons. No clutter, no confusion.
                Just what you need to create great content.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Essential Features</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Bold, italic, code, lists, links, images, and tables.
                Everything you need for lesson content.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Live Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Switch between Write and Preview modes to see exactly how
                your content will look to students.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Image Upload</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Click the image button to upload files or add URLs.
                Simple and straightforward.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Table Support</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Click the table button to insert a 3x3 table.
                Perfect for organizing information.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Markdown Output</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                All content is saved as clean Markdown that works everywhere.
                Portable and future-proof.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>How to Use</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold mb-2">Write Mode</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Click toolbar buttons for formatting</li>
                  <li>• B = Bold, I = Italic, Code = Inline code</li>
                  <li>• List buttons for bullet and numbered lists</li>
                  <li>• Link, Image, and Table buttons for media</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Preview Mode</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Click "Preview" tab to see final result</li>
                  <li>• Clean styling applied automatically</li>
                  <li>• Tables and images display properly</li>
                  <li>• Links are clickable in preview</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default EditorDemo;
