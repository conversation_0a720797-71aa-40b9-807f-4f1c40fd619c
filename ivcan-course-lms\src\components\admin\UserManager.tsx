
import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Loader2, User<PERSON>lus, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { getPromotableUsers, promoteUserToTeacher, UserWithRole } from '@/services/course/roleApi';
import { useToast } from '@/hooks/use-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

const UserManager: React.FC = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [userToPromote, setUserToPromote] = useState<{ id: string; name: string } | null>(null);
  
  // Fetch users who can be promoted to teacher
  const { data: users, isLoading, refetch } = useQuery({
    queryKey: ['promotable-users'],
    queryFn: getPromotableUsers,
  });
  
  // Handle promoting user to teacher
  const promoteMutation = useMutation({
    mutationFn: (userId: string) => promoteUserToTeacher(userId),
    onSuccess: (success) => {
      if (success) {
        queryClient.invalidateQueries({ queryKey: ['promotable-users'] });
        setUserToPromote(null);
      }
    },
    onError: (error) => {
      console.error('Error promoting user:', error);
      toast({
        title: 'Error',
        description: 'Failed to promote user. Please try again.',
        variant: 'destructive',
      });
      setUserToPromote(null);
    },
  });
  
  // Display user's name or email
  const getDisplayName = (user: UserWithRole) => {
    if (user.first_name && user.last_name) {
      return `${user.first_name} ${user.last_name}`;
    }
    return user.id;
  };
  
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-xl">User Management</CardTitle>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={() => refetch()}
          disabled={isLoading}
        >
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4" />
          )}
        </Button>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
          </div>
        ) : !users || users.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No users available to promote to teacher role.
          </div>
        ) : (
          <div className="space-y-2">
            <div className="mb-4">
              <h3 className="text-sm font-medium mb-2">Promote Users to Teacher</h3>
              <p className="text-xs text-gray-500">
                Teachers can create courses, lessons, and manage other teaching functions.
              </p>
            </div>
            
            <div className="grid gap-2">
              {users.map((user) => (
                <div 
                  key={user.id} 
                  className="flex items-center justify-between p-3 border rounded-md hover:bg-gray-50"
                >
                  <div>
                    <div className="font-medium">{getDisplayName(user)}</div>
                    <div className="text-xs text-gray-500">ID: {user.id}</div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setUserToPromote({ id: user.id, name: getDisplayName(user) })}
                    className="flex items-center gap-1"
                  >
                    <UserPlus className="h-3.5 w-3.5" />
                    Promote to Teacher
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}
        
        <AlertDialog 
          open={!!userToPromote} 
          onOpenChange={(open) => !open && setUserToPromote(null)}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Promote to Teacher Role</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to promote <span className="font-medium">{userToPromote?.name}</span> to a teacher role? 
                They will be able to create and manage courses, lessons, and other educational content.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={() => {
                  if (userToPromote) {
                    promoteMutation.mutate(userToPromote.id);
                  }
                }}
                disabled={promoteMutation.isPending}
              >
                {promoteMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Promoting...
                  </>
                ) : (
                  'Confirm Promotion'
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardContent>
    </Card>
  );
};

export default UserManager;
