var x=Object.defineProperty,u=Object.defineProperties;var y=Object.getOwnPropertyDescriptors;var l=Object.getOwnPropertySymbols;var f=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable;var t=(a,r,s)=>r in a?x(a,r,{enumerable:!0,configurable:!0,writable:!0,value:s}):a[r]=s,n=(a,r)=>{for(var s in r||(r={}))f.call(r,s)&&t(a,s,r[s]);if(l)for(var s of l(r))i.call(r,s)&&t(a,s,r[s]);return a},c=(a,r)=>u(a,y(r));var m=(a,r)=>{var s={};for(var e in a)f.call(a,e)&&r.indexOf(e)<0&&(s[e]=a[e]);if(a!=null&&l)for(var e of l(a))r.indexOf(e)<0&&i.call(a,e)&&(s[e]=a[e]);return s};import{r as h,j as d,ce as p,cf as j}from"./vendor-react.BcAa1DKr.js";import{c as w}from"./index.BLDhDn0D.js";const N=h.forwardRef((g,e)=>{var o=g,{className:a,value:r}=o,s=m(o,["className","value"]);return d.jsx(p,c(n({ref:e,className:w("relative h-4 w-full overflow-hidden rounded-full bg-secondary",a)},s),{children:d.jsx(j,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(r||0)}%)`}})}))});N.displayName=p.displayName;export{N as P};
