import React, { useState } from 'react';
import { useOfflineQueue } from '@/hooks/useOfflineQueue';
import { Button } from './ui/button';
import { QueueListIcon } from '@heroicons/react/24/outline';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { ScrollArea } from './ui/scroll-area';
import { toast } from 'sonner';

/**
 * Component that displays the offline queue status and allows processing the queue
 */
const OfflineQueueStatus: React.FC = () => {
  const { queueSize, isOnline, processQueue, getQueue } = useOfflineQueue();
  const [isOpen, setIsOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  // Hide if queue is empty
  if (queueSize === 0) return null;

  // Handle processing the queue
  const handleProcessQueue = async () => {
    if (!isOnline) {
      toast.error('Cannot process queue while offline');
      return;
    }

    setIsProcessing(true);
    try {
      const result = await processQueue();
      toast.success(`Processed ${result.successful} operations`);
      if (result.failed > 0) {
        toast.error(`Failed to process ${result.failed} operations`);
      }
      
      // Close the dialog if all operations were processed successfully
      if (result.successful > 0 && getQueue().length === 0) {
        setIsOpen(false);
      }
    } catch (error) {
      console.error('Error processing queue:', error);
      toast.error('Failed to process operations queue');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button 
          size="sm" 
          variant="outline" 
          className="fixed bottom-4 left-4 z-50 flex items-center gap-2"
        >
          <QueueListIcon className="h-4 w-4" />
          <span>Pending Operations: {queueSize}</span>
        </Button>
      </DialogTrigger>
      
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Offline Operations Queue</DialogTitle>
        </DialogHeader>
        
        <div className="mt-4 space-y-4">
          <p className="text-sm text-muted-foreground">
            You have {queueSize} operation{queueSize !== 1 ? 's' : ''} waiting to be processed.
            {!isOnline && ' You need to be online to process the queue.'}
          </p>
          
          <ScrollArea className="h-60 rounded-md border p-4">
            <div className="space-y-2">
              {getQueue().map((item) => (
                <div 
                  key={item.id} 
                  className="rounded-md bg-muted p-2 text-xs"
                >
                  <div className="font-medium">{item.operation}</div>
                  <div className="mt-1 text-muted-foreground">
                    {new Date(item.timestamp).toLocaleString()}
                  </div>
                  {item.retries > 0 && (
                    <div className="mt-1 text-destructive">
                      Failed attempts: {item.retries}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </ScrollArea>
          
          <div className="flex justify-end gap-2">
            <Button 
              variant="outline"
              onClick={() => setIsOpen(false)}
            >
              Close
            </Button>
            <Button 
              onClick={handleProcessQueue}
              disabled={!isOnline || isProcessing}
            >
              {isProcessing ? 'Processing...' : 'Process Queue'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default OfflineQueueStatus; 