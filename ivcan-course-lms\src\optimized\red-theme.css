/* Red Theme Styles */

:root {
  /* Red color palette */
  --red-50: #fef2f2;
  --red-100: #fee2e2;
  --red-200: #fecaca;
  --red-300: #fca5a5;
  --red-400: #f87171;
  --red-500: #ef4444;
  --red-600: #dc2626;
  --red-700: #b91c1c;
  --red-800: #991b1b;
  --red-900: #7f1d1d;

  /* Primary red color */
  --primary-red: #E63946;
  --primary-red-light: #f87171;
  --primary-red-dark: #c1121f;

  /* Red sidebar color */
  --red-sidebar: #E63946;
  --red-sidebar-dark: #c1121f;
}

/* Force red theme for specific elements */
.text-primary {
  color: var(--primary-red) !important;
}

.bg-primary {
  background-color: var(--primary-red) !important;
}

.border-primary {
  border-color: var(--primary-red) !important;
}

.ring-primary {
  --tw-ring-color: var(--primary-red) !important;
}

/* Buttons */

button[class*="primary"],
[type="button"][class*="primary"],
[type="submit"][class*="primary"] {
  background-color: var(--primary-red) !important;
  border-color: var(--primary-red) !important;
}

/* Links */
a:hover {
  color: var(--primary-red) !important;
}

/* Focus states */
*:focus-visible {
  outline-color: var(--primary-red) !important;
}

/* Specific components */
.spinner {
  border-top-color: var(--primary-red) !important;
}

/* Badges and indicators */

/* Progress bars */
progress::-webkit-progress-value {
  background-color: var(--primary-red) !important;
}

progress::-moz-progress-bar {
  background-color: var(--primary-red) !important;
}

/* Dark mode adjustments */
.dark .text-primary {
  color: var(--primary-red-light) !important;
}

.dark .bg-primary {
  background-color: var(--primary-red-dark) !important;
}

/* Sidebar styles */
aside, [class*="sidebar"], .bg-sidebar {
  background-color: #E63946 !important;
}

.dark aside, .dark [class*="sidebar"], .dark .bg-sidebar {
  background-color: #c1121f !important;
}

/* Sidebar navigation items */
aside[class*="sidebar"] nav a {
  color: white !important;
}

aside[class*="sidebar"] nav a:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

aside[class*="sidebar"] nav a.active {
  background-color: rgba(255, 255, 255, 0.15) !important;
}

/* Sidebar text */
aside[class*="sidebar"] * {
  color: white !important;
}

/* Sidebar borders */
aside[class*="sidebar"] [class*="border"] {
  border-color: rgba(255, 255, 255, 0.1) !important;
}

/* Hover states */
.hover\:text-primary:hover {
  color: var(--primary-red) !important;
}

.hover\:bg-primary:hover {
  background-color: var(--primary-red) !important;
}

/* Active states */
.active\:text-primary:active {
  color: var(--primary-red) !important;
}

.active\:bg-primary:active {
  background-color: var(--primary-red) !important;
}
