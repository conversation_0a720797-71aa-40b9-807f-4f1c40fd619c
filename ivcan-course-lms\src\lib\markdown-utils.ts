/**
 * Utility functions for advanced markdown operations
 */

export interface MarkdownValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

export interface MarkdownStats {
  wordCount: number;
  characterCount: number;
  paragraphCount: number;
  headingCount: number;
  listCount: number;
  imageCount: number;
  linkCount: number;
  codeBlockCount: number;
  tableCount: number;
  readingTimeMinutes: number;
}

/**
 * Validates markdown content for common issues
 */
export function validateMarkdown(content: string): MarkdownValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!content || content.trim().length === 0) {
    errors.push('Content is empty');
    return { valid: false, errors, warnings };
  }

  // Check for unmatched brackets
  const openBrackets = (content.match(/\[/g) || []).length;
  const closeBrackets = (content.match(/\]/g) || []).length;
  if (openBrackets !== closeBrackets) {
    errors.push('Unmatched square brackets detected');
  }

  // Check for unmatched parentheses in links
  const openParens = (content.match(/\(/g) || []).length;
  const closeParens = (content.match(/\)/g) || []).length;
  if (openParens !== closeParens) {
    warnings.push('Unmatched parentheses detected - may affect links');
  }

  // Check for malformed links
  const malformedLinks = content.match(/\[([^\]]*)\]\([^)]*$/gm);
  if (malformedLinks) {
    errors.push(`${malformedLinks.length} malformed link(s) detected`);
  }

  // Check for malformed images
  const malformedImages = content.match(/!\[([^\]]*)\]\([^)]*$/gm);
  if (malformedImages) {
    errors.push(`${malformedImages.length} malformed image(s) detected`);
  }

  // Check for empty links
  const emptyLinks = content.match(/\[([^\]]*)\]\(\s*\)/g);
  if (emptyLinks) {
    warnings.push(`${emptyLinks.length} empty link(s) detected`);
  }

  // Check for empty alt text in images
  const emptyAltImages = content.match(/!\[\s*\]\([^)]+\)/g);
  if (emptyAltImages) {
    warnings.push(`${emptyAltImages.length} image(s) without alt text detected`);
  }

  // Check for unclosed code blocks
  const codeBlockMarkers = (content.match(/```/g) || []).length;
  if (codeBlockMarkers % 2 !== 0) {
    errors.push('Unclosed code block detected');
  }

  // Check for malformed tables
  const tableRows = content.match(/^\|.*\|$/gm);
  if (tableRows) {
    const tableSeparators = content.match(/^\|[-\s|:]+\|$/gm);
    if (tableRows.length > 0 && !tableSeparators) {
      warnings.push('Table detected without proper header separator');
    }
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Calculates statistics for markdown content
 */
export function getMarkdownStats(content: string): MarkdownStats {
  if (!content) {
    return {
      wordCount: 0,
      characterCount: 0,
      paragraphCount: 0,
      headingCount: 0,
      listCount: 0,
      imageCount: 0,
      linkCount: 0,
      codeBlockCount: 0,
      tableCount: 0,
      readingTimeMinutes: 0
    };
  }

  // Remove code blocks and inline code for word counting
  const contentWithoutCode = content
    .replace(/```[\s\S]*?```/g, '')
    .replace(/`[^`]+`/g, '');

  // Word count (excluding code)
  const words = contentWithoutCode
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 0);
  const wordCount = words.length;

  // Character count
  const characterCount = content.length;

  // Paragraph count (non-empty lines that aren't headings, lists, etc.)
  const paragraphs = content
    .split('\n')
    .filter(line => {
      const trimmed = line.trim();
      return trimmed.length > 0 && 
             !trimmed.startsWith('#') && 
             !trimmed.startsWith('-') && 
             !trimmed.startsWith('*') && 
             !trimmed.startsWith('+') && 
             !trimmed.match(/^\d+\./) &&
             !trimmed.startsWith('|') &&
             !trimmed.startsWith('>') &&
             !trimmed.startsWith('```');
    });
  const paragraphCount = paragraphs.length;

  // Heading count
  const headings = content.match(/^#{1,6}\s/gm) || [];
  const headingCount = headings.length;

  // List count (bullet and numbered lists)
  const lists = content.match(/^[\s]*[-*+]\s|^[\s]*\d+\.\s/gm) || [];
  const listCount = lists.length;

  // Image count
  const images = content.match(/!\[([^\]]*)\]\([^)]+\)/g) || [];
  const imageCount = images.length;

  // Link count
  const links = content.match(/\[([^\]]+)\]\([^)]+\)/g) || [];
  const linkCount = links.length;

  // Code block count
  const codeBlocks = content.match(/```[\s\S]*?```/g) || [];
  const codeBlockCount = codeBlocks.length;

  // Table count
  const tables = content.match(/^\|.*\|$/gm);
  const tableSeparators = content.match(/^\|[-\s|:]+\|$/gm);
  const tableCount = tableSeparators ? tableSeparators.length : 0;

  // Reading time (average 200 words per minute)
  const readingTimeMinutes = Math.ceil(wordCount / 200);

  return {
    wordCount,
    characterCount,
    paragraphCount,
    headingCount,
    listCount,
    imageCount,
    linkCount,
    codeBlockCount,
    tableCount,
    readingTimeMinutes
  };
}

/**
 * Extracts all headings from markdown content
 */
export function extractHeadings(content: string): Array<{ level: number; text: string; id: string }> {
  const headingRegex = /^(#{1,6})\s+(.+)$/gm;
  const headings: Array<{ level: number; text: string; id: string }> = [];
  let match;

  while ((match = headingRegex.exec(content)) !== null) {
    const level = match[1].length;
    const text = match[2].trim();
    const id = text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');

    headings.push({ level, text, id });
  }

  return headings;
}

/**
 * Generates a table of contents from markdown content
 */
export function generateTableOfContents(content: string): string {
  const headings = extractHeadings(content);
  
  if (headings.length === 0) {
    return '';
  }

  let toc = '## Table of Contents\n\n';
  
  headings.forEach(heading => {
    const indent = '  '.repeat(heading.level - 1);
    toc += `${indent}- [${heading.text}](#${heading.id})\n`;
  });

  return toc + '\n';
}

/**
 * Cleans up markdown content by fixing common formatting issues
 */
export function cleanupMarkdown(content: string): string {
  let cleaned = content;

  // Fix multiple consecutive line breaks
  cleaned = cleaned.replace(/\n{3,}/g, '\n\n');

  // Fix spacing around headings
  cleaned = cleaned.replace(/^(#{1,6}\s.+)$/gm, '\n$1\n');

  // Fix list formatting
  cleaned = cleaned.replace(/^(\s*[-*+])\s+/gm, '$1 ');
  cleaned = cleaned.replace(/^(\s*\d+\.)\s+/gm, '$1 ');

  // Fix link formatting
  cleaned = cleaned.replace(/\[\s*([^\]]+)\s*\]\(\s*([^)]+)\s*\)/g, '[$1]($2)');

  // Fix image formatting
  cleaned = cleaned.replace(/!\[\s*([^\]]*)\s*\]\(\s*([^)]+)\s*\)/g, '![$1]($2)');

  // Remove trailing whitespace
  cleaned = cleaned.replace(/[ \t]+$/gm, '');

  // Ensure file ends with single newline
  cleaned = cleaned.replace(/\n*$/, '\n');

  return cleaned;
}

/**
 * Converts markdown to plain text (strips all formatting)
 */
export function markdownToPlainText(content: string): string {
  let plainText = content;

  // Remove code blocks
  plainText = plainText.replace(/```[\s\S]*?```/g, '');

  // Remove inline code
  plainText = plainText.replace(/`[^`]+`/g, '');

  // Remove images
  plainText = plainText.replace(/!\[([^\]]*)\]\([^)]+\)/g, '$1');

  // Remove links but keep text
  plainText = plainText.replace(/\[([^\]]+)\]\([^)]+\)/g, '$1');

  // Remove headings markers
  plainText = plainText.replace(/^#{1,6}\s+/gm, '');

  // Remove list markers
  plainText = plainText.replace(/^[\s]*[-*+]\s+/gm, '');
  plainText = plainText.replace(/^[\s]*\d+\.\s+/gm, '');

  // Remove blockquote markers
  plainText = plainText.replace(/^>\s*/gm, '');

  // Remove emphasis
  plainText = plainText.replace(/\*\*([^*]+)\*\*/g, '$1');
  plainText = plainText.replace(/\*([^*]+)\*/g, '$1');
  plainText = plainText.replace(/__([^_]+)__/g, '$1');
  plainText = plainText.replace(/_([^_]+)_/g, '$1');

  // Remove strikethrough
  plainText = plainText.replace(/~~([^~]+)~~/g, '$1');

  // Remove horizontal rules
  plainText = plainText.replace(/^[-*_]{3,}$/gm, '');

  // Clean up whitespace
  plainText = plainText.replace(/\n{2,}/g, '\n\n');
  plainText = plainText.trim();

  return plainText;
}

/**
 * Searches for text within markdown content, ignoring formatting
 */
export function searchMarkdown(content: string, query: string, caseSensitive = false): Array<{ line: number; text: string; match: string }> {
  const lines = content.split('\n');
  const results: Array<{ line: number; text: string; match: string }> = [];
  const searchQuery = caseSensitive ? query : query.toLowerCase();

  lines.forEach((line, index) => {
    const plainLine = markdownToPlainText(line);
    const searchLine = caseSensitive ? plainLine : plainLine.toLowerCase();
    
    if (searchLine.includes(searchQuery)) {
      results.push({
        line: index + 1,
        text: line.trim(),
        match: query
      });
    }
  });

  return results;
}
